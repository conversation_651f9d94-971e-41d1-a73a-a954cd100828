import{j as e}from"./index-BDJ8oeCE.js";import{a as c,b as r,j as s}from"./DefaultLayout-BolUaDEE.js";import{a}from"./index.esm-DSzlmaRN.js";import{C as i,a as l}from"./CRow-C1o2zw34.js";import{C as n,a as d}from"./CCardBody-iimbKiZ7.js";import{C as o}from"./CCardHeader-CFnfD6gM.js";import"./cil-user-Ddrdy7PS.js";const y=()=>e.jsxs(i,{children:[e.jsx(l,{xs:12,children:e.jsx(c,{href:"components/badge/"})}),e.jsx(l,{lg:6,children:e.jsxs(n,{className:"mb-4",children:[e.jsxs(o,{children:[e.jsx("strong",{children:"React Badges"})," ",e.jsx("small",{children:"Dismissing"})]}),e.jsxs(d,{children:[e.jsxs("p",{className:"text-body-secondary small",children:["Bootstrap badge scale to suit the size of the parent element by using relative font sizing and ",e.jsx("code",{children:"em"})," units."]}),e.jsxs(r,{href:"components/badge",children:[e.jsxs("h1",{children:["Example heading ",e.jsx(s,{color:"secondary",children:"New"})]}),e.jsxs("h2",{children:["Example heading ",e.jsx(s,{color:"secondary",children:"New"})]}),e.jsxs("h3",{children:["Example heading ",e.jsx(s,{color:"secondary",children:"New"})]}),e.jsxs("h4",{children:["Example heading ",e.jsx(s,{color:"secondary",children:"New"})]}),e.jsxs("h5",{children:["Example heading ",e.jsx(s,{color:"secondary",children:"New"})]}),e.jsxs("h6",{children:["Example heading ",e.jsx(s,{color:"secondary",children:"New"})]})]}),e.jsx("p",{className:"text-body-secondary small",children:"Badges can be used as part of links or buttons to provide a counter."}),e.jsx(r,{href:"components/badge",children:e.jsxs(a,{color:"primary",children:["Notifications ",e.jsx(s,{color:"secondary",children:"4"})]})}),e.jsx("p",{className:"text-body-secondary small",children:"Remark that depending on how you use them, badges may be complicated for users of screen readers and related assistive technologies."}),e.jsx("p",{className:"text-body-secondary small",children:"Unless the context is clear, consider including additional context with a visually hidden piece of additional text."}),e.jsx(r,{href:"components/badge",children:e.jsxs(a,{color:"primary",children:["Profile ",e.jsx(s,{color:"secondary",children:"9"}),e.jsx("span",{className:"visually-hidden",children:"unread messages"})]})})]})]})}),e.jsxs(l,{lg:6,children:[e.jsxs(n,{className:"mb-4",children:[e.jsxs(o,{children:[e.jsx("strong",{children:"React Badges"})," ",e.jsx("small",{children:"Contextual variations"})]}),e.jsxs(d,{children:[e.jsxs("p",{className:"text-body-secondary small",children:["Add any of the below-mentioned ",e.jsx("code",{children:"color"})," props to modify the presentation of a badge."]}),e.jsxs(r,{href:"components/badge#contextual-variations",children:[e.jsx(s,{color:"primary",children:"primary"}),e.jsx(s,{color:"success",children:"success"}),e.jsx(s,{color:"danger",children:"danger"}),e.jsx(s,{color:"warning",children:"warning"}),e.jsx(s,{color:"info",children:"info"}),e.jsx(s,{color:"light",children:"light"}),e.jsx(s,{color:"dark",children:"dark"})]})]})]}),e.jsxs(n,{className:"mb-4",children:[e.jsxs(o,{children:[e.jsx("strong",{children:"React Badges"})," ",e.jsx("small",{children:"Pill badges"})]}),e.jsxs(d,{children:[e.jsxs("p",{className:"text-body-secondary small",children:["Apply the ",e.jsx("code",{children:'shape="rounded-pill"'})," prop to make badges rounded."]}),e.jsxs(r,{href:"components/badge#pill-badges",children:[e.jsx(s,{color:"primary",shape:"rounded-pill",children:"primary"}),e.jsx(s,{color:"success",shape:"rounded-pill",children:"success"}),e.jsx(s,{color:"danger",shape:"rounded-pill",children:"danger"}),e.jsx(s,{color:"warning",shape:"rounded-pill",children:"warning"}),e.jsx(s,{color:"info",shape:"rounded-pill",children:"info"}),e.jsx(s,{color:"light",shape:"rounded-pill",children:"light"}),e.jsx(s,{color:"dark",shape:"rounded-pill",children:"dark"})]})]})]})]})]});export{y as default};
