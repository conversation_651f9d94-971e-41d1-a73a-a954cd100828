import{j as s}from"./index-BDJ8oeCE.js";import{b as l,c as o,a as t}from"./index.esm-DSzlmaRN.js";import{C as i,a as m}from"./CRow-C1o2zw34.js";import{C as n,a as d}from"./CCardBody-iimbKiZ7.js";import{C as p}from"./CForm-C4rJo8l4.js";import{C as e,a as r}from"./CInputGroupText-BGHrT9V9.js";import{c as x}from"./cil-user-Ddrdy7PS.js";import{C as a}from"./CFormInput-LKfVdWds.js";import{c}from"./cil-lock-locked-DmxpJbVL.js";import"./CFormControlWrapper-CyzWU6Dg.js";import"./CFormControlValidation-_wBnnnml.js";import"./CFormLabel-CzXD3nfE.js";const v=()=>s.jsx("div",{className:"bg-body-tertiary min-vh-100 d-flex flex-row align-items-center",children:s.jsx(l,{children:s.jsx(i,{className:"justify-content-center",children:s.jsx(m,{md:9,lg:7,xl:6,children:s.jsx(n,{className:"mx-4",children:s.jsx(d,{className:"p-4",children:s.jsxs(p,{children:[s.jsx("h1",{children:"Register"}),s.jsx("p",{className:"text-body-secondary",children:"Create your account"}),s.jsxs(e,{className:"mb-3",children:[s.jsx(r,{children:s.jsx(o,{icon:x})}),s.jsx(a,{placeholder:"Username",autoComplete:"username"})]}),s.jsxs(e,{className:"mb-3",children:[s.jsx(r,{children:"@"}),s.jsx(a,{placeholder:"Email",autoComplete:"email"})]}),s.jsxs(e,{className:"mb-3",children:[s.jsx(r,{children:s.jsx(o,{icon:c})}),s.jsx(a,{type:"password",placeholder:"Password",autoComplete:"new-password"})]}),s.jsxs(e,{className:"mb-4",children:[s.jsx(r,{children:s.jsx(o,{icon:c})}),s.jsx(a,{type:"password",placeholder:"Repeat password",autoComplete:"new-password"})]}),s.jsx("div",{className:"d-grid",children:s.jsx(t,{color:"success",children:"Create Account"})})]})})})})})})});export{v as default};
