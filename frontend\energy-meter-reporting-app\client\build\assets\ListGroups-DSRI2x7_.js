import{j as s}from"./index-BDJ8oeCE.js";import{a as x,b as i,j as h}from"./DefaultLayout-BolUaDEE.js";import"./index.esm-DSzlmaRN.js";import{C as j,a as l}from"./CRow-C1o2zw34.js";import{C as a,a as t}from"./CCardBody-iimbKiZ7.js";import{C as n}from"./CCardHeader-CFnfD6gM.js";import{C as r,a as e}from"./CListGroupItem-sk_b22Qj.js";import{C as d}from"./CFormCheck-CL4oiK6y.js";import"./cil-user-Ddrdy7PS.js";import"./CFormControlValidation-_wBnnnml.js";import"./CFormLabel-CzXD3nfE.js";const k=()=>s.jsxs(j,{children:[s.jsxs(l,{xs:12,children:[s.jsx(x,{href:"components/list-group/"}),s.jsxs(a,{className:"mb-4",children:[s.jsxs(n,{children:[s.jsx("strong",{children:"React List Group"})," ",s.jsx("small",{children:"Basic example"})]}),s.jsxs(t,{children:[s.jsx("p",{className:"text-body-secondary small",children:"The default list group is an unordered list with items and the proper CSS classes. Build upon it with the options that follow, or with your CSS as required."}),s.jsx(i,{href:"components/list-group",children:s.jsxs(r,{children:[s.jsx(e,{children:"Cras justo odio"}),s.jsx(e,{children:"Dapibus ac facilisis in"}),s.jsx(e,{children:"Morbi leo risus"}),s.jsx(e,{children:"Porta ac consectetur ac"}),s.jsx(e,{children:"Vestibulum at eros"})]})})]})]})]}),s.jsx(l,{xs:12,children:s.jsxs(a,{className:"mb-4",children:[s.jsxs(n,{children:[s.jsx("strong",{children:"React List Group"})," ",s.jsx("small",{children:"Active items"})]}),s.jsxs(t,{children:[s.jsxs("p",{className:"text-body-secondary small",children:["Add ",s.jsx("code",{children:"active"})," boolean property to a ",s.jsx("code",{children:"<CListGroupItem>"})," to show the current active selection."]}),s.jsx(i,{href:"components/list-group/#active-items",children:s.jsxs(r,{children:[s.jsx(e,{active:!0,children:"Cras justo odio"}),s.jsx(e,{children:"Dapibus ac facilisis in"}),s.jsx(e,{children:"Morbi leo risus"}),s.jsx(e,{children:"Porta ac consectetur ac"}),s.jsx(e,{children:"Vestibulum at eros"})]})})]})]})}),s.jsx(l,{xs:12,children:s.jsxs(a,{className:"mb-4",children:[s.jsxs(n,{children:[s.jsx("strong",{children:"React List Group"})," ",s.jsx("small",{children:"Disabled items"})]}),s.jsxs(t,{children:[s.jsxs("p",{className:"text-body-secondary small",children:["Add ",s.jsx("code",{children:"disabled"})," boolean property to a ",s.jsx("code",{children:"<CListGroupItem>"})," to make it appear disabled."]}),s.jsx(i,{href:"components/list-group/#disabled-items",children:s.jsxs(r,{children:[s.jsx(e,{disabled:!0,children:"Cras justo odio"}),s.jsx(e,{children:"Dapibus ac facilisis in"}),s.jsx(e,{children:"Morbi leo risus"}),s.jsx(e,{children:"Porta ac consectetur ac"}),s.jsx(e,{children:"Vestibulum at eros"})]})})]})]})}),s.jsx(l,{xs:12,children:s.jsxs(a,{className:"mb-4",children:[s.jsxs(n,{children:[s.jsx("strong",{children:"React List Group"})," ",s.jsx("small",{children:"Links and buttons"})]}),s.jsxs(t,{children:[s.jsxs("p",{className:"text-body-secondary small",children:["Use ",s.jsx("code",{children:"<a>"}),"s or ",s.jsx("code",{children:"<button>"}),"s to create"," ",s.jsx("em",{children:"actionable"})," list group items with hover, disabled, and active states by adding"," ",s.jsx("code",{children:'as="a|button"'}),". We separate these pseudo-classes to ensure list groups made of non-interactive elements (like ",s.jsx("code",{children:"<li>"}),"s or"," ",s.jsx("code",{children:"<div>"}),"s) don'tprovide a click or tap affordance."]}),s.jsx(i,{href:"components/list-group/#links-and-buttons",children:s.jsxs(r,{children:[s.jsx(e,{as:"a",href:"#",active:!0,children:"Cras justo odio"}),s.jsx(e,{as:"a",href:"#",children:"Dapibus ac facilisis in"}),s.jsx(e,{as:"a",href:"#",children:"Morbi leo risus"}),s.jsx(e,{as:"a",href:"#",children:"Porta ac consectetur ac"}),s.jsx(e,{as:"a",href:"#",disabled:!0,children:"Vestibulum at eros"})]})})]})]})}),s.jsx(l,{xs:12,children:s.jsxs(a,{className:"mb-4",children:[s.jsxs(n,{children:[s.jsx("strong",{children:"React List Group"})," ",s.jsx("small",{children:"Flush"})]}),s.jsxs(t,{children:[s.jsxs("p",{className:"text-body-secondary small",children:["Add ",s.jsx("code",{children:"flush"})," boolean property to remove some borders and rounded corners to render list group items edge-to-edge in a parent container (e.g., cards)."]}),s.jsx(i,{href:"components/list-group/#flush",children:s.jsxs(r,{flush:!0,children:[s.jsx(e,{children:"Cras justo odio"}),s.jsx(e,{children:"Dapibus ac facilisis in"}),s.jsx(e,{children:"Morbi leo risus"}),s.jsx(e,{children:"Porta ac consectetur ac"}),s.jsx(e,{children:"Vestibulum at eros"})]})})]})]})}),s.jsx(l,{xs:12,children:s.jsxs(a,{className:"mb-4",children:[s.jsxs(n,{children:[s.jsx("strong",{children:"React List Group"})," ",s.jsx("small",{children:"Horizontal"})]}),s.jsxs(t,{children:[s.jsxs("p",{className:"text-body-secondary small",children:["Add ",s.jsx("code",{children:'layout="horizontal"'})," to change the layout of list group items from vertical to horizontal across all breakpoints. Alternatively, choose a responsive variant ",s.jsx("code",{children:'.layout="horizontal-{sm | md | lg | xl | xxl}"'})," ","to make a list group horizontal starting at that breakpoint's"," ",s.jsx("code",{children:"min-width"}),". Currently"," ",s.jsx("strong",{children:"horizontal list groups cannot be combined with flush list groups."})]}),s.jsx(i,{href:"components/list-group/#flush",children:["","-sm","-md","-lg","-xl","-xxl"].map((c,o)=>s.jsxs(r,{className:"mb-2",layout:`horizontal${c}`,children:[s.jsx(e,{children:"Cras justo odio"}),s.jsx(e,{children:"Dapibus ac facilisis in"}),s.jsx(e,{children:"Morbi leo risus"})]},o))})]})]})}),s.jsx(l,{xs:12,children:s.jsxs(a,{className:"mb-4",children:[s.jsxs(n,{children:[s.jsx("strong",{children:"React List Group"})," ",s.jsx("small",{children:"Contextual classes"})]}),s.jsxs(t,{children:[s.jsx("p",{className:"text-body-secondary small",children:"Use contextual classes to style list items with a stateful background and color."}),s.jsx(i,{href:"components/list-group/#contextual-classes",children:s.jsxs(r,{children:[s.jsx(e,{children:"Dapibus ac facilisis in"}),["primary","secondary","success","danger","warning","info","light","dark"].map((c,o)=>s.jsxs(e,{color:c,children:["A simple ",c," list group item"]},o))]})}),s.jsxs("p",{className:"text-body-secondary small",children:["Contextual classes also work with ",s.jsx("code",{children:"<a>"}),"s or"," ",s.jsx("code",{children:"<button>"}),"s. Note the addition of the hover styles here not present in the previous example. Also supported is the ",s.jsx("code",{children:"active"})," state; apply it to indicate an active selection on a contextual list group item."]}),s.jsx(i,{href:"components/list-group/#contextual-classes",children:s.jsxs(r,{children:[s.jsx(e,{as:"a",href:"#",children:"Dapibus ac facilisis in"}),["primary","secondary","success","danger","warning","info","light","dark"].map((c,o)=>s.jsxs(e,{as:"a",href:"#",color:c,children:["A simple ",c," list group item"]},o))]})})]})]})}),s.jsx(l,{xs:12,children:s.jsxs(a,{className:"mb-4",children:[s.jsxs(n,{children:[s.jsx("strong",{children:"React List Group"})," ",s.jsx("small",{children:"With badges"})]}),s.jsxs(t,{children:[s.jsx("p",{className:"text-body-secondary small",children:"Add badges to any list group item to show unread counts, activity, and more."}),s.jsx(i,{href:"components/list-group/#with-badges",children:s.jsxs(r,{children:[s.jsxs(e,{className:"d-flex justify-content-between align-items-center",children:["Cras justo odio",s.jsx(h,{color:"primary",shape:"rounded-pill",children:"14"})]}),s.jsxs(e,{className:"d-flex justify-content-between align-items-center",children:["Dapibus ac facilisis in",s.jsx(h,{color:"primary",shape:"rounded-pill",children:"2"})]}),s.jsxs(e,{className:"d-flex justify-content-between align-items-center",children:["Morbi leo risus",s.jsx(h,{color:"primary",shape:"rounded-pill",children:"1"})]})]})})]})]})}),s.jsx(l,{xs:12,children:s.jsxs(a,{className:"mb-4",children:[s.jsxs(n,{children:[s.jsx("strong",{children:"React List Group"})," ",s.jsx("small",{children:"Custom content"})]}),s.jsxs(t,{children:[s.jsxs("p",{className:"text-body-secondary small",children:["Add nearly any HTML within, even for linked list groups like the one below, with the help of ",s.jsx("a",{href:"https://coreui.io/docs/utilities/flex/",children:"flexbox utilities"}),"."]}),s.jsx(i,{href:"components/list-group/#custom-content",children:s.jsxs(r,{children:[s.jsxs(e,{as:"a",href:"#",active:!0,children:[s.jsxs("div",{className:"d-flex w-100 justify-content-between",children:[s.jsx("h5",{className:"mb-1",children:"List group item heading"}),s.jsx("small",{children:"3 days ago"})]}),s.jsx("p",{className:"mb-1",children:"Donec id elit non mi porta gravida at eget metus. Maecenas sed diam eget risus varius blandit."}),s.jsx("small",{children:"Donec id elit non mi porta."})]}),s.jsxs(e,{as:"a",href:"#",children:[s.jsxs("div",{className:"d-flex w-100 justify-content-between",children:[s.jsx("h5",{className:"mb-1",children:"List group item heading"}),s.jsx("small",{className:"text-body-secondary",children:"3 days ago"})]}),s.jsx("p",{className:"mb-1",children:"Donec id elit non mi porta gravida at eget metus. Maecenas sed diam eget risus varius blandit."}),s.jsx("small",{className:"text-body-secondary",children:"Donec id elit non mi porta."})]}),s.jsxs(e,{as:"a",href:"#",children:[s.jsxs("div",{className:"d-flex w-100 justify-content-between",children:[s.jsx("h5",{className:"mb-1",children:"List group item heading"}),s.jsx("small",{className:"text-body-secondary",children:"3 days ago"})]}),s.jsx("p",{className:"mb-1",children:"Donec id elit non mi porta gravida at eget metus. Maecenas sed diam eget risus varius blandit."}),s.jsx("small",{className:"text-body-secondary",children:"Donec id elit non mi porta."})]})]})})]})]})}),s.jsx(l,{xs:12,children:s.jsxs(a,{className:"mb-4",children:[s.jsxs(n,{children:[s.jsx("strong",{children:"React List Group"})," ",s.jsx("small",{children:"Checkboxes and radios"})]}),s.jsxs(t,{children:[s.jsx("p",{className:"text-body-secondary small",children:"Place CoreUI's checkboxes and radios within list group items and customize as needed."}),s.jsx(i,{href:"components/list-group/#checkboxes-and-radios",children:s.jsxs(r,{children:[s.jsx(e,{children:s.jsx(d,{label:"Cras justo odio"})}),s.jsx(e,{children:s.jsx(d,{label:"Dapibus ac facilisis in",defaultChecked:!0})}),s.jsx(e,{children:s.jsx(d,{label:"Morbi leo risus",defaultChecked:!0})}),s.jsx(e,{children:s.jsx(d,{label:"orta ac consectetur ac"})}),s.jsx(e,{children:s.jsx(d,{label:"Vestibulum at eros"})})]})})]})]})})]});export{k as default};
