import{a as t,q as i,v as c,j as e}from"./index-BDJ8oeCE.js";import{a as I,b as y,C as N,c as V}from"./CToaster-DxUpGnuG.js";import{C as A,a as E}from"./CCardBody-iimbKiZ7.js";import{C as D}from"./CCardHeader-CFnfD6gM.js";import{a as l}from"./index.esm-DSzlmaRN.js";import{C as M,a as $,b as j,c as m,d as k,e as h}from"./CTable-BG2MPlsJ.js";import{C as B,a as F,b as H,c as L}from"./CModalHeader-DX4AicsN.js";import{C as q}from"./CForm-C4rJo8l4.js";import{C as g}from"./CFormInput-LKfVdWds.js";import{C as z}from"./CFormSelect-B3z3ot4z.js";import"./DefaultLayout-BolUaDEE.js";import"./cil-user-Ddrdy7PS.js";import"./CFormControlWrapper-CyzWU6Dg.js";import"./CFormControlValidation-_wBnnnml.js";import"./CFormLabel-CzXD3nfE.js";const oe=()=>{const[w,f]=t.useState([]),[b,d]=t.useState(!1),[a,n]=t.useState({id:null,username:"",password:"",role:"Viewer"}),[U,v]=t.useState(null);t.useEffect(()=>{u()},[]);const u=()=>{i.get(`${c.baseURL}/manageusers`).then(s=>{const r=localStorage.getItem("userRole"),p=localStorage.getItem("userid"),T=r==="Viewer"?s.data.filter(x=>x.id===parseInt(p)):r&&r!=="SuperAdmin"?s.data.filter(x=>x.role!=="SuperAdmin"):s.data;f(T)}).catch(()=>o("Error fetching users","danger"))},R=()=>{const s={username:a.username,role:a.role};a.password&&(s.password=a.password),(a.id?i.put(`${c.baseURL}/manageusers/${a.id}`,s):i.post(`${c.baseURL}/manageusers`,s)).then(p=>{u(),d(!1),o(p.data.message,"success")}).catch(()=>o("Error saving user","danger"))},S=s=>{i.delete(`${c.baseURL}/manageusers/${s}`).then(r=>{u(),o(r.data.message,"success")}).catch(()=>o("Error deleting user","danger"))},C=(s={id:null,username:"",password:"",role:"Viewer"})=>{n({...s,password:""}),d(!0)},o=(s,r)=>{v(e.jsx(I,{autohide:5e3,visible:!0,color:r,className:"text-white align-items-center",children:e.jsxs("div",{className:"d-flex",children:[e.jsx(y,{children:s}),e.jsx(N,{className:"me-2 m-auto",white:!0})]})},Date.now()))};return e.jsxs(e.Fragment,{children:[e.jsx(V,{push:U,placement:"top-center",className:"mt-3"}),e.jsxs(A,{children:[e.jsxs(D,{children:[e.jsx("strong",{children:"Users"}),localStorage.getItem("userRole")!=="Viewer"&&e.jsx(l,{color:"primary",className:"float-end",onClick:()=>C(),children:"Add New"})]}),e.jsx(E,{children:e.jsxs(M,{hover:!0,responsive:!0,children:[e.jsx($,{children:e.jsxs(j,{children:[e.jsx(m,{children:"ID"}),e.jsx(m,{children:"Username"}),e.jsx(m,{children:"Role"}),e.jsx(m,{children:"Actions"})]})}),e.jsx(k,{children:w.map(s=>e.jsxs(j,{children:[e.jsx(h,{children:s.id}),e.jsx(h,{children:s.username}),e.jsx(h,{children:s.role}),e.jsxs(h,{children:[e.jsx(l,{color:"warning",size:"sm",className:"me-2",onClick:()=>C(s),children:"Edit"}),localStorage.getItem("userRole")!=="Viewer"&&e.jsx(l,{color:"danger",size:"sm",onClick:()=>S(s.id),children:"Delete"})]})]},s.id))})]})}),e.jsxs(B,{visible:b,onClose:()=>d(!1),children:[e.jsx(F,{children:a.id?"Edit User":"Add User"}),e.jsx(H,{children:e.jsxs(q,{children:[e.jsx(g,{type:"text",placeholder:"Username",value:a.username,onChange:s=>n({...a,username:s.target.value}),className:"mb-3"}),e.jsx(g,{type:"password",placeholder:a.id?"New Password (optional)":"Password",value:a.password||"",onChange:s=>n({...a,password:s.target.value}),className:"mb-3"}),e.jsxs(z,{value:a.role,onChange:s=>n({...a,role:s.target.value}),disabled:localStorage.getItem("userRole")==="Viewer"&&a.id!==null,children:[e.jsx("option",{value:"Viewer",children:"Viewer"}),e.jsx("option",{value:"Admin",children:"Admin"})]})]})}),e.jsxs(L,{children:[e.jsx(l,{color:"secondary",onClick:()=>d(!1),children:"Cancel"}),e.jsx(l,{color:"primary",onClick:R,children:a.id?"Update":"Add"})]})]})]})]})};export{oe as default};
