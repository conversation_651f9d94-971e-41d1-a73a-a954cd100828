import{j as s,C as r}from"./index-BDJ8oeCE.js";import{a as l,b as e}from"./DefaultLayout-BolUaDEE.js";import{a as n}from"./index.esm-DSzlmaRN.js";import{C as t,a as o}from"./CRow-C1o2zw34.js";import{C as i,a}from"./CCardBody-iimbKiZ7.js";import{C as c}from"./CCardHeader-CFnfD6gM.js";import"./cil-user-Ddrdy7PS.js";const u=()=>s.jsxs(t,{children:[s.jsxs(o,{xs:12,children:[s.jsx(l,{href:"components/spinner/"}),s.jsxs(i,{className:"mb-4",children:[s.jsxs(c,{children:[s.jsx("strong",{children:"React Spinner"})," ",s.jsx("small",{children:"Border"})]}),s.jsxs(a,{children:[s.jsx("p",{className:"text-body-secondary small",children:"Use the border spinners for a lightweight loading indicator."}),s.jsx(e,{href:"components/spinner",children:s.jsx(r,{})}),s.jsxs("p",{className:"text-body-secondary small",children:["The border spinner uses ",s.jsx("code",{children:"currentColor"})," for its ",s.jsx("code",{children:"border-color"}),". You can use any of our text color utilities on the standard spinner."]}),s.jsxs(e,{href:"components/spinner#colors",children:[s.jsx(r,{color:"primary"}),s.jsx(r,{color:"secondary"}),s.jsx(r,{color:"success"}),s.jsx(r,{color:"danger"}),s.jsx(r,{color:"warning"}),s.jsx(r,{color:"info"}),s.jsx(r,{color:"light"}),s.jsx(r,{color:"dark"})]})]})]})]}),s.jsx(o,{xs:12,children:s.jsxs(i,{className:"mb-4",children:[s.jsxs(c,{children:[s.jsx("strong",{children:"React Spinner"})," ",s.jsx("small",{children:"Growing"})]}),s.jsxs(a,{children:[s.jsx("p",{className:"text-body-secondary small",children:"If you don'tfancy a border spinner, switch to the grow spinner. While it doesn't technically spin, it does repeatedly grow!"}),s.jsx(e,{href:"components/spinner#growing-spinner",children:s.jsx(r,{variant:"grow"})}),s.jsxs("p",{className:"text-body-secondary small",children:["Once again, this spinner is built with ",s.jsx("code",{children:"currentColor"}),", so you can easily change its appearance. Here it is in blue, along with the supported variants."]}),s.jsxs(e,{href:"components/spinner#growing-spinner",children:[s.jsx(r,{color:"primary",variant:"grow"}),s.jsx(r,{color:"secondary",variant:"grow"}),s.jsx(r,{color:"success",variant:"grow"}),s.jsx(r,{color:"danger",variant:"grow"}),s.jsx(r,{color:"warning",variant:"grow"}),s.jsx(r,{color:"info",variant:"grow"}),s.jsx(r,{color:"light",variant:"grow"}),s.jsx(r,{color:"dark",variant:"grow"})]})]})]})}),s.jsx(o,{xs:12,children:s.jsxs(i,{className:"mb-4",children:[s.jsxs(c,{children:[s.jsx("strong",{children:"React Spinner"})," ",s.jsx("small",{children:"Size"})]}),s.jsxs(a,{children:[s.jsxs("p",{className:"text-body-secondary small",children:["Add ",s.jsx("code",{children:'size="sm"'})," property to make a smaller spinner that can quickly be used within other components."]}),s.jsxs(e,{href:"components/spinner#size",children:[s.jsx(r,{size:"sm"}),s.jsx(r,{size:"sm",variant:"grow"})]})]})]})}),s.jsx(o,{xs:12,children:s.jsxs(i,{className:"mb-4",children:[s.jsxs(c,{children:[s.jsx("strong",{children:"React Spinner"})," ",s.jsx("small",{children:"Buttons"})]}),s.jsxs(a,{children:[s.jsx("p",{className:"text-body-secondary small",children:"Use spinners within buttons to indicate an action is currently processing or taking place. You may also swap the text out of the spinner element and utilize button text as needed."}),s.jsxs(e,{href:"components/spinner#buttons",children:[s.jsx(n,{color:"primary",disabled:!0,children:s.jsx(r,{as:"span",size:"sm","aria-hidden":"true"})}),s.jsxs(n,{color:"primary",disabled:!0,children:[s.jsx(r,{as:"span",size:"sm","aria-hidden":"true"}),"Loading..."]})]}),s.jsxs(e,{href:"components/spinner#buttons",children:[s.jsx(n,{color:"primary",disabled:!0,children:s.jsx(r,{as:"span",size:"sm",variant:"grow","aria-hidden":"true"})}),s.jsxs(n,{color:"primary",disabled:!0,children:[s.jsx(r,{as:"span",size:"sm",variant:"grow","aria-hidden":"true"}),"Loading..."]})]})]})]})})]});export{u as default};
