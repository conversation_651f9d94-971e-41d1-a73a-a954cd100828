const sql = require('mssql');
const ExcelJS = require('exceljs');
const fs = require('fs');
const path = require('path');
const config = require('./config');
const { getdailyquery, getmonthlyquery, getyearlyquery, getmonthlyAllFeeders } = require('./utils/queries');
const { copyFormatAndText, copyMergedCells, addFormulaForRow,
    writeEMNames, expandMergedCells, applyThickBorderToSheet,
    safeUnmerge, copyColumnLength, moveproductioncells,
    applyProductionFormula, applyMergedCellFormula, protectsheetfn,
    finalTotalFormula, totalLastColumns, applyThinBorderToRange, applyThickToBottom, applyThickToRight } = require('./utils/excelformater');

// MSSQL Database Configuration,
const dbConfig = {
    ...config.db,
    options: {
        encrypt: false, // Change to true if using Azure
        trustServerCertificate: true
    },
    requestTimeout: 60000
};

// Function to Generate Excel Report
async function generateIncomerReport(start_time, em_ids, em_names, parameterArray, plant_name, report_type, protectsheet = false, arrayunlockedcells = []) {
    try {
        const emIDArray = em_ids.split(','); // Convert to array
        const emNameArray = em_names.split(',');
        const dataColumnsLength = parameterArray.length;

        let endTime;
        const startDate = new Date(start_time); // Add 5 hours 30 minutes (330 minutes)

        if (report_type === 1) { // Daily
            endTime = startDate;
            endTime.setHours(23, 59, 59);
        } else if (report_type === 2) { // Monthly
            endTime = new Date(startDate.getFullYear(), startDate.getMonth() + 1, 0); // Last day of the month
            endTime.setHours(23, 59, 59);
        } else if (report_type === 3) { // Yearly
            endTime = new Date(startDate.getFullYear(), 11, 31); // December 31st
            endTime.setHours(23, 59, 59);
        }

        // Add 5 hours and 30 minutes to end_time
        endTime = new Date(endTime.getTime() + 330 * 60 * 1000);

        const end_time = endTime.toISOString();
        // console.log(end_time);

        // Connect to MSSQL
        await sql.connect(dbConfig);

        // Query to Fetch Data
        let query;

        if (report_type === 2) {
            query = getmonthlyquery(start_time, end_time, emIDArray, parameterArray);
        }
        else if (report_type === 3) {
            query = getyearlyquery(start_time, end_time, emIDArray, parameterArray);
        }
        else {
            query = getdailyquery(start_time, end_time, emIDArray, parameterArray);
        }

        const result = await sql.query(query);



        const data = result.recordset;
        if (data.length === 0) {
            console.log('No data found');
            return;
        }

        // Load Template
        const templatePath = path.join(__dirname, 'Report_format', 'Incomer_format.xlsx');
        const workbook = new ExcelJS.Workbook();
        await workbook.xlsx.readFile(templatePath);
        const worksheet = workbook.worksheets[report_type - 1]; // report_type =1 daily. 2 - monthly , 3- Yearly

        const selectedSheetName = worksheet.name;

        // Remove all other sheets except the selected one
        workbook.worksheets.forEach(sheet => {
            if (sheet.name !== selectedSheetName) {
                workbook.removeWorksheet(sheet.id);
            }
        });


        // Organize Data
        let groupedData = {};
        data.forEach(row => {
            //console.log(row.DateAndTime);
            if (!groupedData[row.DateAndTime]) {
                groupedData[row.DateAndTime] = {};
            }
            groupedData[row.DateAndTime][row.TagName] = row.Val;
        });

        worksheet.getCell('D2').value = plant_name;
        switch (report_type) {
            case 1:
                worksheet.getCell('D3').value = 'Daily Report';
                worksheet.getCell('D4').value = 'Date: ' + start_time.split('T')[0];
                break;
            case 2:
                worksheet.getCell('D3').value = 'Monthly Report';
                worksheet.getCell('D4').value = 'Month: ' + start_time.split('T')[0].slice(0, -3);
                break;

            case 3:
                worksheet.getCell('D3').value = 'Yearly Report';
                worksheet.getCell('D4').value = 'Year: ' + start_time.split('T')[0].slice(0, -6);
                break;
            default:
        }


        // Populate Rows
        let startRow = 8; // Assuming data starts from row 8 in template
        Object.keys(groupedData).forEach(datetime => {
            let row = worksheet.getRow(startRow++);
            let dateValue = new Date(datetime);

            //let dateValue = new Date(datetime);
            if (report_type === 3) {
                // Add 5 hours 30 minutes (330 minutes) to the date
                dateValue = new Date(dateValue.getTime() + 330 * 60 * 1000);
            }


            //  console.log(dateValue);
            row.getCell(1).value = dateValue;
            if (report_type === 2)
                row.getCell(1).numFmt = 'yyyy-mm-dd';

            let colIndex = 2;
            emIDArray.forEach(em => {
                row.getCell(colIndex++).value = groupedData[datetime][`${em}_RYPHASE_VOL`];
                row.getCell(colIndex++).value = groupedData[datetime][`${em}_YBPHASE_VOL`];
                row.getCell(colIndex++).value = groupedData[datetime][`${em}_BRPHASE_VOL`];
                row.getCell(colIndex++).value = groupedData[datetime][`${em}_RPHASE_CUR`];
                row.getCell(colIndex++).value = groupedData[datetime][`${em}_YPHASE_CUR`];
                row.getCell(colIndex++).value = groupedData[datetime][`${em}_BPHASE_CUR`];
                row.getCell(colIndex++).value = groupedData[datetime][`${em}_CD_KWH`];
                row.getCell(colIndex++).value = groupedData[datetime][`${em}_AVGPF`];
            });
            row.commit();
        });

        // Delete extra rows for monthly report


        const firstEMColumn = 2; // B column is index 2
        if (emIDArray.length > 1) {
            copyFormatAndText(worksheet, worksheet, "B5:I7", firstEMColumn + dataColumnsLength, emIDArray.length - 1);
            if (report_type === 1) {
                copyFormatAndText(worksheet, worksheet, "B33:I33", firstEMColumn + dataColumnsLength, emIDArray.length - 1);
                copyFormatAndText(worksheet, worksheet, "B8:I32", firstEMColumn + dataColumnsLength, emIDArray.length - 1, true);
            } else if (report_type === 2) {
                copyFormatAndText(worksheet, worksheet, "B39:I39", firstEMColumn + dataColumnsLength, emIDArray.length - 1);
                copyFormatAndText(worksheet, worksheet, "B8:I38", firstEMColumn + dataColumnsLength, emIDArray.length - 1, true);
            } else if (report_type === 3) {
                copyFormatAndText(worksheet, worksheet, "B20:I20", firstEMColumn + dataColumnsLength, emIDArray.length - 1);
                copyFormatAndText(worksheet, worksheet, "B8:I19", firstEMColumn + dataColumnsLength, emIDArray.length - 1, true);
            }

            copyMergedCells(worksheet, worksheet, firstEMColumn, firstEMColumn + dataColumnsLength, emIDArray.length - 1);
        }
        if (report_type === 1) {
            addFormulaForRow(worksheet, 32, 'upper');
        }
        if (report_type === 2) {
            addFormulaForRow(worksheet, 38, 'sum');

            const totalDaysInMonth = new Date(startDate.getFullYear(), startDate.getMonth() + 1, 0).getDate();
            if (totalDaysInMonth < 31) {
                for (let i = 8 + totalDaysInMonth; i <= 38; i++) {
                    worksheet.getRow(i).hidden = true;
                }
            }

        }
        if (report_type === 3) {
            addFormulaForRow(worksheet, 19, 'sum');

        }
        writeEMNames(worksheet, emNameArray);
        expandMergedCells(worksheet, emIDArray);
        applyThickBorderToSheet(worksheet, 8);
        //  
        if (protectsheet) {
            await protectsheetfn(worksheet, arrayunlockedcells);
        }


        // Save File
        const fileName = 'Formatted_Report.xlsx';
        const filePath = path.join(__dirname, fileName);
        await workbook.xlsx.writeFile(filePath);

        console.log(`Report generated successfully: ${filePath}`);
    } catch (error) {
        console.error('Error generating report:', error);
    }
}

async function generateShiftReport(start_time, em_ids, em_names, parameterArray, plant_name, report_type, protectsheet = false, arrayunlockedcells = []) {
    try {
        const emIDArray = em_ids.split(','); // Convert to array
        const emNameArray = em_names.split(',');
        const dataColumnsLength = parameterArray.length;

        let endTime;
        const startDate = new Date(start_time); // Add 5 hours 30 minutes (330 minutes)

        if (report_type === 1) { // Daily
            endTime = startDate;
            endTime.setHours(23, 59, 59);
        } else if (report_type === 2) { // Monthly
            endTime = new Date(startDate.getFullYear(), startDate.getMonth() + 1, 0); // Last day of the month
            endTime.setHours(23, 59, 59);
        } else if (report_type === 3) { // Yearly
            endTime = new Date(startDate.getFullYear(), 11, 31); // December 31st
            endTime.setHours(23, 59, 59);
        }

        // Add 5 hours and 30 minutes to end_time
        endTime = new Date(endTime.getTime() + 330 * 60 * 1000);

        const end_time = endTime.toISOString();
        // console.log(end_time);

        // Connect to MSSQL
        await sql.connect(dbConfig);
        // Query to Fetch Data
        let query;

        if (report_type === 2) {
            query = getmonthlyquery(start_time, end_time, emIDArray, parameterArray);
        }
        else if (report_type === 3) {
            query = getyearlyquery(start_time, end_time, emIDArray, parameterArray);
        }
        else {
            query = getdailyquery(start_time, end_time, emIDArray, parameterArray);
        }



        const result = await sql.query(query);
        const data = result.recordset;
        //console.log(data);
        if (data.length === 0) {
            console.log('No data found');
            return;
        }

        // Load Template
        const templatePath = path.join(__dirname, 'Report_format', 'Shift_Report.xlsx');
        const workbook = new ExcelJS.Workbook();
        await workbook.xlsx.readFile(templatePath);
        const worksheet = workbook.worksheets[0]; // report_type =1 daily. 2 - monthly , 3- Yearly

        const selectedSheetName = worksheet.name;

        // Remove all other sheets except the selected one
        workbook.worksheets.forEach(sheet => {
            if (sheet.name !== selectedSheetName) {
                workbook.removeWorksheet(sheet.id);
            }
        });


        // Organize Data
        let groupedData = {};
        data.forEach(row => {
            //console.log(row.DateAndTime);
            if (!groupedData[row.DateAndTime]) {
                groupedData[row.DateAndTime] = {};
            }
            groupedData[row.DateAndTime][row.TagName] = row.Val;
        });

        worksheet.getCell('C2').value = plant_name;
        switch (report_type) {
            case 1:
                worksheet.getCell('C3').value = 'Daily Report';
                worksheet.getCell('C4').value = 'Date: ' + start_time.split('T')[0];
                break;
            default:
        }

        const originalWidth = worksheet.getColumn(5).width; // Default width if undefined
        if (emIDArray.length > 1) {

            copyFormatAndText(worksheet, worksheet, "E5:E34", 5, ((emIDArray.length - 1) * 3) + 1, false, 1, 0, 5);
            // console.log(originalWidth);
            worksheet.getColumn(5 + ((dataColumnsLength + 1) * (emIDArray.length - 1))).width = originalWidth;
            //  console.log(worksheet.getColumn(5 + (dataColumnsLength * emIDArray.length)).width);

            copyMergedCells(worksheet, worksheet, 5, 5 + ((dataColumnsLength + 1) * (emIDArray.length - 1)), 1, 1, 5, 32);

            safeUnmerge(worksheet, 'E5:E32');
            //    worksheet.spliceColumns(5, 1); // remove  columns before total 
        }
        // Populate Rows

        let startRow = 7; // Assuming data starts from row 7 in template
        Object.keys(groupedData).forEach(datetime => {
            let row = worksheet.getRow(startRow++);
            let dateValue = new Date(datetime);


            //  console.log(dateValue);
            row.getCell(1).value = dateValue;


            let colIndex = 2;
            emIDArray.forEach(em => {
                row.getCell(colIndex++).value = groupedData[datetime][`${em}_CD_KWH`];
                colIndex++;
                row.getCell(colIndex++).value = groupedData[datetime][`${em}_AVGPF`];
            });
            row.commit();
        });

        // Modify this inside your function before populating rows
        if (emIDArray.length > 1) {

            const firstEMColumn = 2; // B column is index 
            console.log(dataColumnsLength);
            copyFormatAndText(worksheet, worksheet, "B5:D6", firstEMColumn + dataColumnsLength + 1, emIDArray.length - 1, false, dataColumnsLength + 1, 0);
            if (report_type === 1) {
                copyFormatAndText(worksheet, worksheet, "B32:D32", firstEMColumn + dataColumnsLength + 1, emIDArray.length - 1, false, dataColumnsLength + 1, 0);
                copyFormatAndText(worksheet, worksheet, "B7:D31", firstEMColumn + dataColumnsLength + 1, emIDArray.length - 1, true, dataColumnsLength + 1, 0);
            }
            copyMergedCells(worksheet, worksheet, firstEMColumn, firstEMColumn + dataColumnsLength + 1, emIDArray.length - 1, 2, 5, 6, 1);
            copyMergedCells(worksheet, worksheet, firstEMColumn, firstEMColumn + dataColumnsLength + 1, emIDArray.length - 1, 2, 7, 31, 1);

            copyColumnLength(worksheet, 2, 4, emIDArray.length - 1);

        }


        if (report_type === 1) {
            addFormulaForRow(worksheet, 31, 'upper', 3, dataColumnsLength + 1, 7, 1);
        }

        writeEMNames(worksheet, emNameArray, emIDArray.length, 5);
        expandMergedCells(worksheet, emIDArray, 3, 'D', 'C', 1);
        moveproductioncells(worksheet, 2, 3, 33, 34, emIDArray);


        applyProductionFormula(worksheet, 1 + (emIDArray.length * 3), 34, 32, 33);

        applyMergedCellFormula(worksheet, 3, 7, 34, (emIDArray.length - 1), 3);

        const lastcolumnId = 2 + (emIDArray.length * 3);
        applyMergedCellFormula(worksheet, lastcolumnId, 7, 31, (emIDArray.length - 1), dataColumnsLength + 1, 'sum', lastcolumnId);

        finalTotalFormula(worksheet, lastcolumnId, 32, (emIDArray.length - 1), dataColumnsLength + 1)

        const lastColumnLetter = worksheet.getColumn(lastcolumnId - 1).letter;
        worksheet.getCell(`${lastColumnLetter}32`).style = { ...worksheet.getCell(`${lastColumnLetter}33`).style };

        applyThickBorderToSheet(worksheet, 7, 0);


        arrayunlockedcells = [`${worksheet.getColumn(lastcolumnId - 1).letter}33`];

        if (protectsheet) {
            await protectsheetfn(worksheet, arrayunlockedcells);
        }


        // Save File
        const fileName = 'Formatted_Report.xlsx';
        const filePath = path.join(__dirname, fileName);
        await workbook.xlsx.writeFile(filePath);

        console.log(`Report generated successfully: ${filePath}`);
    } catch (error) {
        console.error('Error generating report:', error);
    }
}

async function generateOutgoingReport(start_time, em_ids, em_names, parameterArray, plant_name, report_type, protectsheet = false, arrayunlockedcells = []) {
    try {
        const emIDArray = em_ids.split(','); // Convert to array
        const emNameArray = em_names.split(',');
        const dataColumnsLength = parameterArray.length;

        let endTime;
        const startDate = new Date(start_time); // Add 5 hours 30 minutes (330 minutes)

        if (report_type === 1) { // Daily
            endTime = startDate;
            endTime.setHours(23, 59, 59);
        } else if (report_type === 2) { // Monthly
            endTime = new Date(startDate.getFullYear(), startDate.getMonth() + 1, 0); // Last day of the month
            endTime.setHours(23, 59, 59);
        } else if (report_type === 3) { // Yearly
            endTime = new Date(startDate.getFullYear(), 11, 31); // December 31st
            endTime.setHours(23, 59, 59);
        }

        // Add 5 hours and 30 minutes to end_time
        endTime = new Date(endTime.getTime() + 330 * 60 * 1000);

        const end_time = endTime.toISOString();
        // console.log(end_time);

        // Connect to MSSQL
        await sql.connect(dbConfig);
        // Query to Fetch Data
        let query;

        if (report_type === 2) {
            query = getmonthlyquery(start_time, end_time, emIDArray, parameterArray);
        }
        else if (report_type === 3) {
            query = getyearlyquery(start_time, end_time, emIDArray, parameterArray);
        }
        else {
            query = getdailyquery(start_time, end_time, emIDArray, parameterArray);
        }



        const result = await sql.query(query);
        const data = result.recordset;
        if (data.length === 0) {
            console.log('No data found');
            return;
        }

        // Load Template
        const templatePath = path.join(__dirname, 'Report_format', 'Outgoing_format.xlsx');
        const workbook = new ExcelJS.Workbook();
        await workbook.xlsx.readFile(templatePath);
        const worksheet = workbook.worksheets[report_type - 2]; // report_type =1 daily. 2 - monthly , 3- Yearly

        const selectedSheetName = worksheet.name;

        // Remove all other sheets except the selected one
        workbook.worksheets.forEach(sheet => {
            if (sheet.name !== selectedSheetName) {
                workbook.removeWorksheet(sheet.id);
            }
        });

        // Organize Data
        let groupedData = {};
        data.forEach(row => {
            //console.log(row.DateAndTime);
            if (!groupedData[row.DateAndTime]) {
                groupedData[row.DateAndTime] = {};
            }
            groupedData[row.DateAndTime][row.TagName] = row.Val;
        });

        worksheet.getCell('C2').value = plant_name;
        switch (report_type) {
            case 1:
                worksheet.getCell('C3').value = 'Daily Report';
                worksheet.getCell('C4').value = 'Date: ' + start_time.split('T')[0];
                break;
            case 2:
                worksheet.getCell('C3').value = 'Monthly Report';
                worksheet.getCell('C4').value = 'Month: ' + start_time.split('T')[0].slice(0, -3);
                break;

            case 3:
                worksheet.getCell('C3').value = 'Yearly Report';
                worksheet.getCell('C4').value = 'Year: ' + start_time.split('T')[0].slice(0, -6);
                break;
            default:
        }


        const originalWidth = worksheet.getColumn(3).width; // Default width if undefined
        if (emIDArray.length > 1) {
            if (report_type == 2) {
                copyFormatAndText(worksheet, worksheet, "C5:C37", 3, ((emIDArray.length - 1) * 1) + 1, false, 1, 0, 3);
            }
            else if (report_type == 3) {
                copyFormatAndText(worksheet, worksheet, "C5:C18", 3, ((emIDArray.length - 1) * 1) + 1, false, 1, 0, 3);
            }
            // console.log(originalWidth);
            worksheet.getColumn(3 + ((dataColumnsLength) * (emIDArray.length - 1))).width = originalWidth;
            //  console.log(worksheet.getColumn(3 + ((dataColumnsLength ) * (emIDArray.length - 1))),originalWidth);

            copyMergedCells(worksheet, worksheet, 3, 3 + ((dataColumnsLength) * (emIDArray.length - 1)), 1, 1, 5, 37);

            safeUnmerge(worksheet, 'C5:C37');
        }


        // Populate Rows
        let startRow = 7; // Assuming data starts from row 7 in template
        Object.keys(groupedData).forEach(datetime => {
            let row = worksheet.getRow(startRow++);
            let dateValue = new Date(datetime);

            //let dateValue = new Date(datetime);
            if (report_type === 3) {
                // Add 5 hours 30 minutes (330 minutes) to the date
                dateValue = new Date(dateValue.getTime() + 330 * 60 * 1000);
            }


            //  console.log(dateValue);
            row.getCell(1).value = dateValue;
            if (report_type === 2)
                row.getCell(1).numFmt = 'yyyy-mm-dd';

            let colIndex = 2;
            emIDArray.forEach(em => {
                row.getCell(colIndex++).value = groupedData[datetime][`${em}_CD_KWH`];
            });
            row.commit();
        });

        if (emIDArray.length > 1) {

            const firstEMColumn = 2; // B column is index 2
            copyFormatAndText(worksheet, worksheet, "B5:B6", firstEMColumn + dataColumnsLength, emIDArray.length - 1, false, dataColumnsLength, 0, 2);
            if (report_type === 2) {
                copyFormatAndText(worksheet, worksheet, "B7:B37", firstEMColumn + dataColumnsLength, emIDArray.length, true, dataColumnsLength, 0, 2);
            } else if (report_type === 3) {
                copyFormatAndText(worksheet, worksheet, "B7:B18", firstEMColumn + dataColumnsLength, emIDArray.length, true, dataColumnsLength, 0, 2);

            }
            copyColumnLength(worksheet, 2, 2, emIDArray.length - 1);

        }


        if (report_type === 2) {

            const totalDaysInMonth = new Date(startDate.getFullYear(), startDate.getMonth() + 1, 0).getDate();
            if (totalDaysInMonth < 31) {
                //for (let i = 7 + totalDaysInMonth; i <= 37; i++) {
                //  worksheet.getRow(i).hidden = true;
                //}
                const startRow = 7 + totalDaysInMonth;
                const deleteCount = 37 - startRow + 1;
                console.log('Deleting rows:', startRow, 'count', deleteCount);


                worksheet.spliceRows(startRow, deleteCount, [], []);
                //}
            }

        }
        if (report_type === 3) {
            //   addFormulaForRow(worksheet, 19, 'sum');

        }
        writeEMNames(worksheet, emNameArray, 1, 5);
        expandMergedCells(worksheet, emIDArray, dataColumnsLength, 'C', 'C', 1);
        const lastcolumnId = 2 + (emIDArray.length * dataColumnsLength);

        let noofDatarows;
        if (report_type == 2) {
            const totalDaysInMonth = new Date(startDate.getFullYear(), startDate.getMonth() + 1, 0).getDate();
            noofDatarows = totalDaysInMonth;
            for (let rowIndex = 7; rowIndex <= 7 + noofDatarows - 1; rowIndex++) {
                const cell = worksheet.getCell(`A${rowIndex}`);
                cell.numFmt = 'yyyy-mm-dd';
            }
        } else if (report_type == 3) {
            noofDatarows = Object.keys(groupedData).length;
            for (let rowIndex = 7; rowIndex <= 7 + noofDatarows - 1; rowIndex++) {
                const cell = worksheet.getCell(`A${rowIndex}`);
                cell.numFmt = 'mmmm-yyyy';
            }
        }

        totalLastColumns(worksheet, emIDArray, 7, 7 + noofDatarows - 1, lastcolumnId);
        applyThickBorderToSheet(worksheet, 7, 0, 7 + noofDatarows - 1);
        applyThinBorderToRange(worksheet, 1, 7, 2, 7 + noofDatarows - 2);

        const lastRow = 7 + noofDatarows - 1;
        const cellsArray = [`A${lastRow}`, `B${lastRow}`];
        applyThickToBottom(worksheet, cellsArray);



        if (protectsheet) {
            await protectsheetfn(worksheet, []);
        }


        // Save File
        const fileName = 'Formatted_Report.xlsx';
        const filePath = path.join(__dirname, fileName);
        await workbook.xlsx.writeFile(filePath);

        console.log(`Report generated successfully: ${filePath}`);
    } catch (error) {
        console.error('Error generating report:', error);
    }
}


async function generateAllFeederReport(start_time, em_ids, em_names, parameterArray, plant_name, report_type, protectsheet = false, arrayunlockedcells = []) {
    try {
        const emIDArray = em_ids.split(','); // Convert to array
        const emNameArray = em_names.split(',');
        const dataColumnsLength = parameterArray.length;

        let endTime;
        const startDate = new Date(start_time); // Add 5 hours 30 minutes (330 minutes)

        if (report_type === 1) { // Daily
            endTime = startDate;
            endTime.setHours(23, 59, 59);
        } else if (report_type === 2) { // Monthly
            endTime = new Date(startDate.getFullYear(), startDate.getMonth() + 1, 0); // Last day of the month
            endTime.setHours(23, 59, 59);
        } else if (report_type === 3) { // Yearly
            endTime = new Date(startDate.getFullYear(), 11, 31); // December 31st
            endTime.setHours(23, 59, 59);
        }

        // Add 5 hours and 30 minutes to end_time
        endTime = new Date(endTime.getTime() + 330 * 60 * 1000);

        const end_time = endTime.toISOString();
        // console.log(end_time);

        // Connect to MSSQL
        await sql.connect(dbConfig);
        // Query to Fetch Data
        let query;

        query = getmonthlyAllFeeders(start_time, end_time, emIDArray, parameterArray);



        const result = await sql.query(query);
        const data = result.recordset;
        if (data.length === 0) {
            console.log('No data found');
            return;
        }

        // Load Template
        const templatePath = path.join(__dirname, 'Report_format', 'All_Feeder_format.xlsx');
        const workbook = new ExcelJS.Workbook();
        await workbook.xlsx.readFile(templatePath);
        const worksheet = workbook.worksheets[report_type - 1]; // report_type =1 daily. 2 - monthly , 3- Yearly


        // Organize Data
        let groupedData = {};
        data.forEach(row => {
            //console.log(row.DateAndTime);
            if (!groupedData[row.DateAndTime]) {
                groupedData[row.DateAndTime] = {};
            }
            groupedData[row.DateAndTime][row.TagName] = row.Val;
        });

        worksheet.getCell('C2').value = plant_name;
        switch (report_type) {
            case 1:
                worksheet.getCell('C3').value = 'Daily Report';
                worksheet.getCell('C4').value = 'Date: ' + start_time.split('T')[0];
                break;
            case 2:
                worksheet.getCell('C3').value = 'Monthly Report';
                worksheet.getCell('C4').value = 'Month: ' + start_time.split('T')[0].slice(0, -3);
                break;

            case 3:
                worksheet.getCell('C3').value = 'Yearly Report';
                worksheet.getCell('C4').value = 'Year: ' + start_time.split('T')[0].slice(0, -6);
                break;
            default:
        }


        const originalWidth = worksheet.getColumn(2).width; // Default width if undefined
        if (emIDArray.length > 1) {
            if (report_type == 2) {
                copyFormatAndText(worksheet, worksheet, "B5:C37", 2 + dataColumnsLength, (emIDArray.length - 1), false, dataColumnsLength, 0, 2);
            }
            worksheet.getColumn(3 + ((dataColumnsLength) * (emIDArray.length - 1))).width = originalWidth;
            copyMergedCells(worksheet, worksheet, 2, 2 + (dataColumnsLength), dataColumnsLength * (emIDArray.length - 1), dataColumnsLength, 5, 6, 0);
        }


        // Populate Rows
        let startRow = 7; // Assuming data starts from row 7 in template
        Object.keys(groupedData).forEach(datetime => {
            let row = worksheet.getRow(startRow++);
            let dateValue = new Date(datetime);

            //let dateValue = new Date(datetime);
            if (report_type === 3) {
                // Add 5 hours 30 minutes (330 minutes) to the date
                dateValue = new Date(dateValue.getTime() + 330 * 60 * 1000);
            }


            //  console.log(dateValue);
            row.getCell(1).value = dateValue;
            if (report_type === 2)
                row.getCell(1).numFmt = 'yyyy-mm-dd';

            let colIndex = 2;
            emIDArray.forEach(em => {
                row.getCell(colIndex++).value = groupedData[datetime][`${em}_CD_KWH`];
                row.getCell(colIndex++).value = groupedData[datetime][`${em}_AVGPF`];

            });
            row.commit();
        });

        if (emIDArray.length > 1) {

            const firstEMColumn = 2; // B column is index 2
            copyFormatAndText(worksheet, worksheet, "B5:C6", firstEMColumn + dataColumnsLength, emIDArray.length - 1, false, dataColumnsLength, 0, 2);
            if (report_type === 2) {
                copyFormatAndText(worksheet, worksheet, "B7:C37", firstEMColumn + dataColumnsLength, emIDArray.length - 1, true, dataColumnsLength, 0, 2);
            }
            copyColumnLength(worksheet, 2, 3, emIDArray.length - 1);

        }

        const totalDaysInMonth = new Date(startDate.getFullYear(), startDate.getMonth() + 1, 0).getDate();

        if (report_type === 2) {

            if (totalDaysInMonth < 31) {
                //for (let i = 7 + totalDaysInMonth; i <= 37; i++) {
                //  worksheet.getRow(i).hidden = true;
                //}
                const startRow = 7 + totalDaysInMonth;
                const deleteCount = 37 - startRow + 1;
                //                console.log('Deleting rows:', startRow, 'count', deleteCount);


                worksheet.spliceRows(startRow, deleteCount, [], []);
                //}
            }

        }

        writeEMNames(worksheet, emNameArray, dataColumnsLength, 5);
        expandMergedCells(worksheet, emIDArray, dataColumnsLength, 'C', 'C', 0);
        const lastcolumnId = 2 + (emIDArray.length * dataColumnsLength);

        let noofDatarows;
        if (report_type == 2) {
            const totalDaysInMonth = new Date(startDate.getFullYear(), startDate.getMonth() + 1, 0).getDate();
            noofDatarows = totalDaysInMonth;
            for (let rowIndex = 7; rowIndex <= 7 + noofDatarows - 1; rowIndex++) {
                const cell = worksheet.getCell(`A${rowIndex}`);
                cell.numFmt = 'yyyy-mm-dd';
            }
        }

        //  totalLastColumns(worksheet, emIDArray, 7, 7 + noofDatarows - 1, lastcolumnId);

        worksheet.getCell('C5').style = {
            ...worksheet.getCell('C5').style,
            border: {
                ...worksheet.getCell('C5').style.border,
                right: { style: 'thin' }
            }
        };

        applyThickBorderToSheet(worksheet, 7, 0, 7 + noofDatarows - 1);
        applyThinBorderToRange(worksheet, 1, 7, 3, 7 + noofDatarows - 2);

        const lastRow = 7 + noofDatarows - 1;
        const cellsArray = [`A${lastRow}`, `B${lastRow}`, `C${lastRow}`];
        applyThickToBottom(worksheet, cellsArray);


        const worksheet2 = workbook.worksheets[0];
        const TotalFeeders = emIDArray.length;
        worksheet2.getCell('C4').value = 'Month: ' + start_time.split('T')[0].slice(0, -3);
        worksheet2.getCell('B5').value = `No. of Total Feeder = ${TotalFeeders}`;

        // Add formulas to columns B and C for rows 7 to 7 + noofDatarows - 1
        const startColumn = 'B';
        const endColumn = String.fromCharCode(startColumn.charCodeAt(0) + (emIDArray.length * dataColumnsLength) - 1);

        for (let rowIndex = 7; rowIndex <= 7 + noofDatarows - 1; rowIndex++) {
            // Formula for column B
            worksheet2.getCell(`B${rowIndex}`).value = {
                formula: `SUMIFS(FeederWise!$B${rowIndex}:$${endColumn}${rowIndex},FeederWise!$B$6:$${endColumn}$6,"KWH")`
            };

            // Formula for column C
            let weightedSumFormula = '';
            for (let col = 0; col < emIDArray.length * dataColumnsLength; col += 2) {
                const col1 = String.fromCharCode(startColumn.charCodeAt(0) + col);
                const col2 = String.fromCharCode(startColumn.charCodeAt(0) + col + 1);
                weightedSumFormula += `(FeederWise!${col1}${rowIndex}*FeederWise!${col2}${rowIndex})+`;
            }
            //   console.log(weightedSumFormula);
            weightedSumFormula = weightedSumFormula.slice(0, -1); // Remove trailing '+'
            worksheet2.getCell(`C${rowIndex}`).value = {
                formula: `ROUND((${weightedSumFormula})/B${rowIndex},3)`
            };
        }

        if (report_type === 2) {

            if (totalDaysInMonth < 31) {
                //for (let i = 7 + totalDaysInMonth; i <= 37; i++) {
                //  worksheet.getRow(i).hidden = true;
                //}
                const startRow = 7 + totalDaysInMonth;
                const deleteCount = 37 - startRow + 1;
                //                console.log('Deleting rows:', startRow, 'count', deleteCount);


                worksheet2.spliceRows(startRow, deleteCount, [], []);
                //}
            }

        }


        applyThickBorderToSheet(worksheet2, 7, 0, 7 + noofDatarows - 1);
        applyThinBorderToRange(worksheet2, 1, 6, 3, 6 + noofDatarows - 1);

        applyThickToBottom(worksheet2, cellsArray);

        const lastColumnCells = [];
        for (let rowIndex = 6; rowIndex <= 6 + noofDatarows; rowIndex++) {
            lastColumnCells.push(`${String.fromCharCode('A'.charCodeAt(0) + 3 - 1)}${rowIndex}`);
        }


        applyThickToRight(worksheet2, lastColumnCells);


        if (protectsheet) {
            await protectsheetfn(worksheet, []);
            await protectsheetfn(worksheet2, []);
        }


        // Save File
        const fileName = 'Formatted_Report.xlsx';
        const filePath = path.join(__dirname, fileName);
        await workbook.xlsx.writeFile(filePath);

        console.log(`Report generated successfully: ${filePath}`);
    } catch (error) {
        console.error('Error generating report:', error);
    }
}

// Example Execution with Sample Inputs
const startTime = '2024-02-01';
const emIDs = 'EM1,EM2,EM3,EM4,EM5,EM6,EM7,EM8,EM9,EM10'; // Example EM IDs
const emNames = 'HT Incomer1,HT Incomer2,HT Incomer3, HT Incomer4, HT Incomer5,HT Incomer1,HT Incomer2,HT Incomer3, HT Incomer4, HT Incomer5';
const plantName = 'HT Incomer Report';
const arrayunlockcells = [];
const reporttype = 2; // 1 - daily, 2 - monthly, 3- yearly
//1. this is for Incomer Report
//const ParamArray = ['RYPHASE_VOL', 'YBPHASE_VOL', 'BRPHASE_VOL', 'RPHASE_CUR', 'YPHASE_CUR', 'BPHASE_CUR', '_CD_KWH', 'AVGPF'];
//generateIncomerReport(startTime, emIDs, emNames, ParamArray, plantName, reporttype, true, arrayunlockcells);

//2.  For Shift Report
//const ParamArray = ['_CD_KWH', 'AVGPF'];
//generateShiftReport(startTime, emIDs, emNames, ParamArray, plantName, reporttype, true, arrayunlockcells);

//3. For Outgoing Monthly and Yearly reports
//const ParamArray = ['_CD_KWH'];
//generateOutgoingReport(startTime, emIDs, emNames, ParamArray, plantName, reporttype, true, arrayunlockcells);

//4. All Feeder Report
const ParamArray = ['_CD_KWH', 'AVGPF'];
generateAllFeederReport(startTime, emIDs, emNames, ParamArray, plantName, reporttype, true, arrayunlockcells);
