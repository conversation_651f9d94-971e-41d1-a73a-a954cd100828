import{g as on,w as mp,a as Tr,j as hi,q as Ja,v as ns}from"./index-BDJ8oeCE.js";var ls={exports:{}},xp=ls.exports,tn;function yp(){return tn||(tn=1,function(Xe,ds){(function(ft,Et){ft._Highcharts=Et(),Xe.exports=ft._Highcharts})(typeof window>"u"?xp:window,()=>(()=>{let ft,Et;var dt,Dt,G,Z,vt,It,Ht,Xt,Ct,kt,K,Pt,ct,et,B,q,J,lt={};lt.d=(l,t)=>{for(var e in t)lt.o(t,e)&&!lt.o(l,e)&&Object.defineProperty(l,e,{enumerable:!0,get:t[e]})},lt.o=(l,t)=>Object.prototype.hasOwnProperty.call(l,t);var de={};lt.d(de,{default:()=>fp}),function(l){var t,e,i;l.SVG_NS="http://www.w3.org/2000/svg",l.product="Highcharts",l.version="12.1.2",l.win=typeof window<"u"?window:{},l.doc=l.win.document,l.svg=l.doc&&l.doc.createElementNS&&!!l.doc.createElementNS(l.SVG_NS,"svg").createSVGRect,l.pageLang=(i=(e=(t=l.doc)==null?void 0:t.documentElement)==null?void 0:e.closest("[lang]"))==null?void 0:i.lang,l.userAgent=l.win.navigator&&l.win.navigator.userAgent||"",l.isChrome=l.win.chrome,l.isFirefox=l.userAgent.indexOf("Firefox")!==-1,l.isMS=/(edge|msie|trident)/i.test(l.userAgent)&&!l.win.opera,l.isSafari=!l.isChrome&&l.userAgent.indexOf("Safari")!==-1,l.isTouchDevice=/(Mobile|Android|Windows Phone)/.test(l.userAgent),l.isWebKit=l.userAgent.indexOf("AppleWebKit")!==-1,l.deg2rad=2*Math.PI/360,l.marginNames=["plotTop","marginRight","marginBottom","plotLeft"],l.noop=function(){},l.supportsPassiveEvents=function(){let s=!1;if(!l.isMS){let r=Object.defineProperty({},"passive",{get:function(){s=!0}});l.win.addEventListener&&l.win.removeEventListener&&(l.win.addEventListener("testPassive",l.noop,r),l.win.removeEventListener("testPassive",l.noop,r))}return s}(),l.charts=[],l.composed=[],l.dateFormats={},l.seriesTypes={},l.symbolSizes={},l.chartCount=0}(dt||(dt={}));let A=dt,{charts:Fe,doc:ce,win:Ft}=A;function Qt(l,t,e,i){let s=t?"Highcharts error":"Highcharts warning";l===32&&(l=`${s}: Deprecated member`);let r=ps(l),o=r?`${s} #${l}: www.highcharts.com/errors/${l}/`:l.toString();if(i!==void 0){let a="";r&&(o+="?"),pe(i,function(n,h){a+=`
 - ${h}: ${n}`,r&&(o+=encodeURI(h)+"="+encodeURI(n))}),o+=a}Ir(A,"displayError",{chart:e,code:l,message:o,params:i},function(){if(t)throw Error(o);Ft.console&&Qt.messages.indexOf(o)===-1&&console.warn(o)}),Qt.messages.push(o)}function Ar(l,t){return parseInt(l,t||10)}function Ye(l){return typeof l=="string"}function di(l){let t=Object.prototype.toString.call(l);return t==="[object Array]"||t==="[object Array Iterator]"}function ke(l,t){return!!l&&typeof l=="object"&&(!t||!di(l))}function cs(l){return ke(l)&&typeof l.nodeType=="number"}function Cr(l){let t=l&&l.constructor;return!!(ke(l,!0)&&!cs(l)&&t&&t.name&&t.name!=="Object")}function ps(l){return typeof l=="number"&&!isNaN(l)&&l<1/0&&l>-1/0}function we(l){return l!=null}function Pr(l,t,e){let i,s=Ye(t)&&!we(e),r=(o,a)=>{we(o)?l.setAttribute(a,o):s?(i=l.getAttribute(a))||a!=="class"||(i=l.getAttribute(a+"Name")):l.removeAttribute(a)};return Ye(t)?r(e,t):pe(t,r),i}function Lr(l){return di(l)?l:[l]}function Me(l,t){let e;for(e in l||(l={}),t)l[e]=t[e];return l}function Ge(){let l=arguments,t=l.length;for(let e=0;e<t;e++){let i=l[e];if(i!=null)return i}}function us(l,t){Me(l.style,t)}function Or(l){return Math.pow(10,Math.floor(Math.log(l)/Math.LN10))}function Er(l,t){return l>1e14?l:parseFloat(l.toPrecision(t||14))}(Qt||(Qt={})).messages=[],Math.easeInOutSine=function(l){return-.5*(Math.cos(Math.PI*l)-1)};let an=Array.prototype.find?function(l,t){return l.find(t)}:function(l,t){let e,i=l.length;for(e=0;e<i;e++)if(t(l[e],e))return l[e]};function pe(l,t,e){for(let i in l)Object.hasOwnProperty.call(l,i)&&t.call(e||l[i],l[i],i,l)}function Dr(l,t,e){function i(o,a){let n=l.removeEventListener;n&&n.call(l,o,a,!1)}function s(o){let a,n;l.nodeName&&(t?(a={})[t]=!0:a=o,pe(a,function(h,c){if(o[c])for(n=o[c].length;n--;)i(c,o[c][n].fn)}))}let r=typeof l=="function"&&l.prototype||l;if(Object.hasOwnProperty.call(r,"hcEvents")){let o=r.hcEvents;if(t){let a=o[t]||[];e?(o[t]=a.filter(function(n){return e!==n.fn}),i(t,e)):(s(o),o[t]=[])}else s(o),delete r.hcEvents}}function Ir(l,t,e,i){if(e=e||{},ce.createEvent&&(l.dispatchEvent||l.fireEvent&&l!==A)){let s=ce.createEvent("Events");s.initEvent(t,!0,!0),e=Me(s,e),l.dispatchEvent?l.dispatchEvent(e):l.fireEvent(t,e)}else if(l.hcEvents){e.target||Me(e,{preventDefault:function(){e.defaultPrevented=!0},target:l,type:t});let s=[],r=l,o=!1;for(;r.hcEvents;)Object.hasOwnProperty.call(r,"hcEvents")&&r.hcEvents[t]&&(s.length&&(o=!0),s.unshift.apply(s,r.hcEvents[t])),r=Object.getPrototypeOf(r);o&&s.sort((a,n)=>a.order-n.order),s.forEach(a=>{a.fn.call(l,e)===!1&&e.preventDefault()})}i&&!e.defaultPrevented&&i.call(l,e)}let nn=function(){let l=Math.random().toString(36).substring(2,9)+"-",t=0;return function(){return"highcharts-"+(ft?"":l)+t++}}();Ft.jQuery&&(Ft.jQuery.fn.highcharts=function(){let l=[].slice.call(arguments);if(this[0])return l[0]?(new A[Ye(l[0])?l.shift():"Chart"](this[0],l[0],l[1]),this):Fe[Pr(this[0],"data-highcharts-chart")]});let R={addEvent:function(l,t,e,i={}){let s=typeof l=="function"&&l.prototype||l;Object.hasOwnProperty.call(s,"hcEvents")||(s.hcEvents={});let r=s.hcEvents;A.Point&&l instanceof A.Point&&l.series&&l.series.chart&&(l.series.chart.runTrackerClick=!0);let o=l.addEventListener;o&&o.call(l,t,e,!!A.supportsPassiveEvents&&{passive:i.passive===void 0?t.indexOf("touch")!==-1:i.passive,capture:!1}),r[t]||(r[t]=[]);let a={fn:e,order:typeof i.order=="number"?i.order:1/0};return r[t].push(a),r[t].sort((n,h)=>n.order-h.order),function(){Dr(l,t,e)}},arrayMax:function(l){let t=l.length,e=l[0];for(;t--;)l[t]>e&&(e=l[t]);return e},arrayMin:function(l){let t=l.length,e=l[0];for(;t--;)l[t]<e&&(e=l[t]);return e},attr:Pr,clamp:function(l,t,e){return l>t?l<e?l:e:t},clearTimeout:function(l){we(l)&&clearTimeout(l)},correctFloat:Er,createElement:function(l,t,e,i,s){let r=ce.createElement(l);return t&&Me(r,t),s&&us(r,{padding:"0",border:"none",margin:"0"}),e&&us(r,e),i&&i.appendChild(r),r},crisp:function(l,t=0,e){let i=t%2/2,s=e?-1:1;return(Math.round(l*s-i)+i)*s},css:us,defined:we,destroyObjectProperties:function(l,t,e){pe(l,function(i,s){i!==t&&(i!=null&&i.destroy)&&i.destroy(),(i!=null&&i.destroy||!e)&&delete l[s]})},diffObjects:function(l,t,e,i){let s={};return function r(o,a,n,h){let c=e?a:o;pe(o,function(d,p){if(!h&&i&&i.indexOf(p)>-1&&a[p]){d=Lr(d),n[p]=[];for(let u=0;u<Math.max(d.length,a[p].length);u++)a[p][u]&&(d[u]===void 0?n[p][u]=a[p][u]:(n[p][u]={},r(d[u],a[p][u],n[p][u],h+1)))}else ke(d,!0)&&!d.nodeType?(n[p]=di(d)?[]:{},r(d,a[p]||{},n[p],h+1),Object.keys(n[p]).length!==0||p==="colorAxis"&&h===0||delete n[p]):(o[p]!==a[p]||p in o&&!(p in a))&&p!=="__proto__"&&p!=="constructor"&&(n[p]=c[p])})}(l,t,s,0),s},discardElement:function(l){l&&l.parentElement&&l.parentElement.removeChild(l)},erase:function(l,t){let e=l.length;for(;e--;)if(l[e]===t){l.splice(e,1);break}},error:Qt,extend:Me,extendClass:function(l,t){let e=function(){};return e.prototype=new l,Me(e.prototype,t),e},find:an,fireEvent:Ir,getAlignFactor:(l="")=>({center:.5,right:1,middle:.5,bottom:1})[l]||0,getClosestDistance:function(l,t){let e,i,s,r=!t;return l.forEach(o=>{if(o.length>1)for(s=o.length-1;s>0;s--)(i=o[s]-o[s-1])<0&&!r?(t==null||t(),t=void 0):i&&(e===void 0||i<e)&&(e=i)}),e},getMagnitude:Or,getNestedProperty:function(l,t){let e=l.split(".");for(;e.length&&we(t);){let i=e.shift();if(i===void 0||i==="__proto__")return;if(i==="this"){let r;return ke(t)&&(r=t["@this"]),r??t}let s=t[i.replace(/[\\'"]/g,"")];if(!we(s)||typeof s=="function"||typeof s.nodeType=="number"||s===Ft)return;t=s}return t},getStyle:function l(t,e,i){let s;if(e==="width"){let o=Math.min(t.offsetWidth,t.scrollWidth),a=t.getBoundingClientRect&&t.getBoundingClientRect().width;return a<o&&a>=o-1&&(o=Math.floor(a)),Math.max(0,o-(l(t,"padding-left",!0)||0)-(l(t,"padding-right",!0)||0))}if(e==="height")return Math.max(0,Math.min(t.offsetHeight,t.scrollHeight)-(l(t,"padding-top",!0)||0)-(l(t,"padding-bottom",!0)||0));let r=Ft.getComputedStyle(t,void 0);return r&&(s=r.getPropertyValue(e),Ge(i,e!=="opacity")&&(s=Ar(s))),s},insertItem:function(l,t){let e,i=l.options.index,s=t.length;for(e=l.options.isInternal?s:0;e<s+1;e++)if(!t[e]||ps(i)&&i<Ge(t[e].options.index,t[e]._i)||t[e].options.isInternal){t.splice(e,0,l);break}return e},isArray:di,isClass:Cr,isDOMElement:cs,isFunction:function(l){return typeof l=="function"},isNumber:ps,isObject:ke,isString:Ye,merge:function(l,...t){let e,i=[l,...t],s={},r=function(a,n){return typeof a!="object"&&(a={}),pe(n,function(h,c){c!=="__proto__"&&c!=="constructor"&&(!ke(h,!0)||Cr(h)||cs(h)?a[c]=n[c]:a[c]=r(a[c]||{},h))}),a};l===!0&&(s=i[1],i=Array.prototype.slice.call(i,2));let o=i.length;for(e=0;e<o;e++)s=r(s,i[e]);return s},normalizeTickInterval:function(l,t,e,i,s){let r,o=l;e=Ge(e,Or(l));let a=l/e;for(!t&&(t=s?[1,1.2,1.5,2,2.5,3,4,5,6,8,10]:[1,2,2.5,5,10],i===!1&&(e===1?t=t.filter(function(n){return n%1==0}):e<=.1&&(t=[1/e]))),r=0;r<t.length&&(o=t[r],(!s||!(o*e>=l))&&(s||!(a<=(t[r]+(t[r+1]||t[r]))/2)));r++);return Er(o*e,-Math.round(Math.log(.001)/Math.LN10))},objectEach:pe,offset:function(l){let t=ce.documentElement,e=l.parentElement||l.parentNode?l.getBoundingClientRect():{top:0,left:0,width:0,height:0};return{top:e.top+(Ft.pageYOffset||t.scrollTop)-(t.clientTop||0),left:e.left+(Ft.pageXOffset||t.scrollLeft)-(t.clientLeft||0),width:e.width,height:e.height}},pad:function(l,t,e){return Array((t||2)+1-String(l).replace("-","").length).join(e||"0")+l},pick:Ge,pInt:Ar,pushUnique:function(l,t){return 0>l.indexOf(t)&&!!l.push(t)},relativeLength:function(l,t,e){return/%$/.test(l)?t*parseFloat(l)/100+(e||0):parseFloat(l)},removeEvent:Dr,replaceNested:function(l,...t){let e,i;do for(i of(e=l,t))l=l.replace(i[0],i[1]);while(l!==e);return l},splat:Lr,stableSort:function(l,t){let e,i,s=l.length;for(i=0;i<s;i++)l[i].safeI=i;for(l.sort(function(r,o){return(e=t(r,o))===0?r.safeI-o.safeI:e}),i=0;i<s;i++)delete l[i].safeI},syncTimeout:function(l,t,e){return t>0?setTimeout(l,t,e):(l.call(0,e),-1)},timeUnits:{millisecond:1,second:1e3,minute:6e4,hour:36e5,day:864e5,week:6048e5,month:24192e5,year:314496e5},ucfirst:function(l){return Ye(l)?l.substring(0,1).toUpperCase()+l.substring(1):String(l)},uniqueKey:nn,useSerialIds:function(l){return ft=Ge(l,ft)},wrap:function(l,t,e){let i=l[t];l[t]=function(){let s=arguments,r=this;return e.apply(this,[function(){return i.apply(r,arguments.length?arguments:s)}].concat([].slice.call(arguments)))}}},{pageLang:ln,win:ci}=A,{defined:pi,error:Br,extend:ui,isNumber:Nr,isObject:gs,isString:je,merge:hn,objectEach:dn,pad:Jt,splat:cn,timeUnits:ot,ucfirst:pn}=R,un=A.isSafari&&ci.Intl&&!ci.Intl.DateTimeFormat.prototype.formatRange,gn=l=>l.main===void 0,zr=l=>["D","L","M","X","J","V","S"].indexOf(l),fs=class{constructor(l){this.options={timezone:"UTC"},this.variableTimezone=!1,this.Date=ci.Date,this.update(l)}update(l={}){this.dTLCache={},this.options=l=hn(!0,this.options,l);let{timezoneOffset:t,useUTC:e}=l;this.Date=l.Date||ci.Date||Date;let i=l.timezone;pi(e)&&(i=e?"UTC":void 0),t&&t%60==0&&(i="Etc/GMT"+(t>0?"+":"")+t/60),this.variableTimezone=i!=="UTC"&&(i==null?void 0:i.indexOf("Etc/GMT"))!==0,this.timezone=i,["months","shortMonths","weekdays","shortWeekdays"].forEach(s=>{let r=/months/i.test(s),o=/short/.test(s),a={timeZone:"UTC"};a[r?"month":"weekday"]=o?"short":"long",this[s]=(r?[0,1,2,3,4,5,6,7,8,9,10,11]:[3,4,5,6,7,8,9]).map(n=>this.dateFormat(a,(r?31:1)*24*36e5*n))})}toParts(l){let[t,e,i,s,r,o,a]=this.dateTimeFormat({weekday:"narrow",day:"numeric",month:"numeric",year:"numeric",hour:"numeric",minute:"numeric",second:"numeric"},l,"es").split(/(?:, |\/|:)/g);return[s,+i-1,e,r,o,a,Math.floor(Number(l)||0)%1e3,zr(t)].map(Number)}dateTimeFormat(l,t,e=this.options.locale||ln){let i=JSON.stringify(l)+e;je(l)&&(l=this.str2dtf(l));let s=this.dTLCache[i];if(!s){l.timeZone??(l.timeZone=this.timezone);try{s=new Intl.DateTimeFormat(e,l)}catch(r){/Invalid time zone/i.test(r.message)?(Br(34),l.timeZone="UTC",s=new Intl.DateTimeFormat(e,l)):Br(r.message,!1)}}return this.dTLCache[i]=s,(s==null?void 0:s.format(t))||""}str2dtf(l,t={}){let e={L:{fractionalSecondDigits:3},S:{second:"2-digit"},M:{minute:"numeric"},H:{hour:"2-digit"},k:{hour:"numeric"},E:{weekday:"narrow"},a:{weekday:"short"},A:{weekday:"long"},d:{day:"2-digit"},e:{day:"numeric"},b:{month:"short"},B:{month:"long"},m:{month:"2-digit"},o:{month:"numeric"},y:{year:"2-digit"},Y:{year:"numeric"}};return Object.keys(e).forEach(i=>{l.indexOf(i)!==-1&&ui(t,e[i])}),t}makeTime(l,t,e=1,i=0,s,r,o){let a=this.Date.UTC(l,t,e,i,s||0,r||0,o||0);if(this.timezone!=="UTC"){let n=this.getTimezoneOffset(a);if(a+=n,[2,3,8,9,10,11].indexOf(t)!==-1&&(i<5||i>20)){let h=this.getTimezoneOffset(a);n!==h?a+=h-n:n-36e5!==this.getTimezoneOffset(a-36e5)||un||(a-=36e5)}}return a}parse(l){if(!je(l))return l??void 0;let t=(l=l.replace(/\//g,"-").replace(/(GMT|UTC)/,"")).indexOf("Z")>-1||/([+-][0-9]{2}):?[0-9]{2}$/.test(l),e=/^[0-9]{4}-[0-9]{2}-[0-9]{2}$/.test(l);t||e||(l+="Z");let i=Date.parse(l);if(Nr(i))return i+(!t||e?this.getTimezoneOffset(i):0)}getTimezoneOffset(l){if(this.timezone!=="UTC"){let[t,e,i,s,r=0]=this.dateTimeFormat({timeZoneName:"shortOffset"},l,"en").split(/(GMT|:)/).map(Number),o=-(36e5*(i+r/60));if(Nr(o))return o}return 0}dateFormat(l,t,e){var s;let i=(s=A.defaultOptions)==null?void 0:s.lang;if(!pi(t)||isNaN(t))return(i==null?void 0:i.invalidDate)||"";if(je(l=l??"%Y-%m-%d %H:%M:%S")){let r,o=/%\[([a-zA-Z]+)\]/g;for(;r=o.exec(l);)l=l.replace(r[0],this.dateTimeFormat(r[1],t))}if(je(l)&&l.indexOf("%")!==-1){let r=this,[o,a,n,h,c,d,p,u]=this.toParts(t),g=(i==null?void 0:i.weekdays)||this.weekdays,m=(i==null?void 0:i.shortWeekdays)||this.shortWeekdays,x=(i==null?void 0:i.months)||this.months,f=(i==null?void 0:i.shortMonths)||this.shortMonths;dn(ui({a:m?m[u]:g[u].substr(0,3),A:g[u],d:Jt(n),e:Jt(n,2," "),w:u,b:f[a],B:x[a],m:Jt(a+1),o:a+1,y:o.toString().substr(2,2),Y:o,H:Jt(h),k:h,I:Jt(h%12||12),l:h%12||12,M:Jt(c),p:h<12?"AM":"PM",P:h<12?"am":"pm",S:Jt(d),L:Jt(p,3)},A.dateFormats),function(b,y){if(je(l))for(;l.indexOf("%"+y)!==-1;)l=l.replace("%"+y,typeof b=="function"?b.call(r,t):b)})}else if(gs(l)){let r=(this.getTimezoneOffset(t)||0)/36e5,o=this.timezone||"Etc/GMT"+(r>=0?"+":"")+r,{prefix:a="",suffix:n=""}=l;l=a+this.dateTimeFormat(ui({timeZone:o},l),t)+n}return e?pn(l):l}resolveDTLFormat(l){return gs(l,!0)?gs(l,!0)&&gn(l)?{main:l}:l:{main:(l=cn(l))[0],from:l[1],to:l[2]}}getTimeTicks(l,t,e,i){let s=this,r=[],o={},{count:a=1,unitRange:n}=l,[h,c,d,p,u,g]=s.toParts(t),m=(t||0)%1e3,x;if(i??(i=1),pi(t)){if(m=n>=ot.second?0:a*Math.floor(m/a),n>=ot.second&&(g=n>=ot.minute?0:a*Math.floor(g/a)),n>=ot.minute&&(u=n>=ot.hour?0:a*Math.floor(u/a)),n>=ot.hour&&(p=n>=ot.day?0:a*Math.floor(p/a)),n>=ot.day&&(d=n>=ot.month?1:Math.max(1,a*Math.floor(d/a))),n>=ot.month&&(c=n>=ot.year?0:a*Math.floor(c/a)),n>=ot.year&&(h-=h%a),n===ot.week){a&&(t=s.makeTime(h,c,d,p,u,g,m));let y=zr(this.dateTimeFormat({timeZone:this.timezone,weekday:"narrow"},t,"es"));d+=-y+i+(y<i?-7:0)}t=s.makeTime(h,c,d,p,u,g,m),s.variableTimezone&&pi(e)&&(x=e-t>4*ot.month||s.getTimezoneOffset(t)!==s.getTimezoneOffset(e));let f=t,b=1;for(;f<e;)r.push(f),n===ot.year?f=s.makeTime(h+b*a,0):n===ot.month?f=s.makeTime(h,c+b*a):x&&(n===ot.day||n===ot.week)?f=s.makeTime(h,c,d+b*a*(n===ot.day?1:7)):x&&n===ot.hour&&a>1?f=s.makeTime(h,c,d,p+b*a):f+=n*a,b++;r.push(f),n<=ot.hour&&r.length<1e4&&r.forEach(y=>{y%18e5==0&&s.dateFormat("%H%M%S%L",y)==="000000000"&&(o[y]="day")})}return r.info=ui(l,{higherRanks:o,totalRange:n*a}),r}getDateFormat(l,t,e,i){let s=this.dateFormat("%m-%d %H:%M:%S.%L",t),r="01-01 00:00:00.000",o={millisecond:15,second:12,minute:9,hour:6,day:3},a="millisecond",n=a;for(a in ot){if(l===ot.week&&+this.dateFormat("%w",t)===e&&s.substr(6)===r.substr(6)){a="week";break}if(ot[a]>l){a=n;break}if(o[a]&&s.substr(o[a])!==r.substr(o[a]))break;a!=="week"&&(n=a)}return this.resolveDTLFormat(i[a]).main}},{isTouchDevice:fn}=A,{fireEvent:mn,merge:xn}=R,Se={colors:["#2caffe","#544fc5","#00e272","#fe6a35","#6b8abc","#d568fb","#2ee0ca","#fa4b42","#feb56a","#91e8e1"],symbols:["circle","diamond","square","triangle","triangle-down"],lang:{locale:void 0,loading:"Loading...",months:void 0,shortMonths:void 0,weekdays:void 0,numericSymbols:["k","M","G","T","P","E"],resetZoom:"Reset zoom",resetZoomTitle:"Reset zoom level 1:1"},global:{buttonTheme:{fill:"#f7f7f7",padding:8,r:2,stroke:"#cccccc","stroke-width":1,style:{color:"#333333",cursor:"pointer",fontSize:"0.8em",fontWeight:"normal"},states:{hover:{fill:"#e6e6e6"},select:{fill:"#e6e9ff",style:{color:"#000000",fontWeight:"bold"}},disabled:{style:{color:"#cccccc"}}}}},time:{Date:void 0,timezone:"UTC",timezoneOffset:0,useUTC:void 0},chart:{alignThresholds:!1,panning:{enabled:!1,type:"x"},styledMode:!1,borderRadius:0,colorCount:10,allowMutatingData:!0,ignoreHiddenSeries:!0,spacing:[10,10,15,10],resetZoomButton:{theme:{},position:{}},reflow:!0,type:"line",zooming:{singleTouch:!1,resetButton:{theme:{zIndex:6},position:{align:"right",x:-10,y:10}}},width:null,height:null,borderColor:"#334eff",backgroundColor:"#ffffff",plotBorderColor:"#cccccc"},title:{style:{color:"#333333",fontWeight:"bold"},text:"Chart title",margin:15,minScale:.67},subtitle:{style:{color:"#666666",fontSize:"0.8em"},text:""},caption:{margin:15,style:{color:"#666666",fontSize:"0.8em"},text:"",align:"left",verticalAlign:"bottom"},plotOptions:{},legend:{enabled:!0,align:"center",alignColumns:!0,className:"highcharts-no-tooltip",events:{},layout:"horizontal",itemMarginBottom:2,itemMarginTop:2,labelFormatter:function(){return this.name},borderColor:"#999999",borderRadius:0,navigation:{style:{fontSize:"0.8em"},activeColor:"#0022ff",inactiveColor:"#cccccc"},itemStyle:{color:"#333333",cursor:"pointer",fontSize:"0.8em",textDecoration:"none",textOverflow:"ellipsis"},itemHoverStyle:{color:"#000000"},itemHiddenStyle:{color:"#666666",textDecoration:"line-through"},shadow:!1,itemCheckboxStyle:{position:"absolute",width:"13px",height:"13px"},squareSymbol:!0,symbolPadding:5,verticalAlign:"bottom",x:0,y:0,title:{style:{fontSize:"0.8em",fontWeight:"bold"}}},loading:{labelStyle:{fontWeight:"bold",position:"relative",top:"45%"},style:{position:"absolute",backgroundColor:"#ffffff",opacity:.5,textAlign:"center"}},tooltip:{enabled:!0,animation:{duration:300,easing:l=>Math.sqrt(1-Math.pow(l-1,2))},borderRadius:3,dateTimeLabelFormats:{millisecond:"%[AebHMSL]",second:"%[AebHMS]",minute:"%[AebHM]",hour:"%[AebHM]",day:"%[AebY]",week:"Week from %[AebY]",month:"%[BY]",year:"%Y"},footerFormat:"",headerShape:"callout",hideDelay:500,padding:8,shape:"callout",shared:!1,snap:fn?25:10,headerFormat:'<span style="font-size: 0.8em">{ucfirst point.key}</span><br/>',pointFormat:'<span style="color:{point.color}">●</span> {series.name}: <b>{point.y}</b><br/>',backgroundColor:"#ffffff",borderWidth:void 0,shadow:!0,stickOnContact:!1,style:{color:"#333333",cursor:"default",fontSize:"0.8em"},useHTML:!1},credits:{enabled:!0,href:"https://www.highcharts.com?credits",position:{align:"right",x:-10,verticalAlign:"bottom",y:-5},style:{cursor:"pointer",color:"#999999",fontSize:"0.6em"},text:"Highcharts.com"}},ms=new fs(Se.time),zt={defaultOptions:Se,defaultTime:ms,getOptions:function(){return Se},setOptions:function(l){return mn(A,"setOptions",{options:l}),xn(!0,Se,l),l.time&&ms.update(Se.time),l.lang&&"locale"in l.lang&&ms.update({locale:l.lang.locale}),Se}},{isNumber:gi,merge:yn,pInt:Lt,defined:Rr}=R;class pt{static parse(t){return t?new pt(t):pt.None}constructor(t){let e,i,s,r;this.rgba=[NaN,NaN,NaN,NaN],this.input=t;let o=A.Color;if(o&&o!==pt)return new o(t);if(typeof t=="object"&&t.stops!==void 0)this.stops=t.stops.map(a=>new pt(a[1]));else if(typeof t=="string")for(this.input=t=pt.names[t.toLowerCase()]||t,s=pt.parsers.length;s--&&!i;)(e=(r=pt.parsers[s]).regex.exec(t))&&(i=r.parse(e));i&&(this.rgba=i)}get(t){let e=this.input,i=this.rgba;if(typeof e=="object"&&this.stops!==void 0){let s=yn(e);return s.stops=[].slice.call(s.stops),this.stops.forEach((r,o)=>{s.stops[o]=[s.stops[o][0],r.get(t)]}),s}return i&&gi(i[0])?t!=="rgb"&&(t||i[3]!==1)?t==="a"?`${i[3]}`:"rgba("+i.join(",")+")":"rgb("+i[0]+","+i[1]+","+i[2]+")":e}brighten(t){let e=this.rgba;if(this.stops)this.stops.forEach(function(i){i.brighten(t)});else if(gi(t)&&t!==0)for(let i=0;i<3;i++)e[i]+=Lt(255*t),e[i]<0&&(e[i]=0),e[i]>255&&(e[i]=255);return this}setOpacity(t){return this.rgba[3]=t,this}tweenTo(t,e){let i=this.rgba,s=t.rgba;if(!gi(i[0])||!gi(s[0]))return t.input||"none";let r=s[3]!==1||i[3]!==1,o=(n,h)=>n+(i[h]-n)*(1-e),a=s.slice(0,3).map(o).map(Math.round);return r&&a.push(o(s[3],3)),(r?"rgba(":"rgb(")+a.join(",")+")"}}pt.names={white:"#ffffff",black:"#000000"},pt.parsers=[{regex:/rgba\(\s*(\d{1,3})\s*,\s*(\d{1,3})\s*,\s*(\d{1,3})\s*,\s*(\d?(?:\.\d+)?)\s*\)/,parse:function(l){return[Lt(l[1]),Lt(l[2]),Lt(l[3]),parseFloat(l[4],10)]}},{regex:/rgb\(\s*(\d{1,3})\s*,\s*(\d{1,3})\s*,\s*(\d{1,3})\s*\)/,parse:function(l){return[Lt(l[1]),Lt(l[2]),Lt(l[3]),1]}},{regex:/^#([a-f0-9])([a-f0-9])([a-f0-9])([a-f0-9])?$/i,parse:function(l){return[Lt(l[1]+l[1],16),Lt(l[2]+l[2],16),Lt(l[3]+l[3],16),Rr(l[4])?Lt(l[4]+l[4],16)/255:1]}},{regex:/^#([a-f0-9]{2})([a-f0-9]{2})([a-f0-9]{2})([a-f0-9]{2})?$/i,parse:function(l){return[Lt(l[1],16),Lt(l[2],16),Lt(l[3],16),Rr(l[4])?Lt(l[4],16)/255:1]}}],pt.None=new pt("");let{parse:Wr}=pt,{win:bn}=A,{isNumber:xs,objectEach:vn}=R;class Ot{constructor(t,e,i){this.pos=NaN,this.options=e,this.elem=t,this.prop=i}dSetter(){let t=this.paths,e=t&&t[0],i=t&&t[1],s=this.now||0,r=[];if(s!==1&&e&&i)if(e.length===i.length&&s<1)for(let o=0;o<i.length;o++){let a=e[o],n=i[o],h=[];for(let c=0;c<n.length;c++){let d=a[c],p=n[c];xs(d)&&xs(p)&&!(n[0]==="A"&&(c===4||c===5))?h[c]=d+s*(p-d):h[c]=p}r.push(h)}else r=i;else r=this.toD||[];this.elem.attr("d",r,void 0,!0)}update(){let t=this.elem,e=this.prop,i=this.now,s=this.options.step;this[e+"Setter"]?this[e+"Setter"]():t.attr?t.element&&t.attr(e,i,null,!0):t.style[e]=i+this.unit,s&&s.call(t,i,this)}run(t,e,i){let s=this,r=s.options,o=function(h){return!o.stopped&&s.step(h)},a=bn.requestAnimationFrame||function(h){setTimeout(h,13)},n=function(){for(let h=0;h<Ot.timers.length;h++)Ot.timers[h]()||Ot.timers.splice(h--,1);Ot.timers.length&&a(n)};t!==e||this.elem["forceAnimate:"+this.prop]?(this.startTime=+new Date,this.start=t,this.end=e,this.unit=i,this.now=this.start,this.pos=0,o.elem=this.elem,o.prop=this.prop,o()&&Ot.timers.push(o)===1&&a(n)):(delete r.curAnim[this.prop],r.complete&&Object.keys(r.curAnim).length===0&&r.complete.call(this.elem))}step(t){let e,i,s=+new Date,r=this.options,o=this.elem,a=r.complete,n=r.duration,h=r.curAnim;return o.attr&&!o.element?e=!1:t||s>=n+this.startTime?(this.now=this.end,this.pos=1,this.update(),h[this.prop]=!0,i=!0,vn(h,function(c){c!==!0&&(i=!1)}),i&&a&&a.call(o),e=!1):(this.pos=r.easing((s-this.startTime)/n),this.now=this.start+(this.end-this.start)*this.pos,this.update(),e=!0),e}initPath(t,e,i){let s=t.startX,r=t.endX,o=i.slice(),a=t.isArea,n=a?2:1,h=e&&i.length>e.length&&i.hasStackedCliffs,c,d,p,u,g=e&&e.slice();if(!g||h)return[o,o];function m(f,b){for(;f.length<d;){let y=f[0],v=b[d-f.length];if(v&&y[0]==="M"&&(v[0]==="C"?f[0]=["C",y[1],y[2],y[1],y[2],y[1],y[2]]:f[0]=["L",y[1],y[2]]),f.unshift(y),a){let w=f.pop();f.push(f[f.length-1],w)}}}function x(f){for(;f.length<d;){let b=f[Math.floor(f.length/n)-1].slice();if(b[0]==="C"&&(b[1]=b[5],b[2]=b[6]),a){let y=f[Math.floor(f.length/n)].slice();f.splice(f.length/2,0,b,y)}else f.push(b)}}if(s&&r&&r.length){for(p=0;p<s.length;p++){if(s[p]===r[0]){c=p;break}if(s[0]===r[r.length-s.length+p]){c=p,u=!0;break}if(s[s.length-1]===r[r.length-s.length+p]){c=s.length-p;break}}c===void 0&&(g=[])}return g.length&&xs(c)&&(d=o.length+c*n,u?(m(g,o),x(o)):(m(o,g),x(g))),[g,o]}fillSetter(){Ot.prototype.strokeSetter.apply(this,arguments)}strokeSetter(){this.elem.attr(this.prop,Wr(this.start).tweenTo(Wr(this.end),this.pos),void 0,!0)}}Ot.timers=[];let{defined:kn,getStyle:wn,isArray:Mn,isNumber:Sn,isObject:ys,merge:Hr,objectEach:Tn,pick:An}=R;function bs(l){return ys(l)?Hr({duration:500,defer:0},l):{duration:l?500:0,defer:0}}function Xr(l,t){let e=Ot.timers.length;for(;e--;)Ot.timers[e].elem!==l||t&&t!==Ot.timers[e].prop||(Ot.timers[e].stopped=!0)}let St={animate:function(l,t,e){let i,s="",r,o,a;ys(e)||(a=arguments,e={duration:a[2],easing:a[3],complete:a[4]}),Sn(e.duration)||(e.duration=400),e.easing=typeof e.easing=="function"?e.easing:Math[e.easing]||Math.easeInOutSine,e.curAnim=Hr(t),Tn(t,function(n,h){Xr(l,h),o=new Ot(l,e,h),r=void 0,h==="d"&&Mn(t.d)?(o.paths=o.initPath(l,l.pathArray,t.d),o.toD=t.d,i=0,r=1):l.attr?i=l.attr(h):(i=parseFloat(wn(l,h))||0,h!=="opacity"&&(s="px")),r||(r=n),typeof r=="string"&&r.match("px")&&(r=r.replace(/px/g,"")),o.run(i,r,s)})},animObject:bs,getDeferredAnimation:function(l,t,e){let i=bs(t),s=e?[e]:l.series,r=0,o=0;return s.forEach(a=>{let n=bs(a.options.animation);r=ys(t)&&kn(t.defer)?i.defer:Math.max(r,n.duration+n.defer),o=Math.min(i.duration,n.duration)}),l.renderer.forExport&&(r=0),{defer:Math.max(0,r-o),duration:Math.min(r,o)}},setAnimation:function(l,t){t.renderer.globalAnimation=An(l,t.options.chart.animation,!0)},stop:Xr},{SVG_NS:Fr,win:Cn}=A,{attr:Pn,createElement:Ln,css:On,error:Yr,isFunction:En,isString:Gr,objectEach:jr,splat:Dn}=R,{trustedTypes:vs}=Cn,fi=vs&&En(vs.createPolicy)&&vs.createPolicy("highcharts",{createHTML:l=>l}),In=fi?fi.createHTML(""):"";class it{static filterUserAttributes(t){return jr(t,(e,i)=>{let s=!0;it.allowedAttributes.indexOf(i)===-1&&(s=!1),["background","dynsrc","href","lowsrc","src"].indexOf(i)!==-1&&(s=Gr(e)&&it.allowedReferences.some(r=>e.indexOf(r)===0)),s||(Yr(33,!1,void 0,{"Invalid attribute in config":`${i}`}),delete t[i]),Gr(e)&&t[i]&&(t[i]=e.replace(/</g,"&lt;"))}),t}static parseStyle(t){return t.split(";").reduce((e,i)=>{let s=i.split(":").map(o=>o.trim()),r=s.shift();return r&&s.length&&(e[r.replace(/-([a-z])/g,o=>o[1].toUpperCase())]=s.join(":")),e},{})}static setElementHTML(t,e){t.innerHTML=it.emptyHTML,e&&new it(e).addToDOM(t)}constructor(t){this.nodes=typeof t=="string"?this.parseMarkup(t):t}addToDOM(t){return function e(i,s){let r;return Dn(i).forEach(function(o){let a,n=o.tagName,h=o.textContent?A.doc.createTextNode(o.textContent):void 0,c=it.bypassHTMLFiltering;if(n)if(n==="#text")a=h;else if(it.allowedTags.indexOf(n)!==-1||c){let d=n==="svg"?Fr:s.namespaceURI||Fr,p=A.doc.createElementNS(d,n),u=o.attributes||{};jr(o,function(g,m){m!=="tagName"&&m!=="attributes"&&m!=="children"&&m!=="style"&&m!=="textContent"&&(u[m]=g)}),Pn(p,c?u:it.filterUserAttributes(u)),o.style&&On(p,o.style),h&&p.appendChild(h),e(o.children||[],p),a=p}else Yr(33,!1,void 0,{"Invalid tagName in config":n});a&&s.appendChild(a),r=a}),r}(this.nodes,t)}parseMarkup(t){let e,i=[];t=t.trim().replace(/ style=(["'])/g," data-style=$1");try{e=new DOMParser().parseFromString(fi?fi.createHTML(t):t,"text/html")}catch{}if(!e){let r=Ln("div");r.innerHTML=t,e={body:r}}let s=(r,o)=>{let a=r.nodeName.toLowerCase(),n={tagName:a};a==="#text"&&(n.textContent=r.textContent||"");let h=r.attributes;if(h){let c={};[].forEach.call(h,d=>{d.name==="data-style"?n.style=it.parseStyle(d.value):c[d.name]=d.value}),n.attributes=c}if(r.childNodes.length){let c=[];[].forEach.call(r.childNodes,d=>{s(d,c)}),c.length&&(n.children=c)}o.push(n)};return[].forEach.call(e.body.childNodes,r=>s(r,i)),i}}it.allowedAttributes=["alt","aria-controls","aria-describedby","aria-expanded","aria-haspopup","aria-hidden","aria-label","aria-labelledby","aria-live","aria-pressed","aria-readonly","aria-roledescription","aria-selected","class","clip-path","color","colspan","cx","cy","d","dx","dy","disabled","fill","filterUnits","flood-color","flood-opacity","height","href","id","in","in2","markerHeight","markerWidth","offset","opacity","operator","orient","padding","paddingLeft","paddingRight","patternUnits","r","radius","refX","refY","role","scope","slope","src","startOffset","stdDeviation","stroke","stroke-linecap","stroke-width","style","tableValues","result","rowspan","summary","target","tabindex","text-align","text-anchor","textAnchor","textLength","title","type","valign","width","x","x1","x2","xlink:href","y","y1","y2","zIndex"],it.allowedReferences=["https://","http://","mailto:","/","../","./","#"],it.allowedTags=["a","abbr","b","br","button","caption","circle","clipPath","code","dd","defs","div","dl","dt","em","feComponentTransfer","feComposite","feDropShadow","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feMorphology","feOffset","feMerge","feMergeNode","filter","h1","h2","h3","h4","h5","h6","hr","i","img","li","linearGradient","marker","ol","p","path","pattern","pre","rect","small","span","stop","strong","style","sub","sup","svg","table","text","textPath","thead","title","tbody","tspan","td","th","tr","u","ul","#text"],it.emptyHTML=In,it.bypassHTMLFiltering=!1;let{defaultOptions:$r,defaultTime:Ur}=zt,{pageLang:Bn}=A,{extend:Nn,getNestedProperty:zn,isArray:Rn,isNumber:Vr,isObject:Wn,isString:Hn,pick:Xn,ucfirst:Fn}=R,mi={add:(l,t)=>l+t,divide:(l,t)=>t!==0?l/t:"",eq:(l,t)=>l==t,each:function(l){let t=arguments[arguments.length-1];return!!Rn(l)&&l.map((e,i)=>xi(t.body,Nn(Wn(e)?e:{"@this":e},{"@index":i,"@first":i===0,"@last":i===l.length-1}))).join("")},ge:(l,t)=>l>=t,gt:(l,t)=>l>t,if:l=>!!l,le:(l,t)=>l<=t,lt:(l,t)=>l<t,multiply:(l,t)=>l*t,ne:(l,t)=>l!=t,subtract:(l,t)=>l-t,ucfirst:Fn,unless:l=>!l},qr={},Zr=l=>/^["'].+["']$/.test(l);function xi(l="",t,e){let i=/\{([\p{L}\d:\.,;\-\/<>\[\]%_@+"'’= #\(\)]+)\}/gu,s=/\(([\p{L}\d:\.,;\-\/<>\[\]%_@+"'= ]+)\)/gu,r=[],o=/f$/,a=/\.(\d)/,n=(e==null?void 0:e.options.lang)||$r.lang,h=e&&e.time||Ur,c=e&&e.numberFormatter||Kr,d=(x="")=>{let f;return x==="true"||x!=="false"&&((f=Number(x)).toString()===x?f:Zr(x)?x.slice(1,-1):zn(x,t))},p,u,g=0,m;for(;(p=i.exec(l))!==null;){let x=p,f=s.exec(p[1]);f&&(p=f,m=!0),u&&u.isBlock||(u={ctx:t,expression:p[1],find:p[0],isBlock:p[1].charAt(0)==="#",start:p.index,startInner:p.index+p[0].length,length:p[0].length});let b=(u.isBlock?x:p)[1].split(" ")[0].replace("#","");mi[b]&&(u.isBlock&&b===u.fn&&g++,u.fn||(u.fn=b));let y=p[1]==="else";if(u.isBlock&&u.fn&&(p[1]===`/${u.fn}`||y))if(g)!y&&g--;else{let v=u.startInner,w=l.substr(v,p.index-v);u.body===void 0?(u.body=w,u.startInner=p.index+p[0].length):u.elseBody=w,u.find+=w+p[0],y||(r.push(u),u=void 0)}else u.isBlock||r.push(u);if(f&&!(u!=null&&u.isBlock))break}return r.forEach(x=>{let f,b,{body:y,elseBody:v,expression:w,fn:k}=x;if(k){let S=[x],M=[],T=w.length,P=0,L;for(b=0;b<=T;b++){let E=w.charAt(b);L||E!=='"'&&E!=="'"?L===E&&(L=""):L=E,L||E!==" "&&b!==T||(M.push(w.substr(P,b-P)),P=b+1)}for(b=mi[k].length;b--;)S.unshift(d(M[b+1]));f=mi[k].apply(t,S),x.isBlock&&typeof f=="boolean"&&(f=xi(f?y:v,t,e))}else{let S=Zr(w)?[w]:w.split(":");if(f=d(S.shift()||""),S.length&&typeof f=="number"){let M=S.join(":");if(o.test(M)){let T=parseInt((M.match(a)||["","-1"])[1],10);f!==null&&(f=c(f,T,n.decimalPoint,M.indexOf(",")>-1?n.thousandsSep:""))}else f=h.dateFormat(M,f)}s.lastIndex=0,s.test(x.find)&&Hn(f)&&(f=`"${f}"`)}l=l.replace(x.find,Xn(f,""))}),m?xi(l,t,e):l}function Kr(l,t,e,i){var m;t=+t;let s,r,[o,a]=(l=+l||0).toString().split("e").map(Number),n=((m=this==null?void 0:this.options)==null?void 0:m.lang)||$r.lang,h=(l.toString().split(".")[1]||"").split("e")[0].length,c=t,d={};e??(e=n.decimalPoint),i??(i=n.thousandsSep),t===-1?t=Math.min(h,20):Vr(t)?t&&a<0&&((r=t+a)>=0?(o=+o.toExponential(r).split("e")[0],t=r):(o=Math.floor(o),l=t<20?+(o*Math.pow(10,a)).toFixed(t):0,a=0)):t=2,a&&(t??(t=2),l=o),Vr(t)&&t>=0&&(d.minimumFractionDigits=t,d.maximumFractionDigits=t),i===""&&(d.useGrouping=!1);let p=i||e,u=p?"en":(this==null?void 0:this.locale)||n.locale||Bn,g=JSON.stringify(d)+u;return s=(qr[g]??(qr[g]=new Intl.NumberFormat(u,d))).format(l),p&&(s=s.replace(/([,\.])/g,"_$1").replace(/_\,/g,i??",").replace("_.",e??".")),(t||+s!=0)&&(!(a<0)||c)||(s="0"),a&&+s!=0&&(s+="e"+(a<0?"":"+")+a),s}let Yt={dateFormat:function(l,t,e){return Ur.dateFormat(l,t,e)},format:xi,helpers:mi,numberFormat:Kr};(function(l){let t;l.rendererTypes={},l.getRendererType=function(e=t){return l.rendererTypes[e]||l.rendererTypes[t]},l.registerRendererType=function(e,i,s){l.rendererTypes[e]=i,(!t||s)&&(t=e,A.Renderer=i)}})(Dt||(Dt={}));let $e=Dt,{clamp:Yn,pick:Gn,pushUnique:jn,stableSort:ks}=R;(G||(G={})).distribute=function l(t,e,i){let s=t,r=s.reducedLen||e,o=(y,v)=>y.target-v.target,a=[],n=t.length,h=[],c=a.push,d,p,u,g=!0,m,x,f=0,b;for(d=n;d--;)f+=t[d].size;if(f>r){for(ks(t,(y,v)=>(v.rank||0)-(y.rank||0)),u=(b=t[0].rank===t[t.length-1].rank)?n/2:-1,p=b?u:n-1;u&&f>r;)m=t[d=Math.floor(p)],jn(h,d)&&(f-=m.size),p+=u,b&&p>=t.length&&(u/=2,p=u);h.sort((y,v)=>v-y).forEach(y=>c.apply(a,t.splice(y,1)))}for(ks(t,o),t=t.map(y=>({size:y.size,targets:[y.target],align:Gn(y.align,.5)}));g;){for(d=t.length;d--;)m=t[d],x=(Math.min.apply(0,m.targets)+Math.max.apply(0,m.targets))/2,m.pos=Yn(x-m.size*m.align,0,e-m.size);for(d=t.length,g=!1;d--;)d>0&&t[d-1].pos+t[d-1].size>t[d].pos&&(t[d-1].size+=t[d].size,t[d-1].targets=t[d-1].targets.concat(t[d].targets),t[d-1].align=.5,t[d-1].pos+t[d-1].size>e&&(t[d-1].pos=e-t[d-1].size),t.splice(d,1),g=!0)}return c.apply(s,a),d=0,t.some(y=>{let v=0;return(y.targets||[]).some(()=>(s[d].pos=y.pos+v,i!==void 0&&Math.abs(s[d].pos-s[d].target)>i?(s.slice(0,d+1).forEach(w=>delete w.pos),s.reducedLen=(s.reducedLen||e)-.1*e,s.reducedLen>.1*e&&l(s,e,i),!0):(v+=s[d].size,d++,!1)))}),ks(s,o),s};let yi=G,{animate:$n,animObject:Un,stop:_r}=St,{deg2rad:Qr,doc:Te,svg:Vn,SVG_NS:bi,win:qn,isFirefox:Zn}=A,{addEvent:Kn,attr:ws,createElement:_n,crisp:vi,css:Jr,defined:Vt,erase:Qn,extend:Ue,fireEvent:Ms,getAlignFactor:Ss,isArray:to,isFunction:eo,isNumber:Jn,isObject:tl,isString:io,merge:Ts,objectEach:Ae,pick:Gt,pInt:ki,pushUnique:el,replaceNested:il,syncTimeout:sl,uniqueKey:so}=R;class mt{_defaultGetter(t){let e=Gt(this[t+"Value"],this[t],this.element?this.element.getAttribute(t):null,0);return/^-?[\d\.]+$/.test(e)&&(e=parseFloat(e)),e}_defaultSetter(t,e,i){i.setAttribute(e,t)}add(t){let e,i=this.renderer,s=this.element;return t&&(this.parentGroup=t),this.textStr!==void 0&&this.element.nodeName==="text"&&i.buildText(this),this.added=!0,(!t||t.handleZ||this.zIndex)&&(e=this.zIndexSetter()),e||(t?t.element:i.box).appendChild(s),this.onAdd&&this.onAdd(),this}addClass(t,e){let i=e?"":this.attr("class")||"";return(t=(t||"").split(/ /g).reduce(function(s,r){return i.indexOf(r)===-1&&s.push(r),s},i?[i]:[]).join(" "))!==i&&this.attr("class",t),this}afterSetters(){this.doTransform&&(this.updateTransform(),this.doTransform=!1)}align(t,e,i,s=!0){let r=this.renderer,o=r.alignedObjects,a=!!t;t?(this.alignOptions=t,this.alignByTranslate=e,this.alignTo=i):(t=this.alignOptions||{},e=this.alignByTranslate,i=this.alignTo);let n=!i||io(i)?i||"renderer":void 0;n&&(a&&el(o,this),i=void 0);let h=Gt(i,r[n],r),c=(h.x||0)+(t.x||0)+((h.width||0)-(t.width||0))*Ss(t.align),d=(h.y||0)+(t.y||0)+((h.height||0)-(t.height||0))*Ss(t.verticalAlign),p={"text-align":t==null?void 0:t.align};return p[e?"translateX":"x"]=Math.round(c),p[e?"translateY":"y"]=Math.round(d),s&&(this[this.placed?"animate":"attr"](p),this.placed=!0),this.alignAttr=p,this}alignSetter(t){let e={left:"start",center:"middle",right:"end"};e[t]&&(this.alignValue=t,this.element.setAttribute("text-anchor",e[t]))}animate(t,e,i){let s=Un(Gt(e,this.renderer.globalAnimation,!0)),r=s.defer;return Te.hidden&&(s.duration=0),s.duration!==0?(i&&(s.complete=i),sl(()=>{this.element&&$n(this,t,s)},r)):(this.attr(t,void 0,i||s.complete),Ae(t,function(o,a){s.step&&s.step.call(this,o,{prop:a,pos:1,elem:this})},this)),this}applyTextOutline(t){let e=this.element;t.indexOf("contrast")!==-1&&(t=t.replace(/contrast/g,this.renderer.getContrast(e.style.fill)));let i=t.split(" "),s=i[i.length-1],r=i[0];if(r&&r!=="none"&&A.svg){this.fakeTS=!0,r=r.replace(/(^[\d\.]+)(.*?)$/g,function(c,d,p){return 2*Number(d)+p}),this.removeTextOutline();let o=Te.createElementNS(bi,"tspan");ws(o,{class:"highcharts-text-outline",fill:s,stroke:s,"stroke-width":r,"stroke-linejoin":"round"});let a=e.querySelector("textPath")||e;[].forEach.call(a.childNodes,c=>{let d=c.cloneNode(!0);d.removeAttribute&&["fill","stroke","stroke-width","stroke"].forEach(p=>d.removeAttribute(p)),o.appendChild(d)});let n=0;[].forEach.call(a.querySelectorAll("text tspan"),c=>{n+=Number(c.getAttribute("dy"))});let h=Te.createElementNS(bi,"tspan");h.textContent="​",ws(h,{x:Number(e.getAttribute("x")),dy:-n}),o.appendChild(h),a.insertBefore(o,a.firstChild)}}attr(t,e,i,s){let{element:r}=this,o=mt.symbolCustomAttribs,a,n,h=this,c;return typeof t=="string"&&e!==void 0&&(a=t,(t={})[a]=e),typeof t=="string"?h=(this[t+"Getter"]||this._defaultGetter).call(this,t,r):(Ae(t,function(d,p){c=!1,s||_r(this,p),this.symbolName&&o.indexOf(p)!==-1&&(n||(this.symbolAttr(t),n=!0),c=!0),this.rotation&&(p==="x"||p==="y")&&(this.doTransform=!0),c||(this[p+"Setter"]||this._defaultSetter).call(this,d,p,r)},this),this.afterSetters()),i&&i.call(this),h}clip(t){if(t&&!t.clipPath){let e=so()+"-",i=this.renderer.createElement("clipPath").attr({id:e}).add(this.renderer.defs);Ue(t,{clipPath:i,id:e,count:0}),t.add(i)}return this.attr("clip-path",t?`url(${this.renderer.url}#${t.id})`:"none")}crisp(t,e){e=Math.round(e||t.strokeWidth||0);let i=t.x||this.x||0,s=t.y||this.y||0,r=(t.width||this.width||0)+i,o=(t.height||this.height||0)+s,a=vi(i,e),n=vi(s,e);return Ue(t,{x:a,y:n,width:vi(r,e)-a,height:vi(o,e)-n}),Vt(t.strokeWidth)&&(t.strokeWidth=e),t}complexColor(t,e,i){let s=this.renderer,r,o,a,n,h,c,d,p,u,g,m=[],x;Ms(this.renderer,"complexColor",{args:arguments},function(){if(t.radialGradient?o="radialGradient":t.linearGradient&&(o="linearGradient"),o){if(a=t[o],h=s.gradients,c=t.stops,u=i.radialReference,to(a)&&(t[o]=a={x1:a[0],y1:a[1],x2:a[2],y2:a[3],gradientUnits:"userSpaceOnUse"}),o==="radialGradient"&&u&&!Vt(a.gradientUnits)&&(n=a,a=Ts(a,s.getRadialAttr(u,n),{gradientUnits:"userSpaceOnUse"})),Ae(a,function(f,b){b!=="id"&&m.push(b,f)}),Ae(c,function(f){m.push(f)}),h[m=m.join(",")])g=h[m].attr("id");else{a.id=g=so();let f=h[m]=s.createElement(o).attr(a).add(s.defs);f.radAttr=n,f.stops=[],c.forEach(function(b){b[1].indexOf("rgba")===0?(d=(r=pt.parse(b[1])).get("rgb"),p=r.get("a")):(d=b[1],p=1);let y=s.createElement("stop").attr({offset:b[0],"stop-color":d,"stop-opacity":p}).add(f);f.stops.push(y)})}x="url("+s.url+"#"+g+")",i.setAttribute(e,x),i.gradient=m,t.toString=function(){return x}}})}css(t){let e=this.styles,i={},s=this.element,r,o=!e;if(e&&Ae(t,function(a,n){e&&e[n]!==a&&(i[n]=a,o=!0)}),o){e&&(t=Ue(e,i)),t.width===null||t.width==="auto"?delete this.textWidth:s.nodeName.toLowerCase()==="text"&&t.width&&(r=this.textWidth=ki(t.width)),Ue(this.styles,t),r&&!Vn&&this.renderer.forExport&&delete t.width;let a=Zn&&t.fontSize||null;a&&(Jn(a)||/^\d+$/.test(a))&&(t.fontSize+="px");let n=Ts(t);s.namespaceURI===this.SVG_NS&&(["textOutline","textOverflow","whiteSpace","width"].forEach(h=>n&&delete n[h]),n.color&&(n.fill=n.color)),Jr(s,n)}return this.added&&(this.element.nodeName==="text"&&this.renderer.buildText(this),t.textOutline&&this.applyTextOutline(t.textOutline)),this}dashstyleSetter(t){let e,i=this["stroke-width"];if(i==="inherit"&&(i=1),t=t&&t.toLowerCase()){let s=t.replace("shortdashdotdot","3,1,1,1,1,1,").replace("shortdashdot","3,1,1,1").replace("shortdot","1,1,").replace("shortdash","3,1,").replace("longdash","8,3,").replace(/dot/g,"1,3,").replace("dash","4,3,").replace(/,$/,"").split(",");for(e=s.length;e--;)s[e]=""+ki(s[e])*Gt(i,NaN);t=s.join(",").replace(/NaN/g,"none"),this.element.setAttribute("stroke-dasharray",t)}}destroy(){var n;let t=this,e=t.element||{},i=t.renderer,s=e.ownerSVGElement,r=e.nodeName==="SPAN"&&t.parentGroup||void 0,o,a;if(e.onclick=e.onmouseout=e.onmouseover=e.onmousemove=e.point=null,_r(t),t.clipPath&&s){let h=t.clipPath;[].forEach.call(s.querySelectorAll("[clip-path],[CLIP-PATH]"),function(c){c.getAttribute("clip-path").indexOf(h.element.id)>-1&&c.removeAttribute("clip-path")}),t.clipPath=h.destroy()}if(t.connector=(n=t.connector)==null?void 0:n.destroy(),t.stops){for(a=0;a<t.stops.length;a++)t.stops[a].destroy();t.stops.length=0,t.stops=void 0}for(t.safeRemoveChild(e);r&&r.div&&r.div.childNodes.length===0;)o=r.parentGroup,t.safeRemoveChild(r.div),delete r.div,r=o;t.alignOptions&&Qn(i.alignedObjects,t),Ae(t,function(h,c){t[c]&&t[c].parentGroup===t&&t[c].destroy&&t[c].destroy(),delete t[c]})}dSetter(t,e,i){to(t)&&(typeof t[0]=="string"&&(t=this.renderer.pathToSegments(t)),this.pathArray=t,t=t.reduce((s,r,o)=>r&&r.join?(o?s+" ":"")+r.join(" "):(r||"").toString(),"")),/(NaN| {2}|^$)/.test(t)&&(t="M 0 0"),this[e]!==t&&(i.setAttribute(e,t),this[e]=t)}fillSetter(t,e,i){typeof t=="string"?i.setAttribute(e,t):t&&this.complexColor(t,e,i)}hrefSetter(t,e,i){i.setAttributeNS("http://www.w3.org/1999/xlink",e,t)}getBBox(t,e){let i,s,r,o,{alignValue:a,element:n,renderer:h,styles:c,textStr:d}=this,{cache:p,cacheKeys:u}=h,g=n.namespaceURI===this.SVG_NS,m=Gt(e,this.rotation,0),x=h.styledMode?n&&mt.prototype.getStyle.call(n,"font-size"):c.fontSize;if(Vt(d)&&((o=d.toString()).indexOf("<")===-1&&(o=o.replace(/\d/g,"0")),o+=["",h.rootFontSize,x,m,this.textWidth,a,c.lineClamp,c.textOverflow,c.fontWeight].join(",")),o&&!t&&(i=p[o]),!i||i.polygon){if(g||h.forExport){try{r=this.fakeTS&&function(b){let y=n.querySelector(".highcharts-text-outline");y&&Jr(y,{display:b})},eo(r)&&r("none"),i=n.getBBox?Ue({},n.getBBox()):{width:n.offsetWidth,height:n.offsetHeight,x:0,y:0},eo(r)&&r("")}catch{}(!i||i.width<0)&&(i={x:0,y:0,width:0,height:0})}else i=this.htmlGetBBox();s=i.height,g&&(i.height=s={"11px,17":14,"13px,20":16}[`${x||""},${Math.round(s)}`]||s),m&&(i=this.getRotatedBox(i,m));let f={bBox:i};Ms(this,"afterGetBBox",f),i=f.bBox}if(o&&(d===""||i.height>0)){for(;u.length>250;)delete p[u.shift()];p[o]||u.push(o),p[o]=i}return i}getRotatedBox(t,e){let{x:i,y:s,width:r,height:o}=t,{alignValue:a,translateY:n,rotationOriginX:h=0,rotationOriginY:c=0}=this,d=Ss(a),p=Number(this.element.getAttribute("y")||0)-(n?0:s),u=e*Qr,g=(e-90)*Qr,m=Math.cos(u),x=Math.sin(u),f=r*m,b=r*x,y=Math.cos(g),v=Math.sin(g),[[w,k],[S,M]]=[h,c].map(H=>[H-H*m,H*x]),T=i+d*(r-f)+w+M+p*y,P=T+f,L=P-o*y,E=L-f,C=s+p-d*b-k+S+p*v,O=C+b,N=O-o*v,F=N-b,I=Math.min(T,P,L,E),X=Math.min(C,O,N,F),D=Math.max(T,P,L,E)-I,z=Math.max(C,O,N,F)-X;return{x:I,y:X,width:D,height:z,polygon:[[T,C],[P,O],[L,N],[E,F]]}}getStyle(t){return qn.getComputedStyle(this.element||this,"").getPropertyValue(t)}hasClass(t){return(""+this.attr("class")).split(" ").indexOf(t)!==-1}hide(){return this.attr({visibility:"hidden"})}htmlGetBBox(){return{height:0,width:0,x:0,y:0}}constructor(t,e){this.onEvents={},this.opacity=1,this.SVG_NS=bi,this.element=e==="span"||e==="body"?_n(e):Te.createElementNS(this.SVG_NS,e),this.renderer=t,this.styles={},Ms(this,"afterInit")}on(t,e){let{onEvents:i}=this;return i[t]&&i[t](),i[t]=Kn(this.element,t,e),this}opacitySetter(t,e,i){let s=Number(Number(t).toFixed(3));this.opacity=s,i.setAttribute(e,s)}reAlign(){var t;(t=this.alignOptions)!=null&&t.width&&this.alignOptions.align!=="left"&&(this.alignOptions.width=this.getBBox().width,this.placed=!1,this.align())}removeClass(t){return this.attr("class",(""+this.attr("class")).replace(io(t)?RegExp(`(^| )${t}( |$)`):t," ").replace(/ +/g," ").trim())}removeTextOutline(){let t=this.element.querySelector("tspan.highcharts-text-outline");t&&this.safeRemoveChild(t)}safeRemoveChild(t){let e=t.parentNode;e&&e.removeChild(t)}setRadialReference(t){let e=this.element.gradient&&this.renderer.gradients[this.element.gradient];return this.element.radialReference=t,e&&e.radAttr&&e.animate(this.renderer.getRadialAttr(t,e.radAttr)),this}shadow(t){var r;let{renderer:e}=this,i=Ts(((r=this.parentGroup)==null?void 0:r.rotation)===90?{offsetX:-1,offsetY:-1}:{},tl(t)?t:{}),s=e.shadowDefinition(i);return this.attr({filter:t?`url(${e.url}#${s})`:"none"})}show(t=!0){return this.attr({visibility:t?"inherit":"visible"})}"stroke-widthSetter"(t,e,i){this[e]=t,i.setAttribute(e,t)}strokeWidth(){if(!this.renderer.styledMode)return this["stroke-width"]||0;let t=this.getStyle("stroke-width"),e=0,i;return/px$/.test(t)?e=ki(t):t!==""&&(ws(i=Te.createElementNS(bi,"rect"),{width:t,"stroke-width":0}),this.element.parentNode.appendChild(i),e=i.getBBox().width,i.parentNode.removeChild(i)),e}symbolAttr(t){let e=this;mt.symbolCustomAttribs.forEach(function(i){e[i]=Gt(t[i],e[i])}),e.attr({d:e.renderer.symbols[e.symbolName](e.x,e.y,e.width,e.height,e)})}textSetter(t){t!==this.textStr&&(delete this.textPxLength,this.textStr=t,this.added&&this.renderer.buildText(this),this.reAlign())}titleSetter(t){let e=this.element,i=e.getElementsByTagName("title")[0]||Te.createElementNS(this.SVG_NS,"title");e.insertBefore?e.insertBefore(i,e.firstChild):e.appendChild(i),i.textContent=il(Gt(t,""),[/<[^>]*>/g,""]).replace(/&lt;/g,"<").replace(/&gt;/g,">")}toFront(){let t=this.element;return t.parentNode.appendChild(t),this}translate(t,e){return this.attr({translateX:t,translateY:e})}updateTransform(t="transform"){var p;let{element:e,matrix:i,rotation:s=0,rotationOriginX:r,rotationOriginY:o,scaleX:a,scaleY:n,translateX:h=0,translateY:c=0}=this,d=["translate("+h+","+c+")"];Vt(i)&&d.push("matrix("+i.join(",")+")"),s&&(d.push("rotate("+s+" "+Gt(r,e.getAttribute("x"),0)+" "+Gt(o,e.getAttribute("y")||0)+")"),((p=this.text)==null?void 0:p.element.tagName)==="SPAN"&&this.text.attr({rotation:s,rotationOriginX:(r||0)-this.padding,rotationOriginY:(o||0)-this.padding})),(Vt(a)||Vt(n))&&d.push("scale("+Gt(a,1)+" "+Gt(n,1)+")"),d.length&&!(this.text||this).textPath&&e.setAttribute(t,d.join(" "))}visibilitySetter(t,e,i){t==="inherit"?i.removeAttribute(e):this[e]!==t&&i.setAttribute(e,t),this[e]=t}xGetter(t){return this.element.nodeName==="circle"&&(t==="x"?t="cx":t==="y"&&(t="cy")),this._defaultGetter(t)}zIndexSetter(t,e){let i=this.renderer,s=this.parentGroup,r=(s||i).element||i.box,o=this.element,a=r===i.box,n,h,c,d=!1,p,u=this.added,g;if(Vt(t)?(o.setAttribute("data-z-index",t),t=+t,this[e]===t&&(u=!1)):Vt(this[e])&&o.removeAttribute("data-z-index"),this[e]=t,u){for((t=this.zIndex)&&s&&(s.handleZ=!0),g=(n=r.childNodes).length-1;g>=0&&!d;g--)p=!Vt(c=(h=n[g]).getAttribute("data-z-index")),h!==o&&(t<0&&p&&!a&&!g?(r.insertBefore(o,n[g]),d=!0):(ki(c)<=t||p&&(!Vt(t)||t>=0))&&(r.insertBefore(o,n[g+1]),d=!0));d||(r.insertBefore(o,n[a?3:0]),d=!0)}return d}}mt.symbolCustomAttribs=["anchorX","anchorY","clockwise","end","height","innerR","r","start","width","x","y"],mt.prototype.strokeSetter=mt.prototype.fillSetter,mt.prototype.yGetter=mt.prototype.xGetter,mt.prototype.matrixSetter=mt.prototype.rotationOriginXSetter=mt.prototype.rotationOriginYSetter=mt.prototype.rotationSetter=mt.prototype.scaleXSetter=mt.prototype.scaleYSetter=mt.prototype.translateXSetter=mt.prototype.translateYSetter=mt.prototype.verticalAlignSetter=function(l,t){this[t]=l,this.doTransform=!0};let qt=mt,{defined:ro,extend:rl,getAlignFactor:oo,isNumber:Ve,merge:ol,pick:wi,removeEvent:ao}=R;class ue extends qt{constructor(t,e,i,s,r,o,a,n,h,c){let d;super(t,"g"),this.paddingLeftSetter=this.paddingSetter,this.paddingRightSetter=this.paddingSetter,this.doUpdate=!1,this.textStr=e,this.x=i,this.y=s,this.anchorX=o,this.anchorY=a,this.baseline=h,this.className=c,this.addClass(c==="button"?"highcharts-no-tooltip":"highcharts-label"),c&&this.addClass("highcharts-"+c),this.text=t.text(void 0,0,0,n).attr({zIndex:1}),typeof r=="string"&&((d=/^url\((.*?)\)$/.test(r))||this.renderer.symbols[r])&&(this.symbolKey=r),this.bBox=ue.emptyBBox,this.padding=3,this.baselineOffset=0,this.needsBox=t.styledMode||d,this.deferredAttr={},this.alignFactor=0}alignSetter(t){let e=oo(t);this.textAlign=t,e!==this.alignFactor&&(this.alignFactor=e,this.bBox&&Ve(this.xSetting)&&this.attr({x:this.xSetting}))}anchorXSetter(t,e){this.anchorX=t,this.boxAttr(e,Math.round(t)-this.getCrispAdjust()-this.xSetting)}anchorYSetter(t,e){this.anchorY=t,this.boxAttr(e,t-this.ySetting)}boxAttr(t,e){this.box?this.box.attr(t,e):this.deferredAttr[t]=e}css(t){if(t){let e={};t=ol(t),ue.textProps.forEach(i=>{t[i]!==void 0&&(e[i]=t[i],delete t[i])}),this.text.css(e),"fontSize"in e||"fontWeight"in e?this.updateTextPadding():("width"in e||"textOverflow"in e)&&this.updateBoxSize()}return qt.prototype.css.call(this,t)}destroy(){ao(this.element,"mouseenter"),ao(this.element,"mouseleave"),this.text&&this.text.destroy(),this.box&&(this.box=this.box.destroy()),qt.prototype.destroy.call(this)}fillSetter(t,e){t&&(this.needsBox=!0),this.fill=t,this.boxAttr(e,t)}getBBox(t,e){this.textStr&&this.bBox.width===0&&this.bBox.height===0&&this.updateBoxSize();let{padding:i,height:s=0,translateX:r=0,translateY:o=0,width:a=0}=this,n=wi(this.paddingLeft,i),h=e??(this.rotation||0),c={width:a,height:s,x:r+this.bBox.x-n,y:o+this.bBox.y-i+this.baselineOffset};return h&&(c=this.getRotatedBox(c,h)),c}getCrispAdjust(){return(this.renderer.styledMode&&this.box?this.box.strokeWidth():this["stroke-width"]?parseInt(this["stroke-width"],10):0)%2/2}heightSetter(t){this.heightSetting=t,this.doUpdate=!0}afterSetters(){super.afterSetters(),this.doUpdate&&(this.updateBoxSize(),this.doUpdate=!1)}onAdd(){this.text.add(this),this.attr({text:wi(this.textStr,""),x:this.x||0,y:this.y||0}),this.box&&ro(this.anchorX)&&this.attr({anchorX:this.anchorX,anchorY:this.anchorY})}paddingSetter(t,e){Ve(t)?t!==this[e]&&(this[e]=t,this.updateTextPadding()):this[e]=void 0}rSetter(t,e){this.boxAttr(e,t)}strokeSetter(t,e){this.stroke=t,this.boxAttr(e,t)}"stroke-widthSetter"(t,e){t&&(this.needsBox=!0),this["stroke-width"]=t,this.boxAttr(e,t)}"text-alignSetter"(t){this.textAlign=this["text-align"]=t,this.updateTextPadding()}textSetter(t){t!==void 0&&this.text.attr({text:t}),this.updateTextPadding(),this.reAlign()}updateBoxSize(){let t,e=this.text,i={},s=this.padding,r=this.bBox=(!Ve(this.widthSetting)||!Ve(this.heightSetting)||this.textAlign)&&ro(e.textStr)?e.getBBox(void 0,0):ue.emptyBBox;this.width=this.getPaddedWidth(),this.height=(this.heightSetting||r.height||0)+2*s;let o=this.renderer.fontMetrics(e);if(this.baselineOffset=s+Math.min((this.text.firstLineMetrics||o).b,r.height||1/0),this.heightSetting&&(this.baselineOffset+=(this.heightSetting-o.h)/2),this.needsBox&&!e.textPath){if(!this.box){let a=this.box=this.symbolKey?this.renderer.symbol(this.symbolKey):this.renderer.rect();a.addClass((this.className==="button"?"":"highcharts-label-box")+(this.className?" highcharts-"+this.className+"-box":"")),a.add(this)}t=this.getCrispAdjust(),i.x=t,i.y=(this.baseline?-this.baselineOffset:0)+t,i.width=Math.round(this.width),i.height=Math.round(this.height),this.box.attr(rl(i,this.deferredAttr)),this.deferredAttr={}}}updateTextPadding(){let t=this.text,e=t.styles.textAlign||this.textAlign;if(!t.textPath){this.updateBoxSize();let i=this.baseline?0:this.baselineOffset,s=(this.paddingLeft??this.padding)+oo(e)*(this.widthSetting??this.bBox.width);(s!==t.x||i!==t.y)&&(t.attr({align:e,x:s}),i!==void 0&&t.attr("y",i)),t.x=s,t.y=i}}widthSetter(t){this.widthSetting=Ve(t)?t:void 0,this.doUpdate=!0}getPaddedWidth(){let t=this.padding,e=wi(this.paddingLeft,t),i=wi(this.paddingRight,t);return(this.widthSetting||this.bBox.width||0)+e+i}xSetter(t){this.x=t,this.alignFactor&&(t-=this.alignFactor*this.getPaddedWidth(),this["forceAnimate:x"]=!0),this.xSetting=Math.round(t),this.attr("translateX",this.xSetting)}ySetter(t){this.ySetting=this.y=Math.round(t),this.attr("translateY",this.ySetting)}}ue.emptyBBox={width:0,height:0,x:0,y:0},ue.textProps=["color","direction","fontFamily","fontSize","fontStyle","fontWeight","lineClamp","lineHeight","textAlign","textDecoration","textOutline","textOverflow","whiteSpace","width"];let{defined:no,isNumber:al,pick:qe}=R;function lo(l,t,e,i,s){let r=[];if(s){let o=s.start||0,a=qe(s.r,e),n=qe(s.r,i||e),h=2e-4/(s.borderRadius?1:Math.max(a,1)),c=Math.abs((s.end||0)-o-2*Math.PI)<h,d=(s.end||0)-(c?h:0),p=s.innerR,u=qe(s.open,c),g=Math.cos(o),m=Math.sin(o),x=Math.cos(d),f=Math.sin(d),b=qe(s.longArc,d-o-Math.PI<h?0:1),y=["A",a,n,0,b,qe(s.clockwise,1),l+a*x,t+n*f];y.params={start:o,end:d,cx:l,cy:t},r.push(["M",l+a*g,t+n*m],y),no(p)&&((y=["A",p,p,0,b,no(s.clockwise)?1-s.clockwise:0,l+p*g,t+p*m]).params={start:d,end:o,cx:l,cy:t},r.push(u?["M",l+p*x,t+p*f]:["L",l+p*x,t+p*f],y)),u||r.push(["Z"])}return r}function ho(l,t,e,i,s){return s&&s.r?As(l,t,e,i,s):[["M",l,t],["L",l+e,t],["L",l+e,t+i],["L",l,t+i],["Z"]]}function As(l,t,e,i,s){let r=(s==null?void 0:s.r)||0;return[["M",l+r,t],["L",l+e-r,t],["A",r,r,0,0,1,l+e,t+r],["L",l+e,t+i-r],["A",r,r,0,0,1,l+e-r,t+i],["L",l+r,t+i],["A",r,r,0,0,1,l,t+i-r],["L",l,t+r],["A",r,r,0,0,1,l+r,t],["Z"]]}let co={arc:lo,callout:function(l,t,e,i,s){let r=Math.min(s&&s.r||0,e,i),o=r+6,a=s&&s.anchorX,n=s&&s.anchorY||0,h=As(l,t,e,i,{r});if(!al(a)||a<e&&a>0&&n<i&&n>0)return h;if(l+a>e-o)if(n>t+o&&n<t+i-o)h.splice(3,1,["L",l+e,n-6],["L",l+e+6,n],["L",l+e,n+6],["L",l+e,t+i-r]);else if(a<e){let c=n<t+o,d=c?t:t+i;h.splice(c?2:5,0,["L",a,n],["L",l+e-r,d])}else h.splice(3,1,["L",l+e,i/2],["L",a,n],["L",l+e,i/2],["L",l+e,t+i-r]);else if(l+a<o)if(n>t+o&&n<t+i-o)h.splice(7,1,["L",l,n+6],["L",l-6,n],["L",l,n-6],["L",l,t+r]);else if(a>0){let c=n<t+o,d=c?t:t+i;h.splice(c?1:6,0,["L",a,n],["L",l+r,d])}else h.splice(7,1,["L",l,i/2],["L",a,n],["L",l,i/2],["L",l,t+r]);else n>i&&a<e-o?h.splice(5,1,["L",a+6,t+i],["L",a,t+i+6],["L",a-6,t+i],["L",l+r,t+i]):n<0&&a>o&&h.splice(1,1,["L",a-6,t],["L",a,t-6],["L",a+6,t],["L",e-r,t]);return h},circle:function(l,t,e,i){return lo(l+e/2,t+i/2,e/2,i/2,{start:.5*Math.PI,end:2.5*Math.PI,open:!1})},diamond:function(l,t,e,i){return[["M",l+e/2,t],["L",l+e,t+i/2],["L",l+e/2,t+i],["L",l,t+i/2],["Z"]]},rect:ho,roundedRect:As,square:ho,triangle:function(l,t,e,i){return[["M",l+e/2,t],["L",l+e,t+i],["L",l,t+i],["Z"]]},"triangle-down":function(l,t,e,i){return[["M",l,t],["L",l+e,t],["L",l+e/2,t+i],["Z"]]}},{doc:Cs,SVG_NS:nl,win:po}=A,{attr:Ps,extend:ll,fireEvent:hl,isString:dl,objectEach:cl,pick:pl}=R,Ls=(l,t)=>l.substring(0,t)+"…",ul=class{constructor(l){let t=l.styles;this.renderer=l.renderer,this.svgElement=l,this.width=l.textWidth,this.textLineHeight=t&&t.lineHeight,this.textOutline=t&&t.textOutline,this.ellipsis=!!(t&&t.textOverflow==="ellipsis"),this.lineClamp=t==null?void 0:t.lineClamp,this.noWrap=!!(t&&t.whiteSpace==="nowrap")}buildSVG(){let l=this.svgElement,t=l.element,e=l.renderer,i=pl(l.textStr,"").toString(),s=i.indexOf("<")!==-1,r=t.childNodes,o=!l.added&&e.box,a=[i,this.ellipsis,this.noWrap,this.textLineHeight,this.textOutline,l.getStyle("font-size"),l.styles.lineClamp,this.width].join(",");if(a!==l.textCache){l.textCache=a,delete l.actualWidth;for(let n=r.length;n--;)t.removeChild(r[n]);if(s||this.ellipsis||this.width||l.textPath||i.indexOf(" ")!==-1&&(!this.noWrap||/<br.*?>/g.test(i))){if(i!==""){o&&o.appendChild(t);let n=new it(i);this.modifyTree(n.nodes),n.addToDOM(t),this.modifyDOM(),this.ellipsis&&(t.textContent||"").indexOf("…")!==-1&&l.attr("title",this.unescapeEntities(l.textStr||"",["&lt;","&gt;"])),o&&o.removeChild(t)}}else t.appendChild(Cs.createTextNode(this.unescapeEntities(i)));dl(this.textOutline)&&l.applyTextOutline&&l.applyTextOutline(this.textOutline)}}modifyDOM(){let l,t=this.svgElement,e=Ps(t.element,"x");for(t.firstLineMetrics=void 0;(l=t.element.firstChild)&&/^[\s\u200B]*$/.test(l.textContent||" ");)t.element.removeChild(l);[].forEach.call(t.element.querySelectorAll("tspan.highcharts-br"),(o,a)=>{o.nextSibling&&o.previousSibling&&(a===0&&o.previousSibling.nodeType===1&&(t.firstLineMetrics=t.renderer.fontMetrics(o.previousSibling)),Ps(o,{dy:this.getLineHeight(o.nextSibling),x:e}))});let i=this.width||0;if(!i)return;let s=(o,a)=>{var m;let n=o.textContent||"",h=n.replace(/([^\^])-/g,"$1- ").split(" "),c=!this.noWrap&&(h.length>1||t.element.childNodes.length>1),d=this.getLineHeight(a),p=Math.max(0,i-.8*d),u=0,g=t.actualWidth;if(c){let x=[],f=[];for(;a.firstChild&&a.firstChild!==o;)f.push(a.firstChild),a.removeChild(a.firstChild);for(;h.length;)if(h.length&&!this.noWrap&&u>0&&(x.push(o.textContent||""),o.textContent=h.join(" ").replace(/- /g,"-")),this.truncate(o,void 0,h,u===0&&g||0,i,p,(b,y)=>h.slice(0,y).join(" ").replace(/- /g,"-")),g=t.actualWidth,u++,this.lineClamp&&u>=this.lineClamp){h.length&&(this.truncate(o,o.textContent||"",void 0,0,i,p,Ls),o.textContent=((m=o.textContent)==null?void 0:m.replace("…",""))+"…");break}f.forEach(b=>{a.insertBefore(b,o)}),x.forEach(b=>{a.insertBefore(Cs.createTextNode(b),o);let y=Cs.createElementNS(nl,"tspan");y.textContent="​",Ps(y,{dy:d,x:e}),a.insertBefore(y,o)})}else this.ellipsis&&n&&this.truncate(o,n,void 0,0,i,p,Ls)},r=o=>{[].slice.call(o.childNodes).forEach(a=>{a.nodeType===po.Node.TEXT_NODE?s(a,o):(a.className.baseVal.indexOf("highcharts-br")!==-1&&(t.actualWidth=0),r(a))})};r(t.element)}getLineHeight(l){let t=l.nodeType===po.Node.TEXT_NODE?l.parentElement:l;return this.textLineHeight?parseInt(this.textLineHeight.toString(),10):this.renderer.fontMetrics(t||this.svgElement.element).h}modifyTree(l){let t=(e,i)=>{let{attributes:s={},children:r,style:o={},tagName:a}=e,n=this.renderer.styledMode;if(a==="b"||a==="strong"?n?s.class="highcharts-strong":o.fontWeight="bold":(a==="i"||a==="em")&&(n?s.class="highcharts-emphasized":o.fontStyle="italic"),o&&o.color&&(o.fill=o.color),a==="br"){s.class="highcharts-br",e.textContent="​";let h=l[i+1];h&&h.textContent&&(h.textContent=h.textContent.replace(/^ +/gm,""))}else a==="a"&&r&&r.some(h=>h.tagName==="#text")&&(e.children=[{children:r,tagName:"tspan"}]);a!=="#text"&&a!=="a"&&(e.tagName="tspan"),ll(e,{attributes:s,style:o}),r&&r.filter(h=>h.tagName!=="#text").forEach(t)};l.forEach(t),hl(this.svgElement,"afterModifyTree",{nodes:l})}truncate(l,t,e,i,s,r,o){let a,n,h=this.svgElement,{rotation:c}=h,d=[],p=e&&!i?1:0,u=(t||e||"").length,g=u;e||(s=r);let m=function(x,f){let b=f||x,y=l.parentNode;if(y&&d[b]===void 0&&y.getSubStringLength)try{d[b]=i+y.getSubStringLength(0,e?b+1:b)}catch{}return d[b]};if(h.rotation=0,i+(n=m(l.textContent.length))>s){for(;p<=u;)g=Math.ceil((p+u)/2),e&&(a=o(e,g)),n=m(g,a&&a.length-1),p===u?p=u+1:n>s?u=g-1:p=g;u===0?l.textContent="":t&&u===t.length-1||(l.textContent=a||o(t||e,g)),this.ellipsis&&n>s&&this.truncate(l,l.textContent||"",void 0,0,s,r,Ls)}e&&e.splice(0,g),h.actualWidth=n,h.rotation=c}unescapeEntities(l,t){return cl(this.renderer.escapes,function(e,i){t&&t.indexOf(e)!==-1||(l=l.toString().replace(RegExp(e,"g"),i))}),l}},{defaultOptions:gl}=zt,{charts:fl,deg2rad:uo,doc:Ce,isFirefox:go,isMS:fo,isWebKit:ml,noop:xl,SVG_NS:yl,symbolSizes:Ze,win:Os}=A,{addEvent:Mi,attr:Si,createElement:bl,crisp:mo,css:Ti,defined:ge,destroyObjectProperties:vl,extend:fe,isArray:kl,isNumber:Ke,isObject:_e,isString:wl,merge:Es,pick:Ds,pInt:Ml,replaceNested:Sl,uniqueKey:Tl}=R;class Ai{constructor(t,e,i,s,r,o,a){let n,h,c=this.createElement("svg").attr({version:"1.1",class:"highcharts-root"}),d=c.element;a||c.css(this.getStyle(s||{})),t.appendChild(d),Si(t,"dir","ltr"),t.innerHTML.indexOf("xmlns")===-1&&Si(d,"xmlns",this.SVG_NS),this.box=d,this.boxWrapper=c,this.alignedObjects=[],this.url=this.getReferenceURL(),this.createElement("desc").add().element.appendChild(Ce.createTextNode("Created with Highcharts 12.1.2")),this.defs=this.createElement("defs").add(),this.allowHTML=o,this.forExport=r,this.styledMode=a,this.gradients={},this.cache={},this.cacheKeys=[],this.imgCount=0,this.rootFontSize=c.getStyle("font-size"),this.setSize(e,i,!1),go&&t.getBoundingClientRect&&((n=function(){Ti(t,{left:0,top:0}),h=t.getBoundingClientRect(),Ti(t,{left:Math.ceil(h.left)-h.left+"px",top:Math.ceil(h.top)-h.top+"px"})})(),this.unSubPixelFix=Mi(Os,"resize",n))}definition(t){return new it([t]).addToDOM(this.defs.element)}getReferenceURL(){if((go||ml)&&Ce.getElementsByTagName("base").length){if(!ge(Et)){let t=Tl(),e=new it([{tagName:"svg",attributes:{width:8,height:8},children:[{tagName:"defs",children:[{tagName:"clipPath",attributes:{id:t},children:[{tagName:"rect",attributes:{width:4,height:4}}]}]},{tagName:"rect",attributes:{id:"hitme",width:8,height:8,"clip-path":`url(#${t})`,fill:"rgba(0,0,0,0.001)"}}]}]).addToDOM(Ce.body);Ti(e,{position:"fixed",top:0,left:0,zIndex:9e5});let i=Ce.elementFromPoint(6,6);Et=(i&&i.id)==="hitme",Ce.body.removeChild(e)}if(Et)return Sl(Os.location.href.split("#")[0],[/<[^>]*>/g,""],[/([\('\)])/g,"\\$1"],[/ /g,"%20"])}return""}getStyle(t){return this.style=fe({fontFamily:'-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", sans-serif',fontSize:"1rem"},t),this.style}setStyle(t){this.boxWrapper.css(this.getStyle(t))}isHidden(){return!this.boxWrapper.getBBox().width}destroy(){let t=this.defs;return this.box=null,this.boxWrapper=this.boxWrapper.destroy(),vl(this.gradients||{}),this.gradients=null,this.defs=t.destroy(),this.unSubPixelFix&&this.unSubPixelFix(),this.alignedObjects=null,null}createElement(t){return new this.Element(this,t)}getRadialAttr(t,e){return{cx:t[0]-t[2]/2+(e.cx||0)*t[2],cy:t[1]-t[2]/2+(e.cy||0)*t[2],r:(e.r||0)*t[2]}}shadowDefinition(t){let e=[`highcharts-drop-shadow-${this.chartIndex}`,...Object.keys(t).map(s=>`${s}-${t[s]}`)].join("-").toLowerCase().replace(/[^a-z\d\-]/g,""),i=Es({color:"#000000",offsetX:1,offsetY:1,opacity:.15,width:5},t);return this.defs.element.querySelector(`#${e}`)||this.definition({tagName:"filter",attributes:{id:e,filterUnits:i.filterUnits},children:this.getShadowFilterContent(i)}),e}getShadowFilterContent(t){return[{tagName:"feDropShadow",attributes:{dx:t.offsetX,dy:t.offsetY,"flood-color":t.color,"flood-opacity":Math.min(5*t.opacity,1),stdDeviation:t.width/2}}]}buildText(t){new ul(t).buildSVG()}getContrast(t){let e=pt.parse(t).rgba.map(s=>{let r=s/255;return r<=.03928?r/12.92:Math.pow((r+.055)/1.055,2.4)}),i=.2126*e[0]+.7152*e[1]+.0722*e[2];return 1.05/(i+.05)>(i+.05)/.05?"#FFFFFF":"#000000"}button(t,e,i,s,r={},o,a,n,h,c){let d=this.label(t,e,i,h,void 0,void 0,c,void 0,"button"),p=this.styledMode,u=arguments,g=0;r=Es(gl.global.buttonTheme,r),p&&(delete r.fill,delete r.stroke,delete r["stroke-width"]);let m=r.states||{},x=r.style||{};delete r.states,delete r.style;let f=[it.filterUserAttributes(r)],b=[x];return p||["hover","select","disabled"].forEach((y,v)=>{f.push(Es(f[0],it.filterUserAttributes(u[v+5]||m[y]||{}))),b.push(f[v+1].style),delete f[v+1].style}),Mi(d.element,fo?"mouseover":"mouseenter",function(){g!==3&&d.setState(1)}),Mi(d.element,fo?"mouseout":"mouseleave",function(){g!==3&&d.setState(g)}),d.setState=(y=0)=>{if(y!==1&&(d.state=g=y),d.removeClass(/highcharts-button-(normal|hover|pressed|disabled)/).addClass("highcharts-button-"+["normal","hover","pressed","disabled"][y]),!p){d.attr(f[y]);let v=b[y];_e(v)&&d.css(v)}},d.attr(f[0]),!p&&(d.css(fe({cursor:"default"},x)),c&&d.text.css({pointerEvents:"none"})),d.on("touchstart",y=>y.stopPropagation()).on("click",function(y){g!==3&&s.call(d,y)})}crispLine(t,e){let[i,s]=t;return ge(i[1])&&i[1]===s[1]&&(i[1]=s[1]=mo(i[1],e)),ge(i[2])&&i[2]===s[2]&&(i[2]=s[2]=mo(i[2],e)),t}path(t){let e=this.styledMode?{}:{fill:"none"};return kl(t)?e.d=t:_e(t)&&fe(e,t),this.createElement("path").attr(e)}circle(t,e,i){let s=_e(t)?t:t===void 0?{}:{x:t,y:e,r:i},r=this.createElement("circle");return r.xSetter=r.ySetter=function(o,a,n){n.setAttribute("c"+a,o)},r.attr(s)}arc(t,e,i,s,r,o){let a;_e(t)?(e=(a=t).y,i=a.r,s=a.innerR,r=a.start,o=a.end,t=a.x):a={innerR:s,start:r,end:o};let n=this.symbol("arc",t,e,i,i,a);return n.r=i,n}rect(t,e,i,s,r,o){let a=_e(t)?t:t===void 0?{}:{x:t,y:e,r,width:Math.max(i||0,0),height:Math.max(s||0,0)},n=this.createElement("rect");return this.styledMode||(o!==void 0&&(a["stroke-width"]=o,fe(a,n.crisp(a))),a.fill="none"),n.rSetter=function(h,c,d){n.r=h,Si(d,{rx:h,ry:h})},n.rGetter=function(){return n.r||0},n.attr(a)}roundedRect(t){return this.symbol("roundedRect").attr(t)}setSize(t,e,i){this.width=t,this.height=e,this.boxWrapper.animate({width:t,height:e},{step:function(){this.attr({viewBox:"0 0 "+this.attr("width")+" "+this.attr("height")})},duration:Ds(i,!0)?void 0:0}),this.alignElements()}g(t){let e=this.createElement("g");return t?e.attr({class:"highcharts-"+t}):e}image(t,e,i,s,r,o){let a={preserveAspectRatio:"none"};Ke(e)&&(a.x=e),Ke(i)&&(a.y=i),Ke(s)&&(a.width=s),Ke(r)&&(a.height=r);let n=this.createElement("image").attr(a),h=function(c){n.attr({href:t}),o.call(n,c)};if(o){n.attr({href:"data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw=="});let c=new Os.Image;Mi(c,"load",h),c.src=t,c.complete&&h({})}else n.attr({href:t});return n}symbol(t,e,i,s,r,o){let a,n,h,c,d=this,p=/^url\((.*?)\)$/,u=p.test(t),g=!u&&(this.symbols[t]?t:"circle"),m=g&&this.symbols[g];if(m)typeof e=="number"&&(n=m.call(this.symbols,e||0,i||0,s||0,r||0,o)),a=this.path(n),d.styledMode||a.attr("fill","none"),fe(a,{symbolName:g||void 0,x:e,y:i,width:s,height:r}),o&&fe(a,o);else if(u){h=t.match(p)[1];let x=a=this.image(h);x.imgwidth=Ds(o&&o.width,Ze[h]&&Ze[h].width),x.imgheight=Ds(o&&o.height,Ze[h]&&Ze[h].height),c=f=>f.attr({width:f.width,height:f.height}),["width","height"].forEach(f=>{x[`${f}Setter`]=function(b,y){this[y]=b;let{alignByTranslate:v,element:w,width:k,height:S,imgwidth:M,imgheight:T}=this,P=y==="width"?M:T,L=1;o&&o.backgroundSize==="within"&&k&&S&&M&&T?(L=Math.min(k/M,S/T),Si(w,{width:Math.round(M*L),height:Math.round(T*L)})):w&&P&&w.setAttribute(y,P),!v&&M&&T&&this.translate(((k||0)-M*L)/2,((S||0)-T*L)/2)}}),ge(e)&&x.attr({x:e,y:i}),x.isImg=!0,x.symbolUrl=t,ge(x.imgwidth)&&ge(x.imgheight)?c(x):(x.attr({width:0,height:0}),bl("img",{onload:function(){let f=fl[d.chartIndex];this.width===0&&(Ti(this,{position:"absolute",top:"-999em"}),Ce.body.appendChild(this)),Ze[h]={width:this.width,height:this.height},x.imgwidth=this.width,x.imgheight=this.height,x.element&&c(x),this.parentNode&&this.parentNode.removeChild(this),d.imgCount--,d.imgCount||!f||f.hasLoaded||f.onload()},src:h}),this.imgCount++)}return a}clipRect(t,e,i,s){return this.rect(t,e,i,s,0)}text(t,e,i,s){let r={};if(s&&(this.allowHTML||!this.forExport))return this.html(t,e,i);r.x=Math.round(e||0),i&&(r.y=Math.round(i)),ge(t)&&(r.text=t);let o=this.createElement("text").attr(r);return s&&(!this.forExport||this.allowHTML)||(o.xSetter=function(a,n,h){let c=h.getElementsByTagName("tspan"),d=h.getAttribute(n);for(let p=0,u;p<c.length;p++)(u=c[p]).getAttribute(n)===d&&u.setAttribute(n,a);h.setAttribute(n,a)}),o}fontMetrics(t){let e=Ml(qt.prototype.getStyle.call(t,"font-size")||0),i=e<24?e+3:Math.round(1.2*e),s=Math.round(.8*i);return{h:i,b:s,f:e}}rotCorr(t,e,i){let s=t;return e&&i&&(s=Math.max(s*Math.cos(e*uo),4)),{x:-t/3*Math.sin(e*uo),y:s}}pathToSegments(t){let e=[],i=[],s={A:8,C:7,H:2,L:3,M:3,Q:5,S:5,T:3,V:2};for(let r=0;r<t.length;r++)wl(i[0])&&Ke(t[r])&&i.length===s[i[0].toUpperCase()]&&t.splice(r,0,i[0].replace("M","L").replace("m","l")),typeof t[r]=="string"&&(i.length&&e.push(i.slice(0)),i.length=0),i.push(t[r]);return e.push(i.slice(0)),e}label(t,e,i,s,r,o,a,n,h){return new ue(this,t,e,i,s,r,o,a,n,h)}alignElements(){this.alignedObjects.forEach(t=>t.align())}}fe(Ai.prototype,{Element:qt,SVG_NS:yl,escapes:{"&":"&amp;","<":"&lt;",">":"&gt;","'":"&#39;",'"':"&quot;"},symbols:co,draw:xl}),$e.registerRendererType("svg",Ai,!0);let{composed:Al}=A,{attr:Cl,css:Pe,createElement:Pl,defined:xo,extend:Ll,getAlignFactor:Ol,isNumber:El,pInt:Dl,pushUnique:Il}=R;function yo(l,t,e){var s;let i=((s=this.div)==null?void 0:s.style)||e.style;qt.prototype[`${t}Setter`].call(this,l,t,e),i&&(i[t]=l)}let Bl=(l,t)=>{var e;if(!l.div){let i=Cl(l.element,"class"),s=l.css,r=Pl("div",i?{className:i}:void 0,{position:"absolute",left:`${l.translateX||0}px`,top:`${l.translateY||0}px`,...l.styles,display:l.display,opacity:l.opacity,visibility:l.visibility},((e=l.parentGroup)==null?void 0:e.div)||t);l.classSetter=(o,a,n)=>{n.setAttribute("class",o),r.className=o},l.translateXSetter=l.translateYSetter=(o,a)=>{l[a]=o,r.style[a==="translateX"?"left":"top"]=`${o}px`,l.doTransform=!0},l.opacitySetter=l.visibilitySetter=yo,l.css=o=>(s.call(l,o),o.cursor&&(r.style.cursor=o.cursor),o.pointerEvents&&(r.style.pointerEvents=o.pointerEvents),l),l.on=function(){return qt.prototype.on.apply({element:r,onEvents:l.onEvents},arguments),l},l.div=r}return l.div};class Qe extends qt{static compose(t){Il(Al,this.compose)&&(t.prototype.html=function(e,i,s){return new Qe(this,"span").attr({text:e,x:Math.round(i),y:Math.round(s)})})}constructor(t,e){super(t,e),this.css({position:"absolute",...t.styledMode?{}:{fontFamily:t.style.fontFamily,fontSize:t.style.fontSize}})}getSpanCorrection(t,e,i){this.xCorr=-t*i,this.yCorr=-e}css(t){let e,{element:i}=this,s=i.tagName==="SPAN"&&t&&"width"in t,r=s&&t.width;return s&&(delete t.width,this.textWidth=Dl(r)||void 0,e=!0),(t==null?void 0:t.textOverflow)==="ellipsis"&&(t.overflow="hidden"),t!=null&&t.lineClamp&&(t.display="-webkit-box",t.WebkitLineClamp=t.lineClamp,t.WebkitBoxOrient="vertical",t.overflow="hidden"),El(Number(t==null?void 0:t.fontSize))&&(t.fontSize=t.fontSize+"px"),Ll(this.styles,t),Pe(i,t),e&&this.updateTransform(),this}htmlGetBBox(){let{element:t}=this;return{x:t.offsetLeft,y:t.offsetTop,width:t.offsetWidth,height:t.offsetHeight}}updateTransform(){var f;if(!this.added){this.alignOnAdd=!0;return}let{element:t,renderer:e,rotation:i,rotationOriginX:s,rotationOriginY:r,scaleX:o,scaleY:a,styles:n,textAlign:h="left",textWidth:c,translateX:d=0,translateY:p=0,x:u=0,y:g=0}=this,{display:m="block",whiteSpace:x}=n;if(Pe(t,{marginLeft:`${d}px`,marginTop:`${p}px`}),t.tagName==="SPAN"){let b,y=[i,h,t.innerHTML,c,this.textAlign].join(","),v=-(((f=this.parentGroup)==null?void 0:f.padding)*1)||0;if(c!==this.oldTextWidth){let M=this.textPxLength?this.textPxLength:(Pe(t,{width:"",whiteSpace:x||"nowrap"}),t.offsetWidth),T=c||0;(T>this.oldTextWidth||M>T)&&(/[ \-]/.test(t.textContent||t.innerText)||t.style.textOverflow==="ellipsis")&&(Pe(t,{width:M>T||i||o?c+"px":"auto",display:m,whiteSpace:x||"normal"}),this.oldTextWidth=c)}y!==this.cTT&&(b=e.fontMetrics(t).b,xo(i)&&(i!==(this.oldRotation||0)||h!==this.oldAlign)&&this.setSpanRotation(i,v,v),this.getSpanCorrection(!xo(i)&&!this.textWidth&&this.textPxLength||t.offsetWidth,b,Ol(h)));let{xCorr:w=0,yCorr:k=0}=this,S={left:`${u+w}px`,top:`${g+k}px`,textAlign:h,transformOrigin:`${(s??u)-w-u-v}px ${(r??g)-k-g-v}px`};(o||a)&&(S.transform=`scale(${o??1},${a??1})`),Pe(t,S),this.cTT=y,this.oldRotation=i,this.oldAlign=h}}setSpanRotation(t,e,i){Pe(this.element,{transform:`rotate(${t}deg)`,transformOrigin:`${e}% ${i}px`})}add(t){let e,i=this.renderer.box.parentNode,s=[];if(this.parentGroup=t,t&&!(e=t.div)){let r=t;for(;r;)s.push(r),r=r.parentGroup;for(let o of s.reverse())e=Bl(o,i)}return(e||i).appendChild(this.element),this.added=!0,this.alignOnAdd&&this.updateTransform(),this}textSetter(t){t!==this.textStr&&(delete this.bBox,delete this.oldTextWidth,it.setElementHTML(this.element,t??""),this.textStr=t,this.doTransform=!0)}alignSetter(t){this.alignValue=this.textAlign=t,this.doTransform=!0}xSetter(t,e){this[e]=t,this.doTransform=!0}}let me=Qe.prototype;me.visibilitySetter=me.opacitySetter=yo,me.ySetter=me.rotationSetter=me.rotationOriginXSetter=me.rotationOriginYSetter=me.xSetter,function(l){l.xAxis={alignTicks:!0,allowDecimals:void 0,panningEnabled:!0,zIndex:2,zoomEnabled:!0,dateTimeLabelFormats:{millisecond:{main:"%[HMSL]",range:!1},second:{main:"%[HMS]",range:!1},minute:{main:"%[HM]",range:!1},hour:{main:"%[HM]",range:!1},day:{main:"%[eb]"},week:{main:"%[eb]"},month:{main:"%[bY]"},year:{main:"%Y"}},endOnTick:!1,gridLineDashStyle:"Solid",gridZIndex:1,labels:{autoRotationLimit:80,distance:15,enabled:!0,indentation:10,overflow:"justify",reserveSpace:void 0,rotation:void 0,staggerLines:0,step:0,useHTML:!1,zIndex:7,style:{color:"#333333",cursor:"default",fontSize:"0.8em",textOverflow:"ellipsis"}},maxPadding:.01,minorGridLineDashStyle:"Solid",minorTickLength:2,minorTickPosition:"outside",minorTicksPerMajor:5,minPadding:.01,offset:void 0,reversed:void 0,reversedStacks:!1,showEmpty:!0,showFirstLabel:!0,showLastLabel:!0,startOfWeek:1,startOnTick:!1,tickLength:10,tickPixelInterval:100,tickmarkPlacement:"between",tickPosition:"outside",title:{align:"middle",useHTML:!1,x:0,y:0,style:{color:"#666666",fontSize:"0.8em"}},visible:!0,minorGridLineColor:"#f2f2f2",minorGridLineWidth:1,minorTickColor:"#999999",lineColor:"#333333",lineWidth:1,gridLineColor:"#e6e6e6",gridLineWidth:void 0,tickColor:"#333333"},l.yAxis={reversedStacks:!0,endOnTick:!0,maxPadding:.05,minPadding:.05,tickPixelInterval:72,showLastLabel:!0,labels:{x:void 0},startOnTick:!0,title:{text:"Values"},stackLabels:{animation:{},allowOverlap:!1,enabled:!1,crop:!0,overflow:"justify",formatter:function(){let{numberFormatter:t}=this.axis.chart;return t(this.total||0,-1)},style:{color:"#000000",fontSize:"0.7em",fontWeight:"bold",textOutline:"1px contrast"}},gridLineWidth:1,lineWidth:0}}(Z||(Z={}));let Nl=Z,{addEvent:zl,isFunction:Rl,objectEach:Wl,removeEvent:Hl}=R;(vt||(vt={})).registerEventOptions=function(l,t){l.eventOptions=l.eventOptions||{},Wl(t.events,function(e,i){l.eventOptions[i]!==e&&(l.eventOptions[i]&&(Hl(l,i,l.eventOptions[i]),delete l.eventOptions[i]),Rl(e)&&(l.eventOptions[i]=e,zl(l,i,e,{order:0})))})};let Ci=vt,{deg2rad:Is}=A,{clamp:Xl,correctFloat:Pi,defined:Bs,destroyObjectProperties:Fl,extend:bo,fireEvent:Je,getAlignFactor:Yl,isNumber:Li,merge:Gl,objectEach:jl,pick:jt}=R,Le=class{constructor(l,t,e,i,s){this.isNew=!0,this.isNewLabel=!0,this.axis=l,this.pos=t,this.type=e||"",this.parameters=s||{},this.tickmarkOffset=this.parameters.tickmarkOffset,this.options=this.parameters.options,Je(this,"init"),e||i||this.addLabel()}addLabel(){let l=this,t=l.axis,e=t.options,i=t.chart,s=t.categories,r=t.logarithmic,o=t.names,a=l.pos,n=jt(l.options&&l.options.labels,e.labels),h=t.tickPositions,c=a===h[0],d=a===h[h.length-1],p=(!n.step||n.step===1)&&t.tickInterval===1,u=h.info,g=l.label,m,x,f,b=this.parameters.category||(s?jt(s[a],o[a],a):a);r&&Li(b)&&(b=Pi(r.lin2log(b))),t.dateTime&&(u?m=(x=i.time.resolveDTLFormat(e.dateTimeLabelFormats[!e.grid&&u.higherRanks[a]||u.unitName])).main:Li(b)&&(m=t.dateTime.getXDateFormat(b,e.dateTimeLabelFormats||{}))),l.isFirst=c,l.isLast=d;let y={axis:t,chart:i,dateTimeLabelFormat:m,isFirst:c,isLast:d,pos:a,tick:l,tickPositionInfo:u,value:b};Je(this,"labelFormat",y);let v=S=>n.formatter?n.formatter.call(S,S):n.format?(S.text=t.defaultLabelFormatter.call(S),Yt.format(n.format,S,i)):t.defaultLabelFormatter.call(S),w=v.call(y,y),k=x&&x.list;k?l.shortenLabel=function(){for(f=0;f<k.length;f++)if(bo(y,{dateTimeLabelFormat:k[f]}),g.attr({text:v.call(y,y)}),g.getBBox().width<t.getSlotWidth(l)-2*(n.padding||0))return;g.attr({text:""})}:l.shortenLabel=void 0,p&&t._addedPlotLB&&l.moveLabel(w,n),Bs(g)||l.movedLabel?g&&g.textStr!==w&&!p&&(!g.textWidth||n.style.width||g.styles.width||g.css({width:null}),g.attr({text:w}),g.textPxLength=g.getBBox().width):(l.label=g=l.createLabel(w,n),l.rotation=0)}createLabel(l,t,e){let i=this.axis,{renderer:s,styledMode:r}=i.chart,o=Bs(l)&&t.enabled?s.text(l,e==null?void 0:e.x,e==null?void 0:e.y,t.useHTML).add(i.labelGroup):void 0;if(o){let a=t.style.whiteSpace||"normal";r||o.css(Gl(t.style,{whiteSpace:"nowrap"})),o.textPxLength=o.getBBox().width,r||o.css({whiteSpace:a})}return o}destroy(){Fl(this,this.axis)}getPosition(l,t,e,i){let s=this.axis,r=s.chart,o=i&&r.oldChartHeight||r.chartHeight,a={x:l?Pi(s.translate(t+e,void 0,void 0,i)+s.transB):s.left+s.offset+(s.opposite?(i&&r.oldChartWidth||r.chartWidth)-s.right-s.left:0),y:l?o-s.bottom+s.offset-(s.opposite?s.height:0):Pi(o-s.translate(t+e,void 0,void 0,i)-s.transB)};return a.y=Xl(a.y,-1e9,1e9),Je(this,"afterGetPosition",{pos:a}),a}getLabelPosition(l,t,e,i,s,r,o,a){let n,h,c=this.axis,d=c.transA,p=c.isLinked&&c.linkedParent?c.linkedParent.reversed:c.reversed,u=c.staggerLines,g=c.tickRotCorr||{x:0,y:0},m=i||c.reserveSpaceDefault?0:-c.labelOffset*(c.labelAlign==="center"?.5:1),x=s.distance,f={};return n=c.side===0?e.rotation?-x:-e.getBBox().height:c.side===2?g.y+x:Math.cos(e.rotation*Is)*(g.y-e.getBBox(!1,0).height/2),Bs(s.y)&&(n=c.side===0&&c.horiz?s.y+n:s.y),l=l+jt(s.x,[0,1,0,-1][c.side]*x)+m+g.x-(r&&i?r*d*(p?-1:1):0),t=t+n-(r&&!i?r*d*(p?1:-1):0),u&&(h=o/(a||1)%u,c.opposite&&(h=u-h-1),t+=h*(c.labelOffset/u)),f.x=l,f.y=Math.round(t),Je(this,"afterGetLabelPosition",{pos:f,tickmarkOffset:r,index:o}),f}getLabelSize(){return this.label?this.label.getBBox()[this.axis.horiz?"height":"width"]:0}getMarkPath(l,t,e,i,s=!1,r){return r.crispLine([["M",l,t],["L",l+(s?0:-e),t+(s?e:0)]],i)}handleOverflow(l){let t=this.axis,e=t.options.labels,i=l.x,s=t.chart.chartWidth,r=t.chart.spacing,o=jt(t.labelLeft,Math.min(t.pos,r[3])),a=jt(t.labelRight,Math.max(t.isRadial?0:t.pos+t.len,s-r[1])),n=this.label,h=this.rotation,c=Yl(t.labelAlign||n.attr("align")),d=n.getBBox().width,p=t.getSlotWidth(this),u=p,g=1,m,x,f;h||e.overflow!=="justify"?h<0&&i-c*d<o?f=Math.round(i/Math.cos(h*Is)-o):h>0&&i+c*d>a&&(f=Math.round((s-i)/Math.cos(h*Is))):(m=i-c*d,x=i+(1-c)*d,m<o?u=l.x+u*(1-c)-o:x>a&&(u=a-l.x+u*c,g=-1),(u=Math.min(p,u))<p&&t.labelAlign==="center"&&(l.x+=g*(p-u-c*(p-Math.min(d,u)))),(d>u||t.autoRotation&&(n.styles||{}).width)&&(f=u)),f&&n&&(this.shortenLabel?this.shortenLabel():n.css(bo({},{width:Math.floor(f)+"px",lineClamp:t.isRadial?0:1})))}moveLabel(l,t){let e=this,i=e.label,s=e.axis,r=!1,o;i&&i.textStr===l?(e.movedLabel=i,r=!0,delete e.label):jl(s.ticks,function(a){r||a.isNew||a===e||!a.label||a.label.textStr!==l||(e.movedLabel=a.label,r=!0,a.labelPos=e.movedLabel.xy,delete a.label)}),!r&&(e.labelPos||i)&&(o=e.labelPos||i.xy,e.movedLabel=e.createLabel(l,t,o),e.movedLabel&&e.movedLabel.attr({opacity:0}))}render(l,t,e){let i=this.axis,s=i.horiz,r=this.pos,o=jt(this.tickmarkOffset,i.tickmarkOffset),a=this.getPosition(s,r,o,t),n=a.x,h=a.y,c=i.pos,d=c+i.len,p=s?n:h;!i.chart.polar&&this.isNew&&(Pi(p)<c||p>d)&&(e=0);let u=jt(e,this.label&&this.label.newOpacity,1);e=jt(e,1),this.isActive=!0,this.renderGridLine(t,e),this.renderMark(a,e),this.renderLabel(a,t,u,l),this.isNew=!1,Je(this,"afterRender")}renderGridLine(l,t){let e=this.axis,i=e.options,s={},r=this.pos,o=this.type,a=jt(this.tickmarkOffset,e.tickmarkOffset),n=e.chart.renderer,h=this.gridLine,c,d=i.gridLineWidth,p=i.gridLineColor,u=i.gridLineDashStyle;this.type==="minor"&&(d=i.minorGridLineWidth,p=i.minorGridLineColor,u=i.minorGridLineDashStyle),h||(e.chart.styledMode||(s.stroke=p,s["stroke-width"]=d||0,s.dashstyle=u),o||(s.zIndex=1),l&&(t=0),this.gridLine=h=n.path().attr(s).addClass("highcharts-"+(o?o+"-":"")+"grid-line").add(e.gridGroup)),h&&(c=e.getPlotLinePath({value:r+a,lineWidth:h.strokeWidth(),force:"pass",old:l,acrossPanes:!1}))&&h[l||this.isNew?"attr":"animate"]({d:c,opacity:t})}renderMark(l,t){let e=this.axis,i=e.options,s=e.chart.renderer,r=this.type,o=e.tickSize(r?r+"Tick":"tick"),a=l.x,n=l.y,h=jt(i[r!=="minor"?"tickWidth":"minorTickWidth"],!r&&e.isXAxis?1:0),c=i[r!=="minor"?"tickColor":"minorTickColor"],d=this.mark,p=!d;o&&(e.opposite&&(o[0]=-o[0]),d||(this.mark=d=s.path().addClass("highcharts-"+(r?r+"-":"")+"tick").add(e.axisGroup),e.chart.styledMode||d.attr({stroke:c,"stroke-width":h})),d[p?"attr":"animate"]({d:this.getMarkPath(a,n,o[0],d.strokeWidth(),e.horiz,s),opacity:t}))}renderLabel(l,t,e,i){let s=this.axis,r=s.horiz,o=s.options,a=this.label,n=o.labels,h=n.step,c=jt(this.tickmarkOffset,s.tickmarkOffset),d=l.x,p=l.y,u=!0;a&&Li(d)&&(a.xy=l=this.getLabelPosition(d,p,a,r,n,c,i,h),(!this.isFirst||this.isLast||o.showFirstLabel)&&(!this.isLast||this.isFirst||o.showLastLabel)?!r||n.step||n.rotation||t||e===0||this.handleOverflow(l):u=!1,h&&i%h&&(u=!1),u&&Li(l.y)?(l.opacity=e,a[this.isNewLabel?"attr":"animate"](l).show(!0),this.isNewLabel=!1):(a.hide(),this.isNewLabel=!0))}replaceMovedLabel(){let l=this.label,t=this.axis;l&&!this.isNew&&(l.animate({opacity:0},void 0,l.destroy),delete this.label),t.isDirty=!0,this.label=this.movedLabel,delete this.movedLabel}},{animObject:$l}=St,{xAxis:vo,yAxis:Ul}=Nl,{defaultOptions:Ns}=zt,{registerEventOptions:Vl}=Ci,{deg2rad:ql}=A,{arrayMax:ko,arrayMin:Zl,clamp:zs,correctFloat:Bt,defined:Q,destroyObjectProperties:Kl,erase:wo,error:Rs,extend:Oi,fireEvent:at,getClosestDistance:Mo,insertItem:_l,isArray:So,isNumber:W,isString:To,merge:Ei,normalizeTickInterval:Ql,objectEach:Di,pick:Y,relativeLength:Ii,removeEvent:Jl,splat:th,syncTimeout:eh}=R,Ao=(l,t)=>Ql(t,void 0,void 0,Y(l.options.allowDecimals,t<.5||l.tickAmount!==void 0),!!l.tickAmount);Oi(Ns,{xAxis:vo,yAxis:Ei(vo,Ul)});class Oe{constructor(t,e,i){this.init(t,e,i)}init(t,e,i=this.coll){let s=i==="xAxis",r=this.isZAxis||(t.inverted?!s:s);this.chart=t,this.horiz=r,this.isXAxis=s,this.coll=i,at(this,"init",{userOptions:e}),this.opposite=Y(e.opposite,this.opposite),this.side=Y(e.side,this.side,r?this.opposite?0:2:this.opposite?1:3),this.setOptions(e);let o=this.options,a=o.labels;this.type??(this.type=o.type||"linear"),this.uniqueNames??(this.uniqueNames=o.uniqueNames??!0),at(this,"afterSetType"),this.userOptions=e,this.minPixelPadding=0,this.reversed=Y(o.reversed,this.reversed),this.visible=o.visible,this.zoomEnabled=o.zoomEnabled,this.hasNames=this.type==="category"||o.categories===!0,this.categories=So(o.categories)&&o.categories||(this.hasNames?[]:void 0),this.names||(this.names=[],this.names.keys={}),this.plotLinesAndBandsGroups={},this.positiveValuesOnly=!!this.logarithmic,this.isLinked=Q(o.linkedTo),this.ticks={},this.labelEdge=[],this.minorTicks={},this.plotLinesAndBands=[],this.alternateBands={},this.len??(this.len=0),this.minRange=this.userMinRange=o.minRange||o.maxZoom,this.range=o.range,this.offset=o.offset||0,this.max=void 0,this.min=void 0;let n=Y(o.crosshair,th(t.options.tooltip.crosshairs)[s?0:1]);this.crosshair=n===!0?{}:n,t.axes.indexOf(this)===-1&&(s?t.axes.splice(t.xAxis.length,0,this):t.axes.push(this),_l(this,t[this.coll])),t.orderItems(this.coll),this.series=this.series||[],t.inverted&&!this.isZAxis&&s&&!Q(this.reversed)&&(this.reversed=!0),this.labelRotation=W(a.rotation)?a.rotation:void 0,Vl(this,o),at(this,"afterInit")}setOptions(t){let e=this.horiz?{labels:{autoRotation:[-45],padding:3},margin:15}:{labels:{padding:1},title:{rotation:90*this.side}};this.options=Ei(e,Ns[this.coll],t),at(this,"afterSetOptions",{userOptions:t})}defaultLabelFormatter(){let t=this.axis,{numberFormatter:e}=this.chart,i=W(this.value)?this.value:NaN,s=t.chart.time,r=t.categories,o=this.dateTimeLabelFormat,a=Ns.lang,n=a.numericSymbols,h=a.numericSymbolMagnitude||1e3,c=t.logarithmic?Math.abs(i):t.tickInterval,d=n&&n.length,p,u;if(r)u=`${this.value}`;else if(o)u=s.dateFormat(o,i,!0);else if(d&&n&&c>=1e3)for(;d--&&u===void 0;)c>=(p=Math.pow(h,d+1))&&10*i%p==0&&n[d]!==null&&i!==0&&(u=e(i/p,-1)+n[d]);return u===void 0&&(u=Math.abs(i)>=1e4?e(i,-1):e(i,-1,void 0,"")),u}getSeriesExtremes(){let t,e=this;at(this,"getSeriesExtremes",null,function(){e.hasVisibleSeries=!1,e.dataMin=e.dataMax=e.threshold=void 0,e.softThreshold=!e.isXAxis,e.series.forEach(i=>{if(i.reserveSpace()){let s=i.options,r,o=s.threshold,a,n;if(e.hasVisibleSeries=!0,e.positiveValuesOnly&&0>=(o||0)&&(o=void 0),e.isXAxis)(r=i.getColumn("x")).length&&(r=e.logarithmic?r.filter(h=>h>0):r,a=(t=i.getXExtremes(r)).min,n=t.max,W(a)||a instanceof Date||(r=r.filter(W),a=(t=i.getXExtremes(r)).min,n=t.max),r.length&&(e.dataMin=Math.min(Y(e.dataMin,a),a),e.dataMax=Math.max(Y(e.dataMax,n),n)));else{let h=i.applyExtremes();W(h.dataMin)&&(a=h.dataMin,e.dataMin=Math.min(Y(e.dataMin,a),a)),W(h.dataMax)&&(n=h.dataMax,e.dataMax=Math.max(Y(e.dataMax,n),n)),Q(o)&&(e.threshold=o),(!s.softThreshold||e.positiveValuesOnly)&&(e.softThreshold=!1)}}})}),at(this,"afterGetSeriesExtremes")}translate(t,e,i,s,r,o){var m;let a=this.linkedParent||this,n=s&&a.old?a.old.min:a.min;if(!W(n))return NaN;let h=a.minPixelPadding,c=(a.isOrdinal||((m=a.brokenAxis)==null?void 0:m.hasBreaks)||a.logarithmic&&r)&&a.lin2val,d=1,p=0,u=s&&a.old?a.old.transA:a.transA,g=0;return u||(u=a.transA),i&&(d*=-1,p=a.len),a.reversed&&(d*=-1,p-=d*(a.sector||a.len)),e?(g=(t=t*d+p-h)/u+n,c&&(g=a.lin2val(g))):(c&&(t=a.val2lin(t)),g=d*(t-n)*u+p+d*h+(W(o)?u*o:0),a.isRadial||(g=Bt(g))),g}toPixels(t,e){var i;return this.translate(((i=this.chart)==null?void 0:i.time.parse(t))??NaN,!1,!this.horiz,void 0,!0)+(e?0:this.pos)}toValue(t,e){return this.translate(t-(e?0:this.pos),!0,!this.horiz,void 0,!0)}getPlotLinePath(t){let e=this,i=e.chart,s=e.left,r=e.top,o=t.old,a=t.value,n=t.lineWidth,h=o&&i.oldChartHeight||i.chartHeight,c=o&&i.oldChartWidth||i.chartWidth,d=e.transB,p=t.translatedValue,u=t.force,g,m,x,f,b;function y(w,k,S){return u!=="pass"&&(w<k||w>S)&&(u?w=zs(w,k,S):b=!0),w}let v={value:a,lineWidth:n,old:o,force:u,acrossPanes:t.acrossPanes,translatedValue:p};return at(this,"getPlotLinePath",v,function(w){g=x=(p=zs(p=Y(p,e.translate(a,void 0,void 0,o)),-1e9,1e9))+d,m=f=h-p-d,W(p)?e.horiz?(m=r,f=h-e.bottom+(e.options.isInternal?0:i.scrollablePixelsY||0),g=x=y(g,s,s+e.width)):(g=s,x=c-e.right+(i.scrollablePixelsX||0),m=f=y(m,r,r+e.height)):(b=!0,u=!1),w.path=b&&!u?void 0:i.renderer.crispLine([["M",g,m],["L",x,f]],n||1)}),v.path}getLinearTickPositions(t,e,i){let s,r,o,a=Bt(Math.floor(e/t)*t),n=Bt(Math.ceil(i/t)*t),h=[];if(Bt(a+t)===a&&(o=20),this.single)return[e];for(s=a;s<=n&&(h.push(s),(s=Bt(s+t,o))!==r);)r=s;return h}getMinorTickInterval(){let{minorTicks:t,minorTickInterval:e}=this.options;return t===!0?Y(e,"auto"):t!==!1?e:void 0}getMinorTickPositions(){var c;let t=this.options,e=this.tickPositions,i=this.minorTickInterval,s=this.pointRangePadding||0,r=(this.min||0)-s,o=(this.max||0)+s,a=(c=this.brokenAxis)!=null&&c.hasBreaks?this.brokenAxis.unitLength:o-r,n=[],h;if(a&&a/i<this.len/3){let d=this.logarithmic;if(d)this.paddedTicks.forEach(function(p,u,g){u&&n.push.apply(n,d.getLogTickPositions(i,g[u-1],g[u],!0))});else if(this.dateTime&&this.getMinorTickInterval()==="auto")n=n.concat(this.getTimeTicks(this.dateTime.normalizeTimeTickInterval(i),r,o,t.startOfWeek));else for(h=r+(e[0]-r)%i;h<=o&&h!==n[0];h+=i)n.push(h)}return n.length!==0&&this.trimTicks(n),n}adjustForMinRange(){let t=this.options,e=this.logarithmic,i=this.chart.time,{max:s,min:r,minRange:o}=this,a,n,h,c;this.isXAxis&&o===void 0&&!e&&(o=Q(t.min)||Q(t.max)||Q(t.floor)||Q(t.ceiling)?null:Math.min(5*(Mo(this.series.map(d=>{let p=d.getColumn("x");return d.xIncrement?p.slice(0,2):p}))||0),this.dataMax-this.dataMin)),W(s)&&W(r)&&W(o)&&s-r<o&&(n=this.dataMax-this.dataMin>=o,a=(o-s+r)/2,h=[r-a,i.parse(t.min)??r-a],n&&(h[2]=e?e.log2lin(this.dataMin):this.dataMin),c=[(r=ko(h))+o,i.parse(t.max)??r+o],n&&(c[2]=e?e.log2lin(this.dataMax):this.dataMax),(s=Zl(c))-r<o&&(h[0]=s-o,h[1]=i.parse(t.min)??s-o,r=ko(h))),this.minRange=o,this.min=r,this.max=s}getClosest(){let t,e;if(this.categories)e=1;else{let i=[];this.series.forEach(function(s){let r=s.closestPointRange,o=s.getColumn("x");o.length===1?i.push(o[0]):s.sorted&&Q(r)&&s.reserveSpace()&&(e=Q(e)?Math.min(e,r):r)}),i.length&&(i.sort((s,r)=>s-r),t=Mo([i]))}return t&&e?Math.min(t,e):t||e}nameToX(t){let e=So(this.options.categories),i=e?this.categories:this.names,s=t.options.x,r;return t.series.requireSorting=!1,Q(s)||(s=this.uniqueNames&&i?e?i.indexOf(t.name):Y(i.keys[t.name],-1):t.series.autoIncrement()),s===-1?!e&&i&&(r=i.length):W(s)&&(r=s),r!==void 0?(this.names[r]=t.name,this.names.keys[t.name]=r):t.x&&(r=t.x),r}updateNames(){let t=this,e=this.names;e.length>0&&(Object.keys(e.keys).forEach(function(i){delete e.keys[i]}),e.length=0,this.minRange=this.userMinRange,(this.series||[]).forEach(i=>{i.xIncrement=null,(!i.points||i.isDirtyData)&&(t.max=Math.max(t.max||0,i.dataTable.rowCount-1),i.processData(),i.generatePoints());let s=i.getColumn("x").slice();i.data.forEach((r,o)=>{let a=s[o];r!=null&&r.options&&r.name!==void 0&&(a=t.nameToX(r))!==void 0&&a!==r.x&&(s[o]=r.x=a)}),i.dataTable.setColumn("x",s)}))}setAxisTranslation(){let t=this,e=t.max-t.min,i=t.linkedParent,s=!!t.categories,r=t.isXAxis,o=t.axisPointRange||0,a,n=0,h=0,c,d=t.transA;(r||s||o)&&(a=t.getClosest(),i?(n=i.minPointOffset,h=i.pointRangePadding):t.series.forEach(function(p){let u=s?1:r?Y(p.options.pointRange,a,0):t.axisPointRange||0,g=p.options.pointPlacement;if(o=Math.max(o,u),!t.single||s){let m=p.is("xrange")?!r:r;n=Math.max(n,m&&To(g)?0:u/2),h=Math.max(h,m&&g==="on"?0:u)}}),c=t.ordinal&&t.ordinal.slope&&a?t.ordinal.slope/a:1,t.minPointOffset=n*=c,t.pointRangePadding=h*=c,t.pointRange=Math.min(o,t.single&&s?1:e),r&&a&&(t.closestPointRange=a)),t.translationSlope=t.transA=d=t.staticScale||t.len/(e+h||1),t.transB=t.horiz?t.left:t.bottom,t.minPixelPadding=d*n,at(this,"afterSetAxisTranslation")}minFromRange(){let{max:t,min:e}=this;return W(t)&&W(e)&&t-e||void 0}setTickInterval(t){var N,F,I,X;let{categories:e,chart:i,dataMax:s,dataMin:r,dateTime:o,isXAxis:a,logarithmic:n,options:h,softThreshold:c}=this,d=i.time,p=W(this.threshold)?this.threshold:void 0,u=this.minRange||0,{ceiling:g,floor:m,linkedTo:x,softMax:f,softMin:b}=h,y=W(x)&&((N=i[this.coll])==null?void 0:N[x]),v=h.tickPixelInterval,w=h.maxPadding,k=h.minPadding,S=0,M,T=W(h.tickInterval)&&h.tickInterval>=0?h.tickInterval:void 0,P,L,E,C;if(o||e||y||this.getTickAmount(),E=Y(this.userMin,d.parse(h.min)),C=Y(this.userMax,d.parse(h.max)),y?(this.linkedParent=y,M=y.getExtremes(),this.min=Y(M.min,M.dataMin),this.max=Y(M.max,M.dataMax),this.type!==y.type&&Rs(11,!0,i)):(c&&Q(p)&&W(s)&&W(r)&&(r>=p?(P=p,k=0):s<=p&&(L=p,w=0)),this.min=Y(E,P,r),this.max=Y(C,L,s)),W(this.max)&&W(this.min)&&(n&&(this.positiveValuesOnly&&!t&&0>=Math.min(this.min,Y(r,this.min))&&Rs(10,!0,i),this.min=Bt(n.log2lin(this.min),16),this.max=Bt(n.log2lin(this.max),16)),this.range&&W(r)&&(this.userMin=this.min=E=Math.max(r,this.minFromRange()||0),this.userMax=C=this.max,this.range=void 0)),at(this,"foundExtremes"),this.adjustForMinRange(),W(this.min)&&W(this.max)){if(!W(this.userMin)&&W(b)&&b<this.min&&(this.min=E=b),!W(this.userMax)&&W(f)&&f>this.max&&(this.max=C=f),e||this.axisPointRange||(F=this.stacking)!=null&&F.usePercentage||y||!(S=this.max-this.min)||(!Q(E)&&k&&(this.min-=S*k),Q(C)||!w||(this.max+=S*w)),!W(this.userMin)&&W(m)&&(this.min=Math.max(this.min,m)),!W(this.userMax)&&W(g)&&(this.max=Math.min(this.max,g)),c&&W(r)&&W(s)){let D=p||0;!Q(E)&&this.min<D&&r>=D?this.min=h.minRange?Math.min(D,this.max-u):D:!Q(C)&&this.max>D&&s<=D&&(this.max=h.minRange?Math.max(D,this.min+u):D)}!i.polar&&this.min>this.max&&(Q(h.min)?this.max=this.min:Q(h.max)&&(this.min=this.max)),S=this.max-this.min}if(this.min!==this.max&&W(this.min)&&W(this.max)?y&&!T&&v===y.options.tickPixelInterval?this.tickInterval=T=y.tickInterval:this.tickInterval=Y(T,this.tickAmount?S/Math.max(this.tickAmount-1,1):void 0,e?1:S*v/Math.max(this.len,v)):this.tickInterval=1,a&&!t){let D=this.min!==((I=this.old)==null?void 0:I.min)||this.max!==((X=this.old)==null?void 0:X.max);this.series.forEach(function(z){var H;z.forceCrop=(H=z.forceCropping)==null?void 0:H.call(z),z.processData(D)}),at(this,"postProcessData",{hasExtremesChanged:D})}this.setAxisTranslation(),at(this,"initialAxisTranslation"),this.pointRange&&!T&&(this.tickInterval=Math.max(this.pointRange,this.tickInterval));let O=Y(h.minTickInterval,o&&!this.series.some(D=>!D.sorted)?this.closestPointRange:0);!T&&this.tickInterval<O&&(this.tickInterval=O),o||n||T||(this.tickInterval=Ao(this,this.tickInterval)),this.tickAmount||(this.tickInterval=this.unsquish()),this.setTickPositions()}setTickPositions(){var c,d;let t=this.options,e=t.tickPositions,i=t.tickPositioner,s=this.getMinorTickInterval(),r=!this.isPanning,o=r&&t.startOnTick,a=r&&t.endOnTick,n=[],h;if(this.tickmarkOffset=this.categories&&t.tickmarkPlacement==="between"&&this.tickInterval===1?.5:0,this.single=this.min===this.max&&Q(this.min)&&!this.tickAmount&&(this.min%1==0||t.allowDecimals!==!1),e)n=e.slice();else if(W(this.min)&&W(this.max)){if(!((c=this.ordinal)!=null&&c.positions)&&(this.max-this.min)/this.tickInterval>Math.max(2*this.len,200))n=[this.min,this.max],Rs(19,!1,this.chart);else if(this.dateTime)n=this.getTimeTicks(this.dateTime.normalizeTimeTickInterval(this.tickInterval,t.units),this.min,this.max,t.startOfWeek,(d=this.ordinal)==null?void 0:d.positions,this.closestPointRange,!0);else if(this.logarithmic)n=this.logarithmic.getLogTickPositions(this.tickInterval,this.min,this.max);else{let p=this.tickInterval,u=p;for(;u<=2*p&&(n=this.getLinearTickPositions(this.tickInterval,this.min,this.max),this.tickAmount&&n.length>this.tickAmount);)this.tickInterval=Ao(this,u*=1.1)}n.length>this.len&&(n=[n[0],n[n.length-1]])[0]===n[1]&&(n.length=1),i&&(this.tickPositions=n,(h=i.apply(this,[this.min,this.max]))&&(n=h))}this.tickPositions=n,this.minorTickInterval=s==="auto"&&this.tickInterval?this.tickInterval/t.minorTicksPerMajor:s,this.paddedTicks=n.slice(0),this.trimTicks(n,o,a),!this.isLinked&&W(this.min)&&W(this.max)&&(this.single&&n.length<2&&!this.categories&&!this.series.some(p=>p.is("heatmap")&&p.options.pointPlacement==="between")&&(this.min-=.5,this.max+=.5),e||h||this.adjustTickAmount()),at(this,"afterSetTickPositions")}trimTicks(t,e,i){let s=t[0],r=t[t.length-1],o=!this.isOrdinal&&this.minPointOffset||0;if(at(this,"trimTicks"),!this.isLinked){if(e&&s!==-1/0)this.min=s;else for(;this.min-o>t[0];)t.shift();if(i)this.max=r;else for(;this.max+o<t[t.length-1];)t.pop();t.length===0&&Q(s)&&!this.options.tickPositions&&t.push((r+s)/2)}}alignToOthers(){let t,e=this,i=e.chart,s=[this],r=e.options,o=i.options.chart,a=this.coll==="yAxis"&&o.alignThresholds,n=[];if(e.thresholdAlignment=void 0,(o.alignTicks!==!1&&r.alignTicks||a)&&r.startOnTick!==!1&&r.endOnTick!==!1&&!e.logarithmic){let h=d=>{let{horiz:p,options:u}=d;return[p?u.left:u.top,u.width,u.height,u.pane].join(",")},c=h(this);i[this.coll].forEach(function(d){let{series:p}=d;p.length&&p.some(u=>u.visible)&&d!==e&&h(d)===c&&(t=!0,s.push(d))})}if(t&&a){s.forEach(c=>{let d=c.getThresholdAlignment(e);W(d)&&n.push(d)});let h=n.length>1?n.reduce((c,d)=>c+=d,0)/n.length:void 0;s.forEach(c=>{c.thresholdAlignment=h})}return t}getThresholdAlignment(t){if((!W(this.dataMin)||this!==t&&this.series.some(e=>e.isDirty||e.isDirtyData))&&this.getSeriesExtremes(),W(this.threshold)){let e=zs((this.threshold-(this.dataMin||0))/((this.dataMax||0)-(this.dataMin||0)),0,1);return this.options.reversed&&(e=1-e),e}}getTickAmount(){let t=this.options,e=t.tickPixelInterval,i=t.tickAmount;Q(t.tickInterval)||i||!(this.len<e)||this.isRadial||this.logarithmic||!t.startOnTick||!t.endOnTick||(i=2),!i&&this.alignToOthers()&&(i=Math.ceil(this.len/e)+1),i<4&&(this.finalTickAmt=i,i=5),this.tickAmount=i}adjustTickAmount(){let t=this,{finalTickAmt:e,max:i,min:s,options:r,tickPositions:o,tickAmount:a,thresholdAlignment:n}=t,h=o==null?void 0:o.length,c=Y(t.threshold,t.softThreshold?0:null),d,p,u=t.tickInterval,g,m=()=>o.push(Bt(o[o.length-1]+u)),x=()=>o.unshift(Bt(o[0]-u));if(W(n)&&(g=n<.5?Math.ceil(n*(a-1)):Math.floor(n*(a-1)),r.reversed&&(g=a-1-g)),t.hasData()&&W(s)&&W(i)){let f=()=>{t.transA*=(h-1)/(a-1),t.min=r.startOnTick?o[0]:Math.min(s,o[0]),t.max=r.endOnTick?o[o.length-1]:Math.max(i,o[o.length-1])};if(W(g)&&W(t.threshold)){for(;o[g]!==c||o.length!==a||o[0]>s||o[o.length-1]<i;){for(o.length=0,o.push(t.threshold);o.length<a;)o[g]===void 0||o[g]>t.threshold?x():m();if(u>8*t.tickInterval)break;u*=2}f()}else if(h<a){for(;o.length<a;)o.length%2||s===c?m():x();f()}if(Q(e)){for(p=d=o.length;p--;)(e===3&&p%2==1||e<=2&&p>0&&p<d-1)&&o.splice(p,1);t.finalTickAmt=void 0}}}setScale(){var o,a;let{coll:t,stacking:e}=this,i=!1,s=!1;this.series.forEach(n=>{i=i||n.isDirtyData||n.isDirty,s=s||n.xAxis&&n.xAxis.isDirty||!1}),this.setAxisSize();let r=this.len!==(this.old&&this.old.len);r||i||s||this.isLinked||this.forceRedraw||this.userMin!==(this.old&&this.old.userMin)||this.userMax!==(this.old&&this.old.userMax)||this.alignToOthers()?(e&&t==="yAxis"&&e.buildStacks(),this.forceRedraw=!1,this.userMinRange||(this.minRange=void 0),this.getSeriesExtremes(),this.setTickInterval(),e&&t==="xAxis"&&e.buildStacks(),this.isDirty||(this.isDirty=r||this.min!==((o=this.old)==null?void 0:o.min)||this.max!==((a=this.old)==null?void 0:a.max))):e&&e.cleanStacks(),i&&delete this.allExtremes,at(this,"afterSetScale")}setExtremes(t,e,i=!0,s,r){let o=this.chart;this.series.forEach(a=>{delete a.kdTree}),t=o.time.parse(t),e=o.time.parse(e),at(this,"setExtremes",r=Oi(r,{min:t,max:e}),a=>{this.userMin=a.min,this.userMax=a.max,this.eventArgs=a,i&&o.redraw(s)})}setAxisSize(){let t=this.chart,e=this.options,i=e.offsets||[0,0,0,0],s=this.horiz,r=this.width=Math.round(Ii(Y(e.width,t.plotWidth-i[3]+i[1]),t.plotWidth)),o=this.height=Math.round(Ii(Y(e.height,t.plotHeight-i[0]+i[2]),t.plotHeight)),a=this.top=Math.round(Ii(Y(e.top,t.plotTop+i[0]),t.plotHeight,t.plotTop)),n=this.left=Math.round(Ii(Y(e.left,t.plotLeft+i[3]),t.plotWidth,t.plotLeft));this.bottom=t.chartHeight-o-a,this.right=t.chartWidth-r-n,this.len=Math.max(s?r:o,0),this.pos=s?n:a}getExtremes(){let t=this.logarithmic;return{min:t?Bt(t.lin2log(this.min)):this.min,max:t?Bt(t.lin2log(this.max)):this.max,dataMin:this.dataMin,dataMax:this.dataMax,userMin:this.userMin,userMax:this.userMax}}getThreshold(t){let e=this.logarithmic,i=e?e.lin2log(this.min):this.min,s=e?e.lin2log(this.max):this.max;return t===null||t===-1/0?t=i:t===1/0?t=s:i>t?t=i:s<t&&(t=s),this.translate(t,0,1,0,1)}autoLabelAlign(t){let e=(Y(t,0)-90*this.side+720)%360,i={align:"center"};return at(this,"autoLabelAlign",i,function(s){e>15&&e<165?s.align="right":e>195&&e<345&&(s.align="left")}),i.align}tickSize(t){let e=this.options,i=Y(e[t==="tick"?"tickWidth":"minorTickWidth"],t==="tick"&&this.isXAxis&&!this.categories?1:0),s=e[t==="tick"?"tickLength":"minorTickLength"],r;i&&s&&(e[t+"Position"]==="inside"&&(s=-s),r=[s,i]);let o={tickSize:r};return at(this,"afterTickSize",o),o.tickSize}labelMetrics(){let t=this.chart.renderer,e=this.ticks,i=e[Object.keys(e)[0]]||{};return this.chart.renderer.fontMetrics(i.label||i.movedLabel||t.box)}unsquish(){let t=this.options.labels,e=t.padding||0,i=this.horiz,s=this.tickInterval,r=this.len/(((this.categories?1:0)+this.max-this.min)/s),o=t.rotation,a=Bt(.8*this.labelMetrics().h),n=Math.max(this.max-this.min,0),h=function(g){let m=(g+2*e)/(r||1);return(m=m>1?Math.ceil(m):1)*s>n&&g!==1/0&&r!==1/0&&n&&(m=Math.ceil(n/s)),Bt(m*s)},c=s,d,p=Number.MAX_VALUE,u;if(i){if(!t.staggerLines&&(W(o)?u=[o]:r<t.autoRotationLimit&&(u=t.autoRotation)),u){let g,m;for(let x of u)(x===o||x&&x>=-90&&x<=90)&&(m=(g=h(Math.abs(a/Math.sin(ql*x))))+Math.abs(x/360))<p&&(p=m,d=x,c=g)}}else c=h(.75*a);return this.autoRotation=u,this.labelRotation=Y(d,W(o)?o:0),t.step?s:c}getSlotWidth(t){let e=this.chart,i=this.horiz,s=this.options.labels,r=Math.max(this.tickPositions.length-(this.categories?0:1),1),o=e.margin[3];if(t&&W(t.slotWidth))return t.slotWidth;if(i&&s.step<2&&!this.isRadial)return s.rotation?0:(this.staggerLines||1)*this.len/r;if(!i){let a=s.style.width;if(a!==void 0)return parseInt(String(a),10);if(o)return o-e.spacing[3]}return .33*e.chartWidth}renderUnsquish(){let t=this.chart,e=t.renderer,i=this.tickPositions,s=this.ticks,r=this.options.labels,o=r.style,a=this.horiz,n=this.getSlotWidth(),h=Math.max(1,Math.round(n-(a?2*(r.padding||0):r.distance||0))),c={},d=this.labelMetrics(),p=o.lineClamp,u,g=p??(Math.floor(this.len/(i.length*d.h))||1),m=0;To(r.rotation)||(c.rotation=r.rotation||0),i.forEach(function(x){var y;let f=s[x];f.movedLabel&&f.replaceMovedLabel();let b=((y=f.label)==null?void 0:y.textPxLength)||0;b>m&&(m=b)}),this.maxLabelLength=m,this.autoRotation?m>h&&m>d.h?c.rotation=this.labelRotation:this.labelRotation=0:n&&(u=h),c.rotation&&(u=m>.5*t.chartHeight?.33*t.chartHeight:m,p||(g=1)),this.labelAlign=r.align||this.autoLabelAlign(this.labelRotation),this.labelAlign&&(c.align=this.labelAlign),i.forEach(function(x){let f=s[x],b=f&&f.label,y=o.width,v={};b&&(b.attr(c),f.shortenLabel?f.shortenLabel():u&&!y&&o.whiteSpace!=="nowrap"&&(u<(b.textPxLength||0)||b.element.tagName==="SPAN")?b.css(Oi(v,{width:`${u}px`,lineClamp:g})):!b.styles.width||v.width||y||b.css({width:"auto"}),f.rotation=c.rotation)},this),this.tickRotCorr=e.rotCorr(d.b,this.labelRotation||0,this.side!==0)}hasData(){return this.series.some(function(t){return t.hasData()})||this.options.showEmpty&&Q(this.min)&&Q(this.max)}addTitle(t){let e,i=this.chart.renderer,s=this.horiz,r=this.opposite,o=this.options.title,a=this.chart.styledMode;this.axisTitle||((e=o.textAlign)||(e=(s?{low:"left",middle:"center",high:"right"}:{low:r?"right":"left",middle:"center",high:r?"left":"right"})[o.align]),this.axisTitle=i.text(o.text||"",0,0,o.useHTML).attr({zIndex:7,rotation:o.rotation||0,align:e}).addClass("highcharts-axis-title"),a||this.axisTitle.css(Ei(o.style)),this.axisTitle.add(this.axisGroup),this.axisTitle.isNew=!0),a||o.style.width||this.isRadial||this.axisTitle.css({width:this.len+"px"}),this.axisTitle[t?"show":"hide"](t)}generateTick(t){let e=this.ticks;e[t]?e[t].addLabel():e[t]=new Le(this,t)}createGroups(){let{axisParent:t,chart:e,coll:i,options:s}=this,r=e.renderer,o=(a,n,h)=>r.g(a).attr({zIndex:h}).addClass(`highcharts-${i.toLowerCase()}${n} `+(this.isRadial?`highcharts-radial-axis${n} `:"")+(s.className||"")).add(t);this.axisGroup||(this.gridGroup=o("grid","-grid",s.gridZIndex),this.axisGroup=o("axis","",s.zIndex),this.labelGroup=o("axis-labels","-labels",s.labels.zIndex))}getOffset(){let t=this,{chart:e,horiz:i,options:s,side:r,ticks:o,tickPositions:a,coll:n}=t,h=e.inverted&&!t.isZAxis?[1,0,3,2][r]:r,c=t.hasData(),d=s.title,p=s.labels,u=W(s.crossing),g=e.axisOffset,m=e.clipOffset,x=[-1,1,1,-1][r],f,b=0,y,v=0,w=0,k,S;if(t.showAxis=f=c||s.showEmpty,t.staggerLines=t.horiz&&p.staggerLines||void 0,t.createGroups(),c||t.isLinked?(a.forEach(function(M){t.generateTick(M)}),t.renderUnsquish(),t.reserveSpaceDefault=r===0||r===2||{1:"left",3:"right"}[r]===t.labelAlign,Y(p.reserveSpace,!u&&null,t.labelAlign==="center"||null,t.reserveSpaceDefault)&&a.forEach(function(M){w=Math.max(o[M].getLabelSize(),w)}),t.staggerLines&&(w*=t.staggerLines),t.labelOffset=w*(t.opposite?-1:1)):Di(o,function(M,T){M.destroy(),delete o[T]}),d!=null&&d.text&&d.enabled!==!1&&(t.addTitle(f),f&&!u&&d.reserveSpace!==!1&&(t.titleOffset=b=t.axisTitle.getBBox()[i?"height":"width"],v=Q(y=d.offset)?0:Y(d.margin,i?5:10))),t.renderLine(),t.offset=x*Y(s.offset,g[r]?g[r]+(s.margin||0):0),t.tickRotCorr=t.tickRotCorr||{x:0,y:0},S=r===0?-t.labelMetrics().h:r===2?t.tickRotCorr.y:0,k=Math.abs(w)+v,w&&(k-=S,k+=x*(i?Y(p.y,t.tickRotCorr.y+x*p.distance):Y(p.x,x*p.distance))),t.axisTitleMargin=Y(y,k),t.getMaxLabelDimensions&&(t.maxLabelDimensions=t.getMaxLabelDimensions(o,a)),n!=="colorAxis"&&m){let M=this.tickSize("tick");g[r]=Math.max(g[r],(t.axisTitleMargin||0)+b+x*t.offset,k,a&&a.length&&M?M[0]+x*t.offset:0);let T=!t.axisLine||s.offset?0:t.axisLine.strokeWidth()/2;m[h]=Math.max(m[h],T)}at(this,"afterGetOffset")}getLinePath(t){let e=this.chart,i=this.opposite,s=this.offset,r=this.horiz,o=this.left+(i?this.width:0)+s,a=e.chartHeight-this.bottom-(i?this.height:0)+s;return i&&(t*=-1),e.renderer.crispLine([["M",r?this.left:o,r?a:this.top],["L",r?e.chartWidth-this.right:o,r?a:e.chartHeight-this.bottom]],t)}renderLine(){this.axisLine||(this.axisLine=this.chart.renderer.path().addClass("highcharts-axis-line").add(this.axisGroup),this.chart.styledMode||this.axisLine.attr({stroke:this.options.lineColor,"stroke-width":this.options.lineWidth,zIndex:7}))}getTitlePosition(t){let e=this.horiz,i=this.left,s=this.top,r=this.len,o=this.options.title,a=e?i:s,n=this.opposite,h=this.offset,c=o.x,d=o.y,p=this.chart.renderer.fontMetrics(t),u=t?Math.max(t.getBBox(!1,0).height-p.h-1,0):0,g={low:a+(e?0:r),middle:a+r/2,high:a+(e?r:0)}[o.align],m=(e?s+this.height:i)+(e?1:-1)*(n?-1:1)*(this.axisTitleMargin||0)+[-u,u,p.f,-u][this.side],x={x:e?g+c:m+(n?this.width:0)+h+c,y:e?m+d-(n?this.height:0)+h:g+d};return at(this,"afterGetTitlePosition",{titlePosition:x}),x}renderMinorTick(t,e){let i=this.minorTicks;i[t]||(i[t]=new Le(this,t,"minor")),e&&i[t].isNew&&i[t].render(null,!0),i[t].render(null,!1,1)}renderTick(t,e,i){let s=this.isLinked,r=this.ticks;(!s||t>=this.min&&t<=this.max||this.grid&&this.grid.isColumn)&&(r[t]||(r[t]=new Le(this,t)),i&&r[t].isNew&&r[t].render(e,!0,-1),r[t].render(e))}render(){let t,e,i=this,s=i.chart,r=i.logarithmic,o=s.renderer,a=i.options,n=i.isLinked,h=i.tickPositions,c=i.axisTitle,d=i.ticks,p=i.minorTicks,u=i.alternateBands,g=a.stackLabels,m=a.alternateGridColor,x=a.crossing,f=i.tickmarkOffset,b=i.axisLine,y=i.showAxis,v=$l(o.globalAnimation);if(i.labelEdge.length=0,i.overlap=!1,[d,p,u].forEach(function(w){Di(w,function(k){k.isActive=!1})}),W(x)){let w=this.isXAxis?s.yAxis[0]:s.xAxis[0],k=[1,-1,-1,1][this.side];if(w){let S=w.toPixels(x,!0);i.horiz&&(S=w.len-S),i.offset=k*S}}if(i.hasData()||n){let w=i.chart.hasRendered&&i.old&&W(i.old.min);i.minorTickInterval&&!i.categories&&i.getMinorTickPositions().forEach(function(k){i.renderMinorTick(k,w)}),h.length&&(h.forEach(function(k,S){i.renderTick(k,S,w)}),f&&(i.min===0||i.single)&&(d[-1]||(d[-1]=new Le(i,-1,null,!0)),d[-1].render(-1))),m&&h.forEach(function(k,S){e=h[S+1]!==void 0?h[S+1]+f:i.max-f,S%2==0&&k<i.max&&e<=i.max+(s.polar?-f:f)&&(u[k]||(u[k]=new A.PlotLineOrBand(i,{})),t=k+f,u[k].options={from:r?r.lin2log(t):t,to:r?r.lin2log(e):e,color:m,className:"highcharts-alternate-grid"},u[k].render(),u[k].isActive=!0)}),i._addedPlotLB||(i._addedPlotLB=!0,(a.plotLines||[]).concat(a.plotBands||[]).forEach(function(k){i.addPlotBandOrLine(k)}))}[d,p,u].forEach(function(w){let k=[],S=v.duration;Di(w,function(M,T){M.isActive||(M.render(T,!1,0),M.isActive=!1,k.push(T))}),eh(function(){let M=k.length;for(;M--;)w[k[M]]&&!w[k[M]].isActive&&(w[k[M]].destroy(),delete w[k[M]])},w!==u&&s.hasRendered&&S?S:0)}),b&&(b[b.isPlaced?"animate":"attr"]({d:this.getLinePath(b.strokeWidth())}),b.isPlaced=!0,b[y?"show":"hide"](y)),c&&y&&(c[c.isNew?"attr":"animate"](i.getTitlePosition(c)),c.isNew=!1),g&&g.enabled&&i.stacking&&i.stacking.renderStackTotals(),i.old={len:i.len,max:i.max,min:i.min,transA:i.transA,userMax:i.userMax,userMin:i.userMin},i.isDirty=!1,at(this,"afterRender")}redraw(){this.visible&&(this.render(),this.plotLinesAndBands.forEach(function(t){t.render()})),this.series.forEach(function(t){t.isDirty=!0})}getKeepProps(){return this.keepProps||Oe.keepProps}destroy(t){let e=this,i=e.plotLinesAndBands,s=this.eventOptions;if(at(this,"destroy",{keepEvents:t}),t||Jl(e),[e.ticks,e.minorTicks,e.alternateBands].forEach(function(r){Kl(r)}),i){let r=i.length;for(;r--;)i[r].destroy()}for(let r in["axisLine","axisTitle","axisGroup","gridGroup","labelGroup","cross","scrollbar"].forEach(function(o){e[o]&&(e[o]=e[o].destroy())}),e.plotLinesAndBandsGroups)e.plotLinesAndBandsGroups[r]=e.plotLinesAndBandsGroups[r].destroy();Di(e,function(r,o){e.getKeepProps().indexOf(o)===-1&&delete e[o]}),this.eventOptions=s}drawCrosshair(t,e){let i=this.crosshair,s=Y(i&&i.snap,!0),r=this.chart,o,a,n,h=this.cross,c;if(at(this,"drawCrosshair",{e:t,point:e}),t||(t=this.cross&&this.cross.e),i&&(Q(e)||!s)!==!1){if(s?Q(e)&&(a=Y(this.coll!=="colorAxis"?e.crosshairPos:null,this.isXAxis?e.plotX:this.len-e.plotY)):a=t&&(this.horiz?t.chartX-this.pos:this.len-t.chartY+this.pos),Q(a)&&(c={value:e&&(this.isXAxis?e.x:Y(e.stackY,e.y)),translatedValue:a},r.polar&&Oi(c,{isCrosshair:!0,chartX:t&&t.chartX,chartY:t&&t.chartY,point:e}),o=this.getPlotLinePath(c)||null),!Q(o)){this.hideCrosshair();return}n=this.categories&&!this.isRadial,h||(this.cross=h=r.renderer.path().addClass("highcharts-crosshair highcharts-crosshair-"+(n?"category ":"thin ")+(i.className||"")).attr({zIndex:Y(i.zIndex,2)}).add(),!r.styledMode&&(h.attr({stroke:i.color||(n?pt.parse("#ccd3ff").setOpacity(.25).get():"#cccccc"),"stroke-width":Y(i.width,1)}).css({"pointer-events":"none"}),i.dashStyle&&h.attr({dashstyle:i.dashStyle}))),h.show().attr({d:o}),n&&!i.width&&h.attr({"stroke-width":this.transA}),this.cross.e=t}else this.hideCrosshair();at(this,"afterDrawCrosshair",{e:t,point:e})}hideCrosshair(){this.cross&&this.cross.hide(),at(this,"afterHideCrosshair")}update(t,e){let i=this.chart;t=Ei(this.userOptions,t),this.destroy(!0),this.init(i,t),i.isDirtyBox=!0,Y(e,!0)&&i.redraw()}remove(t){let e=this.chart,i=this.coll,s=this.series,r=s.length;for(;r--;)s[r]&&s[r].remove(!1);wo(e.axes,this),wo(e[i]||[],this),e.orderItems(i),this.destroy(),e.isDirtyBox=!0,Y(t,!0)&&e.redraw()}setTitle(t,e){this.update({title:t},e)}setCategories(t,e){this.update({categories:t},e)}}Oe.keepProps=["coll","extKey","hcEvents","len","names","series","userMax","userMin"];let{addEvent:ih,getMagnitude:sh,normalizeTickInterval:rh,timeUnits:Bi}=R;(function(l){function t(){return this.chart.time.getTimeTicks.apply(this.chart.time,arguments)}function e(){if(this.type!=="datetime"){this.dateTime=void 0;return}this.dateTime||(this.dateTime=new i(this))}l.compose=function(s){return s.keepProps.includes("dateTime")||(s.keepProps.push("dateTime"),s.prototype.getTimeTicks=t,ih(s,"afterSetType",e)),s};class i{constructor(r){this.axis=r}normalizeTimeTickInterval(r,o){let a=o||[["millisecond",[1,2,5,10,20,25,50,100,200,500]],["second",[1,2,5,10,15,30]],["minute",[1,2,5,10,15,30]],["hour",[1,2,3,4,6,8,12]],["day",[1,2]],["week",[1,2]],["month",[1,2,3,4,6]],["year",null]],n=a[a.length-1],h=Bi[n[0]],c=n[1],d;for(d=0;d<a.length&&(h=Bi[(n=a[d])[0]],c=n[1],!a[d+1]||!(r<=(h*c[c.length-1]+Bi[a[d+1][0]])/2));d++);h===Bi.year&&r<5*h&&(c=[1,2,5]);let p=rh(r/h,c,n[0]==="year"?Math.max(sh(r/h),1):1);return{unitRange:h,count:p,unitName:n[0]}}getXDateFormat(r,o){let{axis:a}=this,n=a.chart.time;return a.closestPointRange?n.getDateFormat(a.closestPointRange,r,a.options.startOfWeek,o)||n.resolveDTLFormat(o.year).main:n.resolveDTLFormat(o.day).main}}l.Additions=i})(It||(It={}));let oh=It,{addEvent:Co,normalizeTickInterval:ah,pick:nh}=R;(function(l){function t(){this.type!=="logarithmic"?this.logarithmic=void 0:this.logarithmic??(this.logarithmic=new i(this))}function e(){let s=this.logarithmic;s&&(this.lin2val=function(r){return s.lin2log(r)},this.val2lin=function(r){return s.log2lin(r)})}l.compose=function(s){return s.keepProps.includes("logarithmic")||(s.keepProps.push("logarithmic"),Co(s,"afterSetType",t),Co(s,"afterInit",e)),s};class i{constructor(r){this.axis=r}getLogTickPositions(r,o,a,n){let h=this.axis,c=h.len,d=h.options,p=[];if(n||(this.minorAutoInterval=void 0),r>=.5)r=Math.round(r),p=h.getLinearTickPositions(r,o,a);else if(r>=.08){let u,g,m,x,f,b,y,v=Math.floor(o);for(u=r>.3?[1,2,4]:r>.15?[1,2,4,6,8]:[1,2,3,4,5,6,7,8,9],g=v;g<a+1&&!y;g++)for(m=0,x=u.length;m<x&&!y;m++)(f=this.log2lin(this.lin2log(g)*u[m]))>o&&(!n||b<=a)&&b!==void 0&&p.push(b),b>a&&(y=!0),b=f}else{let u=this.lin2log(o),g=this.lin2log(a),m=n?h.getMinorTickInterval():d.tickInterval,x=d.tickPixelInterval/(n?5:1),f=n?c/h.tickPositions.length:c;r=ah(r=nh(m==="auto"?null:m,this.minorAutoInterval,(g-u)*x/(f||1))),p=h.getLinearTickPositions(r,u,g).map(this.log2lin),n||(this.minorAutoInterval=r/5)}return n||(h.tickInterval=r),p}lin2log(r){return Math.pow(10,r)}log2lin(r){return Math.log(r)/Math.LN10}}l.Additions=i})(Ht||(Ht={}));let lh=Ht,{erase:hh,extend:dh,isNumber:Po}=R;(function(l){let t;function e(h){return this.addPlotBandOrLine(h,"plotBands")}function i(h,c){let d=this.userOptions,p=new t(this,h);if(this.visible&&(p=p.render()),p){if(this._addedPlotLB||(this._addedPlotLB=!0,(d.plotLines||[]).concat(d.plotBands||[]).forEach(u=>{this.addPlotBandOrLine(u)})),c){let u=d[c]||[];u.push(h),d[c]=u}this.plotLinesAndBands.push(p)}return p}function s(h){return this.addPlotBandOrLine(h,"plotLines")}function r(h,c,d){d=d||this.options;let p=this.getPlotLinePath({value:c,force:!0,acrossPanes:d.acrossPanes}),u=[],g=this.horiz,m=!Po(this.min)||!Po(this.max)||h<this.min&&c<this.min||h>this.max&&c>this.max,x=this.getPlotLinePath({value:h,force:!0,acrossPanes:d.acrossPanes}),f,b=1,y;if(x&&p)for(m&&(y=x.toString()===p.toString(),b=0),f=0;f<x.length;f+=2){let v=x[f],w=x[f+1],k=p[f],S=p[f+1];(v[0]==="M"||v[0]==="L")&&(w[0]==="M"||w[0]==="L")&&(k[0]==="M"||k[0]==="L")&&(S[0]==="M"||S[0]==="L")&&(g&&k[1]===v[1]?(k[1]+=b,S[1]+=b):g||k[2]!==v[2]||(k[2]+=b,S[2]+=b),u.push(["M",v[1],v[2]],["L",w[1],w[2]],["L",S[1],S[2]],["L",k[1],k[2]],["Z"])),u.isFlat=y}return u}function o(h){this.removePlotBandOrLine(h)}function a(h){let c=this.plotLinesAndBands,d=this.options,p=this.userOptions;if(c){let u=c.length;for(;u--;)c[u].id===h&&c[u].destroy();[d.plotLines||[],p.plotLines||[],d.plotBands||[],p.plotBands||[]].forEach(function(g){for(u=g.length;u--;)(g[u]||{}).id===h&&hh(g,g[u])})}}function n(h){this.removePlotBandOrLine(h)}l.compose=function(h,c){let d=c.prototype;return d.addPlotBand||(t=h,dh(d,{addPlotBand:e,addPlotLine:s,addPlotBandOrLine:i,getPlotBandPath:r,removePlotBand:o,removePlotLine:n,removePlotBandOrLine:a})),c}})(Xt||(Xt={}));let ch=Xt,{addEvent:ph,arrayMax:Lo,arrayMin:Oo,defined:Zt,destroyObjectProperties:uh,erase:gh,fireEvent:fh,merge:Eo,objectEach:mh,pick:xh}=R;class Ni{static compose(t,e){return ph(t,"afterInit",function(){this.labelCollectors.push(()=>{var s;let i=[];for(let r of this.axes)for(let{label:o,options:a}of r.plotLinesAndBands)o&&!((s=a==null?void 0:a.label)!=null&&s.allowOverlap)&&i.push(o);return i})}),ch.compose(Ni,e)}constructor(t,e){this.axis=t,this.options=e,this.id=e.id}render(){fh(this,"render");let{axis:t,options:e}=this,{horiz:i,logarithmic:s}=t,{color:r,events:o,zIndex:a=0}=e,{renderer:n,time:h}=t.chart,c={},d=h.parse(e.to),p=h.parse(e.from),u=h.parse(e.value),g=e.borderWidth,m=e.label,{label:x,svgElem:f}=this,b=[],y,v=Zt(p)&&Zt(d),w=Zt(u),k=!f,S={class:"highcharts-plot-"+(v?"band ":"line ")+(e.className||"")},M=v?"bands":"lines";if(!t.chart.styledMode&&(w?(S.stroke=r||"#999999",S["stroke-width"]=xh(e.width,1),e.dashStyle&&(S.dashstyle=e.dashStyle)):v&&(S.fill=r||"#e6e9ff",g&&(S.stroke=e.borderColor,S["stroke-width"]=g))),c.zIndex=a,M+="-"+a,(y=t.plotLinesAndBandsGroups[M])||(t.plotLinesAndBandsGroups[M]=y=n.g("plot-"+M).attr(c).add()),f||(this.svgElem=f=n.path().attr(S).add(y)),Zt(u))b=t.getPlotLinePath({value:(s==null?void 0:s.log2lin(u))??u,lineWidth:f.strokeWidth(),acrossPanes:e.acrossPanes});else{if(!(Zt(p)&&Zt(d)))return;b=t.getPlotBandPath((s==null?void 0:s.log2lin(p))??p,(s==null?void 0:s.log2lin(d))??d,e)}return!this.eventsAdded&&o&&(mh(o,(T,P)=>{f==null||f.on(P,L=>{o[P].apply(this,[L])})}),this.eventsAdded=!0),(k||!f.d)&&(b!=null&&b.length)?f.attr({d:b}):f&&(b?(f.show(),f.animate({d:b})):f.d&&(f.hide(),x&&(this.label=x=x.destroy()))),m&&(Zt(m.text)||Zt(m.formatter))&&(b!=null&&b.length)&&t.width>0&&t.height>0&&!b.isFlat?(m=Eo({align:i&&v?"center":void 0,x:i?!v&&4:10,verticalAlign:!i&&v?"middle":void 0,y:i?v?16:10:v?6:-4,rotation:i&&!v?90:0,...v?{inside:!0}:{}},m),this.renderLabel(m,b,v,a)):x&&x.hide(),this}renderLabel(t,e,i,s){var g;let r=this.axis,o=r.chart.renderer,a=t.inside,n=this.label;n||(this.label=n=o.text(this.getLabelText(t),0,0,t.useHTML).attr({align:t.textAlign||t.align,rotation:t.rotation,class:"highcharts-plot-"+(i?"band":"line")+"-label "+(t.className||""),zIndex:s}),r.chart.styledMode||n.css(Eo({fontSize:"0.8em",textOverflow:i&&!a?"":"ellipsis"},t.style)),n.add());let h=e.xBounds||[e[0][1],e[1][1],i?e[2][1]:e[0][1]],c=e.yBounds||[e[0][2],e[1][2],i?e[2][2]:e[0][2]],d=Oo(h),p=Oo(c),u=Lo(h)-d;n.align(t,!1,{x:d,y:p,width:u,height:Lo(c)-p}),(!n.alignValue||n.alignValue==="left"||Zt(a))&&n.css({width:(((g=t.style)==null?void 0:g.width)||(i&&a?u:n.rotation===90?r.height-(n.alignAttr.y-r.top):(t.clip?r.width:r.chart.chartWidth)-(n.alignAttr.x-r.left)))+"px"}),n.show(!0)}getLabelText(t){return Zt(t.formatter)?t.formatter.call(this):t.text}destroy(){gh(this.axis.plotLinesAndBands,this),delete this.axis,uh(this)}}let{animObject:yh}=St,{format:Do}=Yt,{composed:bh,dateFormats:vh,doc:Io,isSafari:kh}=A,{distribute:wh}=yi,{addEvent:Mh,clamp:Ee,css:Bo,discardElement:Sh,extend:Th,fireEvent:No,isArray:Ah,isNumber:Ch,isObject:Ph,isString:Ws,merge:Lh,pick:De,pushUnique:Oh,splat:Hs,syncTimeout:Eh}=R;class Xs{constructor(t,e,i){this.allowShared=!0,this.crosshairs=[],this.distance=0,this.isHidden=!0,this.isSticky=!1,this.options={},this.outside=!1,this.chart=t,this.init(t,e),this.pointer=i}bodyFormatter(t){return t.map(e=>{let i=e.series.tooltipOptions,s=e.formatPrefix||"point";return(i[s+"Formatter"]||e.tooltipFormatter).call(e,i[s+"Format"]||"")})}cleanSplit(t){this.chart.series.forEach(function(e){let i=e&&e.tt;i&&(!i.isActive||t?e.tt=i.destroy():i.isActive=!1)})}defaultFormatter(t){let e,i=this.points||Hs(this);return(e=(e=[t.headerFooterFormatter(i[0])]).concat(t.bodyFormatter(i))).push(t.headerFooterFormatter(i[0],!0)),e}destroy(){this.label&&(this.label=this.label.destroy()),this.split&&(this.cleanSplit(!0),this.tt&&(this.tt=this.tt.destroy())),this.renderer&&(this.renderer=this.renderer.destroy(),Sh(this.container)),R.clearTimeout(this.hideTimer)}getAnchor(t,e){let i,{chart:s,pointer:r}=this,o=s.inverted,a=s.plotTop,n=s.plotLeft;if((t=Hs(t))[0].series&&t[0].series.yAxis&&!t[0].series.yAxis.options.reversedStacks&&(t=t.slice().reverse()),this.followPointer&&e)e.chartX===void 0&&(e=r.normalize(e)),i=[e.chartX-n,e.chartY-a];else if(t[0].tooltipPos)i=t[0].tooltipPos;else{let h=0,c=0;t.forEach(function(d){let p=d.pos(!0);p&&(h+=p[0],c+=p[1])}),h/=t.length,c/=t.length,this.shared&&t.length>1&&e&&(o?h=e.chartX:c=e.chartY),i=[h-n,c-a]}return i.map(Math.round)}getClassName(t,e,i){let s=this.options,r=t.series,o=r.options;return[s.className,"highcharts-label",i&&"highcharts-tooltip-header",e?"highcharts-tooltip-box":"highcharts-tooltip",!i&&"highcharts-color-"+De(t.colorIndex,r.colorIndex),o&&o.className].filter(Ws).join(" ")}getLabel({anchorX:t,anchorY:e}={anchorX:0,anchorY:0}){let i=this,s=this.chart.styledMode,r=this.options,o=this.split&&this.allowShared,a=this.container,n=this.chart.renderer;if(this.label){let h=!this.label.hasClass("highcharts-label");(!o&&h||o&&!h)&&this.destroy()}if(!this.label){if(this.outside){let h=this.chart,c=h.options.chart.style,d=$e.getRendererType();this.container=a=A.doc.createElement("div"),a.className="highcharts-tooltip-container "+(h.renderTo.className.match(/(highcharts[a-zA-Z0-9-]+)\s?/gm)||""),Bo(a,{position:"absolute",top:"1px",pointerEvents:"none",zIndex:Math.max(this.options.style.zIndex||0,(c&&c.zIndex||0)+3)}),this.renderer=n=new d(a,0,0,c,void 0,void 0,n.styledMode)}if(o?this.label=n.g("tooltip"):(this.label=n.label("",t,e,r.shape,void 0,void 0,r.useHTML,void 0,"tooltip").attr({padding:r.padding,r:r.borderRadius}),s||this.label.attr({fill:r.backgroundColor,"stroke-width":r.borderWidth||0}).css(r.style).css({pointerEvents:r.style.pointerEvents||(this.shouldStickOnContact()?"auto":"none")})),i.outside){let h=this.label;[h.xSetter,h.ySetter].forEach((c,d)=>{h[d?"ySetter":"xSetter"]=p=>{c.call(h,i.distance),h[d?"y":"x"]=p,a&&(a.style[d?"top":"left"]=`${p}px`)}})}this.label.attr({zIndex:8}).shadow(r.shadow).add()}return a&&!a.parentElement&&A.doc.body.appendChild(a),this.label}getPlayingField(){let{body:t,documentElement:e}=Io,{chart:i,distance:s,outside:r}=this;return{width:r?Math.max(t.scrollWidth,e.scrollWidth,t.offsetWidth,e.offsetWidth,e.clientWidth)-2*s-2:i.chartWidth,height:r?Math.max(t.scrollHeight,e.scrollHeight,t.offsetHeight,e.offsetHeight,e.clientHeight):i.chartHeight}}getPosition(t,e,i){var N,F;let{distance:s,chart:r,outside:o,pointer:a}=this,{inverted:n,plotLeft:h,plotTop:c,polar:d}=r,{plotX:p=0,plotY:u=0}=i,g={},m=n&&i.h||0,{height:x,width:f}=this.getPlayingField(),b=a.getChartPosition(),y=I=>I*b.scaleX,v=I=>I*b.scaleY,w=I=>{let X=I==="x";return[I,X?f:x,X?t:e].concat(o?[X?y(t):v(e),X?b.left-s+y(p+h):b.top-s+v(u+c),0,X?f:x]:[X?t:e,X?p+h:u+c,X?h:c,X?h+r.plotWidth:c+r.plotHeight])},k=w("y"),S=w("x"),M,T=!!i.negative;!d&&((F=(N=r.hoverSeries)==null?void 0:N.yAxis)!=null&&F.reversed)&&(T=!T);let P=!this.followPointer&&De(i.ttBelow,!d&&!n===T),L=function(I,X,D,z,H,V,tt){let j=o?I==="y"?v(s):y(s):s,$=(D-z)/2,_=z<H-s,At=H+s+z<X,ut=H-j-D+$,Mt=H+j-$;if(P&&At)g[I]=Mt;else if(!P&&_)g[I]=ut;else if(_)g[I]=Math.min(tt-z,ut-m<0?ut:ut-m);else{if(!At)return!1;g[I]=Math.max(V,Mt+m+D>X?Mt:Mt+m)}},E=function(I,X,D,z,H){if(H<s||H>X-s)return!1;H<D/2?g[I]=1:H>X-z/2?g[I]=X-z-2:g[I]=H-D/2},C=function(I){[k,S]=[S,k],M=I},O=()=>{L.apply(0,k)!==!1?E.apply(0,S)!==!1||M||(C(!0),O()):M?g.x=g.y=0:(C(!0),O())};return(n&&!d||this.len>1)&&C(),O(),g}hide(t){let e=this;R.clearTimeout(this.hideTimer),t=De(t,this.options.hideDelay),this.isHidden||(this.hideTimer=Eh(function(){let i=e.getLabel();e.getLabel().animate({opacity:0},{duration:t&&150,complete:()=>{i.hide(),e.container&&e.container.remove()}}),e.isHidden=!0},t))}init(t,e){this.chart=t,this.options=e,this.crosshairs=[],this.isHidden=!0,this.split=e.split&&!t.inverted&&!t.polar,this.shared=e.shared||this.split,this.outside=De(e.outside,!!(t.scrollablePixelsX||t.scrollablePixelsY))}shouldStickOnContact(t){return!!(!this.followPointer&&this.options.stickOnContact&&(!t||this.pointer.inClass(t.target,"highcharts-tooltip")))}move(t,e,i,s){let r=this,o=yh(!r.isHidden&&r.options.animation),a=r.followPointer||(r.len||0)>1,n={x:t,y:e};a||(n.anchorX=i,n.anchorY=s),o.step=()=>r.drawTracker(),r.getLabel().animate(n,o)}refresh(t,e){let{chart:i,options:s,pointer:r,shared:o}=this,a=Hs(t),n=a[0],h=s.format,c=s.formatter||this.defaultFormatter,d=i.styledMode,p=this.allowShared;if(!s.enabled||!n.series)return;R.clearTimeout(this.hideTimer),this.allowShared=!(!Ah(t)&&t.series&&t.series.noSharedTooltip),p=p&&!this.allowShared,this.followPointer=!this.split&&n.series.tooltipOptions.followPointer;let u=this.getAnchor(t,e),g=u[0],m=u[1];o&&this.allowShared&&(r.applyInactiveState(a),a.forEach(b=>b.setState("hover")),n.points=a),this.len=a.length;let x=Ws(h)?Do(h,n,i):c.call(n,this);n.points=void 0;let f=n.series;if(this.distance=De(f.tooltipOptions.distance,16),x===!1)this.hide();else{if(this.split&&this.allowShared)this.renderSplit(x,a);else{let b=g,y=m;if(e&&r.isDirectTouch&&(b=e.chartX-i.plotLeft,y=e.chartY-i.plotTop),i.polar||f.options.clip===!1||a.some(v=>r.isDirectTouch||v.series.shouldShowTooltip(b,y))){let v=this.getLabel(p&&this.tt||{});(!s.style.width||d)&&v.css({width:(this.outside?this.getPlayingField():i.spacingBox).width+"px"}),v.attr({class:this.getClassName(n),text:x&&x.join?x.join(""):x}),this.outside&&v.attr({x:Ee(v.x||0,0,this.getPlayingField().width-(v.width||0)-1)}),d||v.attr({stroke:s.borderColor||n.color||f.color||"#666666"}),this.updatePosition({plotX:g,plotY:m,negative:n.negative,ttBelow:n.ttBelow,h:u[2]||0})}else{this.hide();return}}this.isHidden&&this.label&&this.label.attr({opacity:1}).show(),this.isHidden=!1}No(this,"refresh")}renderSplit(t,e){var X;let i=this,{chart:s,chart:{chartWidth:r,chartHeight:o,plotHeight:a,plotLeft:n,plotTop:h,scrollablePixelsY:c=0,scrollablePixelsX:d,styledMode:p},distance:u,options:g,options:{positioner:m},pointer:x}=i,{scrollLeft:f=0,scrollTop:b=0}=((X=s.scrollablePlotArea)==null?void 0:X.scrollingContainer)||{},y=i.outside&&typeof d!="number"?Io.documentElement.getBoundingClientRect():{left:f,right:f+r},v=i.getLabel(),w=this.renderer||s.renderer,k=!!(s.xAxis[0]&&s.xAxis[0].opposite),{left:S,top:M}=x.getChartPosition(),T=h+b,P=0,L=a-c;function E(D,z,H,V,tt=!0){let j,$;return H?(j=k?0:L,$=Ee(D-V/2,y.left,y.right-V-(i.outside?S:0))):(j=z-T,$=Ee($=tt?D-V-u:D+u,tt?$:y.left,y.right)),{x:$,y:j}}Ws(t)&&(t=[!1,t]);let C=t.slice(0,e.length+1).reduce(function(D,z,H){if(z!==!1&&z!==""){let V=e[H-1]||{isHeader:!0,plotX:e[0].plotX,plotY:a,series:{}},tt=V.isHeader,j=tt?i:V.series,$=j.tt=function(gt,nt,bt){let he=gt,{isHeader:li,series:as}=nt;if(!he){let He={padding:g.padding,r:g.borderRadius};p||(He.fill=g.backgroundColor,He["stroke-width"]=g.borderWidth??1),he=w.label("",0,0,g[li?"headerShape":"shape"],void 0,void 0,g.useHTML).addClass(i.getClassName(nt,!0,li)).attr(He).add(v)}return he.isActive=!0,he.attr({text:bt}),p||he.css(g.style).attr({stroke:g.borderColor||nt.color||as.color||"#333333"}),he}(j.tt,V,z.toString()),_=$.getBBox(),At=_.width+$.strokeWidth();tt&&(P=_.height,L+=P,k&&(T-=P));let{anchorX:ut,anchorY:Mt}=function(gt){let nt,bt,{isHeader:he,plotX:li=0,plotY:as=0,series:He}=gt;if(he)nt=Math.max(n+li,n),bt=h+a/2;else{let{xAxis:_a,yAxis:Qa}=He;nt=_a.pos+Ee(li,-u,_a.len+u),He.shouldShowTooltip(0,Qa.pos-h+as,{ignoreX:!0})&&(bt=Qa.pos+as)}return{anchorX:nt=Ee(nt,y.left-u,y.right+u),anchorY:bt}}(V);if(typeof Mt=="number"){let gt=_.height+1,nt=m?m.call(i,At,gt,V):E(ut,Mt,tt,At);D.push({align:m?0:void 0,anchorX:ut,anchorY:Mt,boxWidth:At,point:V,rank:De(nt.rank,tt?1:0),size:gt,target:nt.y,tt:$,x:nt.x})}else $.isActive=!1}return D},[]);!m&&C.some(D=>{let{outside:z}=i,H=(z?S:0)+D.anchorX;return H<y.left&&H+D.boxWidth<y.right||H<S-y.left+D.boxWidth&&y.right-H>H})&&(C=C.map(D=>{let{x:z,y:H}=E(D.anchorX,D.anchorY,D.point.isHeader,D.boxWidth,!1);return Th(D,{target:H,x:z})})),i.cleanSplit(),wh(C,L);let O={left:S,right:S};C.forEach(function(D){let{x:z,boxWidth:H,isHeader:V}=D;!V&&(i.outside&&S+z<O.left&&(O.left=S+z),!V&&i.outside&&O.left+H>O.right&&(O.right=S+z))}),C.forEach(function(D){let{x:z,anchorX:H,anchorY:V,pos:tt,point:{isHeader:j}}=D,$={visibility:tt===void 0?"hidden":"inherit",x:z,y:(tt||0)+T,anchorX:H,anchorY:V};if(i.outside&&z<H){let _=S-O.left;_>0&&(j||($.x=z+_,$.anchorX=H+_),j&&($.x=(O.right-O.left)/2,$.anchorX=H+_))}D.tt.attr($)});let{container:N,outside:F,renderer:I}=i;if(F&&N&&I){let{width:D,height:z,x:H,y:V}=v.getBBox();I.setSize(D+H,z+V,!1),N.style.left=O.left+"px",N.style.top=M+"px"}kh&&v.attr({opacity:v.opacity===1?.999:1})}drawTracker(){if(!this.shouldStickOnContact()){this.tracker&&(this.tracker=this.tracker.destroy());return}let t=this.chart,e=this.label,i=this.shared?t.hoverPoints:t.hoverPoint;if(!e||!i)return;let s={x:0,y:0,width:0,height:0},r=this.getAnchor(i),o=e.getBBox();r[0]+=t.plotLeft-(e.translateX||0),r[1]+=t.plotTop-(e.translateY||0),s.x=Math.min(0,r[0]),s.y=Math.min(0,r[1]),s.width=r[0]<0?Math.max(Math.abs(r[0]),o.width-r[0]):Math.max(Math.abs(r[0]),o.width),s.height=r[1]<0?Math.max(Math.abs(r[1]),o.height-Math.abs(r[1])):Math.max(Math.abs(r[1]),o.height),this.tracker?this.tracker.attr(s):(this.tracker=e.renderer.rect(s).addClass("highcharts-tracker").add(e),t.styledMode||this.tracker.attr({fill:"rgba(0,0,0,0)"}))}styledModeFormat(t){return t.replace('style="font-size: 0.8em"','class="highcharts-header"').replace(/style="color:{(point|series)\.color}"/g,'class="highcharts-color-{$1.colorIndex} {series.options.className} {point.options.className}"')}headerFooterFormatter(t,e){let i=t.series,s=i.tooltipOptions,r=i.xAxis,o=r&&r.dateTime,a={isFooter:e,point:t},n=s.xDateFormat||"",h=s[e?"footerFormat":"headerFormat"];return No(this,"headerFormatter",a,function(c){if(o&&!n&&Ch(t.key)&&(n=o.getXDateFormat(t.key,s.dateTimeLabelFormats)),o&&n){if(Ph(n)){let d=n;vh[0]=p=>i.chart.time.dateFormat(d,p),n="%0"}(t.tooltipDateKeys||["key"]).forEach(d=>{h=h.replace(RegExp("point\\."+d+"([ \\)}])",""),`(point.${d}:${n})$1`)})}i.chart.styledMode&&(h=this.styledModeFormat(h)),c.text=Do(h,t,this.chart)}),a.text||""}update(t){this.destroy(),this.init(this.chart,Lh(!0,this.options,t))}updatePosition(t){let{chart:e,container:i,distance:s,options:r,pointer:o,renderer:a}=this,{height:n=0,width:h=0}=this.getLabel(),{left:c,top:d,scaleX:p,scaleY:u}=o.getChartPosition(),g=(r.positioner||this.getPosition).call(this,h,n,t),m=A.doc,x=(t.plotX||0)+e.plotLeft,f=(t.plotY||0)+e.plotTop,b;a&&i&&(r.positioner&&(g.x+=c-s,g.y+=d-s),b=(r.borderWidth||0)+2*s+2,a.setSize(Ee(h+b,0,m.documentElement.clientWidth)-1,n+b,!1),(p!==1||u!==1)&&(Bo(i,{transform:`scale(${p}, ${u})`}),x*=p,f*=u),x+=c-g.x,f+=d-g.y),this.move(Math.round(g.x),Math.round(g.y||0),x,f)}}(function(l){l.compose=function(t){Oh(bh,"Core.Tooltip")&&Mh(t,"afterInit",function(){let e=this.chart;e.options.tooltip&&(e.tooltip=new l(e,e.options.tooltip,this))})}})(Xs||(Xs={}));let zo=Xs,{animObject:Dh}=St,{defaultOptions:Ih}=zt,{format:Bh}=Yt,{addEvent:Nh,crisp:zh,erase:Rh,extend:zi,fireEvent:Fs,getNestedProperty:Wh,isArray:Hh,isFunction:Xh,isNumber:te,isObject:Ri,merge:Ro,pick:ee,syncTimeout:Fh,removeEvent:Wo,uniqueKey:Yh}=R;class ti{animateBeforeDestroy(){let t=this,e={x:t.startXPos,opacity:0},i=t.getGraphicalProps();i.singular.forEach(function(s){t[s]=t[s].animate(s==="dataLabel"?{x:t[s].startXPos,y:t[s].startYPos,opacity:0}:e)}),i.plural.forEach(function(s){t[s].forEach(function(r){r.element&&r.animate(zi({x:t.startXPos},r.startYPos?{x:r.startXPos,y:r.startYPos}:{}))})})}applyOptions(t,e){let i=this.series,s=i.options.pointValKey||i.pointValKey;return zi(this,t=ti.prototype.optionsToObject.call(this,t)),this.options=this.options?zi(this.options,t):t,t.group&&delete this.group,t.dataLabels&&delete this.dataLabels,s&&(this.y=ti.prototype.getNestedProperty.call(this,s)),this.selected&&(this.state="select"),"name"in this&&e===void 0&&i.xAxis&&i.xAxis.hasNames&&(this.x=i.xAxis.nameToX(this)),this.x===void 0&&i?this.x=e??i.autoIncrement():te(t.x)&&i.options.relativeXValue?this.x=i.autoIncrement(t.x):typeof this.x=="string"&&(e??(e=i.chart.time.parse(this.x)),te(e)&&(this.x=e)),this.isNull=this.isValid&&!this.isValid(),this.formatPrefix=this.isNull?"null":"point",this}destroy(){if(!this.destroyed){let t=this,e=t.series,i=e.chart,s=e.options.dataSorting,r=i.hoverPoints,o=Dh(t.series.chart.renderer.globalAnimation),a=()=>{for(let n in(t.graphic||t.graphics||t.dataLabel||t.dataLabels)&&(Wo(t),t.destroyElements()),t)delete t[n]};t.legendItem&&i.legend.destroyItem(t),r&&(t.setState(),Rh(r,t),r.length||(i.hoverPoints=null)),t===i.hoverPoint&&t.onMouseOut(),s&&s.enabled?(this.animateBeforeDestroy(),Fh(a,o.duration)):a(),i.pointCount--}this.destroyed=!0}destroyElements(t){let e=this,i=e.getGraphicalProps(t);i.singular.forEach(function(s){e[s]=e[s].destroy()}),i.plural.forEach(function(s){e[s].forEach(function(r){r&&r.element&&r.destroy()}),delete e[s]})}firePointEvent(t,e,i){let s=this,r=this.series.options;s.manageEvent(t),t==="click"&&r.allowPointSelect&&(i=function(o){!s.destroyed&&s.select&&s.select(null,o.ctrlKey||o.metaKey||o.shiftKey)}),Fs(s,t,e,i)}getClassName(){return"highcharts-point"+(this.selected?" highcharts-point-select":"")+(this.negative?" highcharts-negative":"")+(this.isNull?" highcharts-null-point":"")+(this.colorIndex!==void 0?" highcharts-color-"+this.colorIndex:"")+(this.options.className?" "+this.options.className:"")+(this.zone&&this.zone.className?" "+this.zone.className.replace("highcharts-negative",""):"")}getGraphicalProps(t){let e,i,s=this,r=[],o={singular:[],plural:[]};for((t=t||{graphic:1,dataLabel:1}).graphic&&r.push("graphic","connector"),t.dataLabel&&r.push("dataLabel","dataLabelPath","dataLabelUpper"),i=r.length;i--;)s[e=r[i]]&&o.singular.push(e);return["graphic","dataLabel"].forEach(function(a){let n=a+"s";t[a]&&s[n]&&o.plural.push(n)}),o}getNestedProperty(t){return t?t.indexOf("custom.")===0?Wh(t,this.options):this[t]:void 0}getZone(){let t=this.series,e=t.zones,i=t.zoneAxis||"y",s,r=0;for(s=e[0];this[i]>=s.value;)s=e[++r];return this.nonZonedColor||(this.nonZonedColor=this.color),s&&s.color&&!this.options.color?this.color=s.color:this.color=this.nonZonedColor,s}hasNewShapeType(){return(this.graphic&&(this.graphic.symbolName||this.graphic.element.nodeName))!==this.shapeType}constructor(t,e,i){this.formatPrefix="point",this.visible=!0,this.point=this,this.series=t,this.applyOptions(e,i),this.id??(this.id=Yh()),this.resolveColor(),t.chart.pointCount++,Fs(this,"afterInit")}isValid(){return(te(this.x)||this.x instanceof Date)&&te(this.y)}optionsToObject(t){var c;let e=this.series,i=e.options.keys,s=i||e.pointArrayMap||["y"],r=s.length,o={},a,n=0,h=0;if(te(t)||t===null)o[s[0]]=t;else if(Hh(t))for(!i&&t.length>r&&((a=typeof t[0])=="string"?(c=e.xAxis)!=null&&c.dateTime?o.x=e.chart.time.parse(t[0]):o.name=t[0]:a==="number"&&(o.x=t[0]),n++);h<r;)i&&t[n]===void 0||(s[h].indexOf(".")>0?ti.prototype.setNestedProperty(o,t[n],s[h]):o[s[h]]=t[n]),n++,h++;else typeof t=="object"&&(o=t,t.dataLabels&&(e.hasDataLabels=()=>!0),t.marker&&(e._hasPointMarkers=!0));return o}pos(t,e=this.plotY){if(!this.destroyed){let{plotX:i,series:s}=this,{chart:r,xAxis:o,yAxis:a}=s,n=0,h=0;if(te(i)&&te(e))return t&&(n=o?o.pos:r.plotLeft,h=a?a.pos:r.plotTop),r.inverted&&o&&a?[a.len-e+h,o.len-i+n]:[i+n,e+h]}}resolveColor(){let t=this.series,e=t.chart.options.chart,i=t.chart.styledMode,s,r,o=e.colorCount,a;delete this.nonZonedColor,t.options.colorByPoint?(i||(s=(r=t.options.colors||t.chart.options.colors)[t.colorCounter],o=r.length),a=t.colorCounter,t.colorCounter++,t.colorCounter===o&&(t.colorCounter=0)):(i||(s=t.color),a=t.colorIndex),this.colorIndex=ee(this.options.colorIndex,a),this.color=ee(this.options.color,s)}setNestedProperty(t,e,i){return i.split(".").reduce(function(s,r,o,a){let n=a.length-1===o;return s[r]=n?e:Ri(s[r],!0)?s[r]:{},s[r]},t),t}shouldDraw(){return!this.isNull}tooltipFormatter(t){var n;let{chart:e,pointArrayMap:i=["y"],tooltipOptions:s}=this.series,{valueDecimals:r="",valuePrefix:o="",valueSuffix:a=""}=s;return e.styledMode&&(t=((n=e.tooltip)==null?void 0:n.styledModeFormat(t))||t),i.forEach(h=>{h="{point."+h,(o||a)&&(t=t.replace(RegExp(h+"}","g"),o+h+"}"+a)),t=t.replace(RegExp(h+"}","g"),h+":,."+r+"f}")}),Bh(t,this,e)}update(t,e,i,s){let r,o=this,a=o.series,n=o.graphic,h=a.chart,c=a.options;function d(){o.applyOptions(t);let p=n&&o.hasMockGraphic,u=o.y===null?!p:p;n&&u&&(o.graphic=n.destroy(),delete o.hasMockGraphic),Ri(t,!0)&&(n&&n.element&&t&&t.marker&&t.marker.symbol!==void 0&&(o.graphic=n.destroy()),t!=null&&t.dataLabels&&o.dataLabel&&(o.dataLabel=o.dataLabel.destroy())),r=o.index;let g={};for(let m of a.dataColumnKeys())g[m]=o[m];a.dataTable.setRow(g,r),c.data[r]=Ri(c.data[r],!0)||Ri(t,!0)?o.options:ee(t,c.data[r]),a.isDirty=a.isDirtyData=!0,!a.fixedBox&&a.hasCartesianSeries&&(h.isDirtyBox=!0),c.legendType==="point"&&(h.isDirtyLegend=!0),e&&h.redraw(i)}e=ee(e,!0),s===!1?d():o.firePointEvent("update",{options:t},d)}remove(t,e){this.series.removePoint(this.series.data.indexOf(this),t,e)}select(t,e){let i=this,s=i.series,r=s.chart;t=ee(t,!i.selected),this.selectedStaging=t,i.firePointEvent(t?"select":"unselect",{accumulate:e},function(){i.selected=i.options.selected=t,s.options.data[s.data.indexOf(i)]=i.options,i.setState(t&&"select"),e||r.getSelectedPoints().forEach(function(o){let a=o.series;o.selected&&o!==i&&(o.selected=o.options.selected=!1,a.options.data[a.data.indexOf(o)]=o.options,o.setState(r.hoverPoints&&a.options.inactiveOtherPoints?"inactive":""),o.firePointEvent("unselect"))})}),delete this.selectedStaging}onMouseOver(t){let{inverted:e,pointer:i}=this.series.chart;i&&(t=t?i.normalize(t):i.getChartCoordinatesFromPoint(this,e),i.runPointActions(t,this))}onMouseOut(){let t=this.series.chart;this.firePointEvent("mouseOut"),this.series.options.inactiveOtherPoints||(t.hoverPoints||[]).forEach(function(e){e.setState()}),t.hoverPoints=t.hoverPoint=null}manageEvent(t){var s,r,o,a,n,h,c;let e=Ro(this.series.options.point,this.options),i=(s=e.events)==null?void 0:s[t];Xh(i)&&(!((r=this.hcEvents)!=null&&r[t])||((a=(o=this.hcEvents)==null?void 0:o[t])==null?void 0:a.map(d=>d.fn).indexOf(i))===-1)?((n=this.importedUserEvent)==null||n.call(this),this.importedUserEvent=Nh(this,t,i),this.hcEvents&&(this.hcEvents[t].userEvent=!0)):this.importedUserEvent&&!i&&((h=this.hcEvents)!=null&&h[t])&&((c=this.hcEvents)!=null&&c[t].userEvent)&&(Wo(this,t),delete this.hcEvents[t],Object.keys(this.hcEvents)||delete this.importedUserEvent)}setState(t,e){var k;let i=this.series,s=this.state,r=i.options.states[t||"normal"]||{},o=Ih.plotOptions[i.type].marker&&i.options.marker,a=o&&o.enabled===!1,n=o&&o.states&&o.states[t||"normal"]||{},h=n.enabled===!1,c=this.marker||{},d=i.chart,p=o&&i.markerAttribs,u=i.halo,g,m,x,f=i.stateMarkerGraphic,b;if((t=t||"")===this.state&&!e||this.selected&&t!=="select"||r.enabled===!1||t&&(h||a&&n.enabled===!1)||t&&c.states&&c.states[t]&&c.states[t].enabled===!1)return;if(this.state=t,p&&(g=i.markerAttribs(this,t)),this.graphic&&!this.hasMockGraphic){if(s&&this.graphic.removeClass("highcharts-point-"+s),t&&this.graphic.addClass("highcharts-point-"+t),!d.styledMode){m=i.pointAttribs(this,t),x=ee(d.options.chart.animation,r.animation);let S=m.opacity;i.options.inactiveOtherPoints&&te(S)&&(this.dataLabels||[]).forEach(function(M){M&&!M.hasClass("highcharts-data-label-hidden")&&(M.animate({opacity:S},x),M.connector&&M.connector.animate({opacity:S},x))}),this.graphic.animate(m,x)}g&&this.graphic.animate(g,ee(d.options.chart.animation,n.animation,o.animation)),f&&f.hide()}else t&&n&&(b=c.symbol||i.symbol,f&&f.currentSymbol!==b&&(f=f.destroy()),g&&(f?f[e?"animate":"attr"]({x:g.x,y:g.y}):b&&(i.stateMarkerGraphic=f=d.renderer.symbol(b,g.x,g.y,g.width,g.height,Ro(o,n)).add(i.markerGroup),f.currentSymbol=b)),!d.styledMode&&f&&this.state!=="inactive"&&f.attr(i.pointAttribs(this,t))),f&&(f[t&&this.isInside?"show":"hide"](),f.element.point=this,f.addClass(this.getClassName(),!0));let y=r.halo,v=this.graphic||f,w=v&&v.visibility||"inherit";y&&y.size&&v&&w!=="hidden"&&!this.isCluster?(u||(i.halo=u=d.renderer.path().add(v.parentGroup)),u.show()[e?"animate":"attr"]({d:this.haloPath(y.size)}),u.attr({class:"highcharts-halo highcharts-color-"+ee(this.colorIndex,i.colorIndex)+(this.className?" "+this.className:""),visibility:w,zIndex:-1}),u.point=this,d.styledMode||u.attr(zi({fill:this.color||i.color,"fill-opacity":y.opacity},it.filterUserAttributes(y.attributes||{})))):(k=u==null?void 0:u.point)!=null&&k.haloPath&&!u.point.destroyed&&u.animate({d:u.point.haloPath(0)},null,u.hide),Fs(this,"afterSetState",{state:t})}haloPath(t){let e=this.pos();return e?this.series.chart.renderer.symbols.circle(zh(e[0],1)-t,e[1]-t,2*t,2*t):[]}}let ie=ti,{parse:Gh}=pt,{charts:Ys,composed:jh,isTouchDevice:$h}=A,{addEvent:Rt,attr:Uh,css:Gs,extend:js,find:Ho,fireEvent:se,isNumber:Wi,isObject:Hi,objectEach:Vh,offset:qh,pick:$t,pushUnique:Zh,splat:Xo}=R;class xt{applyInactiveState(t){let e=[],i;(t||[]).forEach(function(s){i=s.series,e.push(i),i.linkedParent&&e.push(i.linkedParent),i.linkedSeries&&(e=e.concat(i.linkedSeries)),i.navigatorSeries&&e.push(i.navigatorSeries)}),this.chart.series.forEach(function(s){e.indexOf(s)===-1?s.setState("inactive",!0):s.options.inactiveOtherPoints&&s.setAllPointsToState("inactive")})}destroy(){let t=this;this.eventsToUnbind.forEach(e=>e()),this.eventsToUnbind=[],!A.chartCount&&(xt.unbindDocumentMouseUp.forEach(e=>e.unbind()),xt.unbindDocumentMouseUp.length=0,xt.unbindDocumentTouchEnd&&(xt.unbindDocumentTouchEnd=xt.unbindDocumentTouchEnd())),clearInterval(t.tooltipTimeout),Vh(t,function(e,i){t[i]=void 0})}getSelectionMarkerAttrs(t,e){let i={args:{chartX:t,chartY:e},attrs:{},shapeType:"rect"};return se(this,"getSelectionMarkerAttrs",i,s=>{let r,{chart:o,zoomHor:a,zoomVert:n}=this,{mouseDownX:h=0,mouseDownY:c=0}=o,d=s.attrs;d.x=o.plotLeft,d.y=o.plotTop,d.width=a?1:o.plotWidth,d.height=n?1:o.plotHeight,a&&(r=t-h,d.width=Math.max(1,Math.abs(r)),d.x=(r>0?0:r)+h),n&&(r=e-c,d.height=Math.max(1,Math.abs(r)),d.y=(r>0?0:r)+c)}),i}drag(t){let{chart:e}=this,{mouseDownX:i=0,mouseDownY:s=0}=e,{panning:r,panKey:o,selectionMarkerFill:a}=e.options.chart,n=e.plotLeft,h=e.plotTop,c=e.plotWidth,d=e.plotHeight,p=Hi(r)?r.enabled:r,u=o&&t[`${o}Key`],g=t.chartX,m=t.chartY,x,f=this.selectionMarker;if((!f||!f.touch)&&(g<n?g=n:g>n+c&&(g=n+c),m<h?m=h:m>h+d&&(m=h+d),this.hasDragged=Math.sqrt(Math.pow(i-g,2)+Math.pow(s-m,2)),this.hasDragged>10)){x=e.isInsidePlot(i-n,s-h,{visiblePlotOnly:!0});let{shapeType:b,attrs:y}=this.getSelectionMarkerAttrs(g,m);(e.hasCartesianSeries||e.mapView)&&this.hasZoom&&x&&!u&&!f&&(this.selectionMarker=f=e.renderer[b](),f.attr({class:"highcharts-selection-marker",zIndex:7}).add(),e.styledMode||f.attr({fill:a||Gh("#334eff").setOpacity(.25).get()})),f&&f.attr(y),x&&!f&&p&&e.pan(t,r)}}dragStart(t){let e=this.chart;e.mouseIsDown=t.type,e.cancelClick=!1,e.mouseDownX=t.chartX,e.mouseDownY=t.chartY}getSelectionBox(t){let e={args:{marker:t},result:t.getBBox()};return se(this,"getSelectionBox",e),e.result}drop(t){let e,{chart:i,selectionMarker:s}=this;for(let r of i.axes)r.isPanning&&(r.isPanning=!1,(r.options.startOnTick||r.options.endOnTick||r.series.some(o=>o.boosted))&&(r.forceRedraw=!0,r.setExtremes(r.userMin,r.userMax,!1),e=!0));if(e&&i.redraw(),s&&t){if(this.hasDragged){let r=this.getSelectionBox(s);i.transform({axes:i.axes.filter(o=>o.zoomEnabled&&(o.coll==="xAxis"&&this.zoomX||o.coll==="yAxis"&&this.zoomY)),selection:{originalEvent:t,xAxis:[],yAxis:[],...r},from:r})}Wi(i.index)&&(this.selectionMarker=s.destroy())}i&&Wi(i.index)&&(Gs(i.container,{cursor:i._cursor}),i.cancelClick=this.hasDragged>10,i.mouseIsDown=!1,this.hasDragged=0,this.pinchDown=[])}findNearestKDPoint(t,e,i){let s;return t.forEach(function(r){let o=!(r.noSharedTooltip&&e)&&0>r.options.findNearestPointBy.indexOf("y"),a=r.searchPoint(i,o);Hi(a,!0)&&a.series&&(!Hi(s,!0)||function(n,h){var u,g;let c=n.distX-h.distX,d=n.dist-h.dist,p=((u=h.series.group)==null?void 0:u.zIndex)-((g=n.series.group)==null?void 0:g.zIndex);return c!==0&&e?c:d!==0?d:p!==0?p:n.series.index>h.series.index?-1:1}(s,a)>0)&&(s=a)}),s}getChartCoordinatesFromPoint(t,e){let{xAxis:i,yAxis:s}=t.series,r=t.shapeArgs;if(i&&s){let o=t.clientX??t.plotX??0,a=t.plotY||0;return t.isNode&&r&&Wi(r.x)&&Wi(r.y)&&(o=r.x,a=r.y),e?{chartX:s.len+s.pos-a,chartY:i.len+i.pos-o}:{chartX:o+i.pos,chartY:a+s.pos}}if(r&&r.x&&r.y)return{chartX:r.x,chartY:r.y}}getChartPosition(){if(this.chartPosition)return this.chartPosition;let{container:t}=this.chart,e=qh(t);this.chartPosition={left:e.left,top:e.top,scaleX:1,scaleY:1};let{offsetHeight:i,offsetWidth:s}=t;return s>2&&i>2&&(this.chartPosition.scaleX=e.width/s,this.chartPosition.scaleY=e.height/i),this.chartPosition}getCoordinates(t){let e={xAxis:[],yAxis:[]};for(let i of this.chart.axes)e[i.isXAxis?"xAxis":"yAxis"].push({axis:i,value:i.toValue(t[i.horiz?"chartX":"chartY"])});return e}getHoverData(t,e,i,s,r,o){let a=[],n=function(u){return u.visible&&!(!r&&u.directTouch)&&$t(u.options.enableMouseTracking,!0)},h=e,c,d={chartX:o?o.chartX:void 0,chartY:o?o.chartY:void 0,shared:r};se(this,"beforeGetHoverData",d),c=h&&!h.stickyTracking?[h]:i.filter(u=>u.stickyTracking&&(d.filter||n)(u));let p=s&&t||!o?t:this.findNearestKDPoint(c,r,o);return h=p&&p.series,p&&(r&&!h.noSharedTooltip?(c=i.filter(function(u){return d.filter?d.filter(u):n(u)&&!u.noSharedTooltip})).forEach(function(u){let g=Ho(u.points,function(m){return m.x===p.x&&!m.isNull});Hi(g)&&(u.boosted&&u.boost&&(g=u.boost.getPoint(g)),a.push(g))}):a.push(p)),se(this,"afterGetHoverData",d={hoverPoint:p}),{hoverPoint:d.hoverPoint,hoverSeries:h,hoverPoints:a}}getPointFromEvent(t){let e=t.target,i;for(;e&&!i;)i=e.point,e=e.parentNode;return i}onTrackerMouseOut(t){let e=this.chart,i=t.relatedTarget,s=e.hoverSeries;this.isDirectTouch=!1,!s||!i||s.stickyTracking||this.inClass(i,"highcharts-tooltip")||this.inClass(i,"highcharts-series-"+s.index)&&this.inClass(i,"highcharts-tracker")||s.onMouseOut()}inClass(t,e){let i=t,s;for(;i;){if(s=Uh(i,"class")){if(s.indexOf(e)!==-1)return!0;if(s.indexOf("highcharts-container")!==-1)return!1}i=i.parentElement}}constructor(t,e){var i;this.hasDragged=0,this.pointerCaptureEventsToUnbind=[],this.eventsToUnbind=[],this.options=e,this.chart=t,this.runChartClick=!!((i=e.chart.events)!=null&&i.click),this.pinchDown=[],this.setDOMEvents(),se(this,"afterInit")}normalize(t,e){let i=t.touches,s=i?i.length?i.item(0):$t(i.changedTouches,t.changedTouches)[0]:t;e||(e=this.getChartPosition());let r=s.pageX-e.left,o=s.pageY-e.top;return js(t,{chartX:Math.round(r/=e.scaleX),chartY:Math.round(o/=e.scaleY)})}onContainerClick(t){let e=this.chart,i=e.hoverPoint,s=this.normalize(t),r=e.plotLeft,o=e.plotTop;!e.cancelClick&&(i&&this.inClass(s.target,"highcharts-tracker")?(se(i.series,"click",js(s,{point:i})),e.hoverPoint&&i.firePointEvent("click",s)):(js(s,this.getCoordinates(s)),e.isInsidePlot(s.chartX-r,s.chartY-o,{visiblePlotOnly:!0})&&se(e,"click",s)))}onContainerMouseDown(t){var i;let e=(1&(t.buttons||t.button))==1;t=this.normalize(t),A.isFirefox&&t.button!==0&&this.onContainerMouseMove(t),(t.button===void 0||e)&&(this.zoomOption(t),e&&((i=t.preventDefault)==null||i.call(t)),this.dragStart(t))}onContainerMouseLeave(t){let{pointer:e}=Ys[$t(xt.hoverChartIndex,-1)]||{};t=this.normalize(t),this.onContainerMouseMove(t),e&&!this.inClass(t.relatedTarget,"highcharts-tooltip")&&(e.reset(),e.chartPosition=void 0)}onContainerMouseEnter(){delete this.chartPosition}onContainerMouseMove(t){let e=this.chart,i=e.tooltip,s=this.normalize(t);this.setHoverChartIndex(t),(e.mouseIsDown==="mousedown"||this.touchSelect(s))&&this.drag(s),!e.openMenu&&(this.inClass(s.target,"highcharts-tracker")||e.isInsidePlot(s.chartX-e.plotLeft,s.chartY-e.plotTop,{visiblePlotOnly:!0}))&&!(i&&i.shouldStickOnContact(s))&&(this.inClass(s.target,"highcharts-no-tooltip")?this.reset(!1,0):this.runPointActions(s))}onDocumentTouchEnd(t){this.onDocumentMouseUp(t)}onContainerTouchMove(t){this.touchSelect(t)?this.onContainerMouseMove(t):this.touch(t)}onContainerTouchStart(t){this.touchSelect(t)?this.onContainerMouseDown(t):(this.zoomOption(t),this.touch(t,!0))}onDocumentMouseMove(t){let e=this.chart,i=e.tooltip,s=this.chartPosition,r=this.normalize(t,s);!s||e.isInsidePlot(r.chartX-e.plotLeft,r.chartY-e.plotTop,{visiblePlotOnly:!0})||i&&i.shouldStickOnContact(r)||r.target!==e.container.ownerDocument&&this.inClass(r.target,"highcharts-tracker")||this.reset()}onDocumentMouseUp(t){var e,i;(i=(e=Ys[$t(xt.hoverChartIndex,-1)])==null?void 0:e.pointer)==null||i.drop(t)}pinch(t){let e=this,{chart:i,hasZoom:s,lastTouches:r}=e,o=[].map.call(t.touches||[],d=>e.normalize(d)),a=o.length,n=a===1&&(e.inClass(t.target,"highcharts-tracker")&&i.runTrackerClick||e.runChartClick),h=i.tooltip,c=a===1&&$t(h==null?void 0:h.options.followTouchMove,!0);a>1?e.initiated=!0:c&&(e.initiated=!1),s&&e.initiated&&!n&&t.cancelable!==!1&&t.preventDefault(),t.type==="touchstart"?(e.pinchDown=o,e.res=!0,i.mouseDownX=t.chartX):c?this.runPointActions(e.normalize(t)):r&&(se(i,"touchpan",{originalEvent:t,touches:o},()=>{let d=p=>{let u=p[0],g=p[1]||u;return{x:u.chartX,y:u.chartY,width:g.chartX-u.chartX,height:g.chartY-u.chartY}};i.transform({axes:i.axes.filter(p=>p.zoomEnabled&&(this.zoomHor&&p.horiz||this.zoomVert&&!p.horiz)),to:d(o),from:d(r),trigger:t.type})}),e.res&&(e.res=!1,this.reset(!1,0))),e.lastTouches=o}reset(t,e){let i=this.chart,s=i.hoverSeries,r=i.hoverPoint,o=i.hoverPoints,a=i.tooltip,n=a&&a.shared?o:r;t&&n&&Xo(n).forEach(function(h){h.series.isCartesian&&h.plotX===void 0&&(t=!1)}),t?a&&n&&Xo(n).length&&(a.refresh(n),a.shared&&o?o.forEach(function(h){h.setState(h.state,!0),h.series.isCartesian&&(h.series.xAxis.crosshair&&h.series.xAxis.drawCrosshair(null,h),h.series.yAxis.crosshair&&h.series.yAxis.drawCrosshair(null,h))}):r&&(r.setState(r.state,!0),i.axes.forEach(function(h){h.crosshair&&r.series[h.coll]===h&&h.drawCrosshair(null,r)}))):(r&&r.onMouseOut(),o&&o.forEach(function(h){h.setState()}),s&&s.onMouseOut(),a&&a.hide(e),this.unDocMouseMove&&(this.unDocMouseMove=this.unDocMouseMove()),i.axes.forEach(function(h){h.hideCrosshair()}),i.hoverPoints=i.hoverPoint=void 0)}runPointActions(t,e,i){let s=this.chart,r=s.series,o=s.tooltip&&s.tooltip.options.enabled?s.tooltip:void 0,a=!!o&&o.shared,n=e||s.hoverPoint,h=n&&n.series||s.hoverSeries,c=(!t||t.type!=="touchmove")&&(!!e||h&&h.directTouch&&this.isDirectTouch),d=this.getHoverData(n,h,r,c,a,t);n=d.hoverPoint,h=d.hoverSeries;let p=d.hoverPoints,u=h&&h.tooltipOptions.followPointer&&!h.tooltipOptions.split,g=a&&h&&!h.noSharedTooltip;if(n&&(i||n!==s.hoverPoint||o&&o.isHidden)){if((s.hoverPoints||[]).forEach(function(m){p.indexOf(m)===-1&&m.setState()}),s.hoverSeries!==h&&h.onMouseOver(),this.applyInactiveState(p),(p||[]).forEach(function(m){m.setState("hover")}),s.hoverPoint&&s.hoverPoint.firePointEvent("mouseOut"),!n.series)return;s.hoverPoints=p,s.hoverPoint=n,n.firePointEvent("mouseOver",void 0,()=>{o&&n&&o.refresh(g?p:n,t)})}else if(u&&o&&!o.isHidden){let m=o.getAnchor([{}],t);s.isInsidePlot(m[0],m[1],{visiblePlotOnly:!0})&&o.updatePosition({plotX:m[0],plotY:m[1]})}this.unDocMouseMove||(this.unDocMouseMove=Rt(s.container.ownerDocument,"mousemove",m=>{var x,f;return(f=(x=Ys[xt.hoverChartIndex??-1])==null?void 0:x.pointer)==null?void 0:f.onDocumentMouseMove(m)}),this.eventsToUnbind.push(this.unDocMouseMove)),s.axes.forEach(function(m){let x,f=$t((m.crosshair||{}).snap,!0);!f||(x=s.hoverPoint)&&x.series[m.coll]===m||(x=Ho(p,b=>b.series&&b.series[m.coll]===m)),x||!f?m.drawCrosshair(t,x):m.hideCrosshair()})}setDOMEvents(){let t=this.chart.container,e=t.ownerDocument;t.onmousedown=this.onContainerMouseDown.bind(this),t.onmousemove=this.onContainerMouseMove.bind(this),t.onclick=this.onContainerClick.bind(this),this.eventsToUnbind.push(Rt(t,"mouseenter",this.onContainerMouseEnter.bind(this)),Rt(t,"mouseleave",this.onContainerMouseLeave.bind(this))),xt.unbindDocumentMouseUp.some(s=>s.doc===e)||xt.unbindDocumentMouseUp.push({doc:e,unbind:Rt(e,"mouseup",this.onDocumentMouseUp.bind(this))});let i=this.chart.renderTo.parentElement;for(;i&&i.tagName!=="BODY";)this.eventsToUnbind.push(Rt(i,"scroll",()=>{delete this.chartPosition})),i=i.parentElement;this.eventsToUnbind.push(Rt(t,"touchstart",this.onContainerTouchStart.bind(this),{passive:!1}),Rt(t,"touchmove",this.onContainerTouchMove.bind(this),{passive:!1})),xt.unbindDocumentTouchEnd||(xt.unbindDocumentTouchEnd=Rt(e,"touchend",this.onDocumentTouchEnd.bind(this),{passive:!1})),this.setPointerCapture(),Rt(this.chart,"redraw",this.setPointerCapture.bind(this))}setPointerCapture(){var r,o;if(!$h)return;let t=this.pointerCaptureEventsToUnbind,e=this.chart,i=e.container,s=$t((r=e.options.tooltip)==null?void 0:r.followTouchMove,!0)&&e.series.some(a=>a.options.findNearestPointBy.indexOf("y")>-1);!this.hasPointerCapture&&s?(t.push(Rt(i,"pointerdown",a=>{var n,h;(n=a.target)!=null&&n.hasPointerCapture(a.pointerId)&&((h=a.target)==null||h.releasePointerCapture(a.pointerId))}),Rt(i,"pointermove",a=>{var n,h;(h=(n=e.pointer)==null?void 0:n.getPointFromEvent(a))==null||h.onMouseOver(a)})),e.styledMode||Gs(i,{"touch-action":"none"}),i.className+=" highcharts-no-touch-action",this.hasPointerCapture=!0):this.hasPointerCapture&&!s&&(t.forEach(a=>a()),t.length=0,e.styledMode||Gs(i,{"touch-action":$t((o=e.options.chart.style)==null?void 0:o["touch-action"],"manipulation")}),i.className=i.className.replace(" highcharts-no-touch-action",""),this.hasPointerCapture=!1)}setHoverChartIndex(t){var s;let e=this.chart,i=A.charts[$t(xt.hoverChartIndex,-1)];if(i&&i!==e){let r={relatedTarget:e.container};t&&!(t!=null&&t.relatedTarget)&&Object.assign({},t,r),(s=i.pointer)==null||s.onContainerMouseLeave(t||r)}i&&i.mouseIsDown||(xt.hoverChartIndex=e.index)}touch(t,e){let i,{chart:s,pinchDown:r=[]}=this;this.setHoverChartIndex(),(t=this.normalize(t)).touches.length===1?s.isInsidePlot(t.chartX-s.plotLeft,t.chartY-s.plotTop,{visiblePlotOnly:!0})&&!s.openMenu?(e&&this.runPointActions(t),t.type==="touchmove"&&(i=!!r[0]&&Math.pow(r[0].chartX-t.chartX,2)+Math.pow(r[0].chartY-t.chartY,2)>=16),$t(i,!0)&&this.pinch(t)):e&&this.reset():t.touches.length===2&&this.pinch(t)}touchSelect(t){return!!(this.chart.zooming.singleTouch&&t.touches&&t.touches.length===1)}zoomOption(t){let e=this.chart,i=e.inverted,s=e.zooming.type||"",r,o;/touch/.test(t.type)&&(s=$t(e.zooming.pinchType,s)),this.zoomX=r=/x/.test(s),this.zoomY=o=/y/.test(s),this.zoomHor=r&&!i||o&&i,this.zoomVert=o&&!i||r&&i,this.hasZoom=r||o}}xt.unbindDocumentMouseUp=[],function(l){l.compose=function(t){Zh(jh,"Core.Pointer")&&Rt(t,"beforeRender",function(){this.pointer=new l(this,this.options)})}}(xt||(xt={}));let Fo=xt,{fireEvent:Yo,isArray:Kh,objectEach:Xi,uniqueKey:Fi}=R,Yi=class{constructor(l={}){this.autoId=!l.id,this.columns={},this.id=l.id||Fi(),this.modified=this,this.rowCount=0,this.versionTag=Fi();let t=0;Xi(l.columns||{},(e,i)=>{this.columns[i]=e.slice(),t=Math.max(t,e.length)}),this.applyRowCount(t)}applyRowCount(l){this.rowCount=l,Xi(this.columns,t=>{Kh(t)&&(t.length=l)})}getColumn(l,t){return this.columns[l]}getColumns(l,t){return(l||Object.keys(this.columns)).reduce((e,i)=>(e[i]=this.columns[i],e),{})}getRow(l,t){return(t||Object.keys(this.columns)).map(e=>{var i;return(i=this.columns[e])==null?void 0:i[l]})}setColumn(l,t=[],e=0,i){this.setColumns({[l]:t},e,i)}setColumns(l,t,e){let i=this.rowCount;Xi(l,(s,r)=>{this.columns[r]=s.slice(),i=s.length}),this.applyRowCount(i),e!=null&&e.silent||(Yo(this,"afterSetColumns"),this.versionTag=Fi())}setRow(l,t=this.rowCount,e,i){let{columns:s}=this,r=e?this.rowCount+1:t+1;Xi(l,(o,a)=>{let n=s[a]||(i==null?void 0:i.addColumns)!==!1&&Array(r);n&&(e?n.splice(t,0,o):n[t]=o,s[a]=n)}),r>this.rowCount&&this.applyRowCount(r),i!=null&&i.silent||(Yo(this,"afterSetRows"),this.versionTag=Fi())}},{extend:_h,merge:Qh,pick:Go}=R;(function(l){function t(e,i,s){var v,w;let r=this.legendItem=this.legendItem||{},{chart:o,options:a}=this,{baseline:n=0,symbolWidth:h,symbolHeight:c}=e,d=this.symbol||"circle",p=c/2,u=o.renderer,g=r.group,m=n-Math.round((((v=e.fontMetrics)==null?void 0:v.b)||c)*(s?.4:.3)),x={},f,b=a.marker,y=0;if(o.styledMode||(x["stroke-width"]=Math.min(a.lineWidth||0,24),a.dashStyle?x.dashstyle=a.dashStyle:a.linecap==="square"||(x["stroke-linecap"]="round")),r.line=u.path().addClass("highcharts-graph").attr(x).add(g),s&&(r.area=u.path().addClass("highcharts-area").add(g)),x["stroke-linecap"]&&(y=Math.min(r.line.strokeWidth(),h)/2),h){let k=[["M",y,m],["L",h-y,m]];r.line.attr({d:k}),(w=r.area)==null||w.attr({d:[...k,["L",h-y,n],["L",y,n]]})}if(b&&b.enabled!==!1&&h){let k=Math.min(Go(b.radius,p),p);d.indexOf("url")===0&&(b=Qh(b,{width:c,height:c}),k=0),r.symbol=f=u.symbol(d,h/2-k,m-k,2*k,2*k,_h({context:"legend"},b)).addClass("highcharts-point").add(g),f.isMarker=!0}}l.areaMarker=function(e,i){t.call(this,e,i,!0)},l.lineMarker=t,l.rectangle=function(e,i){let s=i.legendItem||{},r=e.options,o=e.symbolHeight,a=r.squareSymbol,n=a?o:e.symbolWidth;s.symbol=this.chart.renderer.rect(a?(e.symbolWidth-o)/2:0,e.baseline-o+1,n,o,Go(e.options.symbolRadius,o/2)).addClass("highcharts-point").attr({zIndex:3}).add(s.group)}})(Ct||(Ct={}));let jo=Ct,{defaultOptions:$o}=zt,{extend:Jh,extendClass:td,merge:ed}=R;(function(l){function t(e,i){let s=$o.plotOptions||{},r=i.defaultOptions,o=i.prototype;return o.type=e,o.pointClass||(o.pointClass=ie),!l.seriesTypes[e]&&(r&&(s[e]=r),l.seriesTypes[e]=i,!0)}l.seriesTypes=A.seriesTypes,l.registerSeriesType=t,l.seriesType=function(e,i,s,r,o){let a=$o.plotOptions||{};if(i=i||"",a[e]=ed(a[i],s),delete l.seriesTypes[e],t(e,td(l.seriesTypes[i]||function(){},r)),l.seriesTypes[e].prototype.type=e,o){class n extends ie{}Jh(n.prototype,o),l.seriesTypes[e].prototype.pointClass=n}return l.seriesTypes[e]}})(kt||(kt={}));let ht=kt,{animObject:Uo,setAnimation:id}=St,{defaultOptions:Gi}=zt,{registerEventOptions:sd}=Ci,{svg:rd,win:od}=A,{seriesTypes:Ie}=ht,{arrayMax:$s,arrayMin:Vo,clamp:qo,correctFloat:Zo,crisp:ad,defined:yt,destroyObjectProperties:nd,diffObjects:ld,erase:Ko,error:ji,extend:Be,find:hd,fireEvent:st,getClosestDistance:dd,getNestedProperty:_o,insertItem:Qo,isArray:Jo,isNumber:wt,isString:cd,merge:Ne,objectEach:Us,pick:rt,removeEvent:pd,syncTimeout:ta}=R;class ze{constructor(){this.zoneAxis="y"}init(t,e){let i;st(this,"init",{options:e}),this.dataTable??(this.dataTable=new Yi);let s=t.series;this.eventsToUnbind=[],this.chart=t,this.options=this.setOptions(e);let r=this.options,o=r.visible!==!1;this.linkedSeries=[],this.bindAxes(),Be(this,{name:r.name,state:"",visible:o,selected:r.selected===!0}),sd(this,r);let a=r.events;(a&&a.click||r.point&&r.point.events&&r.point.events.click||r.allowPointSelect)&&(t.runTrackerClick=!0),this.getColor(),this.getSymbol(),this.isCartesian&&(t.hasCartesianSeries=!0),s.length&&(i=s[s.length-1]),this._i=rt(i&&i._i,-1)+1,this.opacity=this.options.opacity,t.orderItems("series",Qo(this,s)),r.dataSorting&&r.dataSorting.enabled?this.setDataSortingOptions():this.points||this.data||this.setData(r.data,!1),st(this,"afterInit")}is(t){return Ie[t]&&this instanceof Ie[t]}bindAxes(){let t,e=this,i=e.options,s=e.chart;st(this,"bindAxes",null,function(){(e.axisTypes||[]).forEach(function(r){(s[r]||[]).forEach(function(o){t=o.options,(rt(i[r],0)===o.index||i[r]!==void 0&&i[r]===t.id)&&(Qo(e,o.series),e[r]=o,o.isDirty=!0)}),e[r]||e.optionalAxis===r||ji(18,!0,s)})}),st(this,"afterBindAxes")}hasData(){return this.visible&&this.dataMax!==void 0&&this.dataMin!==void 0||this.visible&&this.dataTable.rowCount>0}hasMarkerChanged(t,e){let i=t.marker,s=e.marker||{};return i&&(s.enabled&&!i.enabled||s.symbol!==i.symbol||s.height!==i.height||s.width!==i.width)}autoIncrement(t){let e,i=this.options,{pointIntervalUnit:s,relativeXValue:r}=this.options,o=this.chart.time,a=this.xIncrement??o.parse(i.pointStart)??0;if(this.pointInterval=e=rt(this.pointInterval,i.pointInterval,1),r&&wt(t)&&(e*=t),s){let n=o.toParts(a);s==="day"?n[2]+=e:s==="month"?n[1]+=e:s==="year"&&(n[0]+=e),e=o.makeTime.apply(o,n)-a}return r&&wt(t)?a+e:(this.xIncrement=a+e,a)}setDataSortingOptions(){let t=this.options;Be(this,{requireSorting:!1,sorted:!1,enabledDataSorting:!0,allowDG:!1}),yt(t.pointRange)||(t.pointRange=1)}setOptions(t){var x,f;let e,i=this.chart,s=i.options.plotOptions,r=i.userOptions||{},o=Ne(t),a=i.styledMode,n={plotOptions:s,userOptions:o};st(this,"setOptions",n);let h=n.plotOptions[this.type],c=r.plotOptions||{},d=c.series||{},p=Gi.plotOptions[this.type]||{},u=c[this.type]||{};this.userOptions=n.userOptions;let g=Ne(h,s.series,u,o);this.tooltipOptions=Ne(Gi.tooltip,(x=Gi.plotOptions.series)==null?void 0:x.tooltip,p==null?void 0:p.tooltip,i.userOptions.tooltip,(f=c.series)==null?void 0:f.tooltip,u.tooltip,o.tooltip),this.stickyTracking=rt(o.stickyTracking,u.stickyTracking,d.stickyTracking,!!this.tooltipOptions.shared&&!this.noSharedTooltip||g.stickyTracking),h.marker===null&&delete g.marker,this.zoneAxis=g.zoneAxis||"y";let m=this.zones=(g.zones||[]).map(b=>({...b}));return(g.negativeColor||g.negativeFillColor)&&!g.zones&&(e={value:g[this.zoneAxis+"Threshold"]||g.threshold||0,className:"highcharts-negative"},a||(e.color=g.negativeColor,e.fillColor=g.negativeFillColor),m.push(e)),m.length&&yt(m[m.length-1].value)&&m.push(a?{}:{color:this.color,fillColor:this.fillColor}),st(this,"afterSetOptions",{options:g}),g}getName(){return rt(this.options.name,"Series "+(this.index+1))}getCyclic(t,e,i){let s,r,o=this.chart,a=`${t}Index`,n=`${t}Counter`,h=(i==null?void 0:i.length)||o.options.chart.colorCount;!e&&(yt(r=rt(t==="color"?this.options.colorIndex:void 0,this[a]))?s=r:(o.series.length||(o[n]=0),s=o[n]%h,o[n]+=1),i&&(e=i[s])),s!==void 0&&(this[a]=s),this[t]=e}getColor(){this.chart.styledMode?this.getCyclic("color"):this.options.colorByPoint?this.color="#cccccc":this.getCyclic("color",this.options.color||Gi.plotOptions[this.type].color,this.chart.options.colors)}getPointsCollection(){return(this.hasGroupedData?this.points:this.data)||[]}getSymbol(){let t=this.options.marker;this.getCyclic("symbol",t.symbol,this.chart.options.symbols)}getColumn(t,e){return(e?this.dataTable.modified:this.dataTable).getColumn(t,!0)||[]}findPointIndex(t,e){let i,s,r,o=t.id,a=t.x,n=this.points,h=this.options.dataSorting;if(o){let c=this.chart.get(o);c instanceof ie&&(i=c)}else if(this.linkedParent||this.enabledDataSorting||this.options.relativeXValue){let c=d=>!d.touched&&d.index===t.index;if(h&&h.matchByName?c=d=>!d.touched&&d.name===t.name:this.options.relativeXValue&&(c=d=>!d.touched&&d.options.x===t.x),!(i=hd(n,c)))return}return i&&(r=i&&i.index)!==void 0&&(s=!0),r===void 0&&wt(a)&&(r=this.getColumn("x").indexOf(a,e)),r!==-1&&r!==void 0&&this.cropped&&(r=r>=this.cropStart?r-this.cropStart:r),!s&&wt(r)&&n[r]&&n[r].touched&&(r=void 0),r}updateData(t,e){let i=this.options,s=i.dataSorting,r=this.points,o=[],a=this.requireSorting,n=t.length===r.length,h,c,d,p,u=!0;if(this.xIncrement=null,t.forEach(function(m,x){let f,b=yt(m)&&this.pointClass.prototype.optionsToObject.call({series:this},m)||{},y=b.x;b.id||wt(y)?((f=this.findPointIndex(b,p))===-1||f===void 0?o.push(m):r[f]&&m!==i.data[f]?(r[f].update(m,!1,null,!1),r[f].touched=!0,a&&(p=f+1)):r[f]&&(r[f].touched=!0),(!n||x!==f||s&&s.enabled||this.hasDerivedData)&&(h=!0)):o.push(m)},this),h)for(c=r.length;c--;)(d=r[c])&&!d.touched&&d.remove&&d.remove(!1,e);else!n||s&&s.enabled?u=!1:(t.forEach(function(m,x){m===r[x].y||r[x].destroyed||r[x].update(m,!1,null,!1)}),o.length=0);if(r.forEach(function(m){m&&(m.touched=!1)}),!u)return!1;o.forEach(function(m){this.addPoint(m,!1,null,null,!1)},this);let g=this.getColumn("x");return this.xIncrement===null&&g.length&&(this.xIncrement=$s(g),this.autoIncrement()),!0}dataColumnKeys(){return["x",...this.pointArrayMap||["y"]]}setData(t,e=!0,i,s){var S,M;let r=this.points,o=r&&r.length||0,a=this.options,n=this.chart,h=a.dataSorting,c=this.xAxis,d=a.turboThreshold,p=this.dataTable,u=this.dataColumnKeys(),g=this.pointValKey||"y",m=(this.pointArrayMap||[]).length,x=a.keys,f,b,y=0,v=1,w;n.options.chart.allowMutatingData||(a.data&&delete this.options.data,this.userOptions.data&&delete this.userOptions.data,w=Ne(!0,t));let k=(t=w||t||[]).length;if(h&&h.enabled&&(t=this.sortData(t)),n.options.chart.allowMutatingData&&s!==!1&&k&&o&&!this.cropped&&!this.hasGroupedData&&this.visible&&!this.boosted&&(b=this.updateData(t,i)),!b){this.xIncrement=null,this.colorCounter=0;let T=d&&k>d;if(T){let P=this.getFirstValidPoint(t),L=this.getFirstValidPoint(t,k-1,-1),E=C=>!!(Jo(C)&&(x||wt(C[0])));if(wt(P)&&wt(L)){let C=[],O=[];for(let N of t)C.push(this.autoIncrement()),O.push(N);p.setColumns({x:C,[g]:O})}else if(E(P)&&E(L))if(m){let C=P.length===m?1:0,O=Array(u.length).fill(0).map(()=>[]);for(let N of t){C&&O[0].push(this.autoIncrement());for(let F=C;F<=m;F++)(S=O[F])==null||S.push(N[F-C])}p.setColumns(u.reduce((N,F,I)=>(N[F]=O[I],N),{}))}else{x&&(y=x.indexOf("x"),v=x.indexOf("y"),y=y>=0?y:0,v=v>=0?v:1),P.length===1&&(v=0);let C=[],O=[];if(y===v)for(let N of t)C.push(this.autoIncrement()),O.push(N[v]);else for(let N of t)C.push(N[y]),O.push(N[v]);p.setColumns({x:C,[g]:O})}else T=!1}if(!T){let P=u.reduce((L,E)=>(L[E]=[],L),{});for(f=0;f<k;f++){let L=this.pointClass.prototype.applyOptions.apply({series:this},[t[f]]);for(let E of u)P[E][f]=L[E]}p.setColumns(P)}for(cd(this.getColumn("y")[0])&&ji(14,!0,n),this.data=[],this.options.data=this.userOptions.data=t,f=o;f--;)(M=r[f])==null||M.destroy();c&&(c.minRange=c.userMinRange),this.isDirty=n.isDirtyBox=!0,this.isDirtyData=!!r,i=!1}a.legendType==="point"&&(this.processData(),this.generatePoints()),e&&n.redraw(i)}sortData(t){let e=this,i=e.options.dataSorting.sortKey||"y",s=function(r,o){return yt(o)&&r.pointClass.prototype.optionsToObject.call({series:r},o)||{}};return t.forEach(function(r,o){t[o]=s(e,r),t[o].index=o},this),t.concat().sort((r,o)=>{let a=_o(i,r),n=_o(i,o);return n<a?-1:n>a?1:0}).forEach(function(r,o){r.x=o},this),e.linkedSeries&&e.linkedSeries.forEach(function(r){let o=r.options,a=o.data;o.dataSorting&&o.dataSorting.enabled||!a||(a.forEach(function(n,h){a[h]=s(r,n),t[h]&&(a[h].x=t[h].x,a[h].index=h)}),r.setData(a,!1))}),t}getProcessedData(t){let e=this,{dataTable:i,isCartesian:s,options:r,xAxis:o}=e,a=r.cropThreshold,n=t||e.getExtremesFromAll,h=o==null?void 0:o.logarithmic,c=i.rowCount,d,p,u=0,g,m,x,f=e.getColumn("x"),b=i,y=!1;return o&&(m=(g=o.getExtremes()).min,x=g.max,y=!!(o.categories&&!o.names.length),s&&e.sorted&&!n&&(!a||c>a||e.forceCrop)&&(f[c-1]<m||f[0]>x?b=new Yi:e.getColumn(e.pointValKey||"y").length&&(f[0]<m||f[c-1]>x)&&(b=(d=this.cropData(i,m,x)).modified,u=d.start,p=!0))),f=b.getColumn("x")||[],{modified:b,cropped:p,cropStart:u,closestPointRange:dd([h?f.map(h.log2lin):f],()=>e.requireSorting&&!y&&ji(15,!1,e.chart))}}processData(t){let e=this.xAxis,i=this.dataTable;if(this.isCartesian&&!this.isDirty&&!e.isDirty&&!this.yAxis.isDirty&&!t)return!1;let s=this.getProcessedData();i.modified=s.modified,this.cropped=s.cropped,this.cropStart=s.cropStart,this.closestPointRange=this.basePointRange=s.closestPointRange,st(this,"afterProcessData")}cropData(t,e,i){let s=t.getColumn("x",!0)||[],r=s.length,o={},a,n,h=0,c=r;for(a=0;a<r;a++)if(s[a]>=e){h=Math.max(0,a-1);break}for(n=a;n<r;n++)if(s[n]>i){c=n+1;break}for(let d of this.dataColumnKeys()){let p=t.getColumn(d,!0);p&&(o[d]=p.slice(h,c))}return{modified:new Yi({columns:o}),start:h,end:c}}generatePoints(){var w,k;let t=this.options,e=this.processedData||t.data,i=this.dataTable.modified,s=this.getColumn("x",!0),r=this.pointClass,o=i.rowCount,a=this.cropStart||0,n=this.hasGroupedData,h=t.keys,c=[],d=t.dataGrouping&&t.dataGrouping.groupAll?a:0,p=(w=this.xAxis)==null?void 0:w.categories,u=this.pointArrayMap||["y"],g=this.dataColumnKeys(),m,x,f,b,y=this.data,v;if(!y&&!n){let S=[];S.length=(e==null?void 0:e.length)||0,y=this.data=S}for(h&&n&&(this.options.keys=!1),b=0;b<o;b++)x=a+b,n?((f=new r(this,i.getRow(b,g)||[])).dataGroup=this.groupMap[d+b],(k=f.dataGroup)!=null&&k.options&&(f.options=f.dataGroup.options,Be(f,f.dataGroup.options),delete f.dataLabels)):(f=y[x],v=e?e[x]:i.getRow(b,u),f||v===void 0||(y[x]=f=new r(this,v,s[b]))),f&&(f.index=n?d+b:x,c[b]=f,f.category=(p==null?void 0:p[f.x])??f.x,f.key=f.name??f.category);if(this.options.keys=h,y&&(o!==(m=y.length)||n))for(b=0;b<m;b++)b!==a||n||(b+=o),y[b]&&(y[b].destroyElements(),y[b].plotX=void 0);this.data=y,this.points=c,st(this,"afterGeneratePoints")}getXExtremes(t){return{min:Vo(t),max:$s(t)}}getExtremes(t,e){var w;let{xAxis:i,yAxis:s}=this,r=e||this.getExtremesFromAll||this.options.getExtremesFromAll,o=r&&this.cropped?this.dataTable:this.dataTable.modified,a=o.rowCount,n=t||this.stackedYData,h=n?[n]:((w=this.keysAffectYAxis||this.pointArrayMap||["y"])==null?void 0:w.map(k=>o.getColumn(k,!0)||[]))||[],c=this.getColumn("x",!0),d=[],p=this.requireSorting&&!this.is("column")?1:0,u=!!s&&s.positiveValuesOnly,g=r||this.cropped||!i,m,x,f,b=0,y=0;for(i&&(b=(m=i.getExtremes()).min,y=m.max),f=0;f<a;f++)if(x=c[f],g||(c[f+p]||x)>=b&&(c[f-p]||x)<=y)for(let k of h){let S=k[f];wt(S)&&(S>0||!u)&&d.push(S)}let v={activeYData:d,dataMin:Vo(d),dataMax:$s(d)};return st(this,"afterGetExtremes",{dataExtremes:v}),v}applyExtremes(){let t=this.getExtremes();return this.dataMin=t.dataMin,this.dataMax=t.dataMax,t}getFirstValidPoint(t,e=0,i=1){let s=t.length,r=e;for(;r>=0&&r<s;){if(yt(t[r]))return t[r];r+=i}}translate(){var b;this.generatePoints();let t=this.options,e=t.stacking,i=this.xAxis,s=this.enabledDataSorting,r=this.yAxis,o=this.points,a=o.length,n=this.pointPlacementToXValue(),h=!!n,c=t.threshold,d=t.startFromThreshold?c:0,p,u,g,m,x=Number.MAX_VALUE;function f(y){return qo(y,-1e9,1e9)}for(p=0;p<a;p++){let y,v=o[p],w=v.x,k,S,M=v.y,T=v.low,P=e&&((b=r.stacking)==null?void 0:b.stacks[(this.negStacks&&M<(d?0:c)?"-":"")+this.stackKey]);u=i.translate(w,!1,!1,!1,!0,n),v.plotX=wt(u)?Zo(f(u)):void 0,e&&this.visible&&P&&P[w]&&(m=this.getStackIndicator(m,w,this.index),!v.isNull&&m.key&&(S=(k=P[w]).points[m.key]),k&&Jo(S)&&(T=S[0],M=S[1],T===d&&m.key===P[w].base&&(T=rt(wt(c)?c:r.min)),r.positiveValuesOnly&&yt(T)&&T<=0&&(T=void 0),v.total=v.stackTotal=rt(k.total),v.percentage=yt(v.y)&&k.total?v.y/k.total*100:void 0,v.stackY=M,this.irregularWidths||k.setOffset(this.pointXOffset||0,this.barW||0,void 0,void 0,void 0,this.xAxis))),v.yBottom=yt(T)?f(r.translate(T,!1,!0,!1,!0)):void 0,this.dataModify&&(M=this.dataModify.modifyValue(M,p)),wt(M)&&v.plotX!==void 0&&(y=wt(y=r.translate(M,!1,!0,!1,!0))?f(y):void 0),v.plotY=y,v.isInside=this.isPointInside(v),v.clientX=h?Zo(i.translate(w,!1,!1,!1,!0,n)):u,v.negative=(v.y||0)<(c||0),v.isNull||v.visible===!1||(g!==void 0&&(x=Math.min(x,Math.abs(u-g))),g=u),v.zone=this.zones.length?v.getZone():void 0,!v.graphic&&this.group&&s&&(v.isNew=!0)}this.closestPointRangePx=x,st(this,"afterTranslate")}getValidPoints(t,e,i){let s=this.chart;return(t||this.points||[]).filter(function(r){let{plotX:o,plotY:a}=r;return!!((i||!r.isNull&&wt(a))&&(!e||s.isInsidePlot(o,a,{inverted:s.inverted})))&&r.visible!==!1})}getClipBox(){let{chart:t,xAxis:e,yAxis:i}=this,{x:s,y:r,width:o,height:a}=Ne(t.clipBox);return e&&e.len!==t.plotSizeX&&(o=e.len),i&&i.len!==t.plotSizeY&&(a=i.len),t.inverted&&!this.invertible&&([o,a]=[a,o]),{x:s,y:r,width:o,height:a}}getSharedClipKey(){return this.sharedClipKey=(this.options.xAxis||0)+","+(this.options.yAxis||0),this.sharedClipKey}setClip(){let{chart:t,group:e,markerGroup:i}=this,s=t.sharedClips,r=t.renderer,o=this.getClipBox(),a=this.getSharedClipKey(),n=s[a];n?n.animate(o):s[a]=n=r.clipRect(o),e&&e.clip(this.options.clip===!1?void 0:n),i&&i.clip()}animate(t){let{chart:e,group:i,markerGroup:s}=this,r=e.inverted,o=Uo(this.options.animation),a=[this.getSharedClipKey(),o.duration,o.easing,o.defer].join(","),n=e.sharedClips[a],h=e.sharedClips[a+"m"];if(t&&i){let c=this.getClipBox();if(n)n.attr("height",c.height);else{c.width=0,r&&(c.x=e.plotHeight),n=e.renderer.clipRect(c),e.sharedClips[a]=n;let d={x:-99,y:-99,width:r?e.plotWidth+199:99,height:r?99:e.plotHeight+199};h=e.renderer.clipRect(d),e.sharedClips[a+"m"]=h}i.clip(n),s==null||s.clip(h)}else if(n&&!n.hasClass("highcharts-animating")){let c=this.getClipBox(),d=o.step;(s!=null&&s.element.childNodes.length||e.series.length>1)&&(o.step=function(p,u){d&&d.apply(u,arguments),u.prop==="width"&&(h!=null&&h.element)&&h.attr(r?"height":"width",p+99)}),n.addClass("highcharts-animating").animate(c,o)}}afterAnimate(){this.setClip(),Us(this.chart.sharedClips,(t,e,i)=>{t&&!this.chart.container.querySelector(`[clip-path="url(#${t.id})"]`)&&(t.destroy(),delete i[e])}),this.finishedAnimating=!0,st(this,"afterAnimate")}drawPoints(t=this.points){let e,i,s,r,o,a,n,h=this.chart,c=h.styledMode,{colorAxis:d,options:p}=this,u=p.marker,g=this[this.specialGroup||"markerGroup"],m=this.xAxis,x=rt(u.enabled,!m||!!m.isRadial||null,this.closestPointRangePx>=u.enabledThreshold*u.radius);if(u.enabled!==!1||this._hasPointMarkers)for(e=0;e<t.length;e++)if(r=(s=(i=t[e]).graphic)?"animate":"attr",o=i.marker||{},a=!!i.marker,(x&&o.enabled===void 0||o.enabled)&&!i.isNull&&i.visible!==!1){let f=rt(o.symbol,this.symbol,"rect");n=this.markerAttribs(i,i.selected&&"select"),this.enabledDataSorting&&(i.startXPos=m.reversed?-(n.width||0):m.width);let b=i.isInside!==!1;if(!s&&b&&((n.width||0)>0||i.hasImage)&&(i.graphic=s=h.renderer.symbol(f,n.x,n.y,n.width,n.height,a?o:u).add(g),this.enabledDataSorting&&h.hasRendered&&(s.attr({x:i.startXPos}),r="animate")),s&&r==="animate"&&s[b?"show":"hide"](b).animate(n),s){let y=this.pointAttribs(i,c||!i.selected?void 0:"select");c?d&&s.css({fill:y.fill}):s[r](y)}s&&s.addClass(i.getClassName(),!0)}else s&&(i.graphic=s.destroy())}markerAttribs(t,e){let i=this.options,s=i.marker,r=t.marker||{},o=r.symbol||s.symbol,a={},n,h,c=rt(r.radius,s&&s.radius);e&&(n=s.states[e],c=rt((h=r.states&&r.states[e])&&h.radius,n&&n.radius,c&&c+(n&&n.radiusPlus||0))),t.hasImage=o&&o.indexOf("url")===0,t.hasImage&&(c=0);let d=t.pos();return wt(c)&&d&&(i.crisp&&(d[0]=ad(d[0],t.hasImage?0:o==="rect"?(s==null?void 0:s.lineWidth)||0:1)),a.x=d[0]-c,a.y=d[1]-c),c&&(a.width=a.height=2*c),a}pointAttribs(t,e){let i=this.options.marker,s=t&&t.options,r=s&&s.marker||{},o=s&&s.color,a=t&&t.color,n=t&&t.zone&&t.zone.color,h,c,d=this.color,p,u,g=rt(r.lineWidth,i.lineWidth),m=1;return d=o||n||a||d,p=r.fillColor||i.fillColor||d,u=r.lineColor||i.lineColor||d,e=e||"normal",h=i.states[e]||{},g=rt((c=r.states&&r.states[e]||{}).lineWidth,h.lineWidth,g+rt(c.lineWidthPlus,h.lineWidthPlus,0)),p=c.fillColor||h.fillColor||p,{stroke:u=c.lineColor||h.lineColor||u,"stroke-width":g,fill:p,opacity:m=rt(c.opacity,h.opacity,m)}}destroy(t){let e,i,s,r=this,o=r.chart,a=/AppleWebKit\/533/.test(od.navigator.userAgent),n=r.data||[];for(st(r,"destroy",{keepEventsForUpdate:t}),this.removeEvents(t),(r.axisTypes||[]).forEach(function(h){(s=r[h])&&s.series&&(Ko(s.series,r),s.isDirty=s.forceRedraw=!0)}),r.legendItem&&r.chart.legend.destroyItem(r),e=n.length;e--;)(i=n[e])&&i.destroy&&i.destroy();for(let h of r.zones)nd(h,void 0,!0);R.clearTimeout(r.animationTimeout),Us(r,function(h,c){h instanceof qt&&!h.survive&&h[a&&c==="group"?"hide":"destroy"]()}),o.hoverSeries===r&&(o.hoverSeries=void 0),Ko(o.series,r),o.orderItems("series"),Us(r,function(h,c){t&&c==="hcEvents"||delete r[c]})}applyZones(){let{area:t,chart:e,graph:i,zones:s,points:r,xAxis:o,yAxis:a,zoneAxis:n}=this,{inverted:h,renderer:c}=e,d=this[`${n}Axis`],{isXAxis:p,len:u=0,minPointOffset:g=0}=d||{},m=((i==null?void 0:i.strokeWidth())||0)/2+1,x=(f,b=0,y=0)=>{h&&(y=u-y);let{translated:v=0,lineClip:w}=f,k=y-v;w==null||w.push(["L",b,Math.abs(k)<m?y-m*(k<=0?-1:1):v])};if(s.length&&(i||t)&&d&&wt(d.min)){let f=d.getExtremes().max+g,b=w=>{w.forEach((k,S)=>{(k[0]==="M"||k[0]==="L")&&(w[S]=[k[0],p?u-k[1]:k[1],p?k[2]:u-k[2]])})};if(s.forEach(w=>{w.lineClip=[],w.translated=qo(d.toPixels(rt(w.value,f),!0)||0,0,u)}),i&&!this.showLine&&i.hide(),t&&t.hide(),n==="y"&&r.length<o.len)for(let w of r){let{plotX:k,plotY:S,zone:M}=w,T=M&&s[s.indexOf(M)-1];M&&x(M,k,S),T&&x(T,k,S)}let y=[],v=d.toPixels(d.getExtremes().min-g,!0);s.forEach(w=>{var F,I;let k=w.lineClip||[],S=Math.round(w.translated||0);o.reversed&&k.reverse();let{clip:M,simpleClip:T}=w,P=0,L=0,E=o.len,C=a.len;p?(P=S,E=v):(L=S,C=v);let O=[["M",P,L],["L",E,L],["L",E,C],["L",P,C],["Z"]],N=[O[0],...k,O[1],O[2],...y,O[3],O[4]];y=k.reverse(),v=S,h&&(b(N),t&&b(O)),M?(M.animate({d:N}),T==null||T.animate({d:O})):(M=w.clip=c.path(N),t&&(T=w.simpleClip=c.path(O))),i&&((F=w.graph)==null||F.clip(M)),t&&((I=w.area)==null||I.clip(T))})}else this.visible&&(i&&i.show(),t&&t.show())}plotGroup(t,e,i,s,r){let o=this[t],a=!o,n={visibility:i,zIndex:s||.1};return yt(this.opacity)&&!this.chart.styledMode&&this.state!=="inactive"&&(n.opacity=this.opacity),o||(this[t]=o=this.chart.renderer.g().add(r)),o.addClass("highcharts-"+e+" highcharts-series-"+this.index+" highcharts-"+this.type+"-series "+(yt(this.colorIndex)?"highcharts-color-"+this.colorIndex+" ":"")+(this.options.className||"")+(o.hasClass("highcharts-tracker")?" highcharts-tracker":""),!0),o.attr(n)[a?"attr":"animate"](this.getPlotBox(e)),o}getPlotBox(t){let e=this.xAxis,i=this.yAxis,s=this.chart,r=s.inverted&&!s.polar&&e&&this.invertible&&t==="series";return s.inverted&&(e=i,i=this.xAxis),{translateX:e?e.left:s.plotLeft,translateY:i?i.top:s.plotTop,rotation:r?90:0,rotationOriginX:r?(e.len-i.len)/2:0,rotationOriginY:r?(e.len+i.len)/2:0,scaleX:r?-1:1,scaleY:1}}removeEvents(t){let{eventsToUnbind:e}=this;t||pd(this),e.length&&(e.forEach(i=>{i()}),e.length=0)}render(){var c,d,p,u,g;let t=this,{chart:e,options:i,hasRendered:s}=t,r=Uo(i.animation),o=t.visible?"inherit":"hidden",a=i.zIndex,n=e.seriesGroup,h=t.finishedAnimating?0:r.duration;st(this,"render"),t.plotGroup("group","series",o,a,n),t.markerGroup=t.plotGroup("markerGroup","markers",o,a,n),i.clip!==!1&&t.setClip(),h&&((c=t.animate)==null||c.call(t,!0)),t.drawGraph&&(t.drawGraph(),t.applyZones()),t.visible&&t.drawPoints(),(d=t.drawDataLabels)==null||d.call(t),(p=t.redrawPoints)==null||p.call(t),i.enableMouseTracking&&((u=t.drawTracker)==null||u.call(t)),h&&((g=t.animate)==null||g.call(t)),s||(h&&r.defer&&(h+=r.defer),t.animationTimeout=ta(()=>{t.afterAnimate()},h||0)),t.isDirty=!1,t.hasRendered=!0,st(t,"afterRender")}redraw(){let t=this.isDirty||this.isDirtyData;this.translate(),this.render(),t&&delete this.kdTree}reserveSpace(){return this.visible||!this.chart.options.chart.ignoreHiddenSeries}searchPoint(t,e){let{xAxis:i,yAxis:s}=this,r=this.chart.inverted;return this.searchKDTree({clientX:r?i.len-t.chartY+i.pos:t.chartX-i.pos,plotY:r?s.len-t.chartX+s.pos:t.chartY-s.pos},e,t)}buildKDTree(t){this.buildingKdTree=!0;let e=this,i=e.options.findNearestPointBy.indexOf("y")>-1?2:1;delete e.kdTree,ta(function(){e.kdTree=function s(r,o,a){let n,h,c=r==null?void 0:r.length;if(c)return n=e.kdAxisArray[o%a],r.sort((d,p)=>(d[n]||0)-(p[n]||0)),{point:r[h=Math.floor(c/2)],left:s(r.slice(0,h),o+1,a),right:s(r.slice(h+1),o+1,a)}}(e.getValidPoints(void 0,!e.directTouch),i,i),e.buildingKdTree=!1},e.options.kdNow||(t==null?void 0:t.type)==="touchstart"?0:1)}searchKDTree(t,e,i,s,r){let o=this,[a,n]=this.kdAxisArray,h=e?"distX":"dist",c=(o.options.findNearestPointBy||"").indexOf("y")>-1?2:1,d=!!o.isBubble,p=s||((g,m,x)=>[(g[x]||0)<(m[x]||0)?g:m,!1]),u=r||((g,m)=>g<m);if(this.kdTree||this.buildingKdTree||this.buildKDTree(i),this.kdTree)return function g(m,x,f,b){var P;let y=x.point,v=o.kdAxisArray[f%b],w=y,k=!1;(function(L,E){var z;let C=L[a],O=E[a],N=yt(C)&&yt(O)?C-O:null,F=L[n],I=E[n],X=yt(F)&&yt(I)?F-I:0,D=d&&((z=E.marker)==null?void 0:z.radius)||0;E.dist=Math.sqrt((N&&N*N||0)+X*X)-D,E.distX=yt(N)?Math.abs(N)-D:Number.MAX_VALUE})(m,y);let S=(m[v]||0)-(y[v]||0)+(d&&((P=y.marker)==null?void 0:P.radius)||0),M=S<0?"left":"right",T=S<0?"right":"left";return x[M]&&([w,k]=p(y,g(m,x[M],f+1,b),h)),x[T]&&u(Math.sqrt(S*S),w[h],k)&&(w=p(w,g(m,x[T],f+1,b),h)[0]),w}(t,this.kdTree,c,c)}pointPlacementToXValue(){let{options:t,xAxis:e}=this,i=t.pointPlacement;return i==="between"&&(i=e.reversed?-.5:.5),wt(i)?i*(t.pointRange||e.pointRange):0}isPointInside(t){let{chart:e,xAxis:i,yAxis:s}=this,{plotX:r=-1,plotY:o=-1}=t;return o>=0&&o<=(s?s.len:e.plotHeight)&&r>=0&&r<=(i?i.len:e.plotWidth)}drawTracker(){var p;let t=this,e=t.options,i=e.trackByArea,s=[].concat((i?t.areaPath:t.graphPath)||[]),r=t.chart,o=r.pointer,a=r.renderer,n=((p=r.options.tooltip)==null?void 0:p.snap)||0,h=()=>{e.enableMouseTracking&&r.hoverSeries!==t&&t.onMouseOver()},c="rgba(192,192,192,"+(rd?1e-4:.002)+")",d=t.tracker;d?d.attr({d:s}):t.graph&&(t.tracker=d=a.path(s).attr({visibility:t.visible?"inherit":"hidden",zIndex:2}).addClass(i?"highcharts-tracker-area":"highcharts-tracker-line").add(t.group),r.styledMode||d.attr({"stroke-linecap":"round","stroke-linejoin":"round",stroke:c,fill:i?c:"none","stroke-width":t.graph.strokeWidth()+(i?0:2*n)}),[t.tracker,t.markerGroup,t.dataLabelsGroup].forEach(u=>{u&&(u.addClass("highcharts-tracker").on("mouseover",h).on("mouseout",g=>{o==null||o.onTrackerMouseOut(g)}),e.cursor&&!r.styledMode&&u.css({cursor:e.cursor}),u.on("touchstart",h))})),st(this,"afterDrawTracker")}addPoint(t,e,i,s,r){let o,a,n=this.options,{chart:h,data:c,dataTable:d,xAxis:p}=this,u=p&&p.hasNames&&p.names,g=n.data,m=this.getColumn("x");e=rt(e,!0);let x={series:this};this.pointClass.prototype.applyOptions.apply(x,[t]);let f=x.x;if(a=m.length,this.requireSorting&&f<m[a-1])for(o=!0;a&&m[a-1]>f;)a--;d.setRow(x,a,!0,{addColumns:!1}),u&&x.name&&(u[f]=x.name),g==null||g.splice(a,0,t),(o||this.processedData)&&(this.data.splice(a,0,null),this.processData()),n.legendType==="point"&&this.generatePoints(),i&&(c[0]&&c[0].remove?c[0].remove(!1):([c,g,...Object.values(d.getColumns())].filter(yt).forEach(b=>{b.shift()}),d.rowCount-=1,st(d,"afterDeleteRows"))),r!==!1&&st(this,"addPoint",{point:x}),this.isDirty=!0,this.isDirtyData=!0,e&&h.redraw(s)}removePoint(t,e,i){let s=this,{chart:r,data:o,points:a,dataTable:n}=s,h=o[t],c=function(){[(a==null?void 0:a.length)===o.length?a:void 0,o,s.options.data,...Object.values(n.getColumns())].filter(yt).forEach(d=>{d.splice(t,1)}),n.rowCount-=1,st(n,"afterDeleteRows"),h==null||h.destroy(),s.isDirty=!0,s.isDirtyData=!0,e&&r.redraw()};id(i,r),e=rt(e,!0),h?h.firePointEvent("remove",null,c):c()}remove(t,e,i,s){let r=this,o=r.chart;function a(){r.destroy(s),o.isDirtyLegend=o.isDirtyBox=!0,o.linkSeries(s),rt(t,!0)&&o.redraw(e)}i!==!1?st(r,"remove",null,a):a()}update(t,e){var f,b;st(this,"update",{options:t=ld(t,this.userOptions)});let i=this,s=i.chart,r=i.userOptions,o=i.initialType||i.type,a=s.options.plotOptions,n=Ie[o].prototype,h=i.finishedAnimating&&{animation:!1},c={},d,p,u=["colorIndex","eventOptions","navigatorSeries","symbolIndex","baseSeries"],g=t.type||r.type||s.options.chart.type,m=!(this.hasDerivedData||g&&g!==this.type||t.keys!==void 0||t.pointStart!==void 0||t.pointInterval!==void 0||t.relativeXValue!==void 0||t.joinBy||t.mapData||["dataGrouping","pointStart","pointInterval","pointIntervalUnit","keys"].some(y=>i.hasOptionChanged(y)));g=g||o,m?(u.push("data","isDirtyData","isDirtyCanvas","points","dataTable","processedData","xIncrement","cropped","_hasPointMarkers","hasDataLabels","nodes","layout","level","mapMap","mapData","minY","maxY","minX","maxX","transformGroups"),t.visible!==!1&&u.push("area","graph"),i.parallelArrays.forEach(function(y){u.push(y+"Data")}),t.data&&(t.dataSorting&&Be(i.options.dataSorting,t.dataSorting),this.setData(t.data,!1))):this.dataTable.modified=this.dataTable,t=Ne(r,{index:r.index===void 0?i.index:r.index,pointStart:((f=a==null?void 0:a.series)==null?void 0:f.pointStart)??r.pointStart??i.getColumn("x")[0]},!m&&{data:i.options.data},t,h),m&&t.data&&(t.data=i.options.data),(u=["group","markerGroup","dataLabelsGroup","transformGroup"].concat(u)).forEach(function(y){u[y]=i[y],delete i[y]});let x=!1;if(Ie[g]){if(x=g!==i.type,i.remove(!1,!1,!1,!0),x)if(s.propFromSeries(),Object.setPrototypeOf)Object.setPrototypeOf(i,Ie[g].prototype);else{let y=Object.hasOwnProperty.call(i,"hcEvents")&&i.hcEvents;for(p in n)i[p]=void 0;Be(i,Ie[g].prototype),y?i.hcEvents=y:delete i.hcEvents}}else ji(17,!0,s,{missingModuleFor:g});if(u.forEach(function(y){i[y]=u[y]}),i.init(s,t),m&&this.points)for(let y of((d=i.options).visible===!1?(c.graphic=1,c.dataLabel=1):(this.hasMarkerChanged(d,r)&&(c.graphic=1),(b=i.hasDataLabels)!=null&&b.call(i)||(c.dataLabel=1)),this.points))y&&y.series&&(y.resolveColor(),Object.keys(c).length&&y.destroyElements(c),d.showInLegend===!1&&y.legendItem&&s.legend.destroyItem(y));i.initialType=o,s.linkSeries(),s.setSortedData(),x&&i.linkedSeries.length&&(i.isDirtyData=!0),st(this,"afterUpdate"),rt(e,!0)&&s.redraw(!!m&&void 0)}setName(t){this.name=this.options.name=this.userOptions.name=t,this.chart.isDirtyLegend=!0}hasOptionChanged(t){var a,n;let e=this.chart,i=this.options[t],s=e.options.plotOptions,r=this.userOptions[t],o=rt((a=s==null?void 0:s[this.type])==null?void 0:a[t],(n=s==null?void 0:s.series)==null?void 0:n[t]);return r&&!yt(o)?i!==r:i!==rt(o,i)}onMouseOver(){let t=this.chart,e=t.hoverSeries,i=t.pointer;i==null||i.setHoverChartIndex(),e&&e!==this&&e.onMouseOut(),this.options.events.mouseOver&&st(this,"mouseOver"),this.setState("hover"),t.hoverSeries=this}onMouseOut(){let t=this.options,e=this.chart,i=e.tooltip,s=e.hoverPoint;e.hoverSeries=null,s&&s.onMouseOut(),this&&t.events.mouseOut&&st(this,"mouseOut"),i&&!this.stickyTracking&&(!i.shared||this.noSharedTooltip)&&i.hide(),e.series.forEach(function(r){r.setState("",!0)})}setState(t,e){let i=this,s=i.options,r=i.graph,o=s.inactiveOtherPoints,a=s.states,n=rt(a[t||"normal"]&&a[t||"normal"].animation,i.chart.options.chart.animation),h=s.lineWidth,c=s.opacity;if(t=t||"",i.state!==t&&([i.group,i.markerGroup,i.dataLabelsGroup].forEach(function(d){d&&(i.state&&d.removeClass("highcharts-series-"+i.state),t&&d.addClass("highcharts-series-"+t))}),i.state=t,!i.chart.styledMode)){if(a[t]&&a[t].enabled===!1)return;if(t&&(h=a[t].lineWidth||h+(a[t].lineWidthPlus||0),c=rt(a[t].opacity,c)),r&&!r.dashstyle&&wt(h))for(let d of[r,...this.zones.map(p=>p.graph)])d==null||d.animate({"stroke-width":h},n);o||[i.group,i.markerGroup,i.dataLabelsGroup,i.labelBySeries].forEach(function(d){d&&d.animate({opacity:c},n)})}e&&o&&i.points&&i.setAllPointsToState(t||void 0)}setAllPointsToState(t){this.points.forEach(function(e){e.setState&&e.setState(t)})}setVisible(t,e){var n;let i=this,s=i.chart,r=s.options.chart.ignoreHiddenSeries,o=i.visible;i.visible=t=i.options.visible=i.userOptions.visible=t===void 0?!o:t;let a=t?"show":"hide";["group","dataLabelsGroup","markerGroup","tracker","tt"].forEach(h=>{var c;(c=i[h])==null||c[a]()}),(s.hoverSeries===i||((n=s.hoverPoint)==null?void 0:n.series)===i)&&i.onMouseOut(),i.legendItem&&s.legend.colorizeItem(i,t),i.isDirty=!0,i.options.stacking&&s.series.forEach(h=>{h.options.stacking&&h.visible&&(h.isDirty=!0)}),i.linkedSeries.forEach(h=>{h.setVisible(t,!1)}),r&&(s.isDirtyBox=!0),st(i,a),e!==!1&&s.redraw()}show(){this.setVisible(!0)}hide(){this.setVisible(!1)}select(t){this.selected=t=this.options.selected=t===void 0?!this.selected:t,this.checkbox&&(this.checkbox.checked=t),st(this,t?"select":"unselect")}shouldShowTooltip(t,e,i={}){return i.series=this,i.visiblePlotOnly=!0,this.chart.isInsidePlot(t,e,i)}drawLegendSymbol(t,e){var i;(i=jo[this.options.legendSymbol||"rectangle"])==null||i.call(this,t,e)}}ze.defaultOptions={lineWidth:2,allowPointSelect:!1,crisp:!0,showCheckbox:!1,animation:{duration:1e3},enableMouseTracking:!0,events:{},marker:{enabledThreshold:2,lineColor:"#ffffff",lineWidth:0,radius:4,states:{normal:{animation:!0},hover:{animation:{duration:150},enabled:!0,radiusPlus:2,lineWidthPlus:1},select:{fillColor:"#cccccc",lineColor:"#000000",lineWidth:2}}},point:{events:{}},dataLabels:{animation:{},align:"center",borderWidth:0,defer:!0,formatter:function(){let{numberFormatter:l}=this.series.chart;return typeof this.y!="number"?"":l(this.y,-1)},padding:5,style:{fontSize:"0.7em",fontWeight:"bold",color:"contrast",textOutline:"1px contrast"},verticalAlign:"bottom",x:0,y:0},cropThreshold:300,opacity:1,pointRange:0,softThreshold:!0,states:{normal:{animation:!0},hover:{animation:{duration:150},lineWidthPlus:1,marker:{},halo:{size:10,opacity:.25}},select:{animation:{duration:0}},inactive:{animation:{duration:150},opacity:.2}},stickyTracking:!0,turboThreshold:1e3,findNearestPointBy:"x"},ze.types=ht.seriesTypes,ze.registerType=ht.registerSeriesType,Be(ze.prototype,{axisTypes:["xAxis","yAxis"],coll:"series",colorCounter:0,directTouch:!1,invertible:!0,isCartesian:!0,kdAxisArray:["clientX","plotY"],parallelArrays:["x","y"],pointClass:ie,requireSorting:!0,sorted:!0}),ht.series=ze;let Nt=ze,{animObject:ud,setAnimation:gd}=St,{registerEventOptions:ea}=Ci,{composed:fd,marginNames:ia}=A,{distribute:md}=yi,{format:xd}=Yt,{addEvent:$i,createElement:yd,css:bd,defined:Vs,discardElement:vd,find:kd,fireEvent:Kt,isNumber:sa,merge:xe,pick:Wt,pushUnique:wd,relativeLength:Md,stableSort:Sd,syncTimeout:Td}=R;class qs{constructor(t,e){this.allItems=[],this.initialItemY=0,this.itemHeight=0,this.itemMarginBottom=0,this.itemMarginTop=0,this.itemX=0,this.itemY=0,this.lastItemY=0,this.lastLineHeight=0,this.legendHeight=0,this.legendWidth=0,this.maxItemWidth=0,this.maxLegendWidth=0,this.offsetWidth=0,this.padding=0,this.pages=[],this.symbolHeight=0,this.symbolWidth=0,this.titleHeight=0,this.totalItemWidth=0,this.widthOption=0,this.chart=t,this.setOptions(e),e.enabled&&(this.render(),ea(this,e),$i(this.chart,"endResize",function(){this.legend.positionCheckboxes()})),$i(this.chart,"render",()=>{this.options.enabled&&this.proximate&&(this.proximatePositions(),this.positionItems())})}setOptions(t){let e=Wt(t.padding,8);this.options=t,this.chart.styledMode||(this.itemStyle=t.itemStyle,this.itemHiddenStyle=xe(this.itemStyle,t.itemHiddenStyle)),this.itemMarginTop=t.itemMarginTop,this.itemMarginBottom=t.itemMarginBottom,this.padding=e,this.initialItemY=e-5,this.symbolWidth=Wt(t.symbolWidth,16),this.pages=[],this.proximate=t.layout==="proximate"&&!this.chart.inverted,this.baseline=void 0}update(t,e){let i=this.chart;this.setOptions(xe(!0,this.options,t)),"events"in this.options&&ea(this,this.options),this.destroy(),i.isDirtyLegend=i.isDirtyBox=!0,Wt(e,!0)&&i.redraw(),Kt(this,"afterUpdate",{redraw:e})}colorizeItem(t,e){var h;let i=t.color,{area:s,group:r,label:o,line:a,symbol:n}=t.legendItem||{};if((t instanceof Nt||t instanceof ie)&&(t.color=((h=t.options)==null?void 0:h.legendSymbolColor)||i),r==null||r[e?"removeClass":"addClass"]("highcharts-legend-item-hidden"),!this.chart.styledMode){let{itemHiddenStyle:c={}}=this,d=c.color,{fillColor:p,fillOpacity:u,lineColor:g,marker:m}=t.options,x=f=>(!e&&(f.fill&&(f.fill=d),f.stroke&&(f.stroke=d)),f);o==null||o.css(xe(e?this.itemStyle:c)),a==null||a.attr(x({stroke:g||t.color})),n&&n.attr(x(m&&n.isMarker?t.pointAttribs():{fill:t.color})),s==null||s.attr(x({fill:p||t.color,"fill-opacity":p?1:u??.75}))}t.color=i,Kt(this,"afterColorizeItem",{item:t,visible:e})}positionItems(){this.allItems.forEach(this.positionItem,this),this.chart.isResizing||this.positionCheckboxes()}positionItem(t){let{group:e,x:i=0,y:s=0}=t.legendItem||{},r=this.options,o=r.symbolPadding,a=!r.rtl,n=t.checkbox;if(e&&e.element){let h={translateX:a?i:this.legendWidth-i-2*o-4,translateY:s};e[Vs(e.translateY)?"animate":"attr"](h,void 0,()=>{Kt(this,"afterPositionItem",{item:t})})}n&&(n.x=i,n.y=s)}destroyItem(t){let e=t.checkbox,i=t.legendItem||{};for(let s of["group","label","line","symbol"])i[s]&&(i[s]=i[s].destroy());e&&vd(e),t.legendItem=void 0}destroy(){for(let t of this.getAllItems())this.destroyItem(t);for(let t of["clipRect","up","down","pager","nav","box","title","group"])this[t]&&(this[t]=this[t].destroy());this.display=null}positionCheckboxes(){let t,e=this.group&&this.group.alignAttr,i=this.clipHeight||this.legendHeight,s=this.titleHeight;e&&(t=e.translateY,this.allItems.forEach(function(r){let o,a=r.checkbox;a&&(o=t+s+a.y+(this.scrollOffset||0)+3,bd(a,{left:e.translateX+r.checkboxOffset+a.x-20+"px",top:o+"px",display:this.proximate||o>t-6&&o<t+i-6?"":"none"}))},this))}renderTitle(){let t=this.options,e=this.padding,i=t.title,s,r=0;i.text&&(this.title||(this.title=this.chart.renderer.label(i.text,e-3,e-4,void 0,void 0,void 0,t.useHTML,void 0,"legend-title").attr({zIndex:1}),this.chart.styledMode||this.title.css(i.style),this.title.add(this.group)),i.width||this.title.css({width:this.maxLegendWidth+"px"}),r=(s=this.title.getBBox()).height,this.offsetWidth=s.width,this.contentGroup.attr({translateY:r})),this.titleHeight=r}setText(t){let e=this.options;t.legendItem.label.attr({text:e.labelFormat?xd(e.labelFormat,t,this.chart):e.labelFormatter.call(t)})}renderItem(t){let e=t.legendItem=t.legendItem||{},i=this.chart,s=i.renderer,r=this.options,o=r.layout==="horizontal",a=this.symbolWidth,n=r.symbolPadding||0,h=this.itemStyle,c=this.itemHiddenStyle,d=o?Wt(r.itemDistance,20):0,p=!r.rtl,u=!t.series,g=!u&&t.series.drawLegendSymbol?t.series:t,m=g.options,x=!!this.createCheckboxForItem&&m&&m.showCheckbox,f=r.useHTML,b=t.options.className,y=e.label,v=a+n+d+(x?20:0);!y&&(e.group=s.g("legend-item").addClass("highcharts-"+g.type+"-series highcharts-color-"+t.colorIndex+(b?" "+b:"")+(u?" highcharts-series-"+t.index:"")).attr({zIndex:1}).add(this.scrollGroup),e.label=y=s.text("",p?a+n:-n,this.baseline||0,f),i.styledMode||y.css(xe(t.visible?h:c)),y.attr({align:p?"left":"right",zIndex:2}).add(e.group),!this.baseline&&(this.fontMetrics=s.fontMetrics(y),this.baseline=this.fontMetrics.f+3+this.itemMarginTop,y.attr("y",this.baseline),this.symbolHeight=Wt(r.symbolHeight,this.fontMetrics.f),r.squareSymbol&&(this.symbolWidth=Wt(r.symbolWidth,Math.max(this.symbolHeight,16)),v=this.symbolWidth+n+d+(x?20:0),p&&y.attr("x",this.symbolWidth+n))),g.drawLegendSymbol(this,t),this.setItemEvents&&this.setItemEvents(t,y,f)),x&&!t.checkbox&&this.createCheckboxForItem&&this.createCheckboxForItem(t),this.colorizeItem(t,t.visible),(i.styledMode||!h.width)&&y.css({width:(r.itemWidth||this.widthOption||i.spacingBox.width)-v+"px"}),this.setText(t);let w=y.getBBox(),k=this.fontMetrics&&this.fontMetrics.h||0;t.itemWidth=t.checkboxOffset=r.itemWidth||e.labelWidth||w.width+v,this.maxItemWidth=Math.max(this.maxItemWidth,t.itemWidth),this.totalItemWidth+=t.itemWidth,this.itemHeight=t.itemHeight=Math.round(e.labelHeight||(w.height>1.5*k?w.height:k))}layoutItem(t){let e=this.options,i=this.padding,s=e.layout==="horizontal",r=t.itemHeight,o=this.itemMarginBottom,a=this.itemMarginTop,n=s?Wt(e.itemDistance,20):0,h=this.maxLegendWidth,c=e.alignColumns&&this.totalItemWidth>h?this.maxItemWidth:t.itemWidth,d=t.legendItem||{};s&&this.itemX-i+c>h&&(this.itemX=i,this.lastLineHeight&&(this.itemY+=a+this.lastLineHeight+o),this.lastLineHeight=0),this.lastItemY=a+this.itemY+o,this.lastLineHeight=Math.max(r,this.lastLineHeight),d.x=this.itemX,d.y=this.itemY,s?this.itemX+=c:(this.itemY+=a+r+o,this.lastLineHeight=r),this.offsetWidth=this.widthOption||Math.max((s?this.itemX-i-(t.checkbox?0:n):c)+i,this.offsetWidth)}getAllItems(){let t=[];return this.chart.series.forEach(function(e){let i=e&&e.options;e&&Wt(i.showInLegend,!Vs(i.linkedTo)&&void 0,!0)&&(t=t.concat((e.legendItem||{}).labels||(i.legendType==="point"?e.data:e)))}),Kt(this,"afterGetAllItems",{allItems:t}),t}getAlignment(){let t=this.options;return this.proximate?t.align.charAt(0)+"tv":t.floating?"":t.align.charAt(0)+t.verticalAlign.charAt(0)+t.layout.charAt(0)}adjustMargins(t,e){let i=this.chart,s=this.options,r=this.getAlignment();r&&[/(lth|ct|rth)/,/(rtv|rm|rbv)/,/(rbh|cb|lbh)/,/(lbv|lm|ltv)/].forEach(function(o,a){o.test(r)&&!Vs(t[a])&&(i[ia[a]]=Math.max(i[ia[a]],i.legend[(a+1)%2?"legendHeight":"legendWidth"]+[1,-1,-1,1][a]*s[a%2?"x":"y"]+Wt(s.margin,12)+e[a]+(i.titleOffset[a]||0)))})}proximatePositions(){let t,e=this.chart,i=[],s=this.options.align==="left";for(let r of(this.allItems.forEach(function(o){let a,n,h=s,c,d;o.yAxis&&(o.xAxis.options.reversed&&(h=!h),o.points&&(a=kd(h?o.points:o.points.slice(0).reverse(),function(p){return sa(p.plotY)})),n=this.itemMarginTop+o.legendItem.label.getBBox().height+this.itemMarginBottom,d=o.yAxis.top-e.plotTop,c=o.visible?(a?a.plotY:o.yAxis.height)+(d-.3*n):d+o.yAxis.height,i.push({target:c,size:n,item:o}))},this),md(i,e.plotHeight)))t=r.item.legendItem||{},sa(r.pos)&&(t.y=e.plotTop-e.spacing[0]+r.pos)}render(){let t=this.chart,e=t.renderer,i=this.options,s=this.padding,r=this.getAllItems(),o,a,n,h=this.group,c,d=this.box;this.itemX=s,this.itemY=this.initialItemY,this.offsetWidth=0,this.lastItemY=0,this.widthOption=Md(i.width,t.spacingBox.width-s),c=t.spacingBox.width-2*s-i.x,["rm","lm"].indexOf(this.getAlignment().substring(0,2))>-1&&(c/=2),this.maxLegendWidth=this.widthOption||c,h||(this.group=h=e.g("legend").addClass(i.className||"").attr({zIndex:7}).add(),this.contentGroup=e.g().attr({zIndex:1}).add(h),this.scrollGroup=e.g().add(this.contentGroup)),this.renderTitle(),Sd(r,(p,u)=>(p.options&&p.options.legendIndex||0)-(u.options&&u.options.legendIndex||0)),i.reversed&&r.reverse(),this.allItems=r,this.display=o=!!r.length,this.lastLineHeight=0,this.maxItemWidth=0,this.totalItemWidth=0,this.itemHeight=0,r.forEach(this.renderItem,this),r.forEach(this.layoutItem,this),a=(this.widthOption||this.offsetWidth)+s,n=this.lastItemY+this.lastLineHeight+this.titleHeight,n=this.handleOverflow(n)+s,d||(this.box=d=e.rect().addClass("highcharts-legend-box").attr({r:i.borderRadius}).add(h)),t.styledMode||d.attr({stroke:i.borderColor,"stroke-width":i.borderWidth||0,fill:i.backgroundColor||"none"}).shadow(i.shadow),a>0&&n>0&&d[d.placed?"animate":"attr"](d.crisp.call({},{x:0,y:0,width:a,height:n},d.strokeWidth())),h[o?"show":"hide"](),t.styledMode&&h.getStyle("display")==="none"&&(a=n=0),this.legendWidth=a,this.legendHeight=n,o&&this.align(),this.proximate||this.positionItems(),Kt(this,"afterRender")}align(t=this.chart.spacingBox){let e=this.chart,i=this.options,s=t.y;/(lth|ct|rth)/.test(this.getAlignment())&&e.titleOffset[0]>0?s+=e.titleOffset[0]:/(lbh|cb|rbh)/.test(this.getAlignment())&&e.titleOffset[2]>0&&(s-=e.titleOffset[2]),s!==t.y&&(t=xe(t,{y:s})),e.hasRendered||(this.group.placed=!1),this.group.align(xe(i,{width:this.legendWidth,height:this.legendHeight,verticalAlign:this.proximate?"top":i.verticalAlign}),!0,t)}handleOverflow(t){let e=this,i=this.chart,s=i.renderer,r=this.options,o=r.y,a=r.verticalAlign==="top",n=this.padding,h=r.maxHeight,c=r.navigation,d=Wt(c.animation,!0),p=c.arrowSize||12,u=this.pages,g=this.allItems,m=function(S){typeof S=="number"?k.attr({height:S}):k&&(e.clipRect=k.destroy(),e.contentGroup.clip()),e.contentGroup.div&&(e.contentGroup.div.style.clip=S?"rect("+n+"px,9999px,"+(n+S)+"px,0)":"auto")},x=function(S){return e[S]=s.circle(0,0,1.3*p).translate(p/2,p/2).add(w),i.styledMode||e[S].attr("fill","rgba(0,0,0,0.0001)"),e[S]},f,b,y,v=i.spacingBox.height+(a?-o:o)-n,w=this.nav,k=this.clipRect;return r.layout!=="horizontal"||r.verticalAlign==="middle"||r.floating||(v/=2),h&&(v=Math.min(v,h)),u.length=0,t&&v>0&&t>v&&c.enabled!==!1?(this.clipHeight=f=Math.max(v-20-this.titleHeight-n,0),this.currentPage=Wt(this.currentPage,1),this.fullHeight=t,g.forEach((S,M)=>{let T=(y=S.legendItem||{}).y||0,P=Math.round(y.label.getBBox().height),L=u.length;(!L||T-u[L-1]>f&&(b||T)!==u[L-1])&&(u.push(b||T),L++),y.pageIx=L-1,b&&((g[M-1].legendItem||{}).pageIx=L-1),M===g.length-1&&T+P-u[L-1]>f&&T>u[L-1]&&(u.push(T),y.pageIx=L),T!==b&&(b=T)}),k||(k=e.clipRect=s.clipRect(0,n-2,9999,0),e.contentGroup.clip(k)),m(f),w||(this.nav=w=s.g().attr({zIndex:1}).add(this.group),this.up=s.symbol("triangle",0,0,p,p).add(w),x("upTracker").on("click",function(){e.scroll(-1,d)}),this.pager=s.text("",15,10).addClass("highcharts-legend-navigation"),!i.styledMode&&c.style&&this.pager.css(c.style),this.pager.add(w),this.down=s.symbol("triangle-down",0,0,p,p).add(w),x("downTracker").on("click",function(){e.scroll(1,d)})),e.scroll(0),t=v):w&&(m(),this.nav=w.destroy(),this.scrollGroup.attr({translateY:1}),this.clipHeight=0),t}scroll(t,e){let i=this.chart,s=this.pages,r=s.length,o=this.clipHeight,a=this.options.navigation,n=this.pager,h=this.padding,c=this.currentPage+t;c>r&&(c=r),c>0&&(e!==void 0&&gd(e,i),this.nav.attr({translateX:h,translateY:o+this.padding+7+this.titleHeight,visibility:"inherit"}),[this.up,this.upTracker].forEach(function(d){d.attr({class:c===1?"highcharts-legend-nav-inactive":"highcharts-legend-nav-active"})}),n.attr({text:c+"/"+r}),[this.down,this.downTracker].forEach(function(d){d.attr({x:18+this.pager.getBBox().width,class:c===r?"highcharts-legend-nav-inactive":"highcharts-legend-nav-active"})},this),i.styledMode||(this.up.attr({fill:c===1?a.inactiveColor:a.activeColor}),this.upTracker.css({cursor:c===1?"default":"pointer"}),this.down.attr({fill:c===r?a.inactiveColor:a.activeColor}),this.downTracker.css({cursor:c===r?"default":"pointer"})),this.scrollOffset=-s[c-1]+this.initialItemY,this.scrollGroup.animate({translateY:this.scrollOffset}),this.currentPage=c,this.positionCheckboxes(),Td(()=>{Kt(this,"afterScroll",{currentPage:c})},ud(Wt(e,i.renderer.globalAnimation,!0)).duration))}setItemEvents(t,e,i){let s=this,r=t.legendItem||{},o=s.chart.renderer.boxWrapper,a=t instanceof ie,n=t instanceof Nt,h="highcharts-legend-"+(a?"point":"series")+"-active",c=s.chart.styledMode,d=i?[e,r.symbol]:[r.group],p=u=>{s.allItems.forEach(g=>{t!==g&&[g].concat(g.linkedSeries||[]).forEach(m=>{m.setState(u,!a)})})};for(let u of d)u&&u.on("mouseover",function(){t.visible&&p("inactive"),t.setState("hover"),t.visible&&o.addClass(h),c||e.css(s.options.itemHoverStyle)}).on("mouseout",function(){s.chart.styledMode||e.css(xe(t.visible?s.itemStyle:s.itemHiddenStyle)),p(""),o.removeClass(h),t.setState()}).on("click",function(g){let m=function(){t.setVisible&&t.setVisible(),p(t.visible?"inactive":"")};o.removeClass(h),Kt(s,"itemClick",{browserEvent:g,legendItem:t},m),a?t.firePointEvent("legendItemClick",{browserEvent:g}):n&&Kt(t,"legendItemClick",{browserEvent:g})})}createCheckboxForItem(t){t.checkbox=yd("input",{type:"checkbox",className:"highcharts-legend-checkbox",checked:t.selected,defaultChecked:t.selected},this.options.itemCheckboxStyle,this.chart.container),$i(t.checkbox,"click",function(e){let i=e.target;Kt(t.series||t,"checkboxClick",{checked:i.checked,item:t},function(){t.select()})})}}(function(l){l.compose=function(t){wd(fd,"Core.Legend")&&$i(t,"beforeMargins",function(){this.legend=new l(this,this.options.legend)})}})(qs||(qs={}));let ra=qs,{animate:Zs,animObject:Ad,setAnimation:Ks}=St,{defaultOptions:_s}=zt,{numberFormat:Cd}=Yt,{registerEventOptions:oa}=Ci,{charts:ye,doc:ei,marginNames:aa,svg:Pd,win:na}=A,{seriesTypes:Qs}=ht,{addEvent:Js,attr:la,createElement:tr,css:Ut,defined:re,diffObjects:ha,discardElement:Ld,erase:Od,error:er,extend:oe,find:ir,fireEvent:U,getAlignFactor:Ed,getStyle:sr,isArray:Dd,isNumber:Re,isObject:Id,isString:Ui,merge:_t,objectEach:rr,pick:Tt,pInt:Bd,relativeLength:da,removeEvent:ca,splat:Vi,syncTimeout:Nd,uniqueKey:zd}=R;class ae{static chart(t,e,i){return new ae(t,e,i)}constructor(t,e,i){this.sharedClips={};let s=[...arguments];(Ui(t)||t.nodeName)&&(this.renderTo=s.shift()),this.init(s[0],s[1])}setZoomOptions(){let t=this.options.chart,e=t.zooming;this.zooming={...e,type:Tt(t.zoomType,e.type),key:Tt(t.zoomKey,e.key),pinchType:Tt(t.pinchType,e.pinchType),singleTouch:Tt(t.zoomBySingleTouch,e.singleTouch,!1),resetButton:_t(e.resetButton,t.resetZoomButton)}}init(t,e){U(this,"init",{args:arguments},function(){var o;let i=_t(_s,t),s=i.chart,r=this.renderTo||s.renderTo;this.userOptions=oe({},t),(this.renderTo=Ui(r)?ei.getElementById(r):r)||er(13,!0,this),this.margin=[],this.spacing=[],this.labelCollectors=[],this.callback=e,this.isResizing=0,this.options=i,this.axes=[],this.series=[],this.locale=i.lang.locale??((o=this.renderTo.closest("[lang]"))==null?void 0:o.lang),this.time=new fs(oe(i.time||{},{locale:this.locale})),i.time=this.time.options,this.numberFormatter=(s.numberFormatter||Cd).bind(this),this.styledMode=s.styledMode,this.hasCartesianSeries=s.showAxes,this.index=ye.length,ye.push(this),A.chartCount++,oa(this,s),this.xAxis=[],this.yAxis=[],this.pointCount=this.colorCounter=this.symbolCounter=0,this.setZoomOptions(),U(this,"afterInit"),this.firstRender()})}initSeries(t){let e=this.options.chart,i=t.type||e.type,s=Qs[i];s||er(17,!0,this,{missingModuleFor:i});let r=new s;return typeof r.init=="function"&&r.init(this,t),r}setSortedData(){this.getSeriesOrderByLinks().forEach(function(t){t.points||t.data||!t.enabledDataSorting||t.setData(t.options.data,!1)})}getSeriesOrderByLinks(){return this.series.concat().sort(function(t,e){return t.linkedSeries.length||e.linkedSeries.length?e.linkedSeries.length-t.linkedSeries.length:0})}orderItems(t,e=0){let i=this[t],s=this.options[t]=Vi(this.options[t]).slice(),r=this.userOptions[t]=this.userOptions[t]?Vi(this.userOptions[t]).slice():[];if(this.hasRendered&&(s.splice(e),r.splice(e)),i)for(let o=e,a=i.length;o<a;++o){let n=i[o];n&&(n.index=o,n instanceof Nt&&(n.name=n.getName()),n.options.isInternal||(s[o]=n.options,r[o]=n.userOptions))}}isInsidePlot(t,e,i={}){var x;let{inverted:s,plotBox:r,plotLeft:o,plotTop:a,scrollablePlotBox:n}=this,{scrollLeft:h=0,scrollTop:c=0}=i.visiblePlotOnly&&((x=this.scrollablePlotArea)==null?void 0:x.scrollingContainer)||{},d=i.series,p=i.visiblePlotOnly&&n||r,u=i.inverted?e:t,g=i.inverted?t:e,m={x:u,y:g,isInsidePlot:!0,options:i};if(!i.ignoreX){let f=d&&(s&&!this.polar?d.yAxis:d.xAxis)||{pos:o,len:1/0},b=i.paneCoordinates?f.pos+u:o+u;b>=Math.max(h+o,f.pos)&&b<=Math.min(h+o+p.width,f.pos+f.len)||(m.isInsidePlot=!1)}if(!i.ignoreY&&m.isInsidePlot){let f=!s&&i.axis&&!i.axis.isXAxis&&i.axis||d&&(s?d.xAxis:d.yAxis)||{pos:a,len:1/0},b=i.paneCoordinates?f.pos+g:a+g;b>=Math.max(c+a,f.pos)&&b<=Math.min(c+a+p.height,f.pos+f.len)||(m.isInsidePlot=!1)}return U(this,"afterIsInsidePlot",m),m.isInsidePlot}redraw(t){U(this,"beforeRedraw");let e=this.hasCartesianSeries?this.axes:this.colorAxis||[],i=this.series,s=this.pointer,r=this.legend,o=this.userOptions.legend,a=this.renderer,n=a.isHidden(),h=[],c,d,p,u=this.isDirtyBox,g=this.isDirtyLegend,m;for(a.rootFontSize=a.boxWrapper.getStyle("font-size"),this.setResponsive&&this.setResponsive(!1),Ks(!!this.hasRendered&&t,this),n&&this.temporaryDisplay(),this.layOutTitles(!1),p=i.length;p--;)if(((m=i[p]).options.stacking||m.options.centerInCategory)&&(d=!0,m.isDirty)){c=!0;break}if(c)for(p=i.length;p--;)(m=i[p]).options.stacking&&(m.isDirty=!0);i.forEach(function(x){x.isDirty&&(x.options.legendType==="point"?(typeof x.updateTotals=="function"&&x.updateTotals(),g=!0):o&&(o.labelFormatter||o.labelFormat)&&(g=!0)),x.isDirtyData&&U(x,"updatedData")}),g&&r&&r.options.enabled&&(r.render(),this.isDirtyLegend=!1),d&&this.getStacks(),e.forEach(function(x){x.updateNames(),x.setScale()}),this.getMargins(),e.forEach(function(x){x.isDirty&&(u=!0)}),e.forEach(function(x){let f=x.min+","+x.max;x.extKey!==f&&(x.extKey=f,h.push(function(){U(x,"afterSetExtremes",oe(x.eventArgs,x.getExtremes())),delete x.eventArgs})),(u||d)&&x.redraw()}),u&&this.drawChartBox(),U(this,"predraw"),i.forEach(function(x){(u||x.isDirty)&&x.visible&&x.redraw(),x.isDirtyData=!1}),s&&s.reset(!0),a.draw(),U(this,"redraw"),U(this,"render"),n&&this.temporaryDisplay(!0),h.forEach(function(x){x.call()})}get(t){let e=this.series;function i(r){return r.id===t||r.options&&r.options.id===t}let s=ir(this.axes,i)||ir(this.series,i);for(let r=0;!s&&r<e.length;r++)s=ir(e[r].points||[],i);return s}createAxes(){let t=this.userOptions;for(let e of(U(this,"createAxes"),["xAxis","yAxis"]))for(let i of t[e]=Vi(t[e]||{}))new Oe(this,i,e);U(this,"afterCreateAxes")}getSelectedPoints(){return this.series.reduce((t,e)=>(e.getPointsCollection().forEach(i=>{Tt(i.selectedStaging,i.selected)&&t.push(i)}),t),[])}getSelectedSeries(){return this.series.filter(t=>t.selected)}setTitle(t,e,i){this.applyDescription("title",t),this.applyDescription("subtitle",e),this.applyDescription("caption",void 0),this.layOutTitles(i)}applyDescription(t,e){var o;let i=this,s=this.options[t]=_t(this.options[t],e),r=this[t];r&&e&&(this[t]=r=r.destroy()),s&&!r&&((r=this.renderer.text(s.text,0,0,s.useHTML).attr({align:s.align,class:"highcharts-"+t,zIndex:s.zIndex||4}).css({textOverflow:"ellipsis",whiteSpace:"nowrap"}).add()).update=function(a,n){i.applyDescription(t,a),i.layOutTitles(n)},this.styledMode||r.css(oe(t==="title"?{fontSize:this.options.isStock?"1em":"1.2em"}:{},s.style)),r.textPxLength=r.getBBox().width,r.css({whiteSpace:(o=s.style)==null?void 0:o.whiteSpace}),this[t]=r)}layOutTitles(t=!0){var a,n,h,c;let e=[0,0,0],{options:i,renderer:s,spacingBox:r}=this;["title","subtitle","caption"].forEach(d=>{var x;let p=this[d],u=this.options[d],g=_t(r),m=(p==null?void 0:p.textPxLength)||0;if(p&&u){U(this,"layOutTitle",{alignTo:g,key:d,textPxLength:m});let f=s.fontMetrics(p),b=f.b,y=f.h,v=u.verticalAlign||"top",w=v==="top",k=w&&u.minScale||1,S=d==="title"?w?-3:0:w?e[0]+2:0,M=Math.min(g.width/m,1),T=Math.max(k,M),P=_t({y:v==="bottom"?b:S+b},{align:d==="title"?M<k?"left":"center":(x=this.title)==null?void 0:x.alignValue},u),L=u.width||(M>k?this.chartWidth:g.width)/T;p.alignValue!==P.align&&(p.placed=!1);let E=Math.round(p.css({width:`${L}px`}).getBBox(u.useHTML).height);if(P.height=E,p.align(P,!1,g).attr({align:P.align,scaleX:T,scaleY:T,"transform-origin":`${g.x+m*T*Ed(P.align)} ${y}`}),!u.floating){let C=E*(E<1.2*y?1:T);v==="top"?e[0]=Math.ceil(e[0]+C):v==="bottom"&&(e[2]=Math.ceil(e[2]+C))}}},this),e[0]&&(((a=i.title)==null?void 0:a.verticalAlign)||"top")==="top"&&(e[0]+=((n=i.title)==null?void 0:n.margin)||0),e[2]&&((h=i.caption)==null?void 0:h.verticalAlign)==="bottom"&&(e[2]+=((c=i.caption)==null?void 0:c.margin)||0);let o=!this.titleOffset||this.titleOffset.join(",")!==e.join(",");this.titleOffset=e,U(this,"afterLayOutTitles"),!this.isDirtyBox&&o&&(this.isDirtyBox=this.isDirtyLegend=o,this.hasRendered&&t&&this.isDirtyBox&&this.redraw())}getContainerBox(){let t=[].map.call(this.renderTo.children,i=>{if(i!==this.container){let s=i.style.display;return i.style.display="none",[i,s]}}),e={width:sr(this.renderTo,"width",!0)||0,height:sr(this.renderTo,"height",!0)||0};return t.filter(Boolean).forEach(([i,s])=>{i.style.display=s}),e}getChartSize(){var o;let t=this.options.chart,e=t.width,i=t.height,s=this.getContainerBox(),r=s.height<=1||!((o=this.renderTo.parentElement)!=null&&o.style.height)&&this.renderTo.style.height==="100%";this.chartWidth=Math.max(0,e||s.width||600),this.chartHeight=Math.max(0,da(i,this.chartWidth)||(r?400:s.height)),this.containerBox=s}temporaryDisplay(t){let e=this.renderTo,i;if(t)for(;e&&e.style;)e.hcOrigStyle&&(Ut(e,e.hcOrigStyle),delete e.hcOrigStyle),e.hcOrigDetached&&(ei.body.removeChild(e),e.hcOrigDetached=!1),e=e.parentNode;else for(;e&&e.style&&(ei.body.contains(e)||e.parentNode||(e.hcOrigDetached=!0,ei.body.appendChild(e)),(sr(e,"display",!1)==="none"||e.hcOricDetached)&&(e.hcOrigStyle={display:e.style.display,height:e.style.height,overflow:e.style.overflow},i={display:"block",overflow:"hidden"},e!==this.renderTo&&(i.height=0),Ut(e,i),e.offsetWidth||e.style.setProperty("display","block","important")),(e=e.parentNode)!==ei.body););}setClassName(t){this.container.className="highcharts-container "+(t||"")}getContainer(){var p;let t,e=this.options,i=e.chart,s="data-highcharts-chart",r=zd(),o=this.renderTo,a=Bd(la(o,s));Re(a)&&ye[a]&&ye[a].hasRendered&&ye[a].destroy(),la(o,s,this.index),o.innerHTML=it.emptyHTML,i.skipClone||o.offsetWidth||this.temporaryDisplay(),this.getChartSize();let n=this.chartHeight,h=this.chartWidth;Ut(o,{overflow:"hidden"}),this.styledMode||(t=oe({position:"relative",overflow:"hidden",width:h+"px",height:n+"px",textAlign:"left",lineHeight:"normal",zIndex:0,"-webkit-tap-highlight-color":"rgba(0,0,0,0)",userSelect:"none","touch-action":"manipulation",outline:"none",padding:"0px"},i.style||{}));let c=tr("div",{id:r},t,o);this.container=c,this.getChartSize(),h===this.chartWidth||(h=this.chartWidth,this.styledMode||Ut(c,{width:Tt((p=i.style)==null?void 0:p.width,h+"px")})),this.containerBox=this.getContainerBox(),this._cursor=c.style.cursor;let d=i.renderer||!Pd?$e.getRendererType(i.renderer):Ai;if(this.renderer=new d(c,h,n,void 0,i.forExport,e.exporting&&e.exporting.allowHTML,this.styledMode),Ks(void 0,this),this.setClassName(i.className),this.styledMode)for(let u in e.defs)this.renderer.definition(e.defs[u]);else this.renderer.setStyle(i.style);this.renderer.chartIndex=this.index,U(this,"afterGetContainer")}getMargins(t){let{spacing:e,margin:i,titleOffset:s}=this;this.resetMargins(),s[0]&&!re(i[0])&&(this.plotTop=Math.max(this.plotTop,s[0]+e[0])),s[2]&&!re(i[2])&&(this.marginBottom=Math.max(this.marginBottom,s[2]+e[2])),this.legend&&this.legend.display&&this.legend.adjustMargins(i,e),U(this,"getMargins"),t||this.getAxisMargins()}getAxisMargins(){let t=this,e=t.axisOffset=[0,0,0,0],i=t.colorAxis,s=t.margin,r=function(o){o.forEach(function(a){a.visible&&a.getOffset()})};t.hasCartesianSeries?r(t.axes):i&&i.length&&r(i),aa.forEach(function(o,a){re(s[a])||(t[o]+=e[a])}),t.setChartSize()}getOptions(){return ha(this.userOptions,_s)}reflow(t){var r;let e=this,i=e.containerBox,s=e.getContainerBox();(r=e.pointer)==null||delete r.chartPosition,!e.isPrinting&&!e.isResizing&&i&&s.width&&((s.width!==i.width||s.height!==i.height)&&(R.clearTimeout(e.reflowTimeout),e.reflowTimeout=Nd(function(){e.container&&e.setSize(void 0,void 0,!1)},t?100:0)),e.containerBox=s)}setReflow(){let t=this,e=i=>{var s;(s=t.options)!=null&&s.chart.reflow&&t.hasLoaded&&t.reflow(i)};if(typeof ResizeObserver=="function")new ResizeObserver(e).observe(t.renderTo);else{let i=Js(na,"resize",e);Js(this,"destroy",i)}}setSize(t,e,i){let s=this,r=s.renderer;s.isResizing+=1,Ks(i,s);let o=r.globalAnimation;s.oldChartHeight=s.chartHeight,s.oldChartWidth=s.chartWidth,t!==void 0&&(s.options.chart.width=t),e!==void 0&&(s.options.chart.height=e),s.getChartSize();let{chartWidth:a,chartHeight:n,scrollablePixelsX:h=0,scrollablePixelsY:c=0}=s;(s.isDirtyBox||a!==s.oldChartWidth||n!==s.oldChartHeight)&&(s.styledMode||(o?Zs:Ut)(s.container,{width:`${a+h}px`,height:`${n+c}px`},o),s.setChartSize(!0),r.setSize(a,n,o),s.axes.forEach(function(d){d.isDirty=!0,d.setScale()}),s.isDirtyLegend=!0,s.isDirtyBox=!0,s.layOutTitles(),s.getMargins(),s.redraw(o),s.oldChartHeight=void 0,U(s,"resize"),setTimeout(()=>{s&&U(s,"endResize")},Ad(o).duration)),s.isResizing-=1}setChartSize(t){let e,i,s,r,{chartHeight:o,chartWidth:a,inverted:n,spacing:h,renderer:c}=this,d=this.clipOffset,p=Math[n?"floor":"round"];this.plotLeft=e=Math.round(this.plotLeft),this.plotTop=i=Math.round(this.plotTop),this.plotWidth=s=Math.max(0,Math.round(a-e-this.marginRight)),this.plotHeight=r=Math.max(0,Math.round(o-i-this.marginBottom)),this.plotSizeX=n?r:s,this.plotSizeY=n?s:r,this.spacingBox=c.spacingBox={x:h[3],y:h[0],width:a-h[3]-h[1],height:o-h[0]-h[2]},this.plotBox=c.plotBox={x:e,y:i,width:s,height:r},d&&(this.clipBox={x:p(d[3]),y:p(d[0]),width:p(this.plotSizeX-d[1]-d[3]),height:p(this.plotSizeY-d[0]-d[2])}),t||(this.axes.forEach(function(u){u.setAxisSize(),u.setAxisTranslation()}),c.alignElements()),U(this,"afterSetChartSize",{skipAxes:t})}resetMargins(){U(this,"resetMargins");let t=this,e=t.options.chart,i=e.plotBorderWidth||0,s=i/2;["margin","spacing"].forEach(function(r){let o=e[r],a=Id(o)?o:[o,o,o,o];["Top","Right","Bottom","Left"].forEach(function(n,h){t[r][h]=Tt(e[r+n],a[h])})}),aa.forEach(function(r,o){t[r]=Tt(t.margin[o],t.spacing[o])}),t.axisOffset=[0,0,0,0],t.clipOffset=[s,s,s,s],t.plotBorderWidth=i}drawChartBox(){let t=this.options.chart,e=this.renderer,i=this.chartWidth,s=this.chartHeight,r=this.styledMode,o=this.plotBGImage,a=t.backgroundColor,n=t.plotBackgroundColor,h=t.plotBackgroundImage,c=this.plotLeft,d=this.plotTop,p=this.plotWidth,u=this.plotHeight,g=this.plotBox,m=this.clipRect,x=this.clipBox,f=this.chartBackground,b=this.plotBackground,y=this.plotBorder,v,w,k,S="animate";f||(this.chartBackground=f=e.rect().addClass("highcharts-background").add(),S="attr"),r?v=w=f.strokeWidth():(w=(v=t.borderWidth||0)+(t.shadow?8:0),k={fill:a||"none"},(v||f["stroke-width"])&&(k.stroke=t.borderColor,k["stroke-width"]=v),f.attr(k).shadow(t.shadow)),f[S]({x:w/2,y:w/2,width:i-w-v%2,height:s-w-v%2,r:t.borderRadius}),S="animate",b||(S="attr",this.plotBackground=b=e.rect().addClass("highcharts-plot-background").add()),b[S](g),!r&&(b.attr({fill:n||"none"}).shadow(t.plotShadow),h&&(o?(h!==o.attr("href")&&o.attr("href",h),o.animate(g)):this.plotBGImage=e.image(h,c,d,p,u).add())),m?m.animate({width:x.width,height:x.height}):this.clipRect=e.clipRect(x),S="animate",y||(S="attr",this.plotBorder=y=e.rect().addClass("highcharts-plot-border").attr({zIndex:1}).add()),r||y.attr({stroke:t.plotBorderColor,"stroke-width":t.plotBorderWidth||0,fill:"none"}),y[S](y.crisp({x:c,y:d,width:p,height:u},-y.strokeWidth())),this.isDirtyBox=!1,U(this,"afterDrawChartBox")}propFromSeries(){let t,e,i,s=this,r=s.options.chart,o=s.options.series;["inverted","angular","polar"].forEach(function(a){for(e=Qs[r.type],i=r[a]||e&&e.prototype[a],t=o&&o.length;!i&&t--;)(e=Qs[o[t].type])&&e.prototype[a]&&(i=!0);s[a]=i})}linkSeries(t){let e=this,i=e.series;i.forEach(function(s){s.linkedSeries.length=0}),i.forEach(function(s){let{linkedTo:r}=s.options;if(Ui(r)){let o;(o=r===":previous"?e.series[s.index-1]:e.get(r))&&o.linkedParent!==s&&(o.linkedSeries.push(s),s.linkedParent=o,o.enabledDataSorting&&s.setDataSortingOptions(),s.visible=Tt(s.options.visible,o.options.visible,s.visible))}}),U(this,"afterLinkSeries",{isUpdating:t})}renderSeries(){this.series.forEach(function(t){t.translate(),t.render()})}render(){var c;let t=this.axes,e=this.colorAxis,i=this.renderer,s=this.options.chart.axisLayoutRuns||2,r=d=>{d.forEach(p=>{p.visible&&p.render()})},o=0,a=!0,n,h=0;for(let d of(this.setTitle(),U(this,"beforeMargins"),(c=this.getStacks)==null||c.call(this),this.getMargins(!0),this.setChartSize(),t)){let{options:p}=d,{labels:u}=p;if(this.hasCartesianSeries&&d.horiz&&d.visible&&u.enabled&&d.series.length&&d.coll!=="colorAxis"&&!this.polar){o=p.tickLength,d.createGroups();let g=new Le(d,0,"",!0),m=g.createLabel("x",u);if(g.destroy(),m&&Tt(u.reserveSpace,!Re(p.crossing))&&(o=m.getBBox().height+u.distance+Math.max(p.offset||0,0)),o){m==null||m.destroy();break}}}for(this.plotHeight=Math.max(this.plotHeight-o,0);(a||n||s>1)&&h<s;){let d=this.plotWidth,p=this.plotHeight;for(let u of t)h===0?u.setScale():(u.horiz&&a||!u.horiz&&n)&&u.setTickInterval(!0);h===0?this.getAxisMargins():this.getMargins(),a=d/this.plotWidth>(h?1:1.1),n=p/this.plotHeight>(h?1:1.05),h++}this.drawChartBox(),this.hasCartesianSeries?r(t):e&&e.length&&r(e),this.seriesGroup||(this.seriesGroup=i.g("series-group").attr({zIndex:3}).shadow(this.options.chart.seriesGroupShadow).add()),this.renderSeries(),this.addCredits(),this.setResponsive&&this.setResponsive(),this.hasRendered=!0}addCredits(t){let e=this,i=_t(!0,this.options.credits,t);i.enabled&&!this.credits&&(this.credits=this.renderer.text(i.text+(this.mapCredits||""),0,0).addClass("highcharts-credits").on("click",function(){i.href&&(na.location.href=i.href)}).attr({align:i.position.align,zIndex:8}),e.styledMode||this.credits.css(i.style),this.credits.add().align(i.position),this.credits.update=function(s){e.credits=e.credits.destroy(),e.addCredits(s)})}destroy(){let t,e=this,i=e.axes,s=e.series,r=e.container,o=r&&r.parentNode;for(U(e,"destroy"),e.renderer.forExport?Od(ye,e):ye[e.index]=void 0,A.chartCount--,e.renderTo.removeAttribute("data-highcharts-chart"),ca(e),t=i.length;t--;)i[t]=i[t].destroy();for(this.scroller&&this.scroller.destroy&&this.scroller.destroy(),t=s.length;t--;)s[t]=s[t].destroy();["title","subtitle","chartBackground","plotBackground","plotBGImage","plotBorder","seriesGroup","clipRect","credits","pointer","rangeSelector","legend","resetZoomButton","tooltip","renderer"].forEach(function(a){let n=e[a];n&&n.destroy&&(e[a]=n.destroy())}),r&&(r.innerHTML=it.emptyHTML,ca(r),o&&Ld(r)),rr(e,function(a,n){delete e[n]})}firstRender(){var s;let t=this,e=t.options;t.getContainer(),t.resetMargins(),t.setChartSize(),t.propFromSeries(),t.createAxes();let i=Dd(e.series)?e.series:[];e.series=[],i.forEach(function(r){t.initSeries(r)}),t.linkSeries(),t.setSortedData(),U(t,"beforeRender"),t.render(),(s=t.pointer)==null||s.getChartPosition(),t.renderer.imgCount||t.hasLoaded||t.onload(),t.temporaryDisplay(!0)}onload(){this.callbacks.concat([this.callback]).forEach(function(t){t&&this.index!==void 0&&t.apply(this,[this])},this),U(this,"load"),U(this,"render"),re(this.index)&&this.setReflow(),this.warnIfA11yModuleNotLoaded(),this.hasLoaded=!0}warnIfA11yModuleNotLoaded(){let{options:t,title:e}=this;!t||this.accessibility||(this.renderer.boxWrapper.attr({role:"img","aria-label":(e&&e.element.textContent||"").replace(/</g,"&lt;")}),t.accessibility&&t.accessibility.enabled===!1||er('Highcharts warning: Consider including the "accessibility.js" module to make your chart more usable for people with disabilities. Set the "accessibility.enabled" option to false to remove this warning. See https://www.highcharts.com/docs/accessibility/accessibility-module.',!1,this))}addSeries(t,e,i){let s,r=this;return t&&(e=Tt(e,!0),U(r,"addSeries",{options:t},function(){s=r.initSeries(t),r.isDirtyLegend=!0,r.linkSeries(),s.enabledDataSorting&&s.setData(t.data,!1),U(r,"afterAddSeries",{series:s}),e&&r.redraw(i)})),s}addAxis(t,e,i,s){return this.createAxis(e?"xAxis":"yAxis",{axis:t,redraw:i,animation:s})}addColorAxis(t,e,i){return this.createAxis("colorAxis",{axis:t,redraw:e,animation:i})}createAxis(t,e){let i=new Oe(this,e.axis,t);return Tt(e.redraw,!0)&&this.redraw(e.animation),i}showLoading(t){let e=this,i=e.options,s=i.loading,r=function(){o&&Ut(o,{left:e.plotLeft+"px",top:e.plotTop+"px",width:e.plotWidth+"px",height:e.plotHeight+"px"})},o=e.loadingDiv,a=e.loadingSpan;o||(e.loadingDiv=o=tr("div",{className:"highcharts-loading highcharts-loading-hidden"},null,e.container)),a||(e.loadingSpan=a=tr("span",{className:"highcharts-loading-inner"},null,o),Js(e,"redraw",r)),o.className="highcharts-loading",it.setElementHTML(a,Tt(t,i.lang.loading,"")),e.styledMode||(Ut(o,oe(s.style,{zIndex:10})),Ut(a,s.labelStyle),e.loadingShown||(Ut(o,{opacity:0,display:""}),Zs(o,{opacity:s.style.opacity||.5},{duration:s.showDuration||0}))),e.loadingShown=!0,r()}hideLoading(){let t=this.options,e=this.loadingDiv;e&&(e.className="highcharts-loading highcharts-loading-hidden",this.styledMode||Zs(e,{opacity:0},{duration:t.loading.hideDuration||100,complete:function(){Ut(e,{display:"none"})}})),this.loadingShown=!1}update(t,e,i,s){let r,o,a,n=this,h={credits:"addCredits",title:"setTitle",subtitle:"setSubtitle",caption:"setCaption"},c=t.isResponsiveOptions,d=[];U(n,"update",{options:t}),c||n.setResponsive(!1,!0),t=ha(t,n.options),n.userOptions=_t(n.userOptions,t);let p=t.chart;p&&(_t(!0,n.options.chart,p),this.setZoomOptions(),"className"in p&&n.setClassName(p.className),("inverted"in p||"polar"in p||"type"in p)&&(n.propFromSeries(),r=!0),"alignTicks"in p&&(r=!0),"events"in p&&oa(this,p),rr(p,function(m,x){n.propsRequireUpdateSeries.indexOf("chart."+x)!==-1&&(o=!0),n.propsRequireDirtyBox.indexOf(x)!==-1&&(n.isDirtyBox=!0),n.propsRequireReflow.indexOf(x)===-1||(n.isDirtyBox=!0,c||(a=!0))}),!n.styledMode&&p.style&&n.renderer.setStyle(n.options.chart.style||{})),!n.styledMode&&t.colors&&(this.options.colors=t.colors),rr(t,function(m,x){n[x]&&typeof n[x].update=="function"?n[x].update(m,!1):typeof n[h[x]]=="function"?n[h[x]](m):x!=="colors"&&n.collectionsWithUpdate.indexOf(x)===-1&&_t(!0,n.options[x],t[x]),x!=="chart"&&n.propsRequireUpdateSeries.indexOf(x)!==-1&&(o=!0)}),this.collectionsWithUpdate.forEach(function(m){t[m]&&(Vi(t[m]).forEach(function(x,f){let b,y=re(x.id);y&&(b=n.get(x.id)),!b&&n[m]&&(b=n[m][Tt(x.index,f)])&&(y&&re(b.options.id)||b.options.isInternal)&&(b=void 0),b&&b.coll===m&&(b.update(x,!1),i&&(b.touched=!0)),!b&&i&&n.collectionsWithInit[m]&&(n.collectionsWithInit[m][0].apply(n,[x].concat(n.collectionsWithInit[m][1]||[]).concat([!1])).touched=!0)}),i&&n[m].forEach(function(x){x.touched||x.options.isInternal?delete x.touched:d.push(x)}))}),d.forEach(function(m){m.chart&&m.remove&&m.remove(!1)}),r&&n.axes.forEach(function(m){m.update({},!1)}),o&&n.getSeriesOrderByLinks().forEach(function(m){m.chart&&m.update({},!1)},this);let u=p&&p.width,g=p&&(Ui(p.height)?da(p.height,u||n.chartWidth):p.height);a||Re(u)&&u!==n.chartWidth||Re(g)&&g!==n.chartHeight?n.setSize(u,g,s):Tt(e,!0)&&n.redraw(s),U(n,"afterUpdate",{options:t,redraw:e,animation:s})}setSubtitle(t,e){this.applyDescription("subtitle",t),this.layOutTitles(e)}setCaption(t,e){this.applyDescription("caption",t),this.layOutTitles(e)}showResetZoom(){let t=this,e=_s.lang,i=t.zooming.resetButton,s=i.theme,r=i.relativeTo==="chart"||i.relativeTo==="spacingBox"?null:"plotBox";function o(){t.zoomOut()}U(this,"beforeShowResetZoom",null,function(){t.resetZoomButton=t.renderer.button(e.resetZoom,null,null,o,s).attr({align:i.position.align,title:e.resetZoomTitle}).addClass("highcharts-reset-zoom").add().align(i.position,!1,r)}),U(this,"afterShowResetZoom")}zoomOut(){U(this,"selection",{resetSelection:!0},()=>this.transform({reset:!0,trigger:"zoom"}))}pan(t,e){let i=this,s=typeof e=="object"?e:{enabled:e,type:"x"},r=s.type,o=r&&i[{x:"xAxis",xy:"axes",y:"yAxis"}[r]].filter(n=>n.options.panningEnabled&&!n.options.isInternal),a=i.options.chart;a!=null&&a.panning&&(a.panning=s),U(this,"pan",{originalEvent:t},()=>{i.transform({axes:o,event:t,to:{x:t.chartX-(i.mouseDownX||0),y:t.chartY-(i.mouseDownY||0)},trigger:"pan"}),Ut(i.container,{cursor:"move"})})}transform(t){var g;let{axes:e=this.axes,event:i,from:s={},reset:r,selection:o,to:a={},trigger:n}=t,{inverted:h,time:c}=this,d=!1,p,u;for(let m of((g=this.hoverPoints)==null||g.forEach(x=>x.setState()),e)){let{horiz:x,len:f,minPointOffset:b=0,options:y,reversed:v}=m,w=x?"width":"height",k=x?"x":"y",S=Tt(a[w],m.len),M=Tt(s[w],m.len),T=10>Math.abs(S)?1:S/M,P=(s[k]||0)+M/2-m.pos,L=P-((a[k]??m.pos)+S/2-m.pos)/T,E=v&&!h||!v&&h?-1:1;if(!r&&(P<0||P>m.len))continue;let C=m.toValue(L,!0)+(o||m.isOrdinal?0:b*E),O=m.toValue(L+f/T,!0)-(o||m.isOrdinal?0:b*E||0),N=m.allExtremes;if(C>O&&([C,O]=[O,C]),T===1&&!r&&m.coll==="yAxis"&&!N){for(let nt of m.series){let bt=nt.getExtremes(nt.getProcessedData(!0).modified.getColumn("y")||[],!0);N??(N={dataMin:Number.MAX_VALUE,dataMax:-Number.MAX_VALUE}),Re(bt.dataMin)&&Re(bt.dataMax)&&(N.dataMin=Math.min(bt.dataMin,N.dataMin),N.dataMax=Math.max(bt.dataMax,N.dataMax))}m.allExtremes=N}let{dataMin:F,dataMax:I,min:X,max:D}=oe(m.getExtremes(),N||{}),z=c.parse(y.min),H=c.parse(y.max),V=F??z,tt=I??H,j=O-C,$=m.categories?0:Math.min(j,tt-V),_=V-$*(re(z)?0:y.minPadding),At=tt+$*(re(H)?0:y.maxPadding),ut=m.allowZoomOutside||T===1||n!=="zoom"&&T>1,Mt=Math.min(z??_,_,ut?X:_),gt=Math.max(H??At,At,ut?D:At);(!m.isOrdinal||m.options.overscroll||T!==1||r)&&(C<Mt&&(C=Mt,T>=1&&(O=C+j)),O>gt&&(O=gt,T>=1&&(C=O-j)),(r||m.series.length&&(C!==X||O!==D)&&C>=Mt&&O<=gt)&&(o?o[m.coll].push({axis:m,min:C,max:O}):(m.isPanning=n!=="zoom",m.isPanning&&(u=!0),m.setExtremes(r?void 0:C,r?void 0:O,!1,!1,{move:L,trigger:n,scale:T}),!r&&(C>Mt||O<gt)&&n!=="mousewheel"&&(p=!0)),d=!0),i&&(this[x?"mouseDownX":"mouseDownY"]=i[x?"chartX":"chartY"]))}return d&&(o?U(this,"selection",o,()=>{delete t.selection,t.trigger="zoom",this.transform(t)}):(!p||u||this.resetZoomButton?!p&&this.resetZoomButton&&(this.resetZoomButton=this.resetZoomButton.destroy()):this.showResetZoom(),this.redraw(n==="zoom"&&(this.options.chart.animation??this.pointCount<100)))),d}}oe(ae.prototype,{callbacks:[],collectionsWithInit:{xAxis:[ae.prototype.addAxis,[!0]],yAxis:[ae.prototype.addAxis,[!1]],series:[ae.prototype.addSeries]},collectionsWithUpdate:["xAxis","yAxis","series"],propsRequireDirtyBox:["backgroundColor","borderColor","borderWidth","borderRadius","plotBackgroundColor","plotBackgroundImage","plotBorderColor","plotBorderWidth","plotShadow","shadow"],propsRequireReflow:["margin","marginTop","marginRight","marginBottom","marginLeft","spacing","spacingTop","spacingRight","spacingBottom","spacingLeft"],propsRequireUpdateSeries:["chart.inverted","chart.polar","chart.ignoreHiddenSeries","chart.type","colors","plotOptions","time","tooltip"]});let{stop:Rd}=St,{composed:Wd}=A,{addEvent:ne,createElement:qi,css:or,defined:ar,erase:Hd,merge:pa,pushUnique:ua}=R;function Xd(){let l=this.scrollablePlotArea;(this.scrollablePixelsX||this.scrollablePixelsY)&&!l&&(this.scrollablePlotArea=l=new ii(this)),l==null||l.applyFixed()}function ga(){this.chart.scrollablePlotArea&&(this.chart.scrollablePlotArea.isDirty=!0)}class ii{static compose(t,e,i){ua(Wd,this.compose)&&(ne(t,"afterInit",ga),ne(e,"afterSetChartSize",s=>this.afterSetSize(s.target,s)),ne(e,"render",Xd),ne(i,"show",ga))}static afterSetSize(t,e){let i,s,r,{minWidth:o,minHeight:a}=t.options.chart.scrollablePlotArea||{},{clipBox:n,plotBox:h,inverted:c,renderer:d}=t;if(!d.forExport&&(o?(t.scrollablePixelsX=i=Math.max(0,o-t.chartWidth),i&&(t.scrollablePlotBox=pa(t.plotBox),h.width=t.plotWidth+=i,n[c?"height":"width"]+=i,r=!0)):a&&(t.scrollablePixelsY=s=Math.max(0,a-t.chartHeight),ar(s)&&(t.scrollablePlotBox=pa(t.plotBox),h.height=t.plotHeight+=s,n[c?"width":"height"]+=s,r=!1)),ar(r)&&!e.skipAxes))for(let p of t.axes)(p.horiz===r||t.hasParallelCoordinates&&p.coll==="yAxis")&&(p.setAxisSize(),p.setAxisTranslation())}constructor(t){var u;let e,i=t.options.chart,s=$e.getRendererType(),r=i.scrollablePlotArea||{},o=this.moveFixedElements.bind(this),a={WebkitOverflowScrolling:"touch",overflowX:"hidden",overflowY:"hidden"};t.scrollablePixelsX&&(a.overflowX="auto"),t.scrollablePixelsY&&(a.overflowY="auto"),this.chart=t;let n=this.parentDiv=qi("div",{className:"highcharts-scrolling-parent"},{position:"relative"},t.renderTo),h=this.scrollingContainer=qi("div",{className:"highcharts-scrolling"},a,n),c=this.innerContainer=qi("div",{className:"highcharts-inner-container"},void 0,h),d=this.fixedDiv=qi("div",{className:"highcharts-fixed"},{position:"absolute",overflow:"hidden",pointerEvents:"none",zIndex:(((u=i.style)==null?void 0:u.zIndex)||0)+2,top:0},void 0,!0),p=this.fixedRenderer=new s(d,t.chartWidth,t.chartHeight,i.style);this.mask=p.path().attr({fill:i.backgroundColor||"#fff","fill-opacity":r.opacity??.85,zIndex:-1}).addClass("highcharts-scrollable-mask").add(),h.parentNode.insertBefore(d,h),or(t.renderTo,{overflow:"visible"}),ne(t,"afterShowResetZoom",o),ne(t,"afterApplyDrilldown",o),ne(t,"afterLayOutTitles",o),ne(h,"scroll",()=>{let{pointer:g,hoverPoint:m}=t;g&&(delete g.chartPosition,m&&(e=m),g.runPointActions(void 0,e,!0))}),c.appendChild(t.container)}applyFixed(){var P;let{chart:t,fixedRenderer:e,isDirty:i,scrollingContainer:s}=this,{axisOffset:r,chartWidth:o,chartHeight:a,container:n,plotHeight:h,plotLeft:c,plotTop:d,plotWidth:p,scrollablePixelsX:u=0,scrollablePixelsY:g=0}=t,{scrollPositionX:m=0,scrollPositionY:x=0}=t.options.chart.scrollablePlotArea||{},f=o+u,b=a+g;e.setSize(o,a),(i??!0)&&(this.isDirty=!1,this.moveFixedElements()),Rd(t.container),or(n,{width:`${f}px`,height:`${b}px`}),t.renderer.boxWrapper.attr({width:f,height:b,viewBox:[0,0,f,b].join(" ")}),(P=t.chartBackground)==null||P.attr({width:f,height:b}),or(s,{width:`${o}px`,height:`${a}px`}),ar(i)||(s.scrollLeft=u*m,s.scrollTop=g*x);let y=d-r[0]-1,v=c-r[3]-1,w=d+h+r[2]+1,k=c+p+r[1]+1,S=c+p-u,M=d+h-g,T=[["M",0,0]];u?T=[["M",0,y],["L",c-1,y],["L",c-1,w],["L",0,w],["Z"],["M",S,y],["L",o,y],["L",o,w],["L",S,w],["Z"]]:g&&(T=[["M",v,0],["L",v,d-1],["L",k,d-1],["L",k,0],["Z"],["M",v,M],["L",v,a],["L",k,a],["L",k,M],["Z"]]),t.redrawTrigger!=="adjustHeight"&&this.mask.attr({d:T})}moveFixedElements(){let t,{container:e,inverted:i,scrollablePixelsX:s,scrollablePixelsY:r}=this.chart,o=this.fixedRenderer,a=ii.fixedSelectors;if(s&&!i?t=".highcharts-yaxis":s&&i||r&&!i?t=".highcharts-xaxis":r&&i&&(t=".highcharts-yaxis"),t&&!(this.chart.hasParallelCoordinates&&t===".highcharts-yaxis"))for(let n of[`${t}:not(.highcharts-radial-axis)`,`${t}-labels:not(.highcharts-radial-axis-labels)`])ua(a,n);else for(let n of[".highcharts-xaxis",".highcharts-yaxis"])for(let h of[`${n}:not(.highcharts-radial-axis)`,`${n}-labels:not(.highcharts-radial-axis-labels)`])Hd(a,h);for(let n of a)[].forEach.call(e.querySelectorAll(n),h=>{(h.namespaceURI===o.SVG_NS?o.box:o.box.parentNode).appendChild(h),h.style.pointerEvents="auto"})}}ii.fixedSelectors=[".highcharts-breadcrumbs-group",".highcharts-contextbutton",".highcharts-caption",".highcharts-credits",".highcharts-drillup-button",".highcharts-legend",".highcharts-legend-checkbox",".highcharts-navigator-series",".highcharts-navigator-xaxis",".highcharts-navigator-yaxis",".highcharts-navigator",".highcharts-range-selector-group",".highcharts-reset-zoom",".highcharts-scrollbar",".highcharts-subtitle",".highcharts-title"];let{format:Fd}=Yt,{series:Yd}=ht,{destroyObjectProperties:Gd,fireEvent:fa,getAlignFactor:nr,isNumber:lr,pick:si}=R,ma=class{constructor(l,t,e,i,s){let r=l.chart.inverted,o=l.reversed;this.axis=l;let a=this.isNegative=!!e!=!!o;this.options=t=t||{},this.x=i,this.total=null,this.cumulative=null,this.points={},this.hasValidPoints=!1,this.stack=s,this.leftCliff=0,this.rightCliff=0,this.alignOptions={align:t.align||(r?a?"left":"right":"center"),verticalAlign:t.verticalAlign||(r?"middle":a?"bottom":"top"),y:t.y,x:t.x},this.textAlign=t.textAlign||(r?a?"right":"left":"center")}destroy(){Gd(this,this.axis)}render(l){let t=this.axis.chart,e=this.options,i=e.format,s=i?Fd(i,this,t):e.formatter.call(this);if(this.label)this.label.attr({text:s,visibility:"hidden"});else{this.label=t.renderer.label(s,null,void 0,e.shape,void 0,void 0,e.useHTML,!1,"stack-labels");let r={r:e.borderRadius||0,text:s,padding:si(e.padding,5),visibility:"hidden"};t.styledMode||(r.fill=e.backgroundColor,r.stroke=e.borderColor,r["stroke-width"]=e.borderWidth,this.label.css(e.style||{})),this.label.attr(r),this.label.added||this.label.add(l)}this.label.labelrank=t.plotSizeY,fa(this,"afterRender")}setOffset(l,t,e,i,s,r){let{alignOptions:o,axis:a,label:n,options:h,textAlign:c}=this,d=a.chart,p=this.getStackBox({xOffset:l,width:t,boxBottom:e,boxTop:i,defaultX:s,xAxis:r}),{verticalAlign:u}=o;if(n&&p){let g=n.getBBox(void 0,0),m=n.padding,x=si(h.overflow,"justify")==="justify",f;o.x=h.x||0,o.y=h.y||0;let{x:b,y}=this.adjustStackPosition({labelBox:g,verticalAlign:u,textAlign:c});p.x-=b,p.y-=y,n.align(o,!1,p),(f=d.isInsidePlot(n.alignAttr.x+o.x+b,n.alignAttr.y+o.y+y))||(x=!1),x&&Yd.prototype.justifyDataLabel.call(a,n,o,n.alignAttr,g,p),n.attr({x:n.alignAttr.x,y:n.alignAttr.y,rotation:h.rotation,rotationOriginX:g.width*nr(h.textAlign||"center"),rotationOriginY:g.height/2}),si(!x&&h.crop,!0)&&(f=lr(n.x)&&lr(n.y)&&d.isInsidePlot(n.x-m+(n.width||0),n.y)&&d.isInsidePlot(n.x+m,n.y)),n[f?"show":"hide"]()}fa(this,"afterSetOffset",{xOffset:l,width:t})}adjustStackPosition({labelBox:l,verticalAlign:t,textAlign:e}){return{x:l.width/2+l.width/2*(2*nr(e)-1),y:l.height/2*2*(1-nr(t))}}getStackBox(l){let t=this.axis,e=t.chart,{boxTop:i,defaultX:s,xOffset:r,width:o,boxBottom:a}=l,n=t.stacking.usePercentage?100:si(i,this.total,0),h=t.toPixels(n),c=l.xAxis||e.xAxis[0],d=si(s,c.translate(this.x))+r,p=Math.abs(h-t.toPixels(a||lr(t.min)&&t.logarithmic&&t.logarithmic.lin2log(t.min)||0)),u=e.inverted,g=this.isNegative;return u?{x:(g?h:h-p)-e.plotLeft,y:c.height-d-o+c.top-e.plotTop,width:p,height:o}:{x:d+c.transB-e.plotLeft,y:(g?h-p:h)-e.plotTop,width:o,height:p}}},{getDeferredAnimation:jd}=St,{series:{prototype:$d}}=ht,{addEvent:xa,correctFloat:ri,defined:ya,destroyObjectProperties:Ud,fireEvent:Vd,isNumber:hr,objectEach:be,pick:dr}=R;function qd(){let l=this.inverted;this.axes.forEach(t=>{t.stacking&&t.stacking.stacks&&t.hasVisibleSeries&&(t.stacking.oldStacks=t.stacking.stacks)}),this.series.forEach(t=>{let e=t.xAxis&&t.xAxis.options||{};t.options.stacking&&t.reserveSpace()&&(t.stackKey=[t.type,dr(t.options.stack,""),l?e.top:e.left,l?e.height:e.width].join(","))})}function Zd(){var t;let l=this.stacking;if(l){let e=l.stacks;be(e,(i,s)=>{Ud(i),delete e[s]}),(t=l.stackTotalGroup)==null||t.destroy()}}function Kd(){this.stacking||(this.stacking=new ic(this))}function _d(l,t,e,i){return!ya(l)||l.x!==t||i&&l.stackKey!==i?l={x:t,index:0,key:i,stackKey:i}:l.index++,l.key=[e,t,l.index].join(","),l}function Qd(){let l,t=this,e=t.yAxis,i=t.stackKey||"",s=e.stacking.stacks,r=t.getColumn("x",!0),o=t.options.stacking,a=t[o+"Stacker"];a&&[i,"-"+i].forEach(n=>{var u;let h=r.length,c,d,p;for(;h--;)c=r[h],l=t.getStackIndicator(l,c,t.index,n),d=(u=s[n])==null?void 0:u[c],(p=d==null?void 0:d.points[l.key||""])&&a.call(t,p,d,h)})}function Jd(l,t,e){let i=t.total?100/t.total:0;l[0]=ri(l[0]*i),l[1]=ri(l[1]*i),this.stackedYData[e]=l[1]}function tc(l){(this.is("column")||this.is("columnrange"))&&(this.options.centerInCategory&&this.chart.series.length>1?$d.setStackedPoints.call(this,l,"group"):l.stacking.resetStacks())}function ec(l,t){var M,T;let e,i,s,r,o,a,n,h=t||this.options.stacking;if(!h||!this.reserveSpace()||({group:"xAxis"}[h]||"yAxis")!==l.coll)return;let c=this.getColumn("x",!0),d=this.getColumn(this.pointValKey||"y",!0),p=[],u=d.length,g=this.options,m=g.threshold||0,x=g.startFromThreshold?m:0,f=g.stack,b=t?`${this.type},${h}`:this.stackKey||"",y="-"+b,v=this.negStacks,w=l.stacking,k=w.stacks,S=w.oldStacks;for(w.stacksTouched+=1,n=0;n<u;n++){let P=c[n]||0,L=d[n],E=hr(L)&&L||0;a=(e=this.getStackIndicator(e,P,this.index)).key||"",k[o=(i=v&&E<(x?0:m))?y:b]||(k[o]={}),k[o][P]||((M=S[o])!=null&&M[P]?(k[o][P]=S[o][P],k[o][P].total=null):k[o][P]=new ma(l,l.options.stackLabels,!!i,P,f)),s=k[o][P],L!==null?(s.points[a]=s.points[this.index]=[dr(s.cumulative,x)],ya(s.cumulative)||(s.base=a),s.touched=w.stacksTouched,e.index>0&&this.singleStacks===!1&&(s.points[a][0]=s.points[this.index+","+P+",0"][0])):(delete s.points[a],delete s.points[this.index]);let C=s.total||0;h==="percent"?(r=i?b:y,C=v&&((T=k[r])!=null&&T[P])?(r=k[r][P]).total=Math.max(r.total||0,C)+Math.abs(E):ri(C+Math.abs(E))):h==="group"?hr(L)&&C++:C=ri(C+E),h==="group"?s.cumulative=(C||1)-1:s.cumulative=ri(dr(s.cumulative,x)+E),s.total=C,L!==null&&(s.points[a].push(s.cumulative),p[n]=s.cumulative,s.hasValidPoints=!0)}h==="percent"&&(w.usePercentage=!0),h!=="group"&&(this.stackedYData=p),w.oldStacks={}}class ic{constructor(t){this.oldStacks={},this.stacks={},this.stacksTouched=0,this.axis=t}buildStacks(){let t,e,i=this.axis,s=i.series,r=i.coll==="xAxis",o=i.options.reversedStacks,a=s.length;for(this.resetStacks(),this.usePercentage=!1,e=a;e--;)t=s[o?e:a-e-1],r&&t.setGroupedPoints(i),t.setStackedPoints(i);if(!r)for(e=0;e<a;e++)s[e].modifyStacks();Vd(i,"afterBuildStacks")}cleanStacks(){this.oldStacks&&(this.stacks=this.oldStacks,be(this.stacks,t=>{be(t,e=>{e.cumulative=e.total})}))}resetStacks(){be(this.stacks,t=>{be(t,(e,i)=>{hr(e.touched)&&e.touched<this.stacksTouched?(e.destroy(),delete t[i]):(e.total=null,e.cumulative=null)})})}renderStackTotals(){var a;let t=this.axis,e=t.chart,i=e.renderer,s=this.stacks,r=jd(e,((a=t.options.stackLabels)==null?void 0:a.animation)||!1),o=this.stackTotalGroup=this.stackTotalGroup||i.g("stack-labels").attr({zIndex:6,opacity:0}).add();o.translate(e.plotLeft,e.plotTop),be(s,n=>{be(n,h=>{h.render(o)})}),o.animate({opacity:1},r)}}(K||(K={})).compose=function(l,t,e){let i=t.prototype,s=e.prototype;i.getStacks||(xa(l,"init",Kd),xa(l,"destroy",Zd),i.getStacks=qd,s.getStackIndicator=_d,s.modifyStacks=Qd,s.percentStacker=Jd,s.setGroupedPoints=tc,s.setStackedPoints=ec)};let sc=K,{defined:rc,merge:ba,isObject:oc}=R;class va extends Nt{drawGraph(){let t=this.options,e=(this.gappedPath||this.getGraphPath).call(this),i=this.chart.styledMode;[this,...this.zones].forEach((s,r)=>{let o,a=s.graph,n=a?"animate":"attr",h=s.dashStyle||t.dashStyle;a?(a.endX=this.preventGraphAnimation?null:e.xMap,a.animate({d:e})):e.length&&(s.graph=a=this.chart.renderer.path(e).addClass("highcharts-graph"+(r?` highcharts-zone-graph-${r-1} `:" ")+(r&&s.className||"")).attr({zIndex:1}).add(this.group)),a&&!i&&(o={stroke:!r&&t.lineColor||s.color||this.color||"#cccccc","stroke-width":t.lineWidth||0,fill:this.fillGraph&&this.color||"none"},h?o.dashstyle=h:t.linecap!=="square"&&(o["stroke-linecap"]=o["stroke-linejoin"]="round"),a[n](o).shadow(t.shadow&&ba({filterUnits:"userSpaceOnUse"},oc(t.shadow)?t.shadow:{}))),a&&(a.startX=e.xMap,a.isArea=e.isArea)})}getGraphPath(t,e,i){let s=this,r=s.options,o=[],a=[],n,h=r.step,c=(t=t||s.points).reversed;return c&&t.reverse(),(h={right:1,center:2}[h]||h&&3)&&c&&(h=4-h),(t=this.getValidPoints(t,!1,!(r.connectNulls&&!e&&!i))).forEach(function(d,p){let u,g=d.plotX,m=d.plotY,x=t[p-1],f=d.isNull||typeof m!="number";(d.leftCliff||x&&x.rightCliff)&&!i&&(n=!0),f&&!rc(e)&&p>0?n=!r.connectNulls:f&&!e?n=!0:(p===0||n?u=[["M",d.plotX,d.plotY]]:s.getPointSpline?u=[s.getPointSpline(t,d,p)]:h?(u=h===1?[["L",x.plotX,m]]:h===2?[["L",(x.plotX+g)/2,x.plotY],["L",(x.plotX+g)/2,m]]:[["L",g,x.plotY]]).push(["L",g,m]):u=[["L",g,m]],a.push(d.x),h&&(a.push(d.x),h===2&&a.push(d.x)),o.push.apply(o,u),n=!1)}),o.xMap=a,s.graphPath=o,o}}va.defaultOptions=ba(Nt.defaultOptions,{legendSymbol:"lineMarker"}),ht.registerSeriesType("line",va);let{seriesTypes:{line:cr}}=ht,{extend:ac,merge:nc,objectEach:lc,pick:Zi}=R;class pr extends cr{drawGraph(){this.areaPath=[],super.drawGraph.apply(this);let{areaPath:t,options:e}=this;[this,...this.zones].forEach((i,s)=>{let r={},o=i.fillColor||e.fillColor,a=i.area,n=a?"animate":"attr";a?(a.endX=this.preventGraphAnimation?null:t.xMap,a.animate({d:t})):(r.zIndex=0,(a=i.area=this.chart.renderer.path(t).addClass("highcharts-area"+(s?` highcharts-zone-area-${s-1} `:" ")+(s&&i.className||"")).add(this.group)).isArea=!0),this.chart.styledMode||(r.fill=o||i.color||this.color,r["fill-opacity"]=o?1:e.fillOpacity??.75,a.css({pointerEvents:this.stickyTracking?"none":"auto"})),a[n](r),a.startX=t.xMap,a.shiftUnit=e.step?2:1})}getGraphPath(t){let e,i,s,r=cr.prototype.getGraphPath,o=this.options,a=o.stacking,n=this.yAxis,h=[],c=[],d=this.index,p=n.stacking.stacks[this.stackKey],u=o.threshold,g=Math.round(n.getThreshold(o.threshold)),m=Zi(o.connectNulls,a==="percent"),x=function(k,S,M){let T=t[k],P=a&&p[T.x].points[d],L=T[M+"Null"]||0,E=T[M+"Cliff"]||0,C,O,N=!0;E||L?(C=(L?P[0]:P[1])+E,O=P[0]+E,N=!!L):!a&&t[S]&&t[S].isNull&&(C=O=u),C!==void 0&&(c.push({plotX:e,plotY:C===null?g:n.getThreshold(C),isNull:N,isCliff:!0}),h.push({plotX:e,plotY:O===null?g:n.getThreshold(O),doCurve:!1}))};t=t||this.points,a&&(t=this.getStackPoints(t));for(let k=0,S=t.length;k<S;++k)a||(t[k].leftCliff=t[k].rightCliff=t[k].leftNull=t[k].rightNull=void 0),i=t[k].isNull,e=Zi(t[k].rectPlotX,t[k].plotX),s=a?Zi(t[k].yBottom,g):g,i&&!m||(m||x(k,k-1,"left"),i&&!a&&m||(c.push(t[k]),h.push({x:k,plotX:e,plotY:s})),m||x(k,k+1,"right"));let f=r.call(this,c,!0,!0);h.reversed=!0;let b=r.call(this,h,!0,!0),y=b[0];y&&y[0]==="M"&&(b[0]=["L",y[1],y[2]]);let v=f.concat(b);v.length&&v.push(["Z"]);let w=r.call(this,c,!1,m);return this.chart.series.length>1&&a&&c.some(k=>k.isCliff)&&(v.hasStackedCliffs=w.hasStackedCliffs=!0),v.xMap=f.xMap,this.areaPath=v,w}getStackPoints(t){let e=this,i=[],s=[],r=this.xAxis,o=this.yAxis,a=o.stacking.stacks[this.stackKey],n={},h=o.series,c=h.length,d=o.options.reversedStacks?1:-1,p=h.indexOf(e);if(t=t||this.points,this.options.stacking){for(let g=0;g<t.length;g++)t[g].leftNull=t[g].rightNull=void 0,n[t[g].x]=t[g];lc(a,function(g,m){g.total!==null&&s.push(m)}),s.sort(function(g,m){return g-m});let u=h.map(g=>g.visible);s.forEach(function(g,m){let x=0,f,b;if(n[g]&&!n[g].isNull)i.push(n[g]),[-1,1].forEach(function(y){let v=y===1?"rightNull":"leftNull",w=a[s[m+y]],k=0;if(w){let S=p;for(;S>=0&&S<c;){let M=h[S].index;!(f=w.points[M])&&(M===e.index?n[g][v]=!0:u[S]&&(b=a[g].points[M])&&(k-=b[1]-b[0])),S+=d}}n[g][y===1?"rightCliff":"leftCliff"]=k});else{let y=p;for(;y>=0&&y<c;){let v=h[y].index;if(f=a[g].points[v]){x=f[1];break}y+=d}x=Zi(x,0),x=o.translate(x,0,1,0,1),i.push({isNull:!0,plotX:r.translate(g,0,0,0,1),x:g,plotY:x,yBottom:x})}})}return i}}pr.defaultOptions=nc(cr.defaultOptions,{threshold:0,legendSymbol:"areaMarker"}),ac(pr.prototype,{singleStacks:!1}),ht.registerSeriesType("area",pr);let{line:ka}=ht.seriesTypes,{merge:hc,pick:Ki}=R;class ur extends ka{getPointSpline(t,e,i){let s,r,o,a,n=e.plotX||0,h=e.plotY||0,c=t[i-1],d=t[i+1];function p(g){return g&&!g.isNull&&g.doCurve!==!1&&!e.isCliff}if(p(c)&&p(d)){let g=c.plotX||0,m=c.plotY||0,x=d.plotX||0,f=d.plotY||0,b=0;s=(1.5*n+g)/2.5,r=(1.5*h+m)/2.5,o=(1.5*n+x)/2.5,a=(1.5*h+f)/2.5,o!==s&&(b=(a-r)*(o-n)/(o-s)+h-a),r+=b,a+=b,r>m&&r>h?(r=Math.max(m,h),a=2*h-r):r<m&&r<h&&(r=Math.min(m,h),a=2*h-r),a>f&&a>h?(a=Math.max(f,h),r=2*h-a):a<f&&a<h&&(a=Math.min(f,h),r=2*h-a),e.rightContX=o,e.rightContY=a,e.controlPoints={low:[s,r],high:[o,a]}}let u=["C",Ki(c.rightContX,c.plotX,0),Ki(c.rightContY,c.plotY,0),Ki(s,n,0),Ki(r,h,0),n,h];return c.rightContX=c.rightContY=void 0,u}}ur.defaultOptions=hc(ka.defaultOptions),ht.registerSeriesType("spline",ur);let wa=ur,{area:dc,area:{prototype:gr}}=ht.seriesTypes,{extend:cc,merge:pc}=R;class fr extends wa{}fr.defaultOptions=pc(wa.defaultOptions,dc.defaultOptions),cc(fr.prototype,{getGraphPath:gr.getGraphPath,getStackPoints:gr.getStackPoints,drawGraph:gr.drawGraph}),ht.registerSeriesType("areaspline",fr);let{animObject:uc}=St,{parse:gc}=pt,{noop:fc}=A,{clamp:_i,crisp:Qi,defined:Ma,extend:Sa,fireEvent:Ta,isArray:Aa,isNumber:Ji,merge:mr,pick:We,objectEach:mc}=R;class ts extends Nt{animate(t){let e,i,s=this,r=this.yAxis,o=r.pos,a=r.reversed,n=s.options,{clipOffset:h,inverted:c}=this.chart,d={},p=c?"translateX":"translateY";t&&h?(d.scaleY=.001,i=_i(r.toPixels(n.threshold||0),o,o+r.len),c?(i+=a?-Math.floor(h[0]):Math.ceil(h[2]),d.translateX=i-r.len):(i+=a?Math.ceil(h[0]):-Math.floor(h[2]),d.translateY=i),s.clipBox&&s.setClip(),s.group.attr(d)):(e=Number(s.group.attr(p)),s.group.animate({scaleY:1},Sa(uc(s.options.animation),{step:function(u,g){s.group&&(d[p]=e+g.pos*(o-e),s.group.attr(d))}})))}init(t,e){super.init.apply(this,arguments);let i=this;(t=i.chart).hasRendered&&t.series.forEach(function(s){s.type===i.type&&(s.isDirty=!0)})}getColumnMetrics(){var m,x;let t=this,e=t.options,i=t.xAxis,s=t.yAxis,r=i.options.reversedStacks,o=i.reversed&&!r||!i.reversed&&r,a={},n,h=0;e.grouping===!1?h=1:t.chart.series.forEach(function(f){let b,y=f.yAxis,v=f.options;f.type===t.type&&f.reserveSpace()&&s.len===y.len&&s.pos===y.pos&&(v.stacking&&v.stacking!=="group"?(a[n=f.stackKey]===void 0&&(a[n]=h++),b=a[n]):v.grouping!==!1&&(b=h++),f.columnIndex=b)});let c=Math.min(Math.abs(i.transA)*(!((m=i.brokenAxis)!=null&&m.hasBreaks)&&((x=i.ordinal)==null?void 0:x.slope)||e.pointRange||i.closestPointRange||i.tickInterval||1),i.len),d=c*e.groupPadding,p=(c-2*d)/(h||1),u=Math.min(e.maxPointWidth||i.len,We(e.pointWidth,p*(1-2*e.pointPadding))),g=(t.columnIndex||0)+(o?1:0);return t.columnMetrics={width:u,offset:(p-u)/2+(d+g*p-c/2)*(o?-1:1),paddedWidth:p,columnCount:h},t.columnMetrics}crispCol(t,e,i,s){let r=this.borderWidth,o=this.chart.inverted;return s=Qi(e+s,r,o)-(e=Qi(e,r,o)),this.options.crisp&&(i=Qi(t+i,r)-(t=Qi(t,r))),{x:t,y:e,width:i,height:s}}adjustForMissingColumns(t,e,i,s){var r;if(!i.isNull&&s.columnCount>1){let o=this.xAxis.series.filter(c=>c.visible).map(c=>c.index),a=0,n=0;mc((r=this.xAxis.stacking)==null?void 0:r.stacks,c=>{var g;let d=typeof i.x=="number"?(g=c[i.x.toString()])==null?void 0:g.points:void 0,p=d==null?void 0:d[this.index],u={};if(d&&Aa(p)){let m=this.index,x=Object.keys(d).filter(f=>!f.match(",")&&d[f]&&d[f].length>1).map(parseFloat).filter(f=>o.indexOf(f)!==-1).filter(f=>{let b=this.chart.series[f].options,y=b.stacking&&b.stack;if(Ma(y)){if(Ji(u[y]))return m===f&&(m=u[y]),!1;u[y]=f}return!0}).sort((f,b)=>b-f);a=x.indexOf(m),n=x.length}}),a=this.xAxis.reversed?n-1-a:a;let h=(n-1)*s.paddedWidth+e;t=(i.plotX||0)+h/2-e-a*s.paddedWidth}return t}translate(){let t=this,e=t.chart,i=t.options,s=t.dense=t.closestPointRange*t.xAxis.transA<2,r=t.borderWidth=We(i.borderWidth,s?0:1),o=t.xAxis,a=t.yAxis,n=i.threshold,h=We(i.minPointLength,5),c=t.getColumnMetrics(),d=c.width,p=t.pointXOffset=c.offset,u=t.dataMin,g=t.dataMax,m=t.translatedThreshold=a.getThreshold(n),x=t.barW=Math.max(d,1+2*r);i.pointPadding&&i.crisp&&(x=Math.ceil(x)),Nt.prototype.translate.apply(t),t.points.forEach(function(f){let b=We(f.yBottom,m),y=999+Math.abs(b),v=f.plotX||0,w=_i(f.plotY,-y,a.len+y),k,S=Math.min(w,b),M=Math.max(w,b)-S,T=d,P=v+p,L=x;h&&Math.abs(M)<h&&(M=h,k=!a.reversed&&!f.negative||a.reversed&&f.negative,Ji(n)&&Ji(g)&&f.y===n&&g<=n&&(a.min||0)<n&&(u!==g||(a.max||0)<=n)&&(k=!k,f.negative=!f.negative),S=Math.abs(S-m)>h?b-h:m-(k?h:0)),Ma(f.options.pointWidth)&&(P-=Math.round(((T=L=Math.ceil(f.options.pointWidth))-d)/2)),i.centerInCategory&&(P=t.adjustForMissingColumns(P,T,f,c)),f.barX=P,f.pointWidth=T,f.tooltipPos=e.inverted?[_i(a.len+a.pos-e.plotLeft-w,a.pos-e.plotLeft,a.len+a.pos-e.plotLeft),o.len+o.pos-e.plotTop-P-L/2,M]:[o.left-e.plotLeft+P+L/2,_i(w+a.pos-e.plotTop,a.pos-e.plotTop,a.len+a.pos-e.plotTop),M],f.shapeType=t.pointClass.prototype.shapeType||"roundedRect",f.shapeArgs=t.crispCol(P,f.isNull?m:S,L,f.isNull?0:M)}),Ta(this,"afterColumnTranslate")}drawGraph(){this.group[this.dense?"addClass":"removeClass"]("highcharts-dense-data")}pointAttribs(t,e){let i=this.options,s=this.pointAttrToOptions||{},r=s.stroke||"borderColor",o=s["stroke-width"]||"borderWidth",a,n,h,c=t&&t.color||this.color,d=t&&t[r]||i[r]||c,p=t&&t.options.dashStyle||i.dashStyle,u=t&&t[o]||i[o]||this[o]||0,g=We(t&&t.opacity,i.opacity,1);t&&this.zones.length&&(n=t.getZone(),c=t.options.color||n&&(n.color||t.nonZonedColor)||this.color,n&&(d=n.borderColor||d,p=n.dashStyle||p,u=n.borderWidth||u)),e&&t&&(h=(a=mr(i.states[e],t.options.states&&t.options.states[e]||{})).brightness,c=a.color||h!==void 0&&gc(c).brighten(a.brightness).get()||c,d=a[r]||d,u=a[o]||u,p=a.dashStyle||p,g=We(a.opacity,g));let m={fill:c,stroke:d,"stroke-width":u,opacity:g};return p&&(m.dashstyle=p),m}drawPoints(t=this.points){let e,i=this,s=this.chart,r=i.options,o=s.renderer,a=r.animationLimit||250;t.forEach(function(n){let h=n.plotY,c=n.graphic,d=!!c,p=c&&s.pointCount<a?"animate":"attr";Ji(h)&&n.y!==null?(e=n.shapeArgs,c&&n.hasNewShapeType()&&(c=c.destroy()),i.enabledDataSorting&&(n.startXPos=i.xAxis.reversed?-(e&&e.width||0):i.xAxis.width),!c&&(n.graphic=c=o[n.shapeType](e).add(n.group||i.group),c&&i.enabledDataSorting&&s.hasRendered&&s.pointCount<a&&(c.attr({x:n.startXPos}),d=!0,p="animate")),c&&d&&c[p](mr(e)),s.styledMode||c[p](i.pointAttribs(n,n.selected&&"select")).shadow(n.allowShadow!==!1&&r.shadow),c&&(c.addClass(n.getClassName(),!0),c.attr({visibility:n.visible?"inherit":"hidden"}))):c&&(n.graphic=c.destroy())})}drawTracker(t=this.points){let e,i=this,s=i.chart,r=s.pointer,o=function(a){r==null||r.normalize(a);let n=r==null?void 0:r.getPointFromEvent(a),h=!s.scrollablePlotArea||s.isInsidePlot(a.chartX-s.plotLeft,a.chartY-s.plotTop,{visiblePlotOnly:!0});r&&n&&i.options.enableMouseTracking&&h&&(r.isDirectTouch=!0,n.onMouseOver(a))};t.forEach(function(a){e=Aa(a.dataLabels)?a.dataLabels:a.dataLabel?[a.dataLabel]:[],a.graphic&&(a.graphic.element.point=a),e.forEach(function(n){(n.div||n.element).point=a})}),i._hasTracking||(i.trackerGroups.forEach(function(a){i[a]&&(i[a].addClass("highcharts-tracker").on("mouseover",o).on("mouseout",function(n){r==null||r.onTrackerMouseOut(n)}).on("touchstart",o),!s.styledMode&&i.options.cursor&&i[a].css({cursor:i.options.cursor}))}),i._hasTracking=!0),Ta(this,"afterDrawTracker")}remove(){let t=this,e=t.chart;e.hasRendered&&e.series.forEach(function(i){i.type===t.type&&(i.isDirty=!0)}),Nt.prototype.remove.apply(t,arguments)}}ts.defaultOptions=mr(Nt.defaultOptions,{borderRadius:3,centerInCategory:!1,groupPadding:.2,marker:null,pointPadding:.1,minPointLength:0,cropThreshold:50,pointRange:null,states:{hover:{halo:!1,brightness:.1},select:{color:"#cccccc",borderColor:"#000000"}},dataLabels:{align:void 0,verticalAlign:void 0,y:void 0},startFromThreshold:!0,stickyTracking:!1,tooltip:{distance:6},threshold:0,borderColor:"#ffffff"}),Sa(ts.prototype,{directTouch:!0,getSymbol:fc,negStacks:!0,trackerGroups:["group","dataLabelsGroup"]}),ht.registerSeriesType("column",ts);let es=ts,{getDeferredAnimation:xc}=St,{format:yc}=Yt,{defined:ve,extend:Ca,fireEvent:xr,getAlignFactor:Pa,isArray:le,isString:yr,merge:oi,objectEach:bc,pick:ai,pInt:vc,splat:La}=R;(function(l){function t(){return n(this).some(c=>c==null?void 0:c.enabled)}function e(c,d,p,u,g){var P;let{chart:m,enabledDataSorting:x}=this,f=this.isCartesian&&m.inverted,b=c.plotX,y=c.plotY,v=p.rotation||0,w=ve(b)&&ve(y)&&m.isInsidePlot(b,Math.round(y),{inverted:f,paneCoordinates:!0,series:this}),k=v===0&&ai(p.overflow,x?"none":"justify")==="justify",S=this.visible&&c.visible!==!1&&ve(b)&&(c.series.forceDL||x&&!k||w||ai(p.inside,!!this.options.stacking)&&u&&m.isInsidePlot(b,f?u.x+1:u.y+u.height-1,{inverted:f,paneCoordinates:!0,series:this})),M=c.pos();if(S&&M){var T;let L=d.getBBox(),E=d.getBBox(void 0,0);if(u=Ca({x:M[0],y:Math.round(M[1]),width:0,height:0},u||{}),p.alignTo==="plotEdges"&&this.isCartesian&&(u[f?"x":"y"]=0,u[f?"width":"height"]=((P=this.yAxis)==null?void 0:P.len)||0),Ca(p,{width:L.width,height:L.height}),T=u,x&&this.xAxis&&!k&&this.setDataLabelStartPos(c,d,g,w,T),d.align(oi(p,{width:E.width,height:E.height}),!1,u,!1),d.alignAttr.x+=Pa(p.align)*(E.width-L.width),d.alignAttr.y+=Pa(p.verticalAlign)*(E.height-L.height),d[d.placed?"animate":"attr"]({"text-align":d.alignAttr["text-align"]||"center",x:d.alignAttr.x+(L.width-E.width)/2,y:d.alignAttr.y+(L.height-E.height)/2,rotationOriginX:(d.width||0)/2,rotationOriginY:(d.height||0)/2}),k&&u.height>=0)this.justifyDataLabel(d,p,d.alignAttr,L,u,g);else if(ai(p.crop,!0)){let{x:C,y:O}=d.alignAttr;S=m.isInsidePlot(C,O,{paneCoordinates:!0,series:this})&&m.isInsidePlot(C+L.width-1,O+L.height-1,{paneCoordinates:!0,series:this})}p.shape&&!v&&d[g?"attr":"animate"]({anchorX:M[0],anchorY:M[1]})}g&&x&&(d.placed=!1),S||x&&!k?(d.show(),d.placed=!0):(d.hide(),d.placed=!1)}function i(){return this.plotGroup("dataLabelsGroup","data-labels",this.hasRendered?"inherit":"hidden",this.options.dataLabels.zIndex||6)}function s(c){let d=this.hasRendered||0,p=this.initDataLabelsGroup().attr({opacity:+d});return!d&&p&&(this.visible&&p.show(),this.options.animation?p.animate({opacity:1},c):p.attr({opacity:1})),p}function r(c){var S;let d;c=c||this.points;let p=this,u=p.chart,g=p.options,m=u.renderer,{backgroundColor:x,plotBackgroundColor:f}=u.options.chart,b=m.getContrast(yr(f)&&f||yr(x)&&x||"#000000"),y=n(p),{animation:v,defer:w}=y[0],k=w?xc(u,v,p):{defer:0,duration:0};xr(this,"drawDataLabels"),(S=p.hasDataLabels)!=null&&S.call(p)&&(d=this.initDataLabels(k),c.forEach(M=>{var L,E;let T=M.dataLabels||[];La(a(y,M.dlOptions||((L=M.options)==null?void 0:L.dataLabels))).forEach((C,O)=>{let N=C.enabled&&(M.visible||M.dataLabelOnHidden)&&(!M.isNull||M.dataLabelOnNull)&&function(At,ut){let Mt=ut.filter;if(Mt){let gt=Mt.operator,nt=At[Mt.property],bt=Mt.value;return gt===">"&&nt>bt||gt==="<"&&nt<bt||gt===">="&&nt>=bt||gt==="<="&&nt<=bt||gt==="=="&&nt==bt||gt==="==="&&nt===bt||gt==="!="&&nt!=bt||gt==="!=="&&nt!==bt}return!0}(M,C),{backgroundColor:F,borderColor:I,distance:X,style:D={}}=C,z,H,V,tt={},j=T[O],$=!j,_;N&&(H=ve(z=ai(C[M.formatPrefix+"Format"],C.format))?yc(z,M,u):(C[M.formatPrefix+"Formatter"]||C.formatter).call(M,C),V=C.rotation,!u.styledMode&&(D.color=ai(C.color,D.color,yr(p.color)?p.color:void 0,"#000000"),D.color==="contrast"?(F!=="none"&&(_=F),M.contrastColor=m.getContrast(_!=="auto"&&_||M.color||p.color),D.color=_||!ve(X)&&C.inside||0>vc(X||0)||g.stacking?M.contrastColor:b):delete M.contrastColor,g.cursor&&(D.cursor=g.cursor)),tt={r:C.borderRadius||0,rotation:V,padding:C.padding,zIndex:1},u.styledMode||(tt.fill=F==="auto"?M.color:F,tt.stroke=I==="auto"?M.color:I,tt["stroke-width"]=C.borderWidth),bc(tt,(At,ut)=>{At===void 0&&delete tt[ut]})),!j||N&&ve(H)&&!!j.div==!!C.useHTML&&(j.rotation&&C.rotation||j.rotation===C.rotation)||(j=void 0,$=!0),N&&ve(H)&&(j?tt.text=H:(j=m.label(H,0,0,C.shape,void 0,void 0,C.useHTML,void 0,"data-label")).addClass(" highcharts-data-label-color-"+M.colorIndex+" "+(C.className||"")+(C.useHTML?" highcharts-tracker":"")),j&&(j.options=C,j.attr(tt),u.styledMode?D.width&&j.css({width:D.width,textOverflow:D.textOverflow,whiteSpace:D.whiteSpace}):j.css(D).shadow(C.shadow),xr(j,"beforeAddingDataLabel",{labelOptions:C,point:M}),j.added||j.add(d),p.alignDataLabel(M,j,C,void 0,$),j.isActive=!0,T[O]&&T[O]!==j&&T[O].destroy(),T[O]=j))});let P=T.length;for(;P--;)T[P]&&T[P].isActive?T[P].isActive=!1:((E=T[P])==null||E.destroy(),T.splice(P,1));M.dataLabel=T[0],M.dataLabels=T})),xr(this,"afterDrawDataLabels")}function o(c,d,p,u,g,m){let x=this.chart,f=d.align,b=d.verticalAlign,y=c.box?0:c.padding||0,v=x.inverted?this.yAxis:this.xAxis,w=v?v.left-x.plotLeft:0,k=x.inverted?this.xAxis:this.yAxis,S=k?k.top-x.plotTop:0,{x:M=0,y:T=0}=d,P,L;return(P=(p.x||0)+y+w)<0&&(f==="right"&&M>=0?(d.align="left",d.inside=!0):M-=P,L=!0),(P=(p.x||0)+u.width-y+w)>x.plotWidth&&(f==="left"&&M<=0?(d.align="right",d.inside=!0):M+=x.plotWidth-P,L=!0),(P=p.y+y+S)<0&&(b==="bottom"&&T>=0?(d.verticalAlign="top",d.inside=!0):T-=P,L=!0),(P=(p.y||0)+u.height-y+S)>x.plotHeight&&(b==="top"&&T<=0?(d.verticalAlign="bottom",d.inside=!0):T+=x.plotHeight-P,L=!0),L&&(d.x=M,d.y=T,c.placed=!m,c.align(d,void 0,g)),L}function a(c,d){let p=[],u;if(le(c)&&!le(d))p=c.map(function(g){return oi(g,d)});else if(le(d)&&!le(c))p=d.map(function(g){return oi(c,g)});else if(le(c)||le(d)){if(le(c)&&le(d))for(u=Math.max(c.length,d.length);u--;)p[u]=oi(c[u],d[u])}else p=oi(c,d);return p}function n(c){var p,u;let d=c.chart.options.plotOptions;return La(a(a((p=d==null?void 0:d.series)==null?void 0:p.dataLabels,(u=d==null?void 0:d[c.type])==null?void 0:u.dataLabels),c.options.dataLabels))}function h(c,d,p,u,g){let m=this.chart,x=m.inverted,f=this.xAxis,b=f.reversed,y=((x?d.height:d.width)||0)/2,v=c.pointWidth,w=v?v/2:0;d.startXPos=x?g.x:b?-y-w:f.width-y+w,d.startYPos=x?b?this.yAxis.height-y+w:-y-w:g.y,u?d.visibility==="hidden"&&(d.show(),d.attr({opacity:0}).animate({opacity:1})):d.attr({opacity:1}).animate({opacity:0},void 0,d.hide),m.hasRendered&&(p&&d.attr({x:d.startXPos,y:d.startYPos}),d.placed=!0)}l.compose=function(c){let d=c.prototype;d.initDataLabels||(d.initDataLabels=s,d.initDataLabelsGroup=i,d.alignDataLabel=e,d.drawDataLabels=r,d.justifyDataLabel=o,d.setDataLabelStartPos=h,d.hasDataLabels=t)}})(Pt||(Pt={}));let is=Pt,{composed:kc}=A,{series:Oa}=ht,{merge:wc,pick:ni,pushUnique:Mc}=R;(function(l){function t(e,i,s,r,o){let a=this.chart.inverted,n=e.series,h=(n.xAxis?n.xAxis.len:this.chart.plotSizeX)||0,c=(n.yAxis?n.yAxis.len:this.chart.plotSizeY)||0,d=e.dlBox||e.shapeArgs,p=ni(e.below,e.plotY>ni(this.translatedThreshold,c)),u=ni(s.inside,!!this.options.stacking);if(d){if(r=wc(d),!(s.overflow==="allow"&&s.crop===!1)){r.y<0&&(r.height+=r.y,r.y=0);let g=r.y+r.height-c;g>0&&g<r.height-1&&(r.height-=g)}a&&(r={x:c-r.y-r.height,y:h-r.x-r.width,width:r.height,height:r.width}),u||(a?(r.x+=p?0:r.width,r.width=0):(r.y+=p?r.height:0,r.height=0))}s.align=ni(s.align,!a||u?"center":p?"right":"left"),s.verticalAlign=ni(s.verticalAlign,a||u?"middle":p?"top":"bottom"),Oa.prototype.alignDataLabel.call(this,e,i,s,r,o),s.inside&&e.contrastColor&&i.css({color:e.contrastColor})}l.compose=function(e){is.compose(Oa),Mc(kc,"ColumnDataLabel")&&(e.prototype.alignDataLabel=t)}})(ct||(ct={}));let Sc=ct,{extend:Tc,merge:Ac}=R;class br extends es{}br.defaultOptions=Ac(es.defaultOptions,{}),Tc(br.prototype,{inverted:!0}),ht.registerSeriesType("bar",br);let{column:Cc,line:Ea}=ht.seriesTypes,{addEvent:Pc,extend:Lc,merge:Oc}=R;class ss extends Ea{applyJitter(){let t=this,e=this.options.jitter,i=this.points.length;e&&this.points.forEach(function(s,r){["x","y"].forEach(function(o,a){if(e[o]&&!s.isNull){let n=`plot${o.toUpperCase()}`,h=t[`${o}Axis`],c=e[o]*h.transA;if(h&&!h.logarithmic){let d=Math.max(0,(s[n]||0)-c),p=Math.min(h.len,(s[n]||0)+c);s[n]=d+(p-d)*function(u){let g=1e4*Math.sin(u);return g-Math.floor(g)}(r+a*i),o==="x"&&(s.clientX=s.plotX)}}})})}drawGraph(){this.options.lineWidth?super.drawGraph():this.graph&&(this.graph=this.graph.destroy())}}ss.defaultOptions=Oc(Ea.defaultOptions,{lineWidth:0,findNearestPointBy:"xy",jitter:{x:0,y:0},marker:{enabled:!0},tooltip:{headerFormat:'<span style="color:{point.color}">●</span> <span style="font-size: 0.8em"> {series.name}</span><br/>',pointFormat:"x: <b>{point.x}</b><br/>y: <b>{point.y}</b><br/>"}}),Lc(ss.prototype,{drawTracker:Cc.prototype.drawTracker,sorted:!1,requireSorting:!1,noSharedTooltip:!0,trackerGroups:["group","markerGroup","dataLabelsGroup"]}),Pc(ss,"afterTranslate",function(){this.applyJitter()}),ht.registerSeriesType("scatter",ss);let{deg2rad:Da}=A,{fireEvent:Ec,isNumber:vr,pick:rs,relativeLength:Dc}=R;(function(l){l.getCenter=function(){let t=this.options,e=this.chart,i=2*(t.slicedOffset||0),s=e.plotWidth-2*i,r=e.plotHeight-2*i,o=t.center,a=Math.min(s,r),n=t.thickness,h,c=t.size,d=t.innerSize||0,p,u;typeof c=="string"&&(c=parseFloat(c)),typeof d=="string"&&(d=parseFloat(d));let g=[rs(o==null?void 0:o[0],"50%"),rs(o==null?void 0:o[1],"50%"),rs(c&&c<0?void 0:t.size,"100%"),rs(d&&d<0?void 0:t.innerSize||0,"0%")];for(!e.angular||this instanceof Nt||(g[3]=0),p=0;p<4;++p)u=g[p],h=p<2||p===2&&/%$/.test(u),g[p]=Dc(u,[s,r,a,g[2]][p])+(h?i:0);return g[3]>g[2]&&(g[3]=g[2]),vr(n)&&2*n<g[2]&&n>0&&(g[3]=g[2]-2*n),Ec(this,"afterGetCenter",{positions:g}),g},l.getStartAndEndRadians=function(t,e){let i=vr(t)?t:0,s=vr(e)&&e>i&&e-i<360?e:i+360;return{start:Da*(i+-90),end:Da*(s+-90)}}})(et||(et={}));let Ia=et,{setAnimation:Ic}=St,{addEvent:Ba,defined:Bc,extend:Nc,isNumber:zc,pick:Rc,relativeLength:Wc}=R;class Na extends ie{getConnectorPath(t){let e=t.dataLabelPosition,i=t.options||{},s=i.connectorShape,r=this.connectorShapes[s]||s;return e&&r.call(this,{...e.computed,alignment:e.alignment},e.connectorPosition,i)||[]}getTranslate(){return this.sliced&&this.slicedTranslation||{translateX:0,translateY:0}}haloPath(t){let e=this.shapeArgs;return this.sliced||!this.visible?[]:this.series.chart.renderer.symbols.arc(e.x,e.y,e.r+t,e.r+t,{innerR:e.r-1,start:e.start,end:e.end,borderRadius:e.borderRadius})}constructor(t,e,i){super(t,e,i),this.half=0,this.name??(this.name="Slice");let s=r=>{this.slice(r.type==="select")};Ba(this,"select",s),Ba(this,"unselect",s)}isValid(){return zc(this.y)&&this.y>=0}setVisible(t,e=!0){t!==this.visible&&this.update({visible:t??!this.visible},e,void 0,!1)}slice(t,e,i){let s=this.series;Ic(i,s.chart),e=Rc(e,!0),this.sliced=this.options.sliced=t=Bc(t)?t:!this.sliced,s.options.data[s.data.indexOf(this)]=this.options,this.graphic&&this.graphic.animate(this.getTranslate())}}Nc(Na.prototype,{connectorShapes:{fixedOffset:function(l,t,e){let i=t.breakAt,s=t.touchingSliceAt,r=e.softConnector?["C",l.x+(l.alignment==="left"?-5:5),l.y,2*i.x-s.x,2*i.y-s.y,i.x,i.y]:["L",i.x,i.y];return[["M",l.x,l.y],r,["L",s.x,s.y]]},straight:function(l,t){let e=t.touchingSliceAt;return[["M",l.x,l.y],["L",e.x,e.y]]},crookedLine:function(l,t,e){let{angle:i=this.angle||0,breakAt:s,touchingSliceAt:r}=t,{series:o}=this,[a,n,h]=o.center,c=h/2,{plotLeft:d,plotWidth:p}=o.chart,u=l.alignment==="left",{x:g,y:m}=l,x=s.x;if(e.crookDistance){let b=Wc(e.crookDistance,1);x=u?a+c+(p+d-a-c)*(1-b):d+(a-c)*b}else x=a+(n-m)*Math.tan(i-Math.PI/2);let f=[["M",g,m]];return(u?x<=g&&x>=s.x:x>=g&&x<=s.x)&&f.push(["L",x,m]),f.push(["L",s.x,s.y],["L",r.x,r.y]),f}}});let{getStartAndEndRadians:Hc}=Ia,{noop:za}=A,{clamp:Xc,extend:Fc,fireEvent:Ra,merge:kr,pick:Yc}=R;class wr extends Nt{animate(t){let e=this,i=e.points,s=e.startAngleRad;t||i.forEach(function(r){let o=r.graphic,a=r.shapeArgs;o&&a&&(o.attr({r:Yc(r.startR,e.center&&e.center[3]/2),start:s,end:s}),o.animate({r:a.r,start:a.start,end:a.end},e.options.animation))})}drawEmpty(){let t,e,i=this.startAngleRad,s=this.endAngleRad,r=this.options;this.total===0&&this.center?(t=this.center[0],e=this.center[1],this.graph||(this.graph=this.chart.renderer.arc(t,e,this.center[1]/2,0,i,s).addClass("highcharts-empty-series").add(this.group)),this.graph.attr({d:co.arc(t,e,this.center[2]/2,0,{start:i,end:s,innerR:this.center[3]/2})}),this.chart.styledMode||this.graph.attr({"stroke-width":r.borderWidth,fill:r.fillColor||"none",stroke:r.color||"#cccccc"})):this.graph&&(this.graph=this.graph.destroy())}drawPoints(){let t=this.chart.renderer;this.points.forEach(function(e){e.graphic&&e.hasNewShapeType()&&(e.graphic=e.graphic.destroy()),e.graphic||(e.graphic=t[e.shapeType](e.shapeArgs).add(e.series.group),e.delayedRendering=!0)})}generatePoints(){super.generatePoints(),this.updateTotals()}getX(t,e,i,s){let r=this.center,o=this.radii?this.radii[i.index]||0:r[2]/2,a=s.dataLabelPosition,n=(a==null?void 0:a.distance)||0,h=Math.asin(Xc((t-r[1])/(o+n),-1,1));return r[0]+Math.cos(h)*(o+n)*(e?-1:1)+(n>0?(e?-1:1)*(s.padding||0):0)}hasData(){return!!this.dataTable.rowCount}redrawPoints(){let t,e,i,s,r=this,o=r.chart;this.drawEmpty(),r.group&&!o.styledMode&&r.group.shadow(r.options.shadow),r.points.forEach(function(a){let n={};e=a.graphic,!a.isNull&&e?(s=a.shapeArgs,t=a.getTranslate(),o.styledMode||(i=r.pointAttribs(a,a.selected&&"select")),a.delayedRendering?(e.setRadialReference(r.center).attr(s).attr(t),o.styledMode||e.attr(i).attr({"stroke-linejoin":"round"}),a.delayedRendering=!1):(e.setRadialReference(r.center),o.styledMode||kr(!0,n,i),kr(!0,n,s,t),e.animate(n)),e.attr({visibility:a.visible?"inherit":"hidden"}),e.addClass(a.getClassName(),!0)):e&&(a.graphic=e.destroy())})}sortByAngle(t,e){t.sort(function(i,s){return i.angle!==void 0&&(s.angle-i.angle)*e})}translate(t){Ra(this,"translate"),this.generatePoints();let e=this.options,i=e.slicedOffset,s=Hc(e.startAngle,e.endAngle),r=this.startAngleRad=s.start,o=(this.endAngleRad=s.end)-r,a=this.points,n=e.ignoreHiddenPoint,h=a.length,c,d,p,u,g,m,x,f=0;for(t||(this.center=t=this.getCenter()),m=0;m<h;m++){x=a[m],c=r+f*o,x.isValid()&&(!n||x.visible)&&(f+=x.percentage/100),d=r+f*o;let b={x:t[0],y:t[1],r:t[2]/2,innerR:t[3]/2,start:Math.round(1e3*c)/1e3,end:Math.round(1e3*d)/1e3};x.shapeType="arc",x.shapeArgs=b,(p=(d+c)/2)>1.5*Math.PI?p-=2*Math.PI:p<-Math.PI/2&&(p+=2*Math.PI),x.slicedTranslation={translateX:Math.round(Math.cos(p)*i),translateY:Math.round(Math.sin(p)*i)},u=Math.cos(p)*t[2]/2,g=Math.sin(p)*t[2]/2,x.tooltipPos=[t[0]+.7*u,t[1]+.7*g],x.half=p<-Math.PI/2||p>Math.PI/2?1:0,x.angle=p}Ra(this,"afterTranslate")}updateTotals(){let t=this.points,e=t.length,i=this.options.ignoreHiddenPoint,s,r,o=0;for(s=0;s<e;s++)(r=t[s]).isValid()&&(!i||r.visible)&&(o+=r.y);for(s=0,this.total=o;s<e;s++)(r=t[s]).percentage=o>0&&(r.visible||!i)?r.y/o*100:0,r.total=o}}wr.defaultOptions=kr(Nt.defaultOptions,{borderRadius:3,center:[null,null],clip:!1,colorByPoint:!0,dataLabels:{connectorPadding:5,connectorShape:"crookedLine",crookDistance:void 0,distance:30,enabled:!0,formatter:function(){return this.isNull?void 0:this.name},softConnector:!0,x:0},fillColor:void 0,ignoreHiddenPoint:!0,inactiveOtherPoints:!0,legendType:"point",marker:null,size:null,showInLegend:!1,slicedOffset:10,stickyTracking:!1,tooltip:{followPointer:!0},borderColor:"#ffffff",borderWidth:1,lineWidth:void 0,states:{hover:{brightness:.1}}}),Fc(wr.prototype,{axisTypes:[],directTouch:!0,drawGraph:void 0,drawTracker:es.prototype.drawTracker,getCenter:Ia.getCenter,getSymbol:za,invertible:!1,isCartesian:!1,noSharedTooltip:!0,pointAttribs:es.prototype.pointAttribs,pointClass:Na,requireSorting:!1,searchPoint:za,trackerGroups:["group","dataLabelsGroup"]}),ht.registerSeriesType("pie",wr);let{composed:Gc,noop:jc}=A,{distribute:$c}=yi,{series:Wa}=ht,{arrayMax:Uc,clamp:Ha,defined:Xa,pick:Vc,pushUnique:qc,relativeLength:Fa}=R;(function(l){let t={radialDistributionY:function(o,a){var n;return(((n=a.dataLabelPosition)==null?void 0:n.top)||0)+o.distributeBox.pos},radialDistributionX:function(o,a,n,h,c){let d=c.dataLabelPosition;return o.getX(n<((d==null?void 0:d.top)||0)+2||n>((d==null?void 0:d.bottom)||0)-2?h:n,a.half,a,c)},justify:function(o,a,n,h){var c;return h[0]+(o.half?-1:1)*(n+(((c=a.dataLabelPosition)==null?void 0:c.distance)||0))},alignToPlotEdges:function(o,a,n,h){let c=o.getBBox().width;return a?c+h:n-c-h},alignToConnectors:function(o,a,n,h){let c=0,d;return o.forEach(function(p){(d=p.dataLabel.getBBox().width)>c&&(c=d)}),a?c+h:n-c-h}};function e(o,a){let n=Math.PI/2,{start:h=0,end:c=0}=o.shapeArgs||{},d=o.angle||0;a>0&&h<n&&c>n&&d>n/2&&d<1.5*n&&(d=d<=n?Math.max(n/2,(h+n)/2):Math.min(1.5*n,(n+c)/2));let{center:p,options:u}=this,g=p[2]/2,m=Math.cos(d),x=Math.sin(d),f=p[0]+m*g,b=p[1]+x*g,y=Math.min((u.slicedOffset||0)+(u.borderWidth||0),a/5);return{natural:{x:f+m*a,y:b+x*a},computed:{},alignment:a<0?"center":o.half?"right":"left",connectorPosition:{angle:d,breakAt:{x:f+m*y,y:b+x*y},touchingSliceAt:{x:f,y:b}},distance:a}}function i(){var S;let o=this,a=o.points,n=o.chart,h=n.plotWidth,c=n.plotHeight,d=n.plotLeft,p=Math.round(n.chartWidth/3),u=o.center,g=u[2]/2,m=u[1],x=[[],[]],f=[0,0,0,0],b=o.dataLabelPositioners,y,v,w,k=0;o.visible&&((S=o.hasDataLabels)!=null&&S.call(o))&&(a.forEach(M=>{(M.dataLabels||[]).forEach(T=>{T.shortened&&(T.attr({width:"auto"}).css({width:"auto",textOverflow:"clip"}),T.shortened=!1)})}),Wa.prototype.drawDataLabels.apply(o),a.forEach(M=>{(M.dataLabels||[]).forEach((T,P)=>{var O;let L=u[2]/2,E=T.options,C=Fa((E==null?void 0:E.distance)||0,L);P===0&&x[M.half].push(M),!Xa((O=E==null?void 0:E.style)==null?void 0:O.width)&&T.getBBox().width>p&&(T.css({width:Math.round(.7*p)+"px"}),T.shortened=!0),T.dataLabelPosition=this.getDataLabelPosition(M,C),k=Math.max(k,C)})}),x.forEach((M,T)=>{let P=M.length,L=[],E,C,O=0,N;P&&(o.sortByAngle(M,T-.5),k>0&&(E=Math.max(0,m-g-k),C=Math.min(m+g+k,n.plotHeight),M.forEach(F=>{(F.dataLabels||[]).forEach(I=>{var D;let X=I.dataLabelPosition;X&&X.distance>0&&(X.top=Math.max(0,m-g-X.distance),X.bottom=Math.min(m+g+X.distance,n.plotHeight),O=I.getBBox().height||21,I.lineHeight=n.renderer.fontMetrics(I.text||I).h+2*I.padding,F.distributeBox={target:(((D=I.dataLabelPosition)==null?void 0:D.natural.y)||0)-X.top+I.lineHeight/2,size:O,rank:F.y},L.push(F.distributeBox))})}),$c(L,N=C+O-E,N/5)),M.forEach(F=>{(F.dataLabels||[]).forEach(I=>{let X=I.options||{},D=F.distributeBox,z=I.dataLabelPosition,H=(z==null?void 0:z.natural.y)||0,V=X.connectorPadding||0,tt=I.lineHeight||21,j=(tt-I.getBBox().height)/2,$=0,_=H,At="inherit";if(z){if(L&&Xa(D)&&z.distance>0&&(D.pos===void 0?At="hidden":(w=D.size,_=b.radialDistributionY(F,I))),X.justify)$=b.justify(F,I,g,u);else switch(X.alignTo){case"connectors":$=b.alignToConnectors(M,T,h,d);break;case"plotEdges":$=b.alignToPlotEdges(I,T,h,d);break;default:$=b.radialDistributionX(o,F,_-j,H,I)}if(z.attribs={visibility:At,align:z.alignment},z.posAttribs={x:$+(X.x||0)+({left:V,right:-V}[z.alignment]||0),y:_+(X.y||0)-tt/2},z.computed.x=$,z.computed.y=_-j,Vc(X.crop,!0)){let ut;$-(v=I.getBBox().width)<V&&T===1?(ut=Math.round(v-$+V),f[3]=Math.max(ut,f[3])):$+v>h-V&&T===0&&(ut=Math.round($+v-h+V),f[1]=Math.max(ut,f[1])),_-w/2<0?f[0]=Math.max(Math.round(-_+w/2),f[0]):_+w/2>c&&(f[2]=Math.max(Math.round(_+w/2-c),f[2])),z.sideOverflow=ut}}})}))}),(Uc(f)===0||this.verifyDataLabelOverflow(f))&&(this.placeDataLabels(),this.points.forEach(M=>{(M.dataLabels||[]).forEach(T=>{var C;let{connectorColor:P,connectorWidth:L=1}=T.options||{},E=T.dataLabelPosition;if(L){let O;y=T.connector,E&&E.distance>0?(O=!y,y||(T.connector=y=n.renderer.path().addClass("highcharts-data-label-connector  highcharts-color-"+M.colorIndex+(M.className?" "+M.className:"")).add(o.dataLabelsGroup)),n.styledMode||y.attr({"stroke-width":L,stroke:P||M.color||"#666666"}),y[O?"attr":"animate"]({d:M.getConnectorPath(T)}),y.attr({visibility:(C=E.attribs)==null?void 0:C.visibility})):y&&(T.connector=y.destroy())}})})))}function s(){this.points.forEach(o=>{(o.dataLabels||[]).forEach(a=>{var h;let n=a.dataLabelPosition;n?(n.sideOverflow&&(a.css({width:Math.max(a.getBBox().width-n.sideOverflow,0)+"px",textOverflow:(((h=a.options)==null?void 0:h.style)||{}).textOverflow||"ellipsis"}),a.shortened=!0),a.attr(n.attribs),a[a.moved?"animate":"attr"](n.posAttribs),a.moved=!0):a&&a.attr({y:-9999})}),delete o.distributeBox},this)}function r(o){let a=this.center,n=this.options,h=n.center,c=n.minSize||80,d=c,p=n.size!==null;return!p&&(h[0]!==null?d=Math.max(a[2]-Math.max(o[1],o[3]),c):(d=Math.max(a[2]-o[1]-o[3],c),a[0]+=(o[3]-o[1])/2),h[1]!==null?d=Ha(d,c,a[2]-Math.max(o[0],o[2])):(d=Ha(d,c,a[2]-o[0]-o[2]),a[1]+=(o[0]-o[2])/2),d<a[2]?(a[2]=d,a[3]=Math.min(n.thickness?Math.max(0,d-2*n.thickness):Math.max(0,Fa(n.innerSize||0,d)),d),this.translate(a),this.drawDataLabels&&this.drawDataLabels()):p=!0),p}l.compose=function(o){if(is.compose(Wa),qc(Gc,"PieDataLabel")){let a=o.prototype;a.dataLabelPositioners=t,a.alignDataLabel=jc,a.drawDataLabels=i,a.getDataLabelPosition=e,a.placeDataLabels=s,a.verifyDataLabelOverflow=r}}})(B||(B={}));let Zc=B;(function(l){l.getCenterOfPoints=function(t){let e=t.reduce((i,s)=>(i.x+=s.x,i.y+=s.y,i),{x:0,y:0});return{x:e.x/t.length,y:e.y/t.length}},l.getDistanceBetweenPoints=function(t,e){return Math.sqrt(Math.pow(e.x-t.x,2)+Math.pow(e.y-t.y,2))},l.getAngleBetweenPoints=function(t,e){return Math.atan2(e.x-t.x,e.y-t.y)},l.pointInPolygon=function({x:t,y:e},i){let s=i.length,r,o,a=!1;for(r=0,o=s-1;r<s;o=r++){let[n,h]=i[r],[c,d]=i[o];h>e!=d>e&&t<(c-n)*(e-h)/(d-h)+n&&(a=!a)}return a}})(q||(q={}));let{pointInPolygon:Kc}=q,{addEvent:_c,fireEvent:Ya,objectEach:Ga,pick:Qc}=R;function Jc(l){let t=l.length,e=(c,d)=>!(d.x>=c.x+c.width||d.x+d.width<=c.x||d.y>=c.y+c.height||d.y+d.height<=c.y),i=(c,d)=>{for(let p of c)if(Kc({x:p[0],y:p[1]},d))return!0;return!1},s,r,o,a,n,h=!1;for(let c=0;c<t;c++)(s=l[c])&&(s.oldOpacity=s.opacity,s.newOpacity=1,s.absoluteBox=function(d){var p,u;if(d&&(!d.alignAttr||d.placed)){let g=d.box?0:d.padding||0,m=d.alignAttr||{x:d.attr("x"),y:d.attr("y")},x=d.getBBox();return d.width=x.width,d.height=x.height,{x:m.x+(((p=d.parentGroup)==null?void 0:p.translateX)||0)+g,y:m.y+(((u=d.parentGroup)==null?void 0:u.translateY)||0)+g,width:(d.width||0)-2*g,height:(d.height||0)-2*g,polygon:x==null?void 0:x.polygon}}}(s));l.sort((c,d)=>(d.labelrank||0)-(c.labelrank||0));for(let c=0;c<t;++c){a=(r=l[c])&&r.absoluteBox;let d=a==null?void 0:a.polygon;for(let p=c+1;p<t;++p){n=(o=l[p])&&o.absoluteBox;let u=!1;if(a&&n&&r!==o&&r.newOpacity!==0&&o.newOpacity!==0&&r.visibility!=="hidden"&&o.visibility!=="hidden"){let g=n.polygon;if(d&&g&&d!==g?i(d,g)&&(u=!0):e(a,n)&&(u=!0),u){let m=r.labelrank<o.labelrank?r:o,x=m.text;m.newOpacity=0,x!=null&&x.element.querySelector("textPath")&&x.hide()}}}}for(let c of l)ja(c,this)&&(h=!0);h&&Ya(this,"afterHideAllOverlappingLabels")}function ja(l,t){let e,i,s=!1;return l&&(i=l.newOpacity,l.oldOpacity!==i&&(l.hasClass("highcharts-data-label")?(l[i?"removeClass":"addClass"]("highcharts-data-label-hidden"),e=function(){t.styledMode||l.css({pointerEvents:i?"auto":"none"})},s=!0,l[l.isOld?"animate":"attr"]({opacity:i},void 0,e),Ya(t,"afterHideOverlappingLabel")):l.attr({opacity:i})),l.isOld=!0),s}function tp(){var e;let l=this,t=[];for(let i of l.labelCollectors||[])t=t.concat(i());for(let i of l.yAxis||[])i.stacking&&i.options.stackLabels&&!i.options.stackLabels.allowOverlap&&Ga(i.stacking.stacks,s=>{Ga(s,r=>{r.label&&t.push(r.label)})});for(let i of l.series||[])if(i.visible&&((e=i.hasDataLabels)!=null&&e.call(i))){let s=r=>{for(let o of r)o.visible&&(o.dataLabels||[]).forEach(a=>{var h;let n=a.options||{};a.labelrank=Qc(n.labelrank,o.labelrank,(h=o.shapeArgs)==null?void 0:h.height),n.allowOverlap??Number(n.distance)>0?(a.oldOpacity=a.opacity,a.newOpacity=1,ja(a,l)):t.push(a)})};s(i.nodes||[]),s(i.points)}this.hideOverlappingLabels(t)}let $a={compose:function(l){let t=l.prototype;t.hideOverlappingLabels||(t.hideOverlappingLabels=Jc,_c(l,"render",tp))}},{defaultOptions:ep}=zt,{noop:Ua}=A,{addEvent:Va,extend:ip,isObject:qa,merge:sp,relativeLength:Mr}=R,rp={radius:0,scope:"stack",where:void 0},Za=Ua,Ka=Ua;function op(l,t,e,i,s={}){let r=Za(l,t,e,i,s),{innerR:o=0,r:a=e,start:n=0,end:h=0}=s;if(s.open||!s.borderRadius)return r;let c=h-n,d=Math.sin(c/2),p=Math.max(Math.min(Mr(s.borderRadius||0,a-o),(a-o)/2,a*d/(1+d)),0),u=Math.min(p,c/Math.PI*2*o),g=r.length-1;for(;g--;)(function(m,x,f){let b,y,v,w=m[x],k=m[x+1];if(k[0]==="Z"&&(k=m[0]),(w[0]==="M"||w[0]==="L")&&k[0]==="A"?(b=w,y=k,v=!0):w[0]==="A"&&(k[0]==="M"||k[0]==="L")&&(b=k,y=w),b&&y&&y.params){let S=y[1],M=y[5],T=y.params,{start:P,end:L,cx:E,cy:C}=T,O=M?S-f:S+f,N=O?Math.asin(f/O):0,F=M?N:-N,I=Math.cos(N)*O;v?(T.start=P+F,b[1]=E+I*Math.cos(P),b[2]=C+I*Math.sin(P),m.splice(x+1,0,["A",f,f,0,0,1,E+S*Math.cos(T.start),C+S*Math.sin(T.start)])):(T.end=L-F,y[6]=E+S*Math.cos(T.end),y[7]=C+S*Math.sin(T.end),m.splice(x+1,0,["A",f,f,0,0,1,E+I*Math.cos(L),C+I*Math.sin(L)])),y[4]=Math.abs(T.end-T.start)<Math.PI?0:1}})(r,g,g>1?u:p);return r}function ap(){var l,t;if(this.options.borderRadius&&!(this.chart.is3d&&this.chart.is3d())){let{options:e,yAxis:i}=this,s=e.stacking==="percent",r=(t=(l=ep.plotOptions)==null?void 0:l[this.type])==null?void 0:t.borderRadius,o=Sr(e.borderRadius,qa(r)?r:{}),a=i.options.reversed;for(let n of this.points){let{shapeArgs:h}=n;if(n.shapeType==="roundedRect"&&h){let{width:c=0,height:d=0,y:p=0}=h,u=p,g=d;if(o.scope==="stack"&&n.stackTotal){let b=i.translate(s?100:n.stackTotal,!1,!0,!1,!0),y=i.translate(e.threshold||0,!1,!0,!1,!0),v=this.crispCol(0,Math.min(b,y),0,Math.abs(b-y));u=v.y,g=v.height}let m=(n.negative?-1:1)*(a?-1:1)==-1,x=o.where;!x&&this.is("waterfall")&&Math.abs((n.yBottom||0)-(this.translatedThreshold||0))>this.borderWidth&&(x="all"),x||(x="end");let f=Math.min(Mr(o.radius,c),c/2,x==="all"?d/2:1/0)||0;x==="end"&&(m&&(u-=f),g+=f),ip(h,{brBoxHeight:g,brBoxY:u,r:f})}}}}function Sr(l,t){return qa(l)||(l={radius:l||0}),sp(rp,t,l)}function np(){let l=Sr(this.options.borderRadius);for(let t of this.points){let e=t.shapeArgs;e&&(e.borderRadius=Mr(l.radius,(e.r||0)-(e.innerR||0)))}}function lp(l,t,e,i,s={}){let r=Ka(l,t,e,i,s),{r:o=0,brBoxHeight:a=i,brBoxY:n=t}=s,h=t-n,c=n+a-(t+i),d=h-o>-.1?0:o,p=c-o>-.1?0:o,u=Math.max(d&&h,0),g=Math.max(p&&c,0),m=[l+d,t],x=[l+e-d,t],f=[l+e,t+d],b=[l+e,t+i-p],y=[l+e-p,t+i],v=[l+p,t+i],w=[l,t+i-p],k=[l,t+d],S=(M,T)=>Math.sqrt(Math.pow(M,2)-Math.pow(T,2));if(u){let M=S(d,d-u);m[0]-=M,x[0]+=M,f[1]=k[1]=t+d-u}if(i<d-u){let M=S(d,d-u-i);f[0]=b[0]=l+e-d+M,y[0]=Math.min(f[0],y[0]),v[0]=Math.max(b[0],v[0]),w[0]=k[0]=l+d-M,f[1]=k[1]=t+i}if(g){let M=S(p,p-g);y[0]+=M,v[0]-=M,b[1]=w[1]=t+i-p+g}if(i<p-g){let M=S(p,p-g-i);f[0]=b[0]=l+e-p+M,x[0]=Math.min(f[0],x[0]),m[0]=Math.max(b[0],m[0]),w[0]=k[0]=l+p-M,b[1]=w[1]=t}return r.length=0,r.push(["M",...m],["L",...x],["A",d,d,0,0,1,...f],["L",...b],["A",p,p,0,0,1,...y],["L",...v],["A",p,p,0,0,1,...w],["L",...k],["A",d,d,0,0,1,...m],["Z"]),r}let{diffObjects:hp,extend:dp,find:cp,merge:pp,pick:os,uniqueKey:up}=R;(function(l){function t(i,s){let r=i.condition;(r.callback||function(){return this.chartWidth<=os(r.maxWidth,Number.MAX_VALUE)&&this.chartHeight<=os(r.maxHeight,Number.MAX_VALUE)&&this.chartWidth>=os(r.minWidth,0)&&this.chartHeight>=os(r.minHeight,0)}).call(this)&&s.push(i._id)}function e(i,s){let r=this.options.responsive,o=this.currentResponsive,a=[],n;!s&&r&&r.rules&&r.rules.forEach(d=>{d._id===void 0&&(d._id=up()),this.matchResponsiveRule(d,a)},this);let h=pp(...a.map(d=>cp((r||{}).rules||[],p=>p._id===d)).map(d=>d&&d.chartOptions));h.isResponsiveOptions=!0,a=a.toString()||void 0;let c=o&&o.ruleIds;a===c||(o&&(this.currentResponsive=void 0,this.updatingResponsive=!0,this.update(o.undoOptions,i,!0),this.updatingResponsive=!1),a?((n=hp(h,this.options,!0,this.collectionsWithUpdate)).isResponsiveOptions=!0,this.currentResponsive={ruleIds:a,mergedOptions:h,undoOptions:n},this.updatingResponsive||this.update(h,i,!0)):this.currentResponsive=void 0)}l.compose=function(i){let s=i.prototype;return s.matchResponsiveRule||dp(s,{matchResponsiveRule:t,setResponsive:e}),i}})(J||(J={}));let gp=J;A.AST=it,A.Axis=Oe,A.Chart=ae,A.Color=pt,A.DataLabel=is,A.DataTableCore=Yi,A.Fx=Ot,A.HTMLElement=Qe,A.Legend=ra,A.LegendSymbol=jo,A.OverlappingDataLabels=A.OverlappingDataLabels||$a,A.PlotLineOrBand=Ni,A.Point=ie,A.Pointer=Fo,A.RendererRegistry=$e,A.Series=Nt,A.SeriesRegistry=ht,A.StackItem=ma,A.SVGElement=qt,A.SVGRenderer=Ai,A.Templating=Yt,A.Tick=Le,A.Time=fs,A.Tooltip=zo,A.animate=St.animate,A.animObject=St.animObject,A.chart=ae.chart,A.color=pt.parse,A.dateFormat=Yt.dateFormat,A.defaultOptions=zt.defaultOptions,A.distribute=yi.distribute,A.format=Yt.format,A.getDeferredAnimation=St.getDeferredAnimation,A.getOptions=zt.getOptions,A.numberFormat=Yt.numberFormat,A.seriesType=ht.seriesType,A.setAnimation=St.setAnimation,A.setOptions=zt.setOptions,A.stop=St.stop,A.time=zt.defaultTime,A.timers=Ot.timers,{compose:function(l,t,e){let i=l.types.pie;if(!t.symbolCustomAttribs.includes("borderRadius")){let s=e.prototype.symbols;Va(l,"afterColumnTranslate",ap,{order:9}),Va(i,"afterTranslate",np),t.symbolCustomAttribs.push("borderRadius","brBoxHeight","brBoxY"),Za=s.arc,Ka=s.roundedRect,s.arc=op,s.roundedRect=lp}},optionsToObject:Sr}.compose(A.Series,A.SVGElement,A.SVGRenderer),Sc.compose(A.Series.types.column),is.compose(A.Series),oh.compose(A.Axis),Qe.compose(A.SVGRenderer),ra.compose(A.Chart),lh.compose(A.Axis),$a.compose(A.Chart),Zc.compose(A.Series.types.pie),Ni.compose(A.Chart,A.Axis),Fo.compose(A.Chart),gp.compose(A.Chart),ii.compose(A.Axis,A.Chart,A.Series),sc.compose(A.Axis,A.Chart,A.Series),zo.compose(A.Pointer),R.extend(A,R);let fp=A;return de.default})())}(ls)),ls.exports}var bp=yp();const en=on(bp);var hs={exports:{}},vp=hs.exports,sn;function kp(){return sn||(sn=1,function(Xe,ds){(function(ft,Et){Xe.exports=Et(mp())})(typeof self<"u"?self:vp,function(ft){return function(Et){function dt(G){if(Dt[G])return Dt[G].exports;var Z=Dt[G]={i:G,l:!1,exports:{}};return Et[G].call(Z.exports,Z,Z.exports,dt),Z.l=!0,Z.exports}var Dt={};return dt.m=Et,dt.c=Dt,dt.d=function(G,Z,vt){dt.o(G,Z)||Object.defineProperty(G,Z,{configurable:!1,enumerable:!0,get:vt})},dt.n=function(G){var Z=G&&G.__esModule?function(){return G.default}:function(){return G};return dt.d(Z,"a",Z),Z},dt.o=function(G,Z){return Object.prototype.hasOwnProperty.call(G,Z)},dt.p="",dt(dt.s=0)}([function(Et,dt,Dt){function G(){return G=Object.assign?Object.assign.bind():function(B){for(var q=1;q<arguments.length;q++){var J=arguments[q];for(var lt in J)Object.prototype.hasOwnProperty.call(J,lt)&&(B[lt]=J[lt])}return B},G.apply(this,arguments)}function Z(B){return Xt(B)||Ht(B)||It(B)||vt()}function vt(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function It(B,q){if(B){if(typeof B=="string")return Ct(B,q);var J=Object.prototype.toString.call(B).slice(8,-1);return J==="Object"&&B.constructor&&(J=B.constructor.name),J==="Map"||J==="Set"?Array.from(B):J==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(J)?Ct(B,q):void 0}}function Ht(B){if(typeof Symbol<"u"&&B[Symbol.iterator]!=null||B["@@iterator"]!=null)return Array.from(B)}function Xt(B){if(Array.isArray(B))return Ct(B)}function Ct(B,q){(q==null||q>B.length)&&(q=B.length);for(var J=0,lt=new Array(q);J<q;J++)lt[J]=B[J];return lt}function kt(B){"@babel/helpers - typeof";return(kt=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(q){return typeof q}:function(q){return q&&typeof Symbol=="function"&&q.constructor===Symbol&&q!==Symbol.prototype?"symbol":typeof q})(B)}Object.defineProperty(dt,"__esModule",{value:!0}),Dt.d(dt,"HighchartsReact",function(){return et});var K=Dt(1),Pt=Dt.n(K),ct=typeof window<"u"?K.useLayoutEffect:K.useEffect,et=Object(K.memo)(Object(K.forwardRef)(function(B,q){var J=Object(K.useRef)(),lt=Object(K.useRef)(),de=Object(K.useRef)(B.constructorType),A=Object(K.useRef)(B.highcharts);return ct(function(){function Fe(){var Ft=B.highcharts||(typeof window>"u"?"undefined":kt(window))==="object"&&window.Highcharts,Qt=B.constructorType||"chart";Ft?Ft[Qt]?B.options?lt.current=Ft[Qt](J.current,B.options,B.callback):console.warn('The "options" property was not passed.'):console.warn('The "constructorType" property is incorrect or some required module is not imported.'):console.warn('The "highcharts" property was not passed.')}if(lt.current){if(B.allowChartUpdate!==!1)if(B.constructorType!==de.current||B.highcharts!==A.current)de.current=B.constructorType,A.current=B.highcharts,Fe();else if(!B.immutable&&lt.current){var ce;(ce=lt.current).update.apply(ce,[B.options].concat(Z(B.updateArgs||[!0,!0])))}else Fe()}else Fe()},[B.options,B.allowChartUpdate,B.updateArgs,B.containerProps,B.highcharts,B.constructorType]),ct(function(){return function(){lt.current&&(lt.current.destroy(),lt.current=null)}},[]),Object(K.useImperativeHandle)(q,function(){return{get chart(){return lt.current},container:J}},[]),Pt.a.createElement("div",G({},B.containerProps,{ref:J}))}));dt.default=et},function(Et,dt){Et.exports=ft}])})}(hs)),hs.exports}var wp=kp();const rn=on(wp),Sp=()=>{const[Xe,ds]=Tr.useState([]),[ft,Et]=Tr.useState(()=>new Date);Tr.useEffect(()=>{(async()=>{try{const vt=(await Ja.get(`${ns.baseURL}/locations`)).data.data.slice(0,2),It=vt.map(kt=>{const K=new Date(ft);K.setDate(ft.getDate()-1);const Pt=et=>{const B=et.getFullYear(),q=String(et.getMonth()+1).padStart(2,"0"),J=String(et.getDate()).padStart(2,"0");return`${B}-${q}-${J}`},ct=et=>{const B=et.getFullYear(),q=String(et.getMonth()+1).padStart(2,"0"),J=String(et.getDate()).padStart(2,"0"),lt=String(et.getHours()).padStart(2,"0"),de=String(et.getMinutes()).padStart(2,"0");return`${B}-${q}-${J} ${lt}:${de}`};return Ja.post(`${ns.baseURL}/energy-meters/emdata`,{locationId:kt.id,timeStart:`${Pt(K)} 00:00`,timeEnd:ct(ft)})}),Ht=await Promise.all(It),Xt=new Date(ft.getTime()-ns.chart_IST_OFFSET),Ct=await Ht.map((kt,K)=>{if(!Array.isArray(kt.data.data))return console.error(`Unexpected data format for location ${vt[K].id}:`,kt.data),[];const Pt=kt.data.data.reduce((ct,et)=>{ct[et.TagName]||(ct[et.TagName]={yesterday:[],today:[]});const B=new Date(new Date(et.DateAndTime).getTime()-ns.chart_IST_OFFSET),q=B.toDateString()===Xt.toDateString();return ct[et.TagName][q?"today":"yesterday"].push([B.getHours()*36e5+B.getMinutes()*6e4,et.Val]),ct},{});return Object.entries(Pt).map(([ct,et])=>({name:ct,...et}))});ds(vt.map((kt,K)=>({...kt,kwhData:Ct[K]})))}catch(Z){console.error("Error fetching data:",Z)}})()},[ft]);const dt=G=>{if(!G.kwhData||!Array.isArray(G.kwhData))return console.error("Invalid kwhData for location:",G),null;const Z=Ct=>{const kt=Ct.reduce((K,Pt)=>(Pt.forEach(([ct,et])=>{K[ct]||(K[ct]=0),K[ct]+=et}),K),{});return Object.entries(kt).map(([K,Pt])=>[parseInt(K),Pt])},vt=Z(G.kwhData.map(Ct=>Ct.yesterday||[])),It=Z(G.kwhData.map(Ct=>Ct.today||[])),Ht=vt.length>0?vt[vt.length-1][1]:0,Xt=It.length>0?It[It.length-1][1]:0;return{title:{text:`Location: ${G.name}`},subtitle:{text:`Yesterday Total: ${Ht.toFixed(2)} KWH, Today's Total: ${Xt.toFixed(2)} KWH`},xAxis:{type:"datetime",labels:{format:"{value:%H:%M}"}},yAxis:{title:{text:"KWH"}},tooltip:{formatter:function(){const kt=this.series.name==="Yesterday"?new Date(ft.getTime()-24*60*60*1e3):ft,K=String(Math.floor(this.x/36e5)).padStart(2,"0"),Pt=String(Math.floor(this.x%36e5/6e4)).padStart(2,"0"),ct=kt.toLocaleDateString("en-GB");return`<b>${this.series.name}</b><br/>Date: ${ct}<br/>Time: ${K}:${Pt}<br/>Value: ${this.y}`}},series:[{name:"Yesterday",data:vt,type:"line",dashStyle:"ShortDot",zIndex:1},{name:"Today",data:It,type:"line",zIndex:0}]}},Dt=G=>({title:{text:`Energy Meter: ${G.name}`},xAxis:{type:"datetime",labels:{format:"{value:%H:%M}"}},yAxis:{title:{text:"KWH"}},tooltip:{formatter:function(){const vt=this.series.name==="Yesterday"?new Date(ft.getTime()-24*60*60*1e3):ft,It=String(Math.floor(this.x/36e5)).padStart(2,"0"),Ht=String(Math.floor(this.x%36e5/6e4)).padStart(2,"0"),Xt=vt.toLocaleDateString("en-GB");return`<b>${this.series.name}</b><br/>Date: ${Xt}<br/>Time: ${It}:${Ht}<br/>Value: ${this.y}`}},series:[{name:"Yesterday",data:G.yesterday,type:"line",dashStyle:"ShortDot",zIndex:1},{name:"Today",data:G.today,type:"line",zIndex:0}]});return hi.jsx("div",{children:hi.jsx("div",{style:{display:"grid",gridTemplateColumns:"repeat(auto-fit, minmax(300px, 1fr))",gap:"16px"},children:Xe.map(G=>hi.jsxs("div",{style:{border:"1px solid #ccc",padding:"8px",borderRadius:"8px"},children:[hi.jsx(rn,{highcharts:en,options:dt(G)}),G.kwhData.map(Z=>hi.jsx(rn,{highcharts:en,options:Dt(Z)},Z.name))]},G.id))})})};export{Sp as default};
