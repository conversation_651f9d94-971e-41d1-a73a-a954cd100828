import{a as P,_ as R,R as z,b as T,c as k,P as n,d as E,j as e}from"./index-BDJ8oeCE.js";import{a as I,b as o}from"./DefaultLayout-BolUaDEE.js";import{a as m}from"./index.esm-DSzlmaRN.js";import{R as A}from"./react-DOh6XfOk.js";import{C as B,a as S}from"./CRow-C1o2zw34.js";import{C as r,a}from"./CCardBody-iimbKiZ7.js";import{C as c}from"./CCardHeader-CFnfD6gM.js";import{C as O,a as w,b as u}from"./CCardTitle-Cc00wz7L.js";import"./cil-user-Ddrdy7PS.js";var D=["xxl","xl","lg","md","sm","xs"],s=P.forwardRef(function(l,C){var i,b=l.children,j=l.animation,p=l.as,N=p===void 0?"span":p,v=l.className,f=l.color,g=l.size,t=R(l,["children","animation","as","className","color","size"]),h=[];return D.forEach(function(d){var x=t[d];delete t[d];var y=d==="xs"?"":"-".concat(d);typeof x=="number"&&h.push("col".concat(y,"-").concat(x)),typeof x=="boolean"&&h.push("col".concat(y))}),z.createElement(N,T({className:k(j?"placeholder-".concat(j):"placeholder",(i={},i["bg-".concat(f)]=f,i["placeholder-".concat(g)]=g,i),h,v)},t,{ref:C}),b)});s.propTypes={animation:n.oneOf(["glow","wave"]),as:n.elementType,children:n.node,className:n.string,color:E,size:n.oneOf(["xs","sm","lg"])};s.displayName="CPlaceholder";const J=()=>e.jsx(B,{children:e.jsxs(S,{xs:12,children:[e.jsx(I,{href:"components/placeholder/"}),e.jsxs(r,{className:"mb-4",children:[e.jsx(c,{children:e.jsx("strong",{children:"React Placeholder"})}),e.jsxs(a,{children:[e.jsx("p",{className:"text-body-secondary small",children:'In the example below, we take a typical card component and recreate it with placeholders applied to create a "loading card". Size and proportions are the same between the two.'}),e.jsx(o,{href:"components/placeholder",children:e.jsxs("div",{className:"d-flex justify-content-around p-3",children:[e.jsxs(r,{style:{width:"18rem"},children:[e.jsx(O,{orientation:"top",src:A}),e.jsxs(a,{children:[e.jsx(w,{children:"Card title"}),e.jsx(u,{children:"Some quick example text to build on the card title and make up the bulk of the card's content."}),e.jsx(m,{color:"primary",href:"#",children:"Go somewhere"})]})]}),e.jsxs(r,{style:{width:"18rem"},children:[e.jsxs("svg",{className:"card-img-top",width:"100%",height:"180",xmlns:"http://www.w3.org/2000/svg",role:"img","aria-label":"Placeholder",preserveAspectRatio:"xMidYMid slice",focusable:"false",children:[e.jsx("title",{children:"Placeholder"}),e.jsx("rect",{width:"100%",height:"100%",fill:"#868e96"})]}),e.jsxs(a,{children:[e.jsx(s,{as:w,animation:"glow",xs:7,children:e.jsx(s,{xs:6})}),e.jsxs(s,{as:u,animation:"glow",children:[e.jsx(s,{xs:7}),e.jsx(s,{xs:4}),e.jsx(s,{xs:4}),e.jsx(s,{xs:6}),e.jsx(s,{xs:8})]}),e.jsx(s,{color:"primary",as:m,disabled:!0,href:"#",tabIndex:-1,xs:6})]})]})]})})]})]}),e.jsxs(r,{className:"mb-4",children:[e.jsx(c,{children:e.jsx("strong",{children:"React Placeholder"})}),e.jsxs(a,{children:[e.jsxs("p",{className:"text-body-secondary small",children:["Create placeholders with the ",e.jsx("code",{children:"<CPlaceholder>"})," component and a grid column propx (e.g., ",e.jsxs("code",{children:["xs=",6]}),") to set the ",e.jsx("code",{children:"width"}),". They can replace the text inside an element or be added as a modifier class to an existing component."]}),e.jsxs(o,{href:"components/placeholder",children:[e.jsx("p",{"aria-hidden":"true",children:e.jsx(s,{xs:6})}),e.jsx(s,{color:"primary",as:m,"aria-hidden":"true",disabled:!0,href:"#",tabIndex:-1,xs:4})]})]})]}),e.jsxs(r,{className:"mb-4",children:[e.jsxs(c,{children:[e.jsx("strong",{children:"React Placeholder"})," ",e.jsx("small",{children:" Width"})]}),e.jsxs(a,{children:[e.jsxs("p",{className:"text-body-secondary small",children:["You can change the ",e.jsx("code",{children:"width"})," through grid column classes, width utilities, or inline styles."]}),e.jsxs(o,{href:"components/placeholder#width",children:[e.jsx(s,{xs:6}),e.jsx(s,{className:"w-75"}),e.jsx(s,{style:{width:"30%"}})]})]})]}),e.jsxs(r,{className:"mb-4",children:[e.jsxs(c,{children:[e.jsx("strong",{children:"React Placeholder"})," ",e.jsx("small",{children:" Color"})]}),e.jsxs(a,{children:[e.jsxs("p",{className:"text-body-secondary small",children:["By default, the ",e.jsx("code",{children:"<CPlaceholder>"})," uses ",e.jsx("code",{children:"currentColor"}),". This can be overridden with a custom color or utility class."]}),e.jsxs(o,{href:"components/placeholder#color",children:[e.jsx(s,{xs:12}),e.jsx(s,{color:"primary",xs:12}),e.jsx(s,{color:"secondary",xs:12}),e.jsx(s,{color:"success",xs:12}),e.jsx(s,{color:"danger",xs:12}),e.jsx(s,{color:"warning",xs:12}),e.jsx(s,{color:"info",xs:12}),e.jsx(s,{color:"light",xs:12}),e.jsx(s,{color:"dark",xs:12})]})]})]}),e.jsxs(r,{className:"mb-4",children:[e.jsxs(c,{children:[e.jsx("strong",{children:"React Placeholder"})," ",e.jsx("small",{children:" Sizing"})]}),e.jsxs(a,{children:[e.jsxs("p",{className:"text-body-secondary small",children:["The size of ",e.jsx("code",{children:"<CPlaceholder>"}),"s are based on the typographic style of the parent element. Customize them with ",e.jsx("code",{children:"size"})," prop: ",e.jsx("code",{children:"lg"}),","," ",e.jsx("code",{children:"sm"}),", or ",e.jsx("code",{children:"xs"}),"."]}),e.jsxs(o,{href:"components/placeholder#sizing",children:[e.jsx(s,{xs:12,size:"lg"}),e.jsx(s,{xs:12}),e.jsx(s,{xs:12,size:"sm"}),e.jsx(s,{xs:12,size:"xs"})]})]})]}),e.jsxs(r,{className:"mb-4",children:[e.jsxs(c,{children:[e.jsx("strong",{children:"React Placeholder"})," ",e.jsx("small",{children:" Animation"})]}),e.jsxs(a,{children:[e.jsxs("p",{className:"text-body-secondary small",children:["Animate placeholders with ",e.jsx("code",{children:'animation="glow"'})," or"," ",e.jsx("code",{children:'animation="wave"'})," to better convey the perception of something being ",e.jsx("em",{children:"actively"})," loaded."]}),e.jsxs(o,{href:"components/placeholder#animation",children:[e.jsx(s,{as:"p",animation:"glow",children:e.jsx(s,{xs:12})}),e.jsx(s,{as:"p",animation:"wave",children:e.jsx(s,{xs:12})})]})]})]})]})});export{J as default};
