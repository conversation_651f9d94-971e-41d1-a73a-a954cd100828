import{j as e}from"./index-BDJ8oeCE.js";import{a as o,b as c,C as a,c as r}from"./DefaultLayout-BolUaDEE.js";import{C as s}from"./index.esm-DSzlmaRN.js";import{C as d,a as n}from"./CRow-C1o2zw34.js";import{C as i,a as t}from"./CCardBody-iimbKiZ7.js";import{C as h}from"./CCardHeader-CFnfD6gM.js";import"./cil-user-Ddrdy7PS.js";const C=()=>e.jsx(d,{children:e.jsxs(n,{xs:12,children:[e.jsx(o,{href:"components/breadcrumb/"}),e.jsxs(i,{className:"mb-4",children:[e.jsx(h,{children:e.jsx("strong",{children:"React Breadcrumb"})}),e.jsxs(t,{children:[e.jsxs("p",{className:"text-body-secondary small",children:["The breadcrumb navigation provides links back to each previous page the user navigated through and shows the current location in a website or an application. You don’t have to add separators, because they automatically added in CSS through"," ",e.jsxs("a",{href:"https://developer.mozilla.org/en-US/docs/Web/CSS/::before",children:[" ",e.jsx("code",{children:"::before"})]})," ","and"," ",e.jsxs("a",{href:"https://developer.mozilla.org/en-US/docs/Web/CSS/content",children:[" ",e.jsx("code",{children:"content"})]}),"."]}),e.jsxs(c,{href:"components/breadcrumb",children:[e.jsxs(a,{children:[e.jsx(r,{children:e.jsx(s,{href:"#",children:"Home"})}),e.jsx(r,{active:!0,children:"Library"})]}),e.jsxs(a,{children:[e.jsx(r,{children:e.jsx(s,{href:"#",children:"Home"})}),e.jsx(r,{children:e.jsx(s,{href:"#",children:"Library"})}),e.jsx(r,{active:!0,children:"Data"})]}),e.jsxs(a,{children:[e.jsx(r,{children:e.jsx(s,{href:"#",children:"Home"})}),e.jsx(r,{children:e.jsx(s,{href:"#",children:"Library"})}),e.jsx(r,{children:e.jsx(s,{href:"#",children:"Data"})}),e.jsx(r,{active:!0,children:"Bootstrap"})]})]})]})]})]})});export{C as default};
