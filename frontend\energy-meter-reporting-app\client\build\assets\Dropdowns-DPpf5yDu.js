import{j as e}from"./index-BDJ8oeCE.js";import{a as p,b as i,k as n,l as s,m as o,n as r}from"./DefaultLayout-BolUaDEE.js";import{a as x}from"./index.esm-DSzlmaRN.js";import{C as m,a as l}from"./CRow-C1o2zw34.js";import{C as t,a as c}from"./CCardBody-iimbKiZ7.js";import{C as a}from"./CCardHeader-CFnfD6gM.js";import{C as d}from"./CDropdownDivider-DVoRzmnU.js";import{C as f}from"./CButtonGroup-DjNOigk5.js";import"./cil-user-Ddrdy7PS.js";const A=()=>e.jsxs(m,{children:[e.jsxs(l,{xs:12,children:[e.jsx(p,{href:"components/dropdown/"}),e.jsxs(t,{className:"mb-4",children:[e.jsxs(a,{children:[e.jsx("strong",{children:"React Dropdown"})," ",e.jsx("small",{children:"Single button"})]}),e.jsxs(c,{children:[e.jsxs("p",{className:"text-body-secondary small",children:["Here's how you can put them to work with either ",e.jsx("code",{children:"<button>"})," ","elements:"]}),e.jsx(i,{href:"components/dropdown#single-button",children:e.jsxs(n,{children:[e.jsx(s,{color:"secondary",children:"Dropdown button"}),e.jsxs(o,{children:[e.jsx(r,{href:"#",children:"Action"}),e.jsx(r,{href:"#",children:"Another action"}),e.jsx(r,{href:"#",children:"Something else here"})]})]})}),e.jsx("p",{className:"text-body-secondary small",children:"The best part is you can do this with any button variant, too:"}),e.jsx(i,{href:"components/dropdown#single-button",children:e.jsx(e.Fragment,{children:["primary","secondary","success","info","warning","danger"].map((h,j)=>e.jsxs(n,{variant:"btn-group",children:[e.jsx(s,{color:h,children:h}),e.jsxs(o,{children:[e.jsx(r,{href:"#",children:"Action"}),e.jsx(r,{href:"#",children:"Another action"}),e.jsx(r,{href:"#",children:"Something else here"}),e.jsx(d,{}),e.jsx(r,{href:"#",children:"Separated link"})]})]},j))})})]})]})]}),e.jsx(l,{xs:12,children:e.jsxs(t,{className:"mb-4",children:[e.jsxs(a,{children:[e.jsx("strong",{children:"React Dropdown"})," ",e.jsx("small",{children:"Split button"})]}),e.jsxs(c,{children:[e.jsxs("p",{className:"text-body-secondary small",children:["Similarly, create split button dropdowns with virtually the same markup as single button dropdowns, but with the addition of boolean prop ",e.jsx("code",{children:"split"})," for proper spacing around the dropdown caret."]}),e.jsxs("p",{className:"text-body-secondary small",children:["We use this extra class to reduce the horizontal ",e.jsx("code",{children:"padding"})," on either side of the caret by 25% and remove the ",e.jsx("code",{children:"margin-left"})," that's attached for normal button dropdowns. Those additional changes hold the caret centered in the split button and implement a more properly sized hit area next to the main button."]}),e.jsx(i,{href:"components/dropdown#split-button",children:e.jsx(e.Fragment,{children:["primary","secondary","success","info","warning","danger"].map((h,j)=>e.jsxs(n,{variant:"btn-group",children:[e.jsx(x,{color:h,children:h}),e.jsx(s,{color:h,split:!0}),e.jsxs(o,{children:[e.jsx(r,{href:"#",children:"Action"}),e.jsx(r,{href:"#",children:"Another action"}),e.jsx(r,{href:"#",children:"Something else here"}),e.jsx(d,{}),e.jsx(r,{href:"#",children:"Separated link"})]})]},j))})})]})]})}),e.jsx(l,{xs:12,children:e.jsxs(t,{className:"mb-4",children:[e.jsxs(a,{children:[e.jsx("strong",{children:"React Dropdown"})," ",e.jsx("small",{children:"Sizing"})]}),e.jsxs(c,{children:[e.jsx("p",{className:"text-body-secondary small",children:"Button dropdowns work with buttons of all sizes, including default and split dropdown buttons."}),e.jsxs(i,{href:"components/dropdown#sizing",children:[e.jsxs(n,{variant:"btn-group",children:[e.jsx(s,{color:"secondary",size:"lg",children:"Large button"}),e.jsxs(o,{children:[e.jsx(r,{href:"#",children:"Action"}),e.jsx(r,{href:"#",children:"Another action"}),e.jsx(r,{href:"#",children:"Something else here"}),e.jsx(d,{}),e.jsx(r,{href:"#",children:"Separated link"})]})]}),e.jsxs(n,{variant:"btn-group",children:[e.jsx(x,{color:"secondary",size:"lg",children:"Large split button"}),e.jsx(s,{color:"secondary",size:"lg",split:!0}),e.jsxs(o,{children:[e.jsx(r,{href:"#",children:"Action"}),e.jsx(r,{href:"#",children:"Another action"}),e.jsx(r,{href:"#",children:"Something else here"}),e.jsx(d,{}),e.jsx(r,{href:"#",children:"Separated link"})]})]})]}),e.jsxs(i,{href:"components/dropdown#sizing",children:[e.jsxs(n,{variant:"btn-group",children:[e.jsx(s,{color:"secondary",size:"sm",children:"Small button"}),e.jsxs(o,{children:[e.jsx(r,{href:"#",children:"Action"}),e.jsx(r,{href:"#",children:"Another action"}),e.jsx(r,{href:"#",children:"Something else here"}),e.jsx(d,{}),e.jsx(r,{href:"#",children:"Separated link"})]})]}),e.jsxs(n,{variant:"btn-group",children:[e.jsx(x,{color:"secondary",size:"sm",children:"Small split button"}),e.jsx(s,{color:"secondary",size:"sm",split:!0}),e.jsxs(o,{children:[e.jsx(r,{href:"#",children:"Action"}),e.jsx(r,{href:"#",children:"Another action"}),e.jsx(r,{href:"#",children:"Something else here"}),e.jsx(d,{}),e.jsx(r,{href:"#",children:"Separated link"})]})]})]})]})]})}),e.jsx(l,{xs:12,children:e.jsxs(t,{className:"mb-4",children:[e.jsxs(a,{children:[e.jsx("strong",{children:"React Dropdown"})," ",e.jsx("small",{children:"Single button"})]}),e.jsxs(c,{children:[e.jsxs("p",{className:"text-body-secondary small",children:["Opt into darker dropdowns to match a dark navbar or custom style by set"," ",e.jsx("code",{children:"dark"})," property. No changes are required to the dropdown items."]}),e.jsx(i,{href:"components/dropdown#dark-dropdowns",children:e.jsxs(n,{dark:!0,children:[e.jsx(s,{color:"secondary",children:"Dropdown button"}),e.jsxs(o,{children:[e.jsx(r,{href:"#",children:"Action"}),e.jsx(r,{href:"#",children:"Another action"}),e.jsx(r,{href:"#",children:"Something else here"}),e.jsx(d,{}),e.jsx(r,{href:"#",children:"Separated link"})]})]})}),e.jsx("p",{className:"text-body-secondary small",children:"And putting it to use in a navbar:"}),e.jsx(i,{href:"components/dropdown#dark-dropdowns",children:e.jsx("nav",{className:"navbar navbar-expand-lg navbar-dark bg-dark",children:e.jsxs("div",{className:"container-fluid",children:[e.jsx("a",{className:"navbar-brand",href:"https://coreui.io/react/",children:"Navbar"}),e.jsx("button",{className:"navbar-toggler",type:"button","data-coreui-toggle":"collapse","data-coreui-target":"#navbarNavDarkDropdown","aria-controls":"navbarNavDarkDropdown","aria-expanded":"false","aria-label":"Toggle navigation",children:e.jsx("span",{className:"navbar-toggler-icon"})}),e.jsx("div",{className:"collapse navbar-collapse",id:"navbarNavDarkDropdown",children:e.jsx("ul",{className:"navbar-nav",children:e.jsxs(n,{dark:!0,as:"li",variant:"nav-item",children:[e.jsx(s,{children:"Dropdown"}),e.jsxs(o,{children:[e.jsx(r,{href:"#",children:"Action"}),e.jsx(r,{href:"#",children:"Another action"}),e.jsx(r,{href:"#",children:"Something else here"}),e.jsx(d,{}),e.jsx(r,{href:"#",children:"Separated link"})]})]})})})]})})})]})]})}),e.jsx(l,{xs:12,children:e.jsxs(t,{className:"mb-4",children:[e.jsxs(a,{children:[e.jsx("strong",{children:"React Dropdown"})," ",e.jsx("small",{children:"Dropup"})]}),e.jsxs(c,{children:[e.jsxs("p",{className:"text-body-secondary small",children:["Trigger dropdown menus above elements by adding"," ",e.jsx("code",{children:'direction="dropup"'})," to the ",e.jsx("code",{children:"<CDropdown>"})," ","component."]}),e.jsxs(i,{href:"components/dropdown#dropup",children:[e.jsxs(n,{variant:"btn-group",direction:"dropup",children:[e.jsx(s,{color:"secondary",children:"Dropdown"}),e.jsxs(o,{children:[e.jsx(r,{href:"#",children:"Action"}),e.jsx(r,{href:"#",children:"Another action"}),e.jsx(r,{href:"#",children:"Something else here"}),e.jsx(d,{}),e.jsx(r,{href:"#",children:"Separated link"})]})]}),e.jsxs(n,{variant:"btn-group",direction:"dropup",children:[e.jsx(x,{color:"secondary",children:"Small split button"}),e.jsx(s,{color:"secondary",split:!0}),e.jsxs(o,{children:[e.jsx(r,{href:"#",children:"Action"}),e.jsx(r,{href:"#",children:"Another action"}),e.jsx(r,{href:"#",children:"Something else here"}),e.jsx(d,{}),e.jsx(r,{href:"#",children:"Separated link"})]})]})]})]})]})}),e.jsx(l,{xs:12,children:e.jsxs(t,{className:"mb-4",children:[e.jsxs(a,{children:[e.jsx("strong",{children:"React Dropdown"})," ",e.jsx("small",{children:"Dropright"})]}),e.jsxs(c,{children:[e.jsxs("p",{className:"text-body-secondary small",children:["Trigger dropdown menus at the right of the elements by adding"," ",e.jsx("code",{children:'direction="dropend"'})," to the ",e.jsx("code",{children:"<CDropdown>"})," ","component."]}),e.jsxs(i,{href:"components/dropdown#dropright",children:[e.jsxs(n,{variant:"btn-group",direction:"dropend",children:[e.jsx(s,{color:"secondary",children:"Dropdown"}),e.jsxs(o,{children:[e.jsx(r,{href:"#",children:"Action"}),e.jsx(r,{href:"#",children:"Another action"}),e.jsx(r,{href:"#",children:"Something else here"}),e.jsx(d,{}),e.jsx(r,{href:"#",children:"Separated link"})]})]}),e.jsxs(n,{variant:"btn-group",direction:"dropend",children:[e.jsx(x,{color:"secondary",children:"Small split button"}),e.jsx(s,{color:"secondary",split:!0}),e.jsxs(o,{children:[e.jsx(r,{href:"#",children:"Action"}),e.jsx(r,{href:"#",children:"Another action"}),e.jsx(r,{href:"#",children:"Something else here"}),e.jsx(d,{}),e.jsx(r,{href:"#",children:"Separated link"})]})]})]})]})]})}),e.jsx(l,{xs:12,children:e.jsxs(t,{className:"mb-4",children:[e.jsxs(a,{children:[e.jsx("strong",{children:"React Dropdown"})," ",e.jsx("small",{children:"Dropleft"})]}),e.jsxs(c,{children:[e.jsxs("p",{className:"text-body-secondary small",children:["Trigger dropdown menus at the left of the elements by adding"," ",e.jsx("code",{children:'direction="dropstart"'})," to the ",e.jsx("code",{children:"<CDropdown>"})," ","component."]}),e.jsx(i,{href:"components/dropdown#dropleft",children:e.jsxs(f,{children:[e.jsxs(n,{variant:"btn-group",direction:"dropstart",children:[e.jsx(s,{color:"secondary",split:!0}),e.jsxs(o,{children:[e.jsx(r,{href:"#",children:"Action"}),e.jsx(r,{href:"#",children:"Another action"}),e.jsx(r,{href:"#",children:"Something else here"}),e.jsx(d,{}),e.jsx(r,{href:"#",children:"Separated link"})]})]}),e.jsx(x,{color:"secondary",children:"Small split button"})]})})]})]})})]});export{A as default};
