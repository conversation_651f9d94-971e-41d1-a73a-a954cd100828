import{a as B,_ as N,R as w,b as R,c as G,P as m,j as r}from"./index-BDJ8oeCE.js";import{a as S,b as s,k as d,l as h,m as u,n as o}from"./DefaultLayout-BolUaDEE.js";import{a as e}from"./index.esm-DSzlmaRN.js";import{C as A,a as i}from"./CRow-C1o2zw34.js";import{C as t,a}from"./CCardBody-iimbKiZ7.js";import{C as c}from"./CCardHeader-CFnfD6gM.js";import{C as n}from"./CButtonGroup-DjNOigk5.js";import{C as l}from"./CFormCheck-CL4oiK6y.js";import{C as b,a as g}from"./CInputGroupText-BGHrT9V9.js";import{C as f}from"./CFormInput-LKfVdWds.js";import{C as x}from"./CDropdownDivider-DVoRzmnU.js";import"./cil-user-Ddrdy7PS.js";import"./CFormControlValidation-_wBnnnml.js";import"./CFormLabel-CzXD3nfE.js";import"./CFormControlWrapper-CyzWU6Dg.js";var p=B.forwardRef(function(j,y){var v=j.children,C=j.className,k=N(j,["children","className"]);return w.createElement("div",R({className:G("btn-toolbar",C)},k,{ref:y}),v)});p.propTypes={children:m.node,className:m.string};p.displayName="CButtonToolbar";const W=()=>r.jsxs(A,{children:[r.jsxs(i,{xs:12,children:[r.jsx(S,{href:"components/button-group/"}),r.jsxs(t,{className:"mb-4",children:[r.jsxs(c,{children:[r.jsx("strong",{children:"React Button Group"})," ",r.jsx("span",{children:"Basic example"})]}),r.jsxs(a,{children:[r.jsxs("p",{children:["Wrap a series of ",r.jsx("code",{children:"<CButton>"})," components in"," ",r.jsx("code",{children:"<CButtonGroup>"}),"."," "]}),r.jsx(s,{href:"components/button-group",children:r.jsxs(n,{role:"group","aria-label":"Basic example",children:[r.jsx(e,{color:"primary",children:"Left"}),r.jsx(e,{color:"primary",children:"Middle"}),r.jsx(e,{color:"primary",children:"Right"})]})}),r.jsxs("p",{children:["These classes can also be added to groups of links, as an alternative to the"," ",r.jsx("code",{children:"<CNav>"})," components."]}),r.jsx(s,{href:"components/button-group",children:r.jsxs(n,{children:[r.jsx(e,{href:"#",color:"primary",active:!0,children:"Active link"}),r.jsx(e,{href:"#",color:"primary",children:"Link"}),r.jsx(e,{href:"#",color:"primary",children:"Link"})]})})]})]})]}),r.jsx(i,{xs:12,children:r.jsxs(t,{className:"mb-4",children:[r.jsxs(c,{children:[r.jsx("strong",{children:"React Button Group"})," ",r.jsx("span",{children:"Mixed styles"})]}),r.jsx(a,{children:r.jsx(s,{href:"components/button-group#mixed-styles",children:r.jsxs(n,{role:"group","aria-label":"Basic mixed styles example",children:[r.jsx(e,{color:"danger",children:"Left"}),r.jsx(e,{color:"warning",children:"Middle"}),r.jsx(e,{color:"success",children:"Right"})]})})})]})}),r.jsx(i,{xs:12,children:r.jsxs(t,{className:"mb-4",children:[r.jsxs(c,{children:[r.jsx("strong",{children:"React Button Group"})," ",r.jsx("span",{children:"Outlined styles"})]}),r.jsx(a,{children:r.jsx(s,{href:"components/button-group#outlined-styles",children:r.jsxs(n,{role:"group","aria-label":"Basic outlined example",children:[r.jsx(e,{color:"primary",variant:"outline",children:"Left"}),r.jsx(e,{color:"primary",variant:"outline",children:"Middle"}),r.jsx(e,{color:"primary",variant:"outline",children:"Right"})]})})})]})}),r.jsx(i,{xs:12,children:r.jsxs(t,{className:"mb-4",children:[r.jsxs(c,{children:[r.jsx("strong",{children:"React Button Group"})," ",r.jsx("span",{children:"Checkbox and radio button groups"})]}),r.jsxs(a,{children:[r.jsx("p",{children:"Combine button-like checkbox and radio toggle buttons into a seamless looking button group."}),r.jsx(s,{href:"components/button-group#checkbox-and-radio-button-groups",children:r.jsxs(n,{role:"group","aria-label":"Basic checkbox toggle button group",children:[r.jsx(l,{button:{variant:"outline"},id:"btncheck1",autoComplete:"off",label:"Checkbox 1"}),r.jsx(l,{button:{variant:"outline"},id:"btncheck2",autoComplete:"off",label:"Checkbox 2"}),r.jsx(l,{button:{variant:"outline"},id:"btncheck3",autoComplete:"off",label:"Checkbox 3"})]})}),r.jsx(s,{href:"components/button-group#checkbox-and-radio-button-groups",children:r.jsxs(n,{role:"group","aria-label":"Basic checkbox toggle button group",children:[r.jsx(l,{type:"radio",button:{variant:"outline"},name:"btnradio",id:"btnradio1",autoComplete:"off",label:"Radio 1"}),r.jsx(l,{type:"radio",button:{variant:"outline"},name:"btnradio",id:"btnradio2",autoComplete:"off",label:"Radio 2"}),r.jsx(l,{type:"radio",button:{variant:"outline"},name:"btnradio",id:"btnradio3",autoComplete:"off",label:"Radio 3"})]})})]})]})}),r.jsx(i,{xs:12,children:r.jsxs(t,{className:"mb-4",children:[r.jsxs(c,{children:[r.jsx("strong",{children:"React Button Group"})," ",r.jsx("span",{children:"Button toolbar"})]}),r.jsxs(a,{children:[r.jsx("p",{children:"Join sets of button groups into button toolbars for more complicated components. Use utility classes as needed to space out groups, buttons, and more."}),r.jsx(s,{href:"components/button-group#button-toolbar",children:r.jsxs(p,{role:"group","aria-label":"Toolbar with button groups",children:[r.jsxs(n,{className:"me-2",role:"group","aria-label":"First group",children:[r.jsx(e,{color:"primary",children:"1"}),r.jsx(e,{color:"primary",children:"2"}),r.jsx(e,{color:"primary",children:"3"}),r.jsx(e,{color:"primary",children:"4"})]}),r.jsxs(n,{className:"me-2",role:"group","aria-label":"Second group",children:[r.jsx(e,{color:"secondary",children:"5"}),r.jsx(e,{color:"secondary",children:"6"}),r.jsx(e,{color:"secondary",children:"7"})]}),r.jsx(n,{className:"me-2",role:"group","aria-label":"Third group",children:r.jsx(e,{color:"info",children:"8"})})]})}),r.jsx("p",{children:"Feel free to combine input groups with button groups in your toolbars. Similar to the example above, you’ll likely need some utilities through to space items correctly."}),r.jsxs(s,{href:"components/button-group#button-toolbar",children:[r.jsxs(p,{className:"mb-3",role:"group","aria-label":"Toolbar with button groups",children:[r.jsxs(n,{className:"me-2",role:"group","aria-label":"First group",children:[r.jsx(e,{color:"secondary",variant:"outline",children:"1"}),r.jsx(e,{color:"secondary",variant:"outline",children:"2"}),r.jsx(e,{color:"secondary",variant:"outline",children:"3"}),r.jsx(e,{color:"secondary",variant:"outline",children:"4"})]}),r.jsxs(b,{children:[r.jsx(g,{children:"@"}),r.jsx(f,{placeholder:"Input group example","aria-label":"Input group example","aria-describedby":"btnGroupAddon"})]})]}),r.jsxs(p,{className:"justify-content-between",role:"group","aria-label":"Toolbar with button groups",children:[r.jsxs(n,{className:"me-2",role:"group","aria-label":"First group",children:[r.jsx(e,{color:"secondary",variant:"outline",children:"1"}),r.jsx(e,{color:"secondary",variant:"outline",children:"2"}),r.jsx(e,{color:"secondary",variant:"outline",children:"3"}),r.jsx(e,{color:"secondary",variant:"outline",children:"4"})]}),r.jsxs(b,{children:[r.jsx(g,{children:"@"}),r.jsx(f,{placeholder:"Input group example","aria-label":"Input group example","aria-describedby":"btnGroupAddon"})]})]})]})]})]})}),r.jsx(i,{xs:12,children:r.jsxs(t,{className:"mb-4",children:[r.jsxs(c,{children:[r.jsx("strong",{children:"React Button Group"})," ",r.jsx("span",{children:"Sizing"})]}),r.jsxs(a,{children:[r.jsxs("p",{children:["Alternatively, of implementing button sizing classes to each button in a group, set"," ",r.jsx("code",{children:"size"})," property to all ",r.jsx("code",{children:"<CButtonGroup>"}),"'s, including each one when nesting multiple groups."]}),r.jsxs(s,{href:"components/button-group#sizing",children:[r.jsxs(n,{size:"lg",role:"group","aria-label":"Large button group",children:[r.jsx(e,{color:"dark",variant:"outline",children:"Left"}),r.jsx(e,{color:"dark",variant:"outline",children:"Middle"}),r.jsx(e,{color:"dark",variant:"outline",children:"Right"})]}),r.jsx("br",{}),r.jsxs(n,{role:"group","aria-label":"Default button group",children:[r.jsx(e,{color:"dark",variant:"outline",children:"Left"}),r.jsx(e,{color:"dark",variant:"outline",children:"Middle"}),r.jsx(e,{color:"dark",variant:"outline",children:"Right"})]}),r.jsx("br",{}),r.jsxs(n,{size:"sm",role:"group","aria-label":"Small button group",children:[r.jsx(e,{color:"dark",variant:"outline",children:"Left"}),r.jsx(e,{color:"dark",variant:"outline",children:"Middle"}),r.jsx(e,{color:"dark",variant:"outline",children:"Right"})]})]})]})]})}),r.jsx(i,{xs:12,children:r.jsxs(t,{className:"mb-4",children:[r.jsxs(c,{children:[r.jsx("strong",{children:"React Button Group"})," ",r.jsx("span",{children:"Nesting"})]}),r.jsxs(a,{children:[r.jsxs("p",{className:"text-body-secondary small",children:["Put a ",r.jsx("code",{children:"<CButtonGroup>"})," inside another"," ",r.jsx("code",{children:"<CButtonGroup>"})," when you need dropdown menus combined with a series of buttons."]}),r.jsx(s,{href:"components/button-group#nesting",children:r.jsxs(n,{role:"group","aria-label":"Button group with nested dropdown",children:[r.jsx(e,{color:"primary",children:"1"}),r.jsx(e,{color:"primary",children:"2"}),r.jsxs(d,{variant:"btn-group",children:[r.jsx(h,{color:"primary",children:"Dropdown"}),r.jsxs(u,{children:[r.jsx(o,{href:"#",children:"Action"}),r.jsx(o,{href:"#",children:"Another action"}),r.jsx(o,{href:"#",children:"Something else here"}),r.jsx(x,{}),r.jsx(o,{href:"#",children:"Separated link"})]})]})]})})]})]})}),r.jsx(i,{xs:12,children:r.jsxs(t,{className:"mb-4",children:[r.jsxs(c,{children:[r.jsx("strong",{children:"React Button Group"})," ",r.jsx("span",{children:"Vertical variation"})]}),r.jsxs(a,{children:[r.jsxs("p",{className:"text-body-secondary small",children:["Create a set of buttons that appear vertically stacked rather than horizontally."," ",r.jsx("strong",{children:"Split button dropdowns are not supported here."})]}),r.jsx(s,{href:"components/button-group/#vertical-variation",children:r.jsxs(n,{vertical:!0,role:"group","aria-label":"Vertical button group",children:[r.jsx(e,{color:"dark",children:"Button"}),r.jsx(e,{color:"dark",children:"Button"}),r.jsx(e,{color:"dark",children:"Button"}),r.jsx(e,{color:"dark",children:"Button"}),r.jsx(e,{color:"dark",children:"Button"}),r.jsx(e,{color:"dark",children:"Button"}),r.jsx(e,{color:"dark",children:"Button"})]})}),r.jsx(s,{href:"components/button-group/#vertical-variation",children:r.jsxs(n,{vertical:!0,role:"group","aria-label":"Vertical button group",children:[r.jsx(e,{color:"primary",children:"Button"}),r.jsx(e,{color:"primary",children:"Button"}),r.jsxs(d,{variant:"btn-group",children:[r.jsx(h,{color:"primary",children:"Dropdown"}),r.jsxs(u,{children:[r.jsx(o,{href:"#",children:"Action"}),r.jsx(o,{href:"#",children:"Another action"}),r.jsx(o,{href:"#",children:"Something else here"}),r.jsx(x,{}),r.jsx(o,{href:"#",children:"Separated link"})]})]}),r.jsx(e,{color:"primary",children:"Button"}),r.jsx(e,{color:"primary",children:"Button"}),r.jsxs(d,{variant:"btn-group",children:[r.jsx(h,{color:"primary",children:"Dropdown"}),r.jsxs(u,{children:[r.jsx(o,{href:"#",children:"Action"}),r.jsx(o,{href:"#",children:"Another action"}),r.jsx(o,{href:"#",children:"Something else here"}),r.jsx(x,{}),r.jsx(o,{href:"#",children:"Separated link"})]})]}),r.jsxs(d,{variant:"btn-group",children:[r.jsx(h,{color:"primary",children:"Dropdown"}),r.jsxs(u,{children:[r.jsx(o,{href:"#",children:"Action"}),r.jsx(o,{href:"#",children:"Another action"}),r.jsx(o,{href:"#",children:"Something else here"}),r.jsx(x,{}),r.jsx(o,{href:"#",children:"Separated link"})]})]}),r.jsxs(d,{variant:"btn-group",children:[r.jsx(h,{color:"primary",children:"Dropdown"}),r.jsxs(u,{children:[r.jsx(o,{href:"#",children:"Action"}),r.jsx(o,{href:"#",children:"Another action"}),r.jsx(o,{href:"#",children:"Something else here"}),r.jsx(x,{}),r.jsx(o,{href:"#",children:"Separated link"})]})]})]})}),r.jsx(s,{href:"components/button-group/#vertical-variation",children:r.jsxs(n,{vertical:!0,role:"group","aria-label":"Vertical button group",children:[r.jsx(l,{type:"radio",button:{color:"danger",variant:"outline"},name:"vbtnradio",id:"vbtnradio1",autoComplete:"off",label:"Radio 1",defaultChecked:!0}),r.jsx(l,{type:"radio",button:{color:"danger",variant:"outline"},name:"vbtnradio",id:"vbtnradio2",autoComplete:"off",label:"Radio 2"}),r.jsx(l,{type:"radio",button:{color:"danger",variant:"outline"},name:"vbtnradio",id:"vbtnradio3",autoComplete:"off",label:"Radio 3"})]})})]})]})})]});export{W as default};
