import{a as N,_ as F,R as l,b as p,c as y,P as a}from"./index-BDJ8oeCE.js";import{C as h}from"./CFormControlValidation-_wBnnnml.js";import{C as v}from"./CFormLabel-CzXD3nfE.js";var b=N.forwardRef(function(e,t){var r=e.children,n=e.className,i=F(e,["children","className"]);return l.createElement("div",p({className:y("form-floating",n)},i,{ref:t}),r)});b.propTypes={children:a.node,className:a.string};b.displayName="CFormFloating";var s=N.forwardRef(function(e,t){var r=e.children,n=e.as,i=n===void 0?"div":n,d=e.className,c=F(e,["children","as","className"]);return l.createElement(i,p({className:y("form-text",d)},c,{ref:t}),r)});s.propTypes={as:a.elementType,children:a.node,className:a.string};s.displayName="CFormText";var k=function(e){var t=e.children,r=e.describedby,n=e.feedback,i=e.feedbackInvalid,d=e.feedbackValid,c=e.floatingClassName,m=e.floatingLabel,C=e.id,u=e.invalid,f=e.label,o=e.text,E=e.tooltipFeedback,T=e.valid,g=function(){return l.createElement(h,{describedby:r,feedback:n,feedbackInvalid:i,feedbackValid:d,floatingLabel:m,invalid:u,tooltipFeedback:E,valid:T})};return m?l.createElement(b,{className:c},t,l.createElement(v,{htmlFor:C},f||m),o&&l.createElement(s,{id:r},o),l.createElement(g,null)):l.createElement(l.Fragment,null,f&&l.createElement(v,{htmlFor:C},f),t,o&&l.createElement(s,{id:r},o),l.createElement(g,null))};k.propTypes=p({children:a.node,floatingClassName:a.string,floatingLabel:a.oneOfType([a.node,a.string]),label:a.oneOfType([a.node,a.string]),text:a.oneOfType([a.node,a.string])},h.propTypes);k.displayName="CFormControlWrapper";export{b as C,k as a};
