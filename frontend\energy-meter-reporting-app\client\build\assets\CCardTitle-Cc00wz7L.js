import{a as l,_ as d,R as m,b as o,c as p,P as a}from"./index-BDJ8oeCE.js";var N=l.forwardRef(function(e,n){var t=e.children,s=e.as,c=s===void 0?"img":s,i=e.className,r=e.orientation,h=d(e,["children","as","className","orientation"]);return m.createElement(c,o({className:p(r?"card-img-".concat(r):"card-img",i)},h,{ref:n}),t)});N.propTypes={as:a.elementType,children:a.node,className:a.string,orientation:a.oneOf(["top","bottom"])};N.displayName="CCardImage";var f=l.forwardRef(function(e,n){var t=e.children,s=e.as,c=s===void 0?"p":s,i=e.className,r=d(e,["children","as","className"]);return m.createElement(c,o({className:p("card-text",i)},r,{ref:n}),t)});f.propTypes={as:a.elementType,children:a.node,className:a.string};f.displayName="CCardText";var C=l.forwardRef(function(e,n){var t=e.children,s=e.as,c=s===void 0?"h5":s,i=e.className,r=d(e,["children","as","className"]);return m.createElement(c,o({className:p("card-title",i)},r,{ref:n}),t)});C.propTypes={as:a.elementType,children:a.node,className:a.string};C.displayName="CCardTitle";export{N as C,C as a,f as b};
