const express = require('express');
const router = express.Router();
const locationController = require('../controllers/locationController');
const { authenticate, authorizeRole } = require('../utils/middleware');

// Route to add a new location
router.post('/', authenticate, authorizeRole('SuperAdmin'),locationController.addLocation);

// Route to get all locations
router.get('/', locationController.getAllLocations);

// Route to get a specific location by ID
router.get('/:id', locationController.getLocationById);

// Route to update a location by ID
router.put('/:id',authenticate, authorize<PERSON>ole('SuperAdmin'), locationController.updateLocation);

// Route to delete a location by ID
router.delete('/:id',authenticate, authorize<PERSON><PERSON>('SuperAdmin'), locationController.deleteLocation);

module.exports = router;