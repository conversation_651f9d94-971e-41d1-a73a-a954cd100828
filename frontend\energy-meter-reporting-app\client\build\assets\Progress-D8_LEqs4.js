import{j as s}from"./index-BDJ8oeCE.js";import{a as o,b as a}from"./DefaultLayout-BolUaDEE.js";import"./index.esm-DSzlmaRN.js";import{C as d,a as l}from"./CRow-C1o2zw34.js";import{C as c,a as n}from"./CCardBody-iimbKiZ7.js";import{C as i}from"./CCardHeader-CFnfD6gM.js";import{C as r,a as e}from"./CProgress-CgQiCfdj.js";import"./cil-user-Ddrdy7PS.js";const u=()=>s.jsxs(d,{children:[s.jsxs(l,{xs:12,children:[s.jsx(o,{href:"components/progress/"}),s.jsxs(c,{className:"mb-4",children:[s.jsxs(i,{children:[s.jsx("strong",{children:"React Progress"})," ",s.jsx("small",{children:"Basic example"})]}),s.jsxs(n,{children:[s.jsxs("p",{className:"text-body-secondary small",children:["Progress components are built with two HTML elements, some CSS to set the width, and a few attributes. We don'tuse"," ",s.jsxs("a",{href:"https://developer.mozilla.org/en-US/docs/Web/HTML/Element/progress",children:["the HTML5 ",s.jsx("code",{children:"<progress>"})," element"]}),", ensuring you can stack progress bars, animate them, and place text labels over them."]}),s.jsxs(a,{href:"components/progress",children:[s.jsx(r,{className:"mb-3",children:s.jsx(e,{value:0})}),s.jsx(r,{className:"mb-3",children:s.jsx(e,{value:25})}),s.jsx(r,{className:"mb-3",children:s.jsx(e,{value:50})}),s.jsx(r,{className:"mb-3",children:s.jsx(e,{value:75})}),s.jsx(r,{className:"mb-3",children:s.jsx(e,{value:100})})]})]})]})]}),s.jsx(l,{xs:12,children:s.jsxs(c,{className:"mb-4",children:[s.jsxs(i,{children:[s.jsx("strong",{children:"React Progress"})," ",s.jsx("small",{children:"Labels"})]}),s.jsxs(n,{children:[s.jsxs("p",{className:"text-body-secondary small",children:["Add labels to your progress bars by placing text within the"," ",s.jsx("code",{children:"<CProgressBar>"}),"."]}),s.jsx(a,{href:"components/progress#labels",children:s.jsx(r,{className:"mb-3",children:s.jsx(e,{value:25,children:"25%"})})})]})]})}),s.jsx(l,{xs:12,children:s.jsxs(c,{className:"mb-4",children:[s.jsxs(i,{children:[s.jsx("strong",{children:"React Progress"})," ",s.jsx("small",{children:"Height"})]}),s.jsxs(n,{children:[s.jsxs("p",{className:"text-body-secondary small",children:["We only set a ",s.jsx("code",{children:"height"})," value on the ",s.jsx("code",{children:"<CProgress>"}),", so if you change that value the inner ",s.jsx("code",{children:"<CProgressBar>"})," will automatically resize accordingly."]}),s.jsxs(a,{href:"components/progress#height",children:[s.jsx(r,{height:1,className:"mb-3",children:s.jsx(e,{value:25})}),s.jsx(r,{height:20,className:"mb-3",children:s.jsx(e,{value:25})})]})]})]})}),s.jsx(l,{xs:12,children:s.jsxs(c,{className:"mb-4",children:[s.jsxs(i,{children:[s.jsx("strong",{children:"React Progress"})," ",s.jsx("small",{children:"Backgrounds"})]}),s.jsxs(n,{children:[s.jsxs("p",{className:"text-body-secondary small",children:["Use ",s.jsx("code",{children:"color"})," prop to change the appearance of individual progress bars."]}),s.jsxs(a,{href:"components/progress#backgrounds",children:[s.jsx(r,{className:"mb-3",children:s.jsx(e,{color:"success",value:25})}),s.jsx(r,{className:"mb-3",children:s.jsx(e,{color:"info",value:50})}),s.jsx(r,{className:"mb-3",children:s.jsx(e,{color:"warning",value:75})}),s.jsx(r,{className:"mb-3",children:s.jsx(e,{color:"danger",value:100})})]})]})]})}),s.jsx(l,{xs:12,children:s.jsxs(c,{className:"mb-4",children:[s.jsxs(i,{children:[s.jsx("strong",{children:"React Progress"})," ",s.jsx("small",{children:"Multiple bars"})]}),s.jsxs(n,{children:[s.jsx("p",{className:"text-body-secondary small",children:"Include multiple progress bars in a progress component if you need."}),s.jsx(a,{href:"components/progress#multiple-bars",children:s.jsxs(r,{className:"mb-3",children:[s.jsx(e,{value:15}),s.jsx(e,{color:"success",value:30}),s.jsx(e,{color:"info",value:20})]})})]})]})}),s.jsx(l,{xs:12,children:s.jsxs(c,{className:"mb-4",children:[s.jsxs(i,{children:[s.jsx("strong",{children:"React Progress"})," ",s.jsx("small",{children:"Striped"})]}),s.jsxs(n,{children:[s.jsxs("p",{className:"text-body-secondary small",children:["Add ",s.jsx("code",{children:'variant="striped"'})," to any ",s.jsx("code",{children:"<CProgressBar>"})," to apply a stripe via CSS gradient over the progress bar's background color."]}),s.jsxs(a,{href:"components/progress#striped",children:[s.jsx(r,{className:"mb-3",children:s.jsx(e,{color:"success",variant:"striped",value:25})}),s.jsx(r,{className:"mb-3",children:s.jsx(e,{color:"info",variant:"striped",value:50})}),s.jsx(r,{className:"mb-3",children:s.jsx(e,{color:"warning",variant:"striped",value:75})}),s.jsx(r,{className:"mb-3",children:s.jsx(e,{color:"danger",variant:"striped",value:100})})]})]})]})}),s.jsx(l,{xs:12,children:s.jsxs(c,{className:"mb-4",children:[s.jsxs(i,{children:[s.jsx("strong",{children:"React Progress"})," ",s.jsx("small",{children:"Animated stripes"})]}),s.jsxs(n,{children:[s.jsxs("p",{className:"text-body-secondary small",children:["The striped gradient can also be animated. Add ",s.jsx("code",{children:"animated"})," property to"," ",s.jsx("code",{children:"<CProgressBar>"})," to animate the stripes right to left via CSS3 animations."]}),s.jsxs(a,{href:"components/progress#animated-stripes",children:[s.jsx(r,{className:"mb-3",children:s.jsx(e,{color:"success",variant:"striped",animated:!0,value:25})}),s.jsx(r,{className:"mb-3",children:s.jsx(e,{color:"info",variant:"striped",animated:!0,value:50})}),s.jsx(r,{className:"mb-3",children:s.jsx(e,{color:"warning",variant:"striped",animated:!0,value:75})}),s.jsx(r,{className:"mb-3",children:s.jsx(e,{color:"danger",variant:"striped",animated:!0,value:100})})]})]})]})})]});export{u as default};
