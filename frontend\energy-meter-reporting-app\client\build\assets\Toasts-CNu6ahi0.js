import{a as u,_ as y,R as g,b as v,c as N,P as j,j as s}from"./index-BDJ8oeCE.js";import{a as C,b as a}from"./DefaultLayout-BolUaDEE.js";import{a as p}from"./index.esm-DSzlmaRN.js";import{C as T,a as r}from"./CRow-C1o2zw34.js";import{C as i,a as c}from"./CCardBody-iimbKiZ7.js";import{C as n}from"./CCardHeader-CFnfD6gM.js";import{C as d,a as e,b as t,c as f}from"./CToaster-DxUpGnuG.js";import"./cil-user-Ddrdy7PS.js";var o=u.forwardRef(function(l,h){var m=l.children,x=l.className,w=l.closeButton,b=y(l,["children","className","closeButton"]);return g.createElement("div",v({className:N("toast-header",x)},b,{ref:h}),m,w&&g.createElement(d,null))});o.propTypes={children:j.node,className:j.string,closeButton:j.bool};o.displayName="CToastHeader";const R=()=>{const[l,h]=u.useState(0),m=u.useRef(),x=s.jsxs(e,{children:[s.jsxs(o,{closeButton:!0,children:[s.jsx("svg",{className:"rounded me-2",width:"20",height:"20",xmlns:"http://www.w3.org/2000/svg",preserveAspectRatio:"xMidYMid slice",focusable:"false",role:"img",children:s.jsx("rect",{width:"100%",height:"100%",fill:"#007aff"})}),s.jsx("strong",{className:"me-auto",children:"CoreUI for React.js"}),s.jsx("small",{children:"7 min ago"})]}),s.jsx(t,{children:"Hello, world! This is a toast message."})]});return s.jsxs(s.Fragment,{children:[s.jsx(p,{color:"primary",onClick:()=>h(x),children:"Send a toast"}),s.jsx(f,{ref:m,push:l,placement:"top-end"})]})},Y=()=>s.jsxs(T,{children:[s.jsxs(r,{xs:12,children:[s.jsx(C,{href:"components/toast/"}),s.jsxs(i,{className:"mb-4",children:[s.jsxs(n,{children:[s.jsx("strong",{children:"React Toast"})," ",s.jsx("small",{children:"Basic"})]}),s.jsxs(c,{children:[s.jsx("p",{className:"text-body-secondary small",children:"Toasts are as flexible as you need and have very little required markup. At a minimum, we require a single element to contain your “toasted” content and strongly encourage a dismiss button."}),s.jsx(a,{href:"components/toast",children:s.jsxs(e,{autohide:!1,visible:!0,children:[s.jsxs(o,{closeButton:!0,children:[s.jsx("svg",{className:"rounded me-2",width:"20",height:"20",xmlns:"http://www.w3.org/2000/svg",preserveAspectRatio:"xMidYMid slice",focusable:"false",role:"img",children:s.jsx("rect",{width:"100%",height:"100%",fill:"#007aff"})}),s.jsx("strong",{className:"me-auto",children:"CoreUI for React.js"}),s.jsx("small",{children:"7 min ago"})]}),s.jsx(t,{children:"Hello, world! This is a toast message."})]})}),s.jsx(a,{href:"components/toast",children:R()})]})]})]}),s.jsx(r,{xs:12,children:s.jsxs(i,{className:"mb-4",children:[s.jsxs(n,{children:[s.jsx("strong",{children:"React Toast"})," ",s.jsx("small",{children:"Translucent"})]}),s.jsxs(c,{children:[s.jsx("p",{className:"text-body-secondary small",children:"Toasts are slightly translucent to blend in with what's below them."}),s.jsx(a,{href:"components/toast#translucent",tabContentClassName:"bg-dark",children:s.jsxs(e,{autohide:!1,visible:!0,children:[s.jsxs(o,{closeButton:!0,children:[s.jsx("svg",{className:"rounded me-2",width:"20",height:"20",xmlns:"http://www.w3.org/2000/svg",preserveAspectRatio:"xMidYMid slice",focusable:"false",role:"img",children:s.jsx("rect",{width:"100%",height:"100%",fill:"#007aff"})}),s.jsx("strong",{className:"me-auto",children:"CoreUI for React.js"}),s.jsx("small",{children:"7 min ago"})]}),s.jsx(t,{children:"Hello, world! This is a toast message."})]})})]})]})}),s.jsx(r,{xs:12,children:s.jsxs(i,{className:"mb-4",children:[s.jsxs(n,{children:[s.jsx("strong",{children:"React Toast"})," ",s.jsx("small",{children:"Stacking"})]}),s.jsxs(c,{children:[s.jsx("p",{className:"text-body-secondary small",children:"You can stack toasts by wrapping them in a toast container, which will vertically add some spacing."}),s.jsx(a,{href:"components/toast#stacking",children:s.jsxs(f,{className:"position-static",children:[s.jsxs(e,{autohide:!1,visible:!0,children:[s.jsxs(o,{closeButton:!0,children:[s.jsx("svg",{className:"rounded me-2",width:"20",height:"20",xmlns:"http://www.w3.org/2000/svg",preserveAspectRatio:"xMidYMid slice",focusable:"false",role:"img",children:s.jsx("rect",{width:"100%",height:"100%",fill:"#007aff"})}),s.jsx("strong",{className:"me-auto",children:"CoreUI for React.js"}),s.jsx("small",{children:"7 min ago"})]}),s.jsx(t,{children:"Hello, world! This is a toast message."})]}),s.jsxs(e,{autohide:!1,visible:!0,children:[s.jsxs(o,{closeButton:!0,children:[s.jsx("svg",{className:"rounded me-2",width:"20",height:"20",xmlns:"http://www.w3.org/2000/svg",preserveAspectRatio:"xMidYMid slice",focusable:"false",role:"img",children:s.jsx("rect",{width:"100%",height:"100%",fill:"#007aff"})}),s.jsx("strong",{className:"me-auto",children:"CoreUI for React.js"}),s.jsx("small",{children:"7 min ago"})]}),s.jsx(t,{children:"Hello, world! This is a toast message."})]})]})})]})]})}),s.jsx(r,{xs:12,children:s.jsxs(i,{className:"mb-4",children:[s.jsxs(n,{children:[s.jsx("strong",{children:"React Toast"})," ",s.jsx("small",{children:"Custom content"})]}),s.jsxs(c,{children:[s.jsxs("p",{className:"text-body-secondary small",children:["Customize your toasts by removing sub-components, tweaking them with"," ",s.jsx("a",{href:"https://coreui.io/docs/utilities/api",children:"utilities"}),", or by adding your own markup. Here we've created a simpler toast by removing the default"," ",s.jsx("code",{children:"<CToastHeader>"}),", adding a custom hide icon from"," ",s.jsx("a",{href:"https://coreui.io/icons/",children:"CoreUI Icons"}),", and using some"," ",s.jsx("a",{href:"https://coreui.io/docs/utilities/flex",children:"flexbox utilities"})," to adjust the layout."]}),s.jsx(a,{href:"components/toast#custom-content",children:s.jsx(e,{autohide:!1,className:"align-items-center",visible:!0,children:s.jsxs("div",{className:"d-flex",children:[s.jsx(t,{children:"Hello, world! This is a toast message."}),s.jsx(d,{className:"me-2 m-auto"})]})})}),s.jsx("p",{className:"text-body-secondary small",children:"Alternatively, you can also add additional controls and components to toasts."}),s.jsx(a,{href:"components/toast#custom-content",children:s.jsx(e,{autohide:!1,visible:!0,children:s.jsxs(t,{children:["Hello, world! This is a toast message.",s.jsxs("div",{className:"mt-2 pt-2 border-top",children:[s.jsx(p,{type:"button",color:"primary",size:"sm",children:"Take action"}),s.jsx(d,{as:p,color:"secondary",size:"sm",className:"ms-1",children:"Close"})]})]})})})]})]})}),s.jsx(r,{xs:12,children:s.jsxs(i,{className:"mb-4",children:[s.jsxs(n,{children:[s.jsx("strong",{children:"React Toast"})," ",s.jsx("small",{children:"Custom content"})]}),s.jsxs(c,{children:[s.jsxs("p",{className:"text-body-secondary small",children:["Building on the above example, you can create different toast color schemes with our"," ",s.jsx("a",{href:"https://coreui.io/docs/utilities/colors",children:"color"})," and"," ",s.jsx("a",{href:"https://coreui.io/docs/utilities/background",children:"background"})," utilities. Here we've set ",s.jsx("code",{children:'color="primary"'})," and added ",s.jsx("code",{children:".text-white"})," ","class to the ",s.jsx("code",{children:"<Ctoast>"}),", and then set ",s.jsx("code",{children:"white"})," property to our close button. For a crisp edge, we remove the default border with"," ",s.jsx("code",{children:".border-0"}),"."]}),s.jsx(a,{href:"components/toast#color-schemes",children:s.jsx(e,{autohide:!1,color:"primary",className:"text-white align-items-center",visible:!0,children:s.jsxs("div",{className:"d-flex",children:[s.jsx(t,{children:"Hello, world! This is a toast message."}),s.jsx(d,{className:"me-2 m-auto",white:!0})]})})})]})]})})]});export{Y as default};
