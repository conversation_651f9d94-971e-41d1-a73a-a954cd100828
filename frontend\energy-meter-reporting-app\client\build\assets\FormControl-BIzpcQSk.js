import{j as e}from"./index-BDJ8oeCE.js";import{a as m,b as r}from"./DefaultLayout-BolUaDEE.js";import{a as c}from"./index.esm-DSzlmaRN.js";import{C as d,a}from"./CRow-C1o2zw34.js";import{C as i,a as o}from"./CCardBody-iimbKiZ7.js";import{C as t}from"./CCardHeader-CFnfD6gM.js";import{C as n}from"./CForm-C4rJo8l4.js";import{C as s}from"./CFormLabel-CzXD3nfE.js";import{C as l}from"./CFormInput-LKfVdWds.js";import{C as x}from"./CFormTextarea-Cg-QPt4v.js";import"./cil-user-Ddrdy7PS.js";import"./CFormControlWrapper-CyzWU6Dg.js";import"./CFormControlValidation-_wBnnnml.js";const R=()=>e.jsxs(d,{children:[e.jsxs(a,{xs:12,children:[e.jsx(m,{href:"forms/form-control/"}),e.jsxs(i,{className:"mb-4",children:[e.jsx(t,{children:e.jsx("strong",{children:"React Form Control"})}),e.jsx(o,{children:e.jsx(r,{href:"forms/form-control",children:e.jsxs(n,{children:[e.jsxs("div",{className:"mb-3",children:[e.jsx(s,{htmlFor:"exampleFormControlInput1",children:"Email address"}),e.jsx(l,{type:"email",id:"exampleFormControlInput1",placeholder:"<EMAIL>"})]}),e.jsxs("div",{className:"mb-3",children:[e.jsx(s,{htmlFor:"exampleFormControlTextarea1",children:"Example textarea"}),e.jsx(x,{id:"exampleFormControlTextarea1",rows:3})]})]})})})]})]}),e.jsx(a,{xs:12,children:e.jsxs(i,{className:"mb-4",children:[e.jsxs(t,{children:[e.jsx("strong",{children:"React Form Control"})," ",e.jsx("small",{children:"Sizing"})]}),e.jsxs(o,{children:[e.jsxs("p",{className:"text-body-secondary small",children:["Set heights using ",e.jsx("code",{children:"size"})," property like ",e.jsx("code",{children:'size="lg"'})," and"," ",e.jsx("code",{children:'size="sm"'}),"."]}),e.jsxs(r,{href:"forms/form-control#sizing",children:[e.jsx(l,{type:"text",size:"lg",placeholder:"Large input","aria-label":"lg input example"}),e.jsx("br",{}),e.jsx(l,{type:"text",placeholder:"Default input","aria-label":"default input example"}),e.jsx("br",{}),e.jsx(l,{type:"text",size:"sm",placeholder:"Small input","aria-label":"sm input example"})]})]})]})}),e.jsx(a,{xs:12,children:e.jsxs(i,{className:"mb-4",children:[e.jsxs(t,{children:[e.jsx("strong",{children:"React Form Control"})," ",e.jsx("small",{children:"Disabled"})]}),e.jsxs(o,{children:[e.jsxs("p",{className:"text-body-secondary small",children:["Add the ",e.jsx("code",{children:"disabled"})," boolean attribute on an input to give it a grayed out appearance and remove pointer events."]}),e.jsxs(r,{href:"forms/form-control#disabled",children:[e.jsx(l,{type:"text",placeholder:"Disabled input","aria-label":"Disabled input example",disabled:!0}),e.jsx("br",{}),e.jsx(l,{type:"text",placeholder:"Disabled readonly input","aria-label":"Disabled input example",disabled:!0,readOnly:!0}),e.jsx("br",{})]})]})]})}),e.jsx(a,{xs:12,children:e.jsxs(i,{className:"mb-4",children:[e.jsxs(t,{children:[e.jsx("strong",{children:"React Form Control"})," ",e.jsx("small",{children:"Readonly"})]}),e.jsxs(o,{children:[e.jsxs("p",{className:"text-body-secondary small",children:["Add the ",e.jsx("code",{children:"readOnly"})," boolean attribute on an input to prevent modification of the input's value. Read-only inputs appear lighter (just like disabled inputs), but retain the standard cursor."]}),e.jsx(r,{href:"forms/form-control#readonly",children:e.jsx(l,{type:"text",placeholder:"Readonly input here...","aria-label":"readonly input example",readOnly:!0})})]})]})}),e.jsx(a,{xs:12,children:e.jsxs(i,{className:"mb-4",children:[e.jsxs(t,{children:[e.jsx("strong",{children:"React Form Control"})," ",e.jsx("small",{children:"Readonly plain text"})]}),e.jsxs(o,{children:[e.jsxs("p",{className:"text-body-secondary small",children:["If you want to have ",e.jsx("code",{children:"<input readonly>"})," elements in your form styled as plain text, use the ",e.jsx("code",{children:"plainText"})," boolean property to remove the default form field styling and preserve the correct margin and padding."]}),e.jsxs(r,{href:"components/accordion",children:[e.jsxs(d,{className:"mb-3",children:[e.jsx(s,{htmlFor:"staticEmail",className:"col-sm-2 col-form-label",children:"Email"}),e.jsx("div",{className:"col-sm-10",children:e.jsx(l,{type:"text",id:"staticEmail",defaultValue:"<EMAIL>",readOnly:!0,plainText:!0})})]}),e.jsxs(d,{className:"mb-3",children:[e.jsx(s,{htmlFor:"inputPassword",className:"col-sm-2 col-form-label",children:"Password"}),e.jsx("div",{className:"col-sm-10",children:e.jsx(l,{type:"password",id:"inputPassword"})})]})]}),e.jsx(r,{href:"components/accordion",children:e.jsxs(n,{className:"row g-3",children:[e.jsxs("div",{className:"col-auto",children:[e.jsx(s,{htmlFor:"staticEmail2",className:"visually-hidden",children:"Email"}),e.jsx(l,{type:"text",id:"staticEmail2",defaultValue:"<EMAIL>",readOnly:!0,plainText:!0})]}),e.jsxs("div",{className:"col-auto",children:[e.jsx(s,{htmlFor:"inputPassword2",className:"visually-hidden",children:"Password"}),e.jsx(l,{type:"password",id:"inputPassword2",placeholder:"Password"})]}),e.jsx("div",{className:"col-auto",children:e.jsx(c,{color:"primary",type:"submit",className:"mb-3",children:"Confirm identity"})})]})})]})]})}),e.jsx(a,{xs:12,children:e.jsxs(i,{className:"mb-4",children:[e.jsxs(t,{children:[e.jsx("strong",{children:"React Form Control"})," ",e.jsx("small",{children:"File input"})]}),e.jsx(o,{children:e.jsxs(r,{href:"forms/form-control#file-input",children:[e.jsxs("div",{className:"mb-3",children:[e.jsx(s,{htmlFor:"formFile",children:"Default file input example"}),e.jsx(l,{type:"file",id:"formFile"})]}),e.jsxs("div",{className:"mb-3",children:[e.jsx(s,{htmlFor:"formFileMultiple",children:"Multiple files input example"}),e.jsx(l,{type:"file",id:"formFileMultiple",multiple:!0})]}),e.jsxs("div",{className:"mb-3",children:[e.jsx(s,{htmlFor:"formFileDisabled",children:"Disabled file input example"}),e.jsx(l,{type:"file",id:"formFileDisabled",disabled:!0})]}),e.jsxs("div",{className:"mb-3",children:[e.jsx(s,{htmlFor:"formFileSm",children:"Small file input example"}),e.jsx(l,{type:"file",size:"sm",id:"formFileSm"})]}),e.jsxs("div",{children:[e.jsx(s,{htmlFor:"formFileLg",children:"Large file input example"}),e.jsx(l,{type:"file",size:"lg",id:"formFileLg"})]})]})})]})}),e.jsx(a,{xs:12,children:e.jsxs(i,{className:"mb-4",children:[e.jsxs(t,{children:[e.jsx("strong",{children:"React Form Control"})," ",e.jsx("small",{children:"Color"})]}),e.jsx(o,{children:e.jsxs(r,{href:"forms/form-control#color",children:[e.jsx(s,{htmlFor:"exampleColorInput",children:"Color picker"}),e.jsx(l,{type:"color",id:"exampleColorInput",defaultValue:"#563d7c",title:"Choose your color"})]})})]})})]});export{R as default};
