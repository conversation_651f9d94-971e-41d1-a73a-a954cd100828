DECLARE @TimeStart DATETIME = DATEADD(SECOND, -30, '2024-01-01');
    DECLARE @TimeEnd DATETIME = DATEADD(SECOND, +1, '2024-12-31T23:59:59.000Z');
    DECLARE @EMIDs TABLE (EMID NVARCHAR(10));
    INSERT INTO @EMIDs (EMID) VALUES ('EM1'); INSERT INTO @EMIDs (EMID) VALUES ('EM2'); INSERT INTO @EMIDs (EMID) VALUES ('EM3');
    
    DECLARE @Parameters TABLE (ParameterName NVARCHAR(50));
     INSERT INTO @Parameters (ParameterName) VALUES ('RYPHASE_VOL'); INSERT INTO @Parameters (ParameterName) VALUES ('YBPHASE_VOL'); INSERT INTO @Parameters (ParameterName) VALUES ('BRPHASE_VOL'); INSERT INTO @Parameters (ParameterName) VALUES ('RPHASE_CUR'); INSERT INTO @Parameters (ParameterName) VALUES ('YPHASE_CUR'); INSERT INTO @Parameters (ParameterName) VALUES ('BPHASE_CUR'); INSERT INTO @Parameters (ParameterName) VALUES ('TOTAL_KWH'); INSERT INTO @Parameters (ParameterName) VALUES ('AVGPF');

           
    
    WITH TagIndices AS (
        SELECT TagIndex, TagName FROM dbo.TagTable_01
        WHERE EXISTS (
            SELECT 1 FROM @EMIDs e
            JOIN @Parameters p ON TagName = e.EMID + '_' + p.ParameterName
        )
    ),
    HourlyData AS (
-- Find the closest reading to either hh:00:00 or hh+1:00:00
SELECT 
CASE 
WHEN DATEPART(MINUTE, f.DateAndTime) >= 30 
THEN DATEADD(HOUR, DATEDIFF(HOUR, 0, f.DateAndTime) + 1, 0)  
ELSE DATEADD(HOUR, DATEDIFF(HOUR, 0, f.DateAndTime), 0)  
END AS RoundedDateTime,
f.DateAndTime AS ActualDateTime,
f.TagIndex,
f.Val,
ROW_NUMBER() OVER (
PARTITION BY t.TagName, 
      CASE 
          WHEN DATEPART(MINUTE, f.DateAndTime) >= 30 
          THEN DATEADD(HOUR, DATEDIFF(HOUR, 0, f.DateAndTime) + 1, 0)  
          ELSE DATEADD(HOUR, DATEDIFF(HOUR, 0, f.DateAndTime), 0)  
      END 
ORDER BY ABS(DATEDIFF(SECOND, 
CASE 
 WHEN DATEPART(MINUTE, f.DateAndTime) >= 30 
 THEN DATEADD(HOUR, DATEDIFF(HOUR, 0, f.DateAndTime) + 1, 0)  
 ELSE DATEADD(HOUR, DATEDIFF(HOUR, 0, f.DateAndTime), 0)  
END, f.DateAndTime))
) AS rn
FROM dbo.FloatTable_01 f
JOIN TagIndices t ON f.TagIndex = t.TagIndex
WHERE f.DateAndTime >= @TimeStart 
AND f.DateAndTime < @TimeEnd
),
LastReading AS (
-- Find the closest reading to 23:57 for each day
SELECT 
CAST(DATEADD(DAY, DATEDIFF(DAY, 0, f.DateAndTime), '23:57:00') AS DATETIME) AS RoundedDateTime,
f.DateAndTime AS ActualDateTime,
f.TagIndex,
f.Val,
ROW_NUMBER() OVER (
PARTITION BY t.TagName, CAST(f.DateAndTime AS DATE) 
ORDER BY ABS(DATEDIFF(SECOND, 
CAST(DATEADD(DAY, DATEDIFF(DAY, 0, f.DateAndTime), '23:57:00') AS DATETIME), 
f.DateAndTime))
) AS rn
FROM dbo.FloatTable_01 f
JOIN TagIndices t ON f.TagIndex = t.TagIndex
WHERE f.DateAndTime >= @TimeStart 
AND f.DateAndTime < @TimeEnd
),
FinalData AS (
-- Combine HourlyData and LastReading, ensuring final rounding
SELECT 
CASE 
WHEN DATEPART(MINUTE, h.ActualDateTime) >= 30 
THEN DATEADD(HOUR, DATEDIFF(HOUR, 0, h.ActualDateTime) + 1, 0)  
ELSE DATEADD(HOUR, DATEDIFF(HOUR, 0, h.ActualDateTime), 0)  
END AS DateAndTime, 
t.TagName, 
h.Val
FROM HourlyData h
JOIN TagIndices t ON h.TagIndex = t.TagIndex
WHERE h.rn = 1

UNION ALL

SELECT 
l.RoundedDateTime AS DateAndTime, 
t.TagName, 
l.Val
FROM LastReading l
JOIN TagIndices t ON l.TagIndex = t.TagIndex
WHERE l.rn = 1
),
    
    ProcessedData AS (
    SELECT CAST(DateAndTime AS DATE) AS ReadingDate, TagName, Val FROM FinalData
    WHERE DateAndTime >= @TimeStart 
    AND DateAndTime < @TimeEnd
    ),

    MedianData AS (
    SELECT 
        ReadingDate, 
        TagName,
        PERCENTILE_CONT(0.5) WITHIN GROUP (ORDER BY Val) OVER (PARTITION BY ReadingDate, TagName) AS MedianVal
    FROM ProcessedData
    WHERE TagName NOT LIKE '%_KWH' -- Exclude KWH for now
    ),
    -- Get Max KWH Value (Last of the Day)
    MaxKWH AS (
    SELECT 
        f.ReadingDate,
        f.TagName,
        MAX(f.Val) AS MaxVal
    FROM ProcessedData f
    WHERE f.TagName LIKE '%_KWH' -- Only select KWH values
    GROUP BY f.ReadingDate, f.TagName
    )

    ,
MonthlyData As (
SELECT DISTINCT ReadingDate , TagName, MedianVal AS Val FROM MedianData
UNION ALL
SELECT DISTINCT ReadingDate , TagName, MaxVal AS Val FROM MaxKWH
),
MonthlyMedian AS (
    -- Compute the median for Voltage & Current per month
    SELECT 
        YEAR(ReadingDate) AS Year,
        MONTH(ReadingDate) AS Month,
        TagName,
        PERCENTILE_CONT(0.5) WITHIN GROUP (ORDER BY Val) 
        OVER (PARTITION BY YEAR(ReadingDate), MONTH(ReadingDate), TagName) AS MedianVal
    FROM MonthlyData
    WHERE TagName NOT LIKE '%_KWH' -- Exclude KWH for now
),
MonthlyTotalKWH AS (
    -- Compute the total KWH consumed per month
    SELECT 
        YEAR(ReadingDate) AS Year,
        MONTH(ReadingDate) AS Month,
        TagName,
        SUM(Val) AS TotalKWH
    FROM MonthlyData
    WHERE TagName LIKE '%_KWH' -- Only KWH values
    GROUP BY YEAR(ReadingDate), MONTH(ReadingDate), TagName
),
YearlyData AS (
    -- Combine median and max KWH values
    SELECT DISTINCT Year, Month, TagName, MedianVal AS Val FROM MonthlyMedian
    UNION ALL
    SELECT DISTINCT Year, Month, TagName, TotalKWH AS Val FROM MonthlyTotalKWH
)

SELECT 
    FORMAT(DATEFROMPARTS(Year, Month, 1), 'MMMM-yyyy') AS DateAndTime,
    TagName,
    Val
FROM YearlyData
ORDER BY Year, Month, TagName;