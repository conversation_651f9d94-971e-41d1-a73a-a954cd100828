import{j as e}from"./index-BDJ8oeCE.js";import{a as o,b as l}from"./DefaultLayout-BolUaDEE.js";import"./index.esm-DSzlmaRN.js";import{C as t,a as i}from"./CRow-C1o2zw34.js";import{C as r,a as n}from"./CCardBody-iimbKiZ7.js";import{C as a}from"./CCardHeader-CFnfD6gM.js";import{C as s}from"./CFormSelect-B3z3ot4z.js";import"./cil-user-Ddrdy7PS.js";import"./CFormControlWrapper-CyzWU6Dg.js";import"./CFormControlValidation-_wBnnnml.js";import"./CFormLabel-CzXD3nfE.js";const C=()=>e.jsxs(t,{children:[e.jsxs(i,{xs:12,children:[e.jsx(o,{href:"forms/select/"}),e.jsxs(r,{className:"mb-4",children:[e.jsxs(a,{children:[e.jsx("strong",{children:"React Select"})," ",e.jsx("small",{children:"Default"})]}),e.jsx(n,{children:e.jsx(l,{href:"forms/select",children:e.jsxs(s,{"aria-label":"Default select example",children:[e.jsx("option",{children:"Open this select menu"}),e.jsx("option",{value:"1",children:"One"}),e.jsx("option",{value:"2",children:"Two"}),e.jsx("option",{value:"3",children:"Three"})]})})})]})]}),e.jsx(i,{xs:12,children:e.jsxs(r,{className:"mb-4",children:[e.jsxs(a,{children:[e.jsx("strong",{children:"React Select"})," ",e.jsx("small",{children:"Sizing"})]}),e.jsxs(n,{children:[e.jsx("p",{className:"text-body-secondary small",children:"You may also choose from small and large custom selects to match our similarly sized text inputs."}),e.jsxs(l,{href:"forms/select#sizing",children:[e.jsxs(s,{size:"lg",className:"mb-3","aria-label":"Large select example",children:[e.jsx("option",{children:"Open this select menu"}),e.jsx("option",{value:"1",children:"One"}),e.jsx("option",{value:"2",children:"Two"}),e.jsx("option",{value:"3",children:"Three"})]}),e.jsxs(s,{size:"sm",className:"mb-3","aria-label":"Small select example",children:[e.jsx("option",{children:"Open this select menu"}),e.jsx("option",{value:"1",children:"One"}),e.jsx("option",{value:"2",children:"Two"}),e.jsx("option",{value:"3",children:"Three"})]})]}),e.jsxs("p",{className:"text-body-secondary small",children:["The ",e.jsx("code",{children:"multiple"})," attribute is also supported:"]}),e.jsx(l,{href:"forms/select#sizing",children:e.jsxs(s,{size:"lg",multiple:!0,"aria-label":"Multiple select example",children:[e.jsx("option",{children:"Open this select menu"}),e.jsx("option",{value:"1",children:"One"}),e.jsx("option",{value:"2",children:"Two"}),e.jsx("option",{value:"3",children:"Three"})]})}),e.jsxs("p",{className:"text-body-secondary small",children:["As is the ",e.jsx("code",{children:"htmlSize"})," property:"]}),e.jsx(l,{href:"forms/select#sizing",children:e.jsxs(s,{size:"lg",multiple:!0,"aria-label":"Multiple select example",children:[e.jsx("option",{children:"Open this select menu"}),e.jsx("option",{value:"1",children:"One"}),e.jsx("option",{value:"2",children:"Two"}),e.jsx("option",{value:"3",children:"Three"})]})})]})]})}),e.jsx(i,{xs:12,children:e.jsxs(r,{className:"mb-4",children:[e.jsxs(a,{children:[e.jsx("strong",{children:"React Select"})," ",e.jsx("small",{children:"Disabled"})]}),e.jsxs(n,{children:[e.jsxs("p",{className:"text-body-secondary small",children:["Add the ",e.jsx("code",{children:"disabled"})," boolean attribute on a select to give it a grayed out appearance and remove pointer events."]}),e.jsx(l,{href:"forms/select#disabled",children:e.jsxs(s,{"aria-label":"Disabled select example",disabled:!0,children:[e.jsx("option",{children:"Open this select menu"}),e.jsx("option",{value:"1",children:"One"}),e.jsx("option",{value:"2",children:"Two"}),e.jsx("option",{value:"3",children:"Three"})]})})]})]})})]});export{C as default};
