import{j as e}from"./index-BDJ8oeCE.js";import{a as c,b as s}from"./DefaultLayout-BolUaDEE.js";import{a as o}from"./index.esm-DSzlmaRN.js";import{C as l,a as t}from"./CRow-C1o2zw34.js";import{C as a,a as n}from"./CCardBody-iimbKiZ7.js";import{C as i}from"./CCardHeader-CFnfD6gM.js";import{C as r}from"./CPopover-DsLOhH2M.js";import"./cil-user-Ddrdy7PS.js";import"./getRTLPlacement-BTVXPs3l.js";import"./getTransitionDurationFromElement-Cpu4p4hx.js";const C=()=>e.jsxs(l,{children:[e.jsxs(t,{xs:12,children:[e.jsx(c,{href:"components/popover/"}),e.jsxs(a,{className:"mb-4",children:[e.jsxs(i,{children:[e.jsx("strong",{children:"React Popover"})," ",e.jsx("small",{children:"Basic example"})]}),e.jsx(n,{children:e.jsx(s,{href:"components/popover",children:e.jsx(r,{title:"Popover title",content:"And here’s some amazing content. It’s very engaging. Right?",placement:"right",children:e.jsx(o,{color:"danger",size:"lg",children:"Click to toggle popover"})})})})]})]}),e.jsx(t,{xs:12,children:e.jsxs(a,{className:"mb-4",children:[e.jsxs(i,{children:[e.jsx("strong",{children:"React Popover"})," ",e.jsx("small",{children:"Four directions"})]}),e.jsxs(n,{children:[e.jsx("p",{className:"text-body-secondary small",children:"Four options are available: top, right, bottom, and left aligned. Directions are mirrored when using CoreUI for React in RTL."}),e.jsxs(s,{href:"components/popover#four-directions",children:[e.jsx(r,{content:"Vivamus sagittis lacus vel augue laoreet rutrum faucibus.",placement:"top",children:e.jsx(o,{color:"secondary",children:"Popover on top"})}),e.jsx(r,{content:"Vivamus sagittis lacus vel augue laoreet rutrum faucibus.",placement:"right",children:e.jsx(o,{color:"secondary",children:"Popover on right"})}),e.jsx(r,{content:"Vivamus sagittis lacus vel augue laoreet rutrum faucibus.",placement:"bottom",children:e.jsx(o,{color:"secondary",children:"Popover on bottom"})}),e.jsx(r,{content:"Vivamus sagittis lacus vel augue laoreet rutrum faucibus.",placement:"left",children:e.jsx(o,{color:"secondary",children:"Popover on left"})})]})]})]})})]});export{C as default};
