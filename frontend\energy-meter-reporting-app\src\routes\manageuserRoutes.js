const express = require('express');
const { authenticate, authorizeRoles } = require('../utils/middleware');
const { addUser, updateUser, deleteUser, getUsers } = require('../controllers/manageuserController');
const User = require('../models/userModel'); // Assuming you have a User model defined in models/userModel.js

const router = express.Router();
// Admin routes for user management
//router.get('/', authenticate, getUsers);
//router.post('/', authenticate, addUser);
//router.put('/:id', authenticate, updateUser);
//router.delete('/:id', authenticate, deleteUser);
const restrictSuperAdminActions = (req, res, next) => {
    const { role } = req.body; // Assuming the role of the user being added/deleted is in the request body
    if (req.user.role === 'Admin' && role === 'SuperAdmin') {
        return res.status(403).json({ message: '<PERSON><PERSON> cannot add or delete users with the SuperAdmin role.' });
    }
    next();
};
const restrictTargetSuperAdmin = async (req, res, next) => {
    try {
        
        const { id } = req.params; // User ID from the request params
        const targetUser = await User.findByPk(id); // Fetch the target user from the database
        
        if (!targetUser) {
            return res.status(404).json({ message: 'User not found' });
        }

        // Check if the target user is a SuperAdmin and the logged-in user is an Admin
        if (req.user.role !== 'SuperAdmin' && targetUser.role === 'SuperAdmin') {
            return res.status(403).json({ message: 'Only SuperAdmin can modify or delete users with the SuperAdmin role.' });
        }
        if(req.user.role === 'Viewer' && targetUser.role === 'Admin'){
            return res.status(403).json({ message: 'Viewers cannot modify or delete Admin users.' });
        }

        next();
    } catch (error) {
        res.status(500).json({ message: 'Error checking permissions', error });
    }
};

const allowSelfPasswordChange = async (req, res, next) => {
    const { id } = req.params; // User ID from the request params
    if (req.user.role === 'Viewer' && parseInt(id) !== req.user.id) {
        return res.status(403).json({ message: 'Viewers can only update their own password.' });
    }
    next();
};


router.get('/', getUsers);
router.post('/', authenticate, authorizeRoles('Admin', 'SuperAdmin'), restrictSuperAdminActions, addUser);
router.put('/:id',authenticate,allowSelfPasswordChange, restrictTargetSuperAdmin, updateUser);
router.delete('/:id',authenticate, authorizeRoles('Admin', 'SuperAdmin'), restrictTargetSuperAdmin, deleteUser);
module.exports = router;