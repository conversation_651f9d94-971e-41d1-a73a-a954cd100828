import{j as e}from"./index-BDJ8oeCE.js";import{a as m,b as a}from"./DefaultLayout-BolUaDEE.js";import"./index.esm-DSzlmaRN.js";import{C as d,a as r}from"./CRow-C1o2zw34.js";import{C as o,a as t}from"./CCardBody-iimbKiZ7.js";import{C as i}from"./CCardHeader-CFnfD6gM.js";import{C as l}from"./CFormControlWrapper-CyzWU6Dg.js";import{C as n}from"./CFormInput-LKfVdWds.js";import{C as s}from"./CFormLabel-CzXD3nfE.js";import{C as c}from"./CFormTextarea-Cg-QPt4v.js";import{C as h}from"./CFormSelect-B3z3ot4z.js";import"./cil-user-Ddrdy7PS.js";import"./CFormControlValidation-_wBnnnml.js";const v=()=>e.jsxs(d,{children:[e.jsxs(r,{xs:12,children:[e.jsx(m,{href:"forms/floating-labels/"}),e.jsxs(o,{className:"mb-4",children:[e.jsx(i,{children:e.jsx("strong",{children:"React Floating labels"})}),e.jsxs(t,{children:[e.jsxs("p",{className:"text-body-secondary small",children:["Wrap a pair of ",e.jsx("code",{children:"<CFormInput>"})," and ",e.jsx("code",{children:"<CFormLabel>"})," ","elements in ",e.jsx("code",{children:"CFormFloating"})," to enable floating labels with textual form fields. A ",e.jsx("code",{children:"placeholder"})," is required on each ",e.jsx("code",{children:"<CFormInput>"})," ","as our method of CSS-only floating labels uses the ",e.jsx("code",{children:":placeholder-shown"})," ","pseudo-element. Also note that the ",e.jsx("code",{children:"<CFormInput>"})," must come first so we can utilize a sibling selector (e.g., ",e.jsx("code",{children:"~"}),")."]}),e.jsxs(a,{href:"forms/floating-labels",children:[e.jsxs(l,{className:"mb-3",children:[e.jsx(n,{type:"email",id:"floatingInput",placeholder:"<EMAIL>"}),e.jsx(s,{htmlFor:"floatingInput",children:"Email address"})]}),e.jsxs(l,{children:[e.jsx(n,{type:"password",id:"floatingPassword",placeholder:"Password"}),e.jsx(s,{htmlFor:"floatingPassword",children:"Password"})]})]}),e.jsxs("p",{className:"text-body-secondary small",children:["When there's a ",e.jsx("code",{children:"value"})," already defined, ",e.jsx("code",{children:"<CFormLabel>"}),"s will automatically adjust to their floated position."]}),e.jsx(a,{href:"forms/floating-labels",children:e.jsxs(l,{children:[e.jsx(n,{type:"email",id:"floatingInputValue",placeholder:"<EMAIL>",defaultValue:"<EMAIL>"}),e.jsx(s,{htmlFor:"floatingInputValue",children:"Input with value"})]})})]})]})]}),e.jsx(r,{xs:12,children:e.jsxs(o,{className:"mb-4",children:[e.jsxs(i,{children:[e.jsx("strong",{children:"React Floating labels"})," ",e.jsx("small",{children:"Textareas"})]}),e.jsxs(t,{children:[e.jsxs("p",{className:"text-body-secondary small",children:["By default, ",e.jsx("code",{children:"<CFormTextarea>"}),"s will be the same height as"," ",e.jsx("code",{children:"<CFormInput>"}),"s."]}),e.jsx(a,{href:"forms/floating-labels#textareas",children:e.jsxs(l,{children:[e.jsx(c,{id:"floatingTextarea",placeholder:"Leave a comment here"}),e.jsx(s,{htmlFor:"floatingTextarea",children:"Comments"})]})}),e.jsxs("p",{className:"text-body-secondary small",children:["To set a custom height on your ",e.jsx("code",{children:"<CFormTextarea;>"}),", do not use the"," ",e.jsx("code",{children:"rows"})," attribute. Instead, set an explicit ",e.jsx("code",{children:"height"})," (either inline or via custom CSS)."]}),e.jsx(a,{href:"forms/floating-labels#textareas",children:e.jsxs(l,{children:[e.jsx(c,{placeholder:"Leave a comment here",id:"floatingTextarea2",style:{height:"100px"}}),e.jsx(s,{htmlFor:"floatingTextarea2",children:"Comments"})]})})]})]})}),e.jsx(r,{xs:12,children:e.jsxs(o,{className:"mb-4",children:[e.jsxs(i,{children:[e.jsx("strong",{children:"React Floating labels"})," ",e.jsx("small",{children:"Selects"})]}),e.jsxs(t,{children:[e.jsxs("p",{className:"text-body-secondary small",children:["Other than ",e.jsx("code",{children:"<CFormInput>"}),", floating labels are only available on"," ",e.jsx("code",{children:"<CFormSelect>"}),"s. They work in the same way, but unlike"," ",e.jsx("code",{children:"<CFormInput>"}),"s, they'll always show the"," ",e.jsx("code",{children:"<CFormLabel>"})," in its floated state."," ",e.jsxs("strong",{children:["Selects with ",e.jsx("code",{children:"size"})," and ",e.jsx("code",{children:"multiple"})," are not supported."]})]}),e.jsx(a,{href:"forms/floating-labels#selects",children:e.jsxs(l,{children:[e.jsxs(h,{id:"floatingSelect","aria-label":"Floating label select example",children:[e.jsx("option",{children:"Open this select menu"}),e.jsx("option",{value:"1",children:"One"}),e.jsx("option",{value:"2",children:"Two"}),e.jsx("option",{value:"3",children:"Three"})]}),e.jsx(s,{htmlFor:"floatingSelect",children:"Works with selects"})]})})]})]})}),e.jsx(r,{xs:12,children:e.jsxs(o,{className:"mb-4",children:[e.jsxs(i,{children:[e.jsx("strong",{children:"React Floating labels"})," ",e.jsx("small",{children:"Layout"})]}),e.jsxs(t,{children:[e.jsx("p",{className:"text-body-secondary small",children:"When working with the CoreUI for Bootstrap grid system, be sure to place form elements within column classes."}),e.jsx(a,{href:"forms/floating-labels#layout",children:e.jsxs(d,{xs:{gutter:2},children:[e.jsx(r,{md:!0,children:e.jsxs(l,{children:[e.jsx(n,{type:"email",id:"floatingInputGrid",placeholder:"<EMAIL>",defaultValue:"<EMAIL>"}),e.jsx(s,{htmlFor:"floatingInputGrid",children:"Email address"})]})}),e.jsx(r,{md:!0,children:e.jsxs(l,{children:[e.jsxs(h,{id:"floatingSelectGrid","aria-label":"Floating label select example",children:[e.jsx("option",{children:"Open this select menu"}),e.jsx("option",{value:"1",children:"One"}),e.jsx("option",{value:"2",children:"Two"}),e.jsx("option",{value:"3",children:"Three"})]}),e.jsx(s,{htmlFor:"floatingSelectGrid",children:"Works with selects"})]})})]})})]})]})})]});export{v as default};
