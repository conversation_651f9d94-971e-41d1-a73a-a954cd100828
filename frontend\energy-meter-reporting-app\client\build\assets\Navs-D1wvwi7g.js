import{j as e}from"./index-BDJ8oeCE.js";import{a as j,b as n,d as r,e as i,f as s,k as t,l as o,m as x,n as h}from"./DefaultLayout-BolUaDEE.js";import"./index.esm-DSzlmaRN.js";import{C as m,a as l}from"./CRow-C1o2zw34.js";import{C as a,a as c}from"./CCardBody-iimbKiZ7.js";import{C as d}from"./CCardHeader-CFnfD6gM.js";import"./cil-user-Ddrdy7PS.js";const w=()=>e.jsxs(m,{children:[e.jsxs(l,{xs:12,children:[e.jsx(j,{href:"components/nav-tabs/"}),e.jsxs(a,{className:"mb-4",children:[e.jsxs(d,{children:[e.jsx("strong",{children:"React Navs"})," ",e.jsx("small",{children:"Base navs"})]}),e.jsxs(c,{children:[e.jsxs("p",{className:"text-body-secondary small",children:["The base ",e.jsx("code",{children:".nav"})," component is built with flexbox and provide a strong foundation for building all types of navigation components. It includes some style overrides (for working with lists), some link padding for larger hit areas, and basic disabled styling."]}),e.jsx(n,{href:"components/nav-tabs#base-nav",children:e.jsxs(r,{children:[e.jsx(i,{children:e.jsx(s,{href:"#",active:!0,children:"Active"})}),e.jsx(i,{children:e.jsx(s,{href:"#",children:"Link"})}),e.jsx(i,{children:e.jsx(s,{href:"#",children:"Link"})}),e.jsx(i,{children:e.jsx(s,{href:"#",disabled:!0,children:"Disabled"})})]})}),e.jsxs("p",{className:"text-body-secondary small",children:["Classes are used throughout, so your markup can be super flexible. Use"," ",e.jsx("code",{children:"<ul>"}),"s like above, ",e.jsx("code",{children:"<ol>"})," if the order of your items is important, or roll your own with a ",e.jsx("code",{children:"<nav>"})," element. Because the .nav uses display: flex, the nav links behave the same as nav items would, but without the extra markup."]}),e.jsx(n,{href:"components/nav-tabs#base-nav",children:e.jsxs(r,{as:"nav",children:[e.jsx(s,{href:"#",active:!0,children:"Active"}),e.jsx(s,{href:"#",children:"Link"}),e.jsx(s,{href:"#",children:"Link"}),e.jsx(s,{href:"#",disabled:!0,children:"Disabled"})]})})]})]})]}),e.jsx(l,{xs:12,children:e.jsxs(a,{className:"mb-4",children:[e.jsxs(d,{children:[e.jsx("strong",{children:"React Navs"})," ",e.jsx("small",{children:"Horizontal alignment"})]}),e.jsxs(c,{children:[e.jsxs("p",{className:"text-body-secondary small",children:["Change the horizontal alignment of your nav with"," ",e.jsx("a",{href:"https://coreui.io/docs/layout/grid/#horizontal-alignment",children:"flexbox utilities"}),". By default, navs are left-aligned, but you can easily change them to center or right aligned."]}),e.jsxs("p",{className:"text-body-secondary small",children:["Centered with ",e.jsx("code",{children:".justify-content-center"}),":"]}),e.jsx(n,{href:"components/nav-tabs#horizontal-alignment",children:e.jsxs(r,{className:"justify-content-center",children:[e.jsx(i,{children:e.jsx(s,{href:"#",active:!0,children:"Active"})}),e.jsx(i,{children:e.jsx(s,{href:"#",children:"Link"})}),e.jsx(i,{children:e.jsx(s,{href:"#",children:"Link"})}),e.jsx(i,{children:e.jsx(s,{href:"#",disabled:!0,children:"Disabled"})})]})}),e.jsxs("p",{className:"text-body-secondary small",children:["Right-aligned with ",e.jsx("code",{children:".justify-content-end"}),":"]}),e.jsx(n,{href:"components/nav-tabs#base-nav",children:e.jsxs(r,{className:"justify-content-end",children:[e.jsx(i,{children:e.jsx(s,{href:"#",active:!0,children:"Active"})}),e.jsx(i,{children:e.jsx(s,{href:"#",children:"Link"})}),e.jsx(i,{children:e.jsx(s,{href:"#",children:"Link"})}),e.jsx(i,{children:e.jsx(s,{href:"#",disabled:!0,children:"Disabled"})})]})})]})]})}),e.jsx(l,{xs:12,children:e.jsxs(a,{className:"mb-4",children:[e.jsxs(d,{children:[e.jsx("strong",{children:"React Navs"})," ",e.jsx("small",{children:"Vertical"})]}),e.jsxs(c,{children:[e.jsxs("p",{className:"text-body-secondary small",children:["Stack your navigation by changing the flex item direction with the"," ",e.jsx("code",{children:".flex-column"})," utility. Need to stack them on some viewports but not others? Use the responsive versions (e.g., ",e.jsx("code",{children:".flex-sm-column"}),")."]}),e.jsx(n,{href:"components/nav-tabs#vertical",children:e.jsxs(r,{className:"flex-column",children:[e.jsx(i,{children:e.jsx(s,{href:"#",active:!0,children:"Active"})}),e.jsx(i,{children:e.jsx(s,{href:"#",children:"Link"})}),e.jsx(i,{children:e.jsx(s,{href:"#",children:"Link"})}),e.jsx(i,{children:e.jsx(s,{href:"#",disabled:!0,children:"Disabled"})})]})})]})]})}),e.jsx(l,{xs:12,children:e.jsxs(a,{className:"mb-4",children:[e.jsxs(d,{children:[e.jsx("strong",{children:"React Navs"})," ",e.jsx("small",{children:"Tabs"})]}),e.jsxs(c,{children:[e.jsxs("p",{className:"text-body-secondary small",children:["Takes the basic nav from above and adds the ",e.jsx("code",{children:'variant="tabs"'})," class to generate a tabbed interface"]}),e.jsx(n,{href:"components/nav-tabs#tabs",children:e.jsxs(r,{variant:"tabs",children:[e.jsx(i,{children:e.jsx(s,{href:"#",active:!0,children:"Active"})}),e.jsx(i,{children:e.jsx(s,{href:"#",children:"Link"})}),e.jsx(i,{children:e.jsx(s,{href:"#",children:"Link"})}),e.jsx(i,{children:e.jsx(s,{href:"#",disabled:!0,children:"Disabled"})})]})})]})]})}),e.jsx(l,{xs:12,children:e.jsxs(a,{className:"mb-4",children:[e.jsxs(d,{children:[e.jsx("strong",{children:"React Navs"})," ",e.jsx("small",{children:"Pills"})]}),e.jsxs(c,{children:[e.jsxs("p",{className:"text-body-secondary small",children:["Take that same HTML, but use ",e.jsx("code",{children:'variant="pills"'})," instead:"]}),e.jsx(n,{href:"components/nav-tabs#pills",children:e.jsxs(r,{variant:"pills",children:[e.jsx(i,{children:e.jsx(s,{href:"#",active:!0,children:"Active"})}),e.jsx(i,{children:e.jsx(s,{href:"#",children:"Link"})}),e.jsx(i,{children:e.jsx(s,{href:"#",children:"Link"})}),e.jsx(i,{children:e.jsx(s,{href:"#",disabled:!0,children:"Disabled"})})]})})]})]})}),e.jsx(l,{xs:12,children:e.jsxs(a,{className:"mb-4",children:[e.jsxs(d,{children:[e.jsx("strong",{children:"React Navs"})," ",e.jsx("small",{children:"Fill and justify"})]}),e.jsxs(c,{children:[e.jsxs("p",{className:"text-body-secondary small",children:["Force your ",e.jsx("code",{children:".nav"}),"'s contents to extend the full available width one of two modifier classes. To proportionately fill all available space with your"," ",e.jsx("code",{children:".nav-item"}),"s, use ",e.jsx("code",{children:'layout="fill"'}),". Notice that all horizontal space is occupied, but not every nav item has the same width."]}),e.jsx(n,{href:"components/nav-tabs#fill-and-justify",children:e.jsxs(r,{variant:"pills",layout:"fill",children:[e.jsx(i,{children:e.jsx(s,{href:"#",active:!0,children:"Active"})}),e.jsx(i,{children:e.jsx(s,{href:"#",children:"Link"})}),e.jsx(i,{children:e.jsx(s,{href:"#",children:"Link"})}),e.jsx(i,{children:e.jsx(s,{href:"#",disabled:!0,children:"Disabled"})})]})}),e.jsxs("p",{className:"text-body-secondary small",children:["For equal-width elements, use ",e.jsx("code",{children:'layout="justified"'}),". All horizontal space will be occupied by nav links, but unlike the .nav-fill above, every nav item will be the same width."]}),e.jsx(n,{href:"components/nav-tabs#fill-and-justify",children:e.jsxs(r,{variant:"pills",layout:"justified",children:[e.jsx(i,{children:e.jsx(s,{href:"#",active:!0,children:"Active"})}),e.jsx(i,{children:e.jsx(s,{href:"#",children:"Link"})}),e.jsx(i,{children:e.jsx(s,{href:"#",children:"Link"})}),e.jsx(i,{children:e.jsx(s,{href:"#",disabled:!0,children:"Disabled"})})]})})]})]})}),e.jsx(l,{xs:12,children:e.jsxs(a,{className:"mb-4",children:[e.jsxs(d,{children:[e.jsx("strong",{children:"React Navs"})," ",e.jsx("small",{children:"Working with flex utilities"})]}),e.jsxs(c,{children:[e.jsxs("p",{className:"text-body-secondary small",children:["If you need responsive nav variations, consider using a series of"," ",e.jsx("a",{href:"https://coreui.io/docs/utilities/flex",children:"flexbox utilities"}),". While more verbose, these utilities offer greater customization across responsive breakpoints. In the example below, our nav will be stacked on the lowest breakpoint, then adapt to a horizontal layout that fills the available width starting from the small breakpoint."]}),e.jsx(n,{href:"components/nav-tabs#working-with-flex-utilities",children:e.jsxs(r,{as:"nav",variant:"pills",className:"flex-column flex-sm-row",children:[e.jsx(s,{href:"#",active:!0,children:"Active"}),e.jsx(s,{href:"#",children:"Link"}),e.jsx(s,{href:"#",children:"Link"}),e.jsx(s,{href:"#",disabled:!0,children:"Disabled"})]})})]})]})}),e.jsx(l,{xs:12,children:e.jsxs(a,{className:"mb-4",children:[e.jsxs(d,{children:[e.jsx("strong",{children:"React Navs"})," ",e.jsx("small",{children:"Tabs with dropdowns"})]}),e.jsx(c,{children:e.jsx(n,{href:"components/nav-tabs#tabs-with-dropdowns",children:e.jsxs(r,{children:[e.jsx(i,{children:e.jsx(s,{href:"#",active:!0,children:"Active"})}),e.jsxs(t,{variant:"nav-item",children:[e.jsx(o,{color:"secondary",children:"Dropdown button"}),e.jsxs(x,{children:[e.jsx(h,{href:"#",children:"Action"}),e.jsx(h,{href:"#",children:"Another action"}),e.jsx(h,{href:"#",children:"Something else here"})]})]}),e.jsx(i,{children:e.jsx(s,{href:"#",children:"Link"})}),e.jsx(i,{children:e.jsx(s,{href:"#",disabled:!0,children:"Disabled"})})]})})})]})}),e.jsx(l,{xs:12,children:e.jsxs(a,{className:"mb-4",children:[e.jsxs(d,{children:[e.jsx("strong",{children:"React Navs"})," ",e.jsx("small",{children:"Pills with dropdowns"})]}),e.jsx(c,{children:e.jsx(n,{href:"components/nav-tabs#pills-with-dropdowns",children:e.jsxs(r,{variant:"pills",children:[e.jsx(i,{children:e.jsx(s,{href:"#",active:!0,children:"Active"})}),e.jsxs(t,{variant:"nav-item",children:[e.jsx(o,{color:"secondary",children:"Dropdown button"}),e.jsxs(x,{children:[e.jsx(h,{href:"#",children:"Action"}),e.jsx(h,{href:"#",children:"Another action"}),e.jsx(h,{href:"#",children:"Something else here"})]})]}),e.jsx(i,{children:e.jsx(s,{href:"#",children:"Link"})}),e.jsx(i,{children:e.jsx(s,{href:"#",disabled:!0,children:"Disabled"})})]})})})]})})]});export{w as default};
