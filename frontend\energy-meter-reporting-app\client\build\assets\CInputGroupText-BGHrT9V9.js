import{a as i,_ as l,R as o,b as m,c as u,P as s}from"./index-BDJ8oeCE.js";var d=i.forwardRef(function(e,t){var a,r=e.children,p=e.className,n=e.size,c=l(e,["children","className","size"]);return o.createElement("div",m({className:u("input-group",(a={},a["input-group-".concat(n)]=n,a),p)},c,{ref:t}),r)});d.propTypes={children:s.node,className:s.string,size:s.oneOf(["sm","lg"])};d.displayName="CInputGroup";var N=i.forwardRef(function(e,t){var a=e.children,r=e.as,p=r===void 0?"span":r,n=e.className,c=l(e,["children","as","className"]);return o.createElement(p,m({className:u("input-group-text",n)},c,{ref:t}),a)});N.propTypes={as:s.elementType,children:s.node,className:s.string};N.displayName="CInputGroupText";export{d as C,N as a};
