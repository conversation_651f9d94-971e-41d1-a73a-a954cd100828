import{a as p,_ as g,R as n,b,c as u,P as r,j as e}from"./index-BDJ8oeCE.js";import{a as f,b as l}from"./DefaultLayout-BolUaDEE.js";import"./index.esm-DSzlmaRN.js";import{C as y,a as t}from"./CRow-C1o2zw34.js";import{C as c,a as d}from"./CCardBody-iimbKiZ7.js";import{C as i}from"./CCardHeader-CFnfD6gM.js";import{C as a}from"./CFormLabel-CzXD3nfE.js";import"./cil-user-Ddrdy7PS.js";var s=p.forwardRef(function(o,h){var j=o.className,m=o.label,x=g(o,["className","label"]);return n.createElement(n.Fragment,null,m&&n.createElement(a,{htmlFor:x.id},m),n.createElement("input",b({type:"range",className:u("form-range",j)},x,{ref:h})))});s.propTypes={className:r.string,label:r.oneOfType([r.node,r.string])};s.displayName="CFormRange";const T=()=>e.jsxs(y,{children:[e.jsxs(t,{xs:12,children:[e.jsx(f,{href:"forms/range/"}),e.jsxs(c,{className:"mb-4",children:[e.jsxs(i,{children:[e.jsx("strong",{children:"React Range"})," ",e.jsx("small",{})]}),e.jsxs(d,{children:[e.jsxs("p",{className:"text-body-secondary small",children:["Create custom ",e.jsx("code",{children:'<input type="range">'})," controls with"," ",e.jsx("code",{children:"<CFormRange>"}),"."]}),e.jsxs(l,{href:"forms/range",tabContentClassName:"bg-opacity-10",children:[e.jsx(a,{htmlFor:"customRange1",children:"Example range"}),e.jsx(s,{id:"customRange1"})]})]})]})]}),e.jsx(t,{xs:12,children:e.jsxs(c,{className:"mb-4",children:[e.jsxs(i,{children:[e.jsx("strong",{children:"React Range"})," ",e.jsx("small",{children:"Disabled"})]}),e.jsxs(d,{children:[e.jsxs("p",{className:"text-body-secondary small",children:["Add the ",e.jsx("code",{children:"disabled"})," boolean attribute on an input to give it a grayed out appearance and remove pointer events."]}),e.jsxs(l,{href:"forms/range#disabled",tabContentClassName:"bg-opacity-10",children:[e.jsx(a,{htmlFor:"disabledRange",children:"Disabled range"}),e.jsx(s,{id:"disabledRange",disabled:!0})]})]})]})}),e.jsx(t,{xs:12,children:e.jsxs(c,{className:"mb-4",children:[e.jsxs(i,{children:[e.jsx("strong",{children:"React Range"})," ",e.jsx("small",{children:"Min and max"})]}),e.jsxs(d,{children:[e.jsxs("p",{className:"text-body-secondary small",children:["Range inputs have implicit values for ",e.jsx("code",{children:"min"})," and ",e.jsx("code",{children:"max"}),"—",e.jsx("code",{children:"0"})," and ",e.jsx("code",{children:"100"}),", respectively. You may specify new values for those using the ",e.jsx("code",{children:"min"})," and ",e.jsx("code",{children:"max"})," attributes."]}),e.jsxs(l,{href:"forms/range#min-and-max",tabContentClassName:"bg-opacity-10",children:[e.jsx(a,{htmlFor:"customRange2",children:"Example range"}),e.jsx(s,{min:0,max:5,defaultValue:"3",id:"customRange2"})]})]})]})}),e.jsx(t,{xs:12,children:e.jsxs(c,{className:"mb-4",children:[e.jsxs(i,{children:[e.jsx("strong",{children:"React Range"})," ",e.jsx("small",{children:"Steps"})]}),e.jsxs(d,{children:[e.jsxs("p",{className:"text-body-secondary small",children:['By default, range inputs "snap" to integer values. To change this, you can specify a ',e.jsx("code",{children:"step"})," value. In the example below, we double the number of steps by using ",e.jsx("code",{children:'step="0.5"'}),"."]}),e.jsxs(l,{href:"forms/range#steps",tabContentClassName:"bg-opacity-10",children:[e.jsx(a,{htmlFor:"customRange3",children:"Example range"}),e.jsx(s,{min:0,max:5,step:.5,defaultValue:"3",id:"customRange3"})]})]})]})})]});export{T as default};
