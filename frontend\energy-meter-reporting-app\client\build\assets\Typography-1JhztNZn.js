import{j as e}from"./index-BDJ8oeCE.js";import{D as l}from"./DefaultLayout-BolUaDEE.js";import"./index.esm-DSzlmaRN.js";import{C as s,a as i}from"./CCardBody-iimbKiZ7.js";import{C as a}from"./CCardHeader-CFnfD6gM.js";import"./cil-user-Ddrdy7PS.js";const o=()=>e.jsxs(e.Fragment,{children:[e.jsxs(s,{className:"mb-4",children:[e.jsxs(a,{children:["Headings",e.jsx(l,{href:"https://coreui.io/docs/content/typography/"})]}),e.jsxs(i,{children:[e.jsx("p",{children:"Documentation and examples for Bootstrap typography, including global settings, headings, body text, lists, and more."}),e.jsxs("table",{className:"table",children:[e.jsx("thead",{children:e.jsxs("tr",{children:[e.jsx("th",{children:"Heading"}),e.jsx("th",{children:"Example"})]})}),e.jsxs("tbody",{children:[e.jsxs("tr",{children:[e.jsx("td",{children:e.jsx("p",{children:e.jsx("code",{className:"highlighter-rouge",children:"<h1></h1>"})})}),e.jsx("td",{children:e.jsx("span",{className:"h1",children:"h1. Bootstrap heading"})})]}),e.jsxs("tr",{children:[e.jsx("td",{children:e.jsx("p",{children:e.jsx("code",{className:"highlighter-rouge",children:"<h2></h2>"})})}),e.jsx("td",{children:e.jsx("span",{className:"h2",children:"h2. Bootstrap heading"})})]}),e.jsxs("tr",{children:[e.jsx("td",{children:e.jsx("p",{children:e.jsx("code",{className:"highlighter-rouge",children:"<h3></h3>"})})}),e.jsx("td",{children:e.jsx("span",{className:"h3",children:"h3. Bootstrap heading"})})]}),e.jsxs("tr",{children:[e.jsx("td",{children:e.jsx("p",{children:e.jsx("code",{className:"highlighter-rouge",children:"<h4></h4>"})})}),e.jsx("td",{children:e.jsx("span",{className:"h4",children:"h4. Bootstrap heading"})})]}),e.jsxs("tr",{children:[e.jsx("td",{children:e.jsx("p",{children:e.jsx("code",{className:"highlighter-rouge",children:"<h5></h5>"})})}),e.jsx("td",{children:e.jsx("span",{className:"h5",children:"h5. Bootstrap heading"})})]}),e.jsxs("tr",{children:[e.jsx("td",{children:e.jsx("p",{children:e.jsx("code",{className:"highlighter-rouge",children:"<h6></h6>"})})}),e.jsx("td",{children:e.jsx("span",{className:"h6",children:"h6. Bootstrap heading"})})]})]})]})]})]}),e.jsxs(s,{className:"mb-4",children:[e.jsx(a,{children:"Headings"}),e.jsxs(i,{children:[e.jsxs("p",{children:[e.jsx("code",{className:"highlighter-rouge",children:".h1"})," through",e.jsx("code",{className:"highlighter-rouge",children:".h6"}),"classes are also available, for when you want to match the font styling of a heading but cannot use the associated HTML element."]}),e.jsxs("div",{className:"bd-example",children:[e.jsx("p",{className:"h1",children:"h1. Bootstrap heading"}),e.jsx("p",{className:"h2",children:"h2. Bootstrap heading"}),e.jsx("p",{className:"h3",children:"h3. Bootstrap heading"}),e.jsx("p",{className:"h4",children:"h4. Bootstrap heading"}),e.jsx("p",{className:"h5",children:"h5. Bootstrap heading"}),e.jsx("p",{className:"h6",children:"h6. Bootstrap heading"})]})]})]}),e.jsxs(s,{className:"mb-4",children:[e.jsx("div",{className:"card-header",children:"Display headings"}),e.jsxs("div",{className:"card-body",children:[e.jsxs("p",{children:["Traditional heading elements are designed to work best in the meat of your page content. When you need a heading to stand out, consider using a ",e.jsx("strong",{children:"display heading"}),"—a larger, slightly more opinionated heading style."]}),e.jsx("div",{className:"bd-example bd-example-type",children:e.jsx("table",{className:"table",children:e.jsxs("tbody",{children:[e.jsx("tr",{children:e.jsx("td",{children:e.jsx("span",{className:"display-1",children:"Display 1"})})}),e.jsx("tr",{children:e.jsx("td",{children:e.jsx("span",{className:"display-2",children:"Display 2"})})}),e.jsx("tr",{children:e.jsx("td",{children:e.jsx("span",{className:"display-3",children:"Display 3"})})}),e.jsx("tr",{children:e.jsx("td",{children:e.jsx("span",{className:"display-4",children:"Display 4"})})})]})})})]})]}),e.jsxs(s,{className:"mb-4",children:[e.jsx(a,{children:"Inline text elements"}),e.jsxs(i,{children:[e.jsxs("p",{children:["Traditional heading elements are designed to work best in the meat of your page content. When you need a heading to stand out, consider using a ",e.jsx("strong",{children:"display heading"}),"—a larger, slightly more opinionated heading style."]}),e.jsxs("div",{className:"bd-example",children:[e.jsxs("p",{children:["You can use the mark tag to ",e.jsx("mark",{children:"highlight"})," text."]}),e.jsx("p",{children:e.jsx("del",{children:"This line of text is meant to be treated as deleted text."})}),e.jsx("p",{children:e.jsx("s",{children:"This line of text is meant to be treated as no longer accurate."})}),e.jsx("p",{children:e.jsx("ins",{children:"This line of text is meant to be treated as an addition to the document."})}),e.jsx("p",{children:e.jsx("u",{children:"This line of text will render as underlined"})}),e.jsx("p",{children:e.jsx("small",{children:"This line of text is meant to be treated as fine print."})}),e.jsx("p",{children:e.jsx("strong",{children:"This line rendered as bold text."})}),e.jsx("p",{children:e.jsx("em",{children:"This line rendered as italicized text."})})]})]})]}),e.jsxs(s,{className:"mb-4",children:[e.jsx(a,{children:"Description list alignment"}),e.jsxs(i,{children:[e.jsxs("p",{children:["Align terms and descriptions horizontally by using our grid system’s predefined classes (or semantic mixins). For longer terms, you can optionally add a"," ",e.jsx("code",{className:"highlighter-rouge",children:".text-truncate"})," class to truncate the text with an ellipsis."]}),e.jsx("div",{className:"bd-example",children:e.jsxs("dl",{className:"row",children:[e.jsx("dt",{className:"col-sm-3",children:"Description lists"}),e.jsx("dd",{className:"col-sm-9",children:"A description list is perfect for defining terms."}),e.jsx("dt",{className:"col-sm-3",children:"Euismod"}),e.jsxs("dd",{className:"col-sm-9",children:[e.jsx("p",{children:"Vestibulum id ligula porta felis euismod semper eget lacinia odio sem nec elit."}),e.jsx("p",{children:"Donec id elit non mi porta gravida at eget metus."})]}),e.jsx("dt",{className:"col-sm-3",children:"Malesuada porta"}),e.jsx("dd",{className:"col-sm-9",children:"Etiam porta sem malesuada magna mollis euismod."}),e.jsx("dt",{className:"col-sm-3 text-truncate",children:"Truncated term is truncated"}),e.jsx("dd",{className:"col-sm-9",children:"Fusce dapibus, tellus ac cursus commodo, tortor mauris condimentum nibh, ut fermentum massa justo sit amet risus."}),e.jsx("dt",{className:"col-sm-3",children:"Nesting"}),e.jsx("dd",{className:"col-sm-9",children:e.jsxs("dl",{className:"row",children:[e.jsx("dt",{className:"col-sm-4",children:"Nested definition list"}),e.jsx("dd",{className:"col-sm-8",children:"Aenean posuere, tortor sed cursus feugiat, nunc augue blandit nunc."})]})})]})})]})]})]});export{o as default};
