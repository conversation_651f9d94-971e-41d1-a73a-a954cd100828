# API Documentation

## Base URL
`http://localhost:3000/api`

---

## Authentication

### Register User
**POST** `/auth/register`

- **Request Body**:
  ```json
  {
    "username": "string",
    "password": "string",
    "role": "Admin | Viewer"
  }
  ```
- **Response**:
  ```json
  {
    "message": "User registered successfully"
  }
  ```

### Login
**POST** `/auth/login`

- **Request Body**:
  ```json
  {
    "username": "string",
    "password": "string"
  }
  ```
- **Response**:
  ```json
  {
    "token": "string"
  }
  ```

---

## Energy Meters

### Add Energy Meter
**POST** `/energy-meters/add`

- **Request Body**:
  ```json
  {
    "tagName": "string",
    "feederName": "string"
  }
  ```
- **Response**:
  ```json
  {
    "message": "Energy meter added successfully",
    "data": { "id": 1, "tagName": "string", "feederName": "string" }
  }
  ```

### Get All Energy Meters
**GET** `/energy-meters`

- **Response**:
  ```json
  {
    "message": "Energy meters retrieved successfully",
    "data": [ { "id": 1, "tagName": "string", "feederName": "string" } ]
  }
  ```

### Get Energy Meter by ID
**GET** `/energy-meters/:id`

- **Response**:
  ```json
  {
    "message": "Energy meter retrieved successfully",
    "data": { "id": 1, "tagName": "string", "feederName": "string" }
  }
  ```

### Update Energy Meter
**PUT** `/energy-meters/:id`

- **Request Body**:
  ```json
  {
    "tagName": "string",
    "feederName": "string"
  }
  ```
- **Response**:
  ```json
  {
    "message": "Energy meter updated successfully",
    "data": { "id": 1, "tagName": "string", "feederName": "string" }
  }
  ```

### Delete Energy Meter
**DELETE** `/energy-meters/:id`

- **Response**:
  ```json
  {
    "message": "Energy meter deleted successfully"
  }
  ```

---

## Locations

### Add Location
**POST** `/locations/add`

- **Request Body**:
  ```json
  {
    "name": "string",
    "description": "string"
  }
  ```
- **Response**:
  ```json
  {
    "message": "Location added successfully",
    "data": { "id": 1, "name": "string", "description": "string" }
  }
  ```

### Get All Locations
**GET** `/locations`

- **Response**:
  ```json
  {
    "message": "Locations retrieved successfully",
    "data": [ { "id": 1, "name": "string", "description": "string" } ]
  }
  ```

### Get Location by ID
**GET** `/locations/:id`

- **Response**:
  ```json
  {
    "message": "Location retrieved successfully",
    "data": { "id": 1, "name": "string", "description": "string" }
  }
  ```

### Update Location
**PUT** `/locations/:id`

- **Request Body**:
  ```json
  {
    "name": "string",
    "description": "string"
  }
  ```
- **Response**:
  ```json
  {
    "message": "Location updated successfully",
    "data": { "id": 1, "name": "string", "description": "string" }
  }
  ```

### Delete Location
**DELETE** `/locations/:id`

- **Response**:
  ```json
  {
    "message": "Location deleted successfully"
  }
  ```

---

## Mappings

### Map Energy Meter to Location
**POST** `/mappings/map`

- **Request Body**:
  ```json
  {
    "energyMeterId": 1,
    "locationId": 1
  }
  ```
- **Response**:
  ```json
  {
    "message": "Energy meter mapped to location successfully",
    "mapping": { "id": 1, "energyMeterId": 1, "locationId": 1 }
  }
  ```

### Get All Mappings
**GET** `/mappings`

- **Response**:
  ```json
  [
    {
      "id": 1,
      "energyMeterId": 1,
      "locationId": 1
    }
  ]
  ```

### Get Mapping by ID
**GET** `/mappings/:id`

- **Response**:
  ```json
  {
    "id": 1,
    "energyMeterId": 1,
    "locationId": 1
  }
  ```

### Update Mapping
**PUT** `/mappings/:id`

- **Request Body**:
  ```json
  {
    "energyMeterId": 1,
    "locationId": 1
  }
  ```
- **Response**:
  ```json
  {
    "message": "Mapping updated successfully",
    "mapping": { "id": 1, "energyMeterId": 1, "locationId": 1 }
  }
  ```

### Delete Mapping
**DELETE** `/mappings/:id`

- **Response**:
  ```json
  {
    "message": "Mapping deleted successfully"
  }
  ```

---

## Reports

### Generate Report
**GET** `/reports/generate`

- **Response**:
  ```json
  {
    "message": "Report generated successfully",
    "data": { /* report data */ }
  }
  ```

---

Save this content as `API_Documentation.md` in your project directory.# API Documentation

## Base URL
`http://localhost:3000/api`

---

## Authentication

### Register User
**POST** `/auth/register`

- **Request Body**:
  ```json
  {
    "username": "string",
    "password": "string",
    "role": "Admin | Viewer"
  }
  ```
- **Response**:
  ```json
  {
    "message": "User registered successfully"
  }
  ```

### Login
**POST** `/auth/login`

- **Request Body**:
  ```json
  {
    "username": "string",
    "password": "string"
  }
  ```
- **Response**:
  ```json
  {
    "token": "string"
  }
  ```

---

## Energy Meters

### Add Energy Meter
**POST** `/energy-meters/add`

- **Request Body**:
  ```json
  {
    "tagName": "string",
    "feederName": "string"
  }
  ```
- **Response**:
  ```json
  {
    "message": "Energy meter added successfully",
    "data": { "id": 1, "tagName": "string", "feederName": "string" }
  }
  ```

### Get All Energy Meters
**GET** `/energy-meters`

- **Response**:
  ```json
  {
    "message": "Energy meters retrieved successfully",
    "data": [ { "id": 1, "tagName": "string", "feederName": "string" } ]
  }
  ```

### Get Energy Meter by ID
**GET** `/energy-meters/:id`

- **Response**:
  ```json
  {
    "message": "Energy meter retrieved successfully",
    "data": { "id": 1, "tagName": "string", "feederName": "string" }
  }
  ```

### Update Energy Meter
**PUT** `/energy-meters/:id`

- **Request Body**:
  ```json
  {
    "tagName": "string",
    "feederName": "string"
  }
  ```
- **Response**:
  ```json
  {
    "message": "Energy meter updated successfully",
    "data": { "id": 1, "tagName": "string", "feederName": "string" }
  }
  ```

### Delete Energy Meter
**DELETE** `/energy-meters/:id`

- **Response**:
  ```json
  {
    "message": "Energy meter deleted successfully"
  }
  ```

---

## Locations

### Add Location
**POST** `/locations/add`

- **Request Body**:
  ```json
  {
    "name": "string",
    "description": "string"
  }
  ```
- **Response**:
  ```json
  {
    "message": "Location added successfully",
    "data": { "id": 1, "name": "string", "description": "string" }
  }
  ```

### Get All Locations
**GET** `/locations`

- **Response**:
  ```json
  {
    "message": "Locations retrieved successfully",
    "data": [ { "id": 1, "name": "string", "description": "string" } ]
  }
  ```

### Get Location by ID
**GET** `/locations/:id`

- **Response**:
  ```json
  {
    "message": "Location retrieved successfully",
    "data": { "id": 1, "name": "string", "description": "string" }
  }
  ```

### Update Location
**PUT** `/locations/:id`

- **Request Body**:
  ```json
  {
    "name": "string",
    "description": "string"
  }
  ```
- **Response**:
  ```json
  {
    "message": "Location updated successfully",
    "data": { "id": 1, "name": "string", "description": "string" }
  }
  ```

### Delete Location
**DELETE** `/locations/:id`

- **Response**:
  ```json
  {
    "message": "Location deleted successfully"
  }
  ```

---

## Mappings

### Map Energy Meter to Location
**POST** `/mappings/map`

- **Request Body**:
  ```json
  {
    "energyMeterId": 1,
    "locationId": 1
  }
  ```
- **Response**:
  ```json
  {
    "message": "Energy meter mapped to location successfully",
    "mapping": { "id": 1, "energyMeterId": 1, "locationId": 1 }
  }
  ```

### Get All Mappings
**GET** `/mappings`

- **Response**:
  ```json
  [
    {
      "id": 1,
      "energyMeterId": 1,
      "locationId": 1
    }
  ]
  ```

### Get Mapping by ID
**GET** `/mappings/:id`

- **Response**:
  ```json
  {
    "id": 1,
    "energyMeterId": 1,
    "locationId": 1
  }
  ```

### Update Mapping
**PUT** `/mappings/:id`

- **Request Body**:
  ```json
  {
    "energyMeterId": 1,
    "locationId": 1
  }
  ```
- **Response**:
  ```json
  {
    "message": "Mapping updated successfully",
    "mapping": { "id": 1, "energyMeterId": 1, "locationId": 1 }
  }
  ```

### Delete Mapping
**DELETE** `/mappings/:id`

- **Response**:
  ```json
  {
    "message": "Mapping deleted successfully"
  }
  ```

---

## Reports

### Generate Report
**GET** `/reports/generate`

- **Response**:
  ```json
  {
    "message": "Report generated successfully",
    "data": { /* report data */ }
  }
  ```

---
