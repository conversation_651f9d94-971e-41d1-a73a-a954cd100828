const Mapping = require('../models/mappingModel');
const EnergyMeter = require('../models/energyMeterModel');
const Location = require('../models/locationModel');

// Function to map an energy meter to a location
exports.mapEnergyMeter = async (req, res) => {
    try {
        const { energyMeterId, locationId } = req.body;

        // Check if energy meter and location exist
        const energyMeter = await EnergyMeter.findByPk(energyMeterId);
        const location = await Location.findByPk(locationId);

        if (!energyMeter || !location) {
            return res.status(404).json({ message: 'Energy meter or location not found' });
        }

        // Create mapping
        const mapping = await Mapping.create({ energyMeterId, locationId });

        res.status(201).json({ message: 'Energy meter mapped to location successfully', mapping });
    } catch (error) {
        res.status(500).json({ message: 'Error mapping energy meter to location', error });
    }
};

// Function to get all mappings
exports.getAllMappings = async (req, res) => {
    try {
        const mappings = await Mapping.findAll();
        res.status(200).json({ message: 'Mapping fetched successfully', data: mappings });
    } catch (error) {
        res.status(500).json({ message: 'Error retrieving mappings', error });
    }
};

// Function to get a specific mapping by ID
exports.getMappingById = async (req, res) => {
    try {
        const { id } = req.params;
        const mapping = await Mapping.findByPk(id);

        if (!mapping) {
            return res.status(404).json({ message: 'Mapping not found' });
        }
        res.status(200).json({ message: 'Mapping fetched successfully', data: mapping });

    } catch (error) {
        res.status(500).json({ message: 'Error retrieving mapping', error });
    }
};

// Function to update a mapping
exports.updateMapping = async (req, res) => {
    try {
        console.log('Mapping object:', Mapping); // Debug log to inspect the Mapping object

        const { id } = req.params;
        const { energyMeterId, locationId } = req.body;

        // Validate input
        const energyMeter = await EnergyMeter.findByPk(energyMeterId);
        const location = await Location.findByPk(locationId);

        if (!energyMeter || !location) {
            return res.status(404).json({ message: 'Energy meter or location not found' });
        }

        // Update the mapping and get the count of affected rows
        const [updatedRowsCount] = await Mapping.update(
            { energyMeterId, locationId },
            { where: { id } }
        );

        if (updatedRowsCount === 0) {
            return res.status(404).json({ message: 'Mapping not found' });
        }

        // Fetch the updated mapping manually
        const updatedMapping = await Mapping.findByPk(id);

        res.status(200).json({ message: 'Mapping updated successfully', mapping: updatedMapping });
    } catch (error) {
        console.error('Error updating mapping:', error); // Log the error for debugging
        res.status(500).json({ message: 'Error updating mapping', error });
    }
};

// Function to delete a mapping
exports.deleteMapping = async (req, res) => {
    try {
        const { id } = req.params;
        const result = await Mapping.destroy({ where: { id } });

        if (!result) {
            return res.status(404).json({ message: 'Mapping not found' });
        }

        res.status(200).json({ message: 'Mapping deleted successfully' });
    } catch (error) {
        res.status(500).json({ message: 'Error deleting mapping', error });
    }
};

// Function to get mappings by location
exports.getMappingsByLocation = async (req, res) => {
    try {
        const { locationId } = req.params;
        const mappings = await Mapping.findAll({ where: { locationId } });
        res.status(200).json({ message: 'Mapping fetched successfully', data: mappings });

    } catch (error) {
        res.status(500).json({ message: 'Error retrieving mappings by location', error });
    }
};

// Function to get mappings by energy meter
exports.getMappingsByEnergyMeter = async (req, res) => {
    try {
        const { energyMeterId } = req.params;
        const mappings = await Mapping.findAll({ where: { energyMeterId } });
        res.status(200).json({ message: 'Mapping fetched successfully', data: mappings });

    } catch (error) {
        res.status(500).json({ message: 'Error retrieving mappings by energy meter', error });
    }
};
exports.getEnergyMeterIdsByLocationId = async (id) => {
    try {
        const meters = await Mapping.findAll({ where: { locationId: id } });
        return meters.map(meter => meter.energyMeterId);
    } catch (error) {
        throw new Error(`Error retrieving energy Ids: ${error.message}`);
    }
};