import{a as i,_ as P,R as V,b as D,c as N,P as v,j as e}from"./index-BDJ8oeCE.js";import{a as F,b as t}from"./DefaultLayout-BolUaDEE.js";import{a as l,C as w}from"./index.esm-DSzlmaRN.js";import{C as L,a as u}from"./CRow-C1o2zw34.js";import{C as m,a as h}from"./CCardBody-iimbKiZ7.js";import{C as x}from"./CCardHeader-CFnfD6gM.js";import{C as o,a as n,b as a,c as j}from"./CModalHeader-DX4AicsN.js";import{C as A}from"./CPopover-DsLOhH2M.js";import{C as S}from"./CTooltip-CKesHgCe.js";import"./cil-user-Ddrdy7PS.js";import"./getRTLPlacement-BTVXPs3l.js";import"./getTransitionDurationFromElement-Cpu4p4hx.js";var c=i.forwardRef(function(r,s){var p=r.children,d=r.as,b=d===void 0?"h5":d,g=r.className,f=P(r,["children","as","className"]);return V.createElement(b,D({className:N("modal-title",g)},f,{ref:s}),p)});c.propTypes={as:v.elementType,children:v.node,className:v.string};c.displayName="CModalTitle";const T=()=>{const[r,s]=i.useState(!1);return e.jsxs(e.Fragment,{children:[e.jsx(l,{color:"primary",onClick:()=>s(!r),children:"Launch demo modal"}),e.jsxs(o,{visible:r,onClose:()=>s(!1),children:[e.jsx(n,{children:e.jsx(c,{children:"Modal title"})}),e.jsx(a,{children:"Woohoo, you're reading this text in a modal!"}),e.jsxs(j,{children:[e.jsx(l,{color:"secondary",onClick:()=>s(!1),children:"Close"}),e.jsx(l,{color:"primary",children:"Save changes"})]})]})]})},R=()=>{const[r,s]=i.useState(!1);return e.jsxs(e.Fragment,{children:[e.jsx(l,{color:"primary",onClick:()=>s(!r),children:"Launch static backdrop modal"}),e.jsxs(o,{backdrop:"static",visible:r,onClose:()=>s(!1),children:[e.jsx(n,{children:e.jsx(c,{children:"Modal title"})}),e.jsx(a,{children:"I will not close if you click outside me. Don'teven try to press escape key."}),e.jsxs(j,{children:[e.jsx(l,{color:"secondary",onClick:()=>s(!1),children:"Close"}),e.jsx(l,{color:"primary",children:"Save changes"})]})]})]})},z=()=>{const[r,s]=i.useState(!1);return e.jsxs(e.Fragment,{children:[e.jsx(l,{color:"primary",onClick:()=>s(!r),children:"Launch demo modal"}),e.jsxs(o,{visible:r,onClose:()=>s(!1),children:[e.jsx(n,{children:e.jsx(c,{children:"Modal title"})}),e.jsxs(a,{children:[e.jsx("p",{children:"Cras mattis consectetur purus sit amet fermentum. Cras justo odio, dapibus ac facilisis in, egestas eget quam. Morbi leo risus, porta ac consectetur ac, vestibulum at eros."}),e.jsx("p",{children:"Praesent commodo cursus magna, vel scelerisque nisl consectetur et. Vivamus sagittis lacus vel augue laoreet rutrum faucibus dolor auctor."}),e.jsx("p",{children:"Aenean lacinia bibendum nulla sed consectetur. Praesent commodo cursus magna, vel scelerisque nisl consectetur et. Donec sed odio dui. Donec ullamcorper nulla non metus auctor fringilla."}),e.jsx("p",{children:"Cras mattis consectetur purus sit amet fermentum. Cras justo odio, dapibus ac facilisis in, egestas eget quam. Morbi leo risus, porta ac consectetur ac, vestibulum at eros."}),e.jsx("p",{children:"Praesent commodo cursus magna, vel scelerisque nisl consectetur et. Vivamus sagittis lacus vel augue laoreet rutrum faucibus dolor auctor."}),e.jsx("p",{children:"Aenean lacinia bibendum nulla sed consectetur. Praesent commodo cursus magna, vel scelerisque nisl consectetur et. Donec sed odio dui. Donec ullamcorper nulla non metus auctor fringilla."}),e.jsx("p",{children:"Cras mattis consectetur purus sit amet fermentum. Cras justo odio, dapibus ac facilisis in, egestas eget quam. Morbi leo risus, porta ac consectetur ac, vestibulum at eros."}),e.jsx("p",{children:"Praesent commodo cursus magna, vel scelerisque nisl consectetur et. Vivamus sagittis lacus vel augue laoreet rutrum faucibus dolor auctor."}),e.jsx("p",{children:"Aenean lacinia bibendum nulla sed consectetur. Praesent commodo cursus magna, vel scelerisque nisl consectetur et. Donec sed odio dui. Donec ullamcorper nulla non metus auctor fringilla."}),e.jsx("p",{children:"Cras mattis consectetur purus sit amet fermentum. Cras justo odio, dapibus ac facilisis in, egestas eget quam. Morbi leo risus, porta ac consectetur ac, vestibulum at eros."}),e.jsx("p",{children:"Praesent commodo cursus magna, vel scelerisque nisl consectetur et. Vivamus sagittis lacus vel augue laoreet rutrum faucibus dolor auctor."}),e.jsx("p",{children:"Aenean lacinia bibendum nulla sed consectetur. Praesent commodo cursus magna, vel scelerisque nisl consectetur et. Donec sed odio dui. Donec ullamcorper nulla non metus auctor fringilla."}),e.jsx("p",{children:"Cras mattis consectetur purus sit amet fermentum. Cras justo odio, dapibus ac facilisis in, egestas eget quam. Morbi leo risus, porta ac consectetur ac, vestibulum at eros."}),e.jsx("p",{children:"Praesent commodo cursus magna, vel scelerisque nisl consectetur et. Vivamus sagittis lacus vel augue laoreet rutrum faucibus dolor auctor."}),e.jsx("p",{children:"Aenean lacinia bibendum nulla sed consectetur. Praesent commodo cursus magna, vel scelerisque nisl consectetur et. Donec sed odio dui. Donec ullamcorper nulla non metus auctor fringilla."}),e.jsx("p",{children:"Cras mattis consectetur purus sit amet fermentum. Cras justo odio, dapibus ac facilisis in, egestas eget quam. Morbi leo risus, porta ac consectetur ac, vestibulum at eros."}),e.jsx("p",{children:"Praesent commodo cursus magna, vel scelerisque nisl consectetur et. Vivamus sagittis lacus vel augue laoreet rutrum faucibus dolor auctor."}),e.jsx("p",{children:"Aenean lacinia bibendum nulla sed consectetur. Praesent commodo cursus magna, vel scelerisque nisl consectetur et. Donec sed odio dui. Donec ullamcorper nulla non metus auctor fringilla."})]}),e.jsxs(j,{children:[e.jsx(l,{color:"secondary",onClick:()=>s(!1),children:"Close"}),e.jsx(l,{color:"primary",children:"Save changes"})]})]})]})},B=()=>{const[r,s]=i.useState(!1);return e.jsxs(e.Fragment,{children:[e.jsx(l,{color:"primary",onClick:()=>s(!r),children:"Launch demo modal"}),e.jsxs(o,{scrollable:!0,visible:r,onClose:()=>s(!1),children:[e.jsx(n,{children:e.jsx(c,{children:"Modal title"})}),e.jsxs(a,{children:[e.jsx("p",{children:"Cras mattis consectetur purus sit amet fermentum. Cras justo odio, dapibus ac facilisis in, egestas eget quam. Morbi leo risus, porta ac consectetur ac, vestibulum at eros."}),e.jsx("p",{children:"Praesent commodo cursus magna, vel scelerisque nisl consectetur et. Vivamus sagittis lacus vel augue laoreet rutrum faucibus dolor auctor."}),e.jsx("p",{children:"Aenean lacinia bibendum nulla sed consectetur. Praesent commodo cursus magna, vel scelerisque nisl consectetur et. Donec sed odio dui. Donec ullamcorper nulla non metus auctor fringilla."}),e.jsx("p",{children:"Cras mattis consectetur purus sit amet fermentum. Cras justo odio, dapibus ac facilisis in, egestas eget quam. Morbi leo risus, porta ac consectetur ac, vestibulum at eros."}),e.jsx("p",{children:"Praesent commodo cursus magna, vel scelerisque nisl consectetur et. Vivamus sagittis lacus vel augue laoreet rutrum faucibus dolor auctor."}),e.jsx("p",{children:"Aenean lacinia bibendum nulla sed consectetur. Praesent commodo cursus magna, vel scelerisque nisl consectetur et. Donec sed odio dui. Donec ullamcorper nulla non metus auctor fringilla."}),e.jsx("p",{children:"Cras mattis consectetur purus sit amet fermentum. Cras justo odio, dapibus ac facilisis in, egestas eget quam. Morbi leo risus, porta ac consectetur ac, vestibulum at eros."}),e.jsx("p",{children:"Praesent commodo cursus magna, vel scelerisque nisl consectetur et. Vivamus sagittis lacus vel augue laoreet rutrum faucibus dolor auctor."}),e.jsx("p",{children:"Aenean lacinia bibendum nulla sed consectetur. Praesent commodo cursus magna, vel scelerisque nisl consectetur et. Donec sed odio dui. Donec ullamcorper nulla non metus auctor fringilla."}),e.jsx("p",{children:"Cras mattis consectetur purus sit amet fermentum. Cras justo odio, dapibus ac facilisis in, egestas eget quam. Morbi leo risus, porta ac consectetur ac, vestibulum at eros."}),e.jsx("p",{children:"Praesent commodo cursus magna, vel scelerisque nisl consectetur et. Vivamus sagittis lacus vel augue laoreet rutrum faucibus dolor auctor."}),e.jsx("p",{children:"Aenean lacinia bibendum nulla sed consectetur. Praesent commodo cursus magna, vel scelerisque nisl consectetur et. Donec sed odio dui. Donec ullamcorper nulla non metus auctor fringilla."}),e.jsx("p",{children:"Cras mattis consectetur purus sit amet fermentum. Cras justo odio, dapibus ac facilisis in, egestas eget quam. Morbi leo risus, porta ac consectetur ac, vestibulum at eros."}),e.jsx("p",{children:"Praesent commodo cursus magna, vel scelerisque nisl consectetur et. Vivamus sagittis lacus vel augue laoreet rutrum faucibus dolor auctor."}),e.jsx("p",{children:"Aenean lacinia bibendum nulla sed consectetur. Praesent commodo cursus magna, vel scelerisque nisl consectetur et. Donec sed odio dui. Donec ullamcorper nulla non metus auctor fringilla."}),e.jsx("p",{children:"Cras mattis consectetur purus sit amet fermentum. Cras justo odio, dapibus ac facilisis in, egestas eget quam. Morbi leo risus, porta ac consectetur ac, vestibulum at eros."}),e.jsx("p",{children:"Praesent commodo cursus magna, vel scelerisque nisl consectetur et. Vivamus sagittis lacus vel augue laoreet rutrum faucibus dolor auctor."}),e.jsx("p",{children:"Aenean lacinia bibendum nulla sed consectetur. Praesent commodo cursus magna, vel scelerisque nisl consectetur et. Donec sed odio dui. Donec ullamcorper nulla non metus auctor fringilla."})]}),e.jsxs(j,{children:[e.jsx(l,{color:"secondary",onClick:()=>s(!1),children:"Close"}),e.jsx(l,{color:"primary",children:"Save changes"})]})]})]})},X=()=>{const[r,s]=i.useState(!1);return e.jsxs(e.Fragment,{children:[e.jsx(l,{color:"primary",onClick:()=>s(!r),children:"Vertically centered modal"}),e.jsxs(o,{alignment:"center",visible:r,onClose:()=>s(!1),children:[e.jsx(n,{children:e.jsx(c,{children:"Modal title"})}),e.jsx(a,{children:"Cras mattis consectetur purus sit amet fermentum. Cras justo odio, dapibus ac facilisis in, egestas eget quam. Morbi leo risus, porta ac consectetur ac, vestibulum at eros."}),e.jsxs(j,{children:[e.jsx(l,{color:"secondary",onClick:()=>s(!1),children:"Close"}),e.jsx(l,{color:"primary",children:"Save changes"})]})]})]})},E=()=>{const[r,s]=i.useState(!1);return e.jsxs(e.Fragment,{children:[e.jsx(l,{color:"primary",onClick:()=>s(!r),children:"Vertically centered scrollable modal"}),e.jsxs(o,{alignment:"center",scrollable:!0,visible:r,onClose:()=>s(!1),children:[e.jsx(n,{children:e.jsx(c,{children:"Modal title"})}),e.jsxs(a,{children:[e.jsx("p",{children:"Cras mattis consectetur purus sit amet fermentum. Cras justo odio, dapibus ac facilisis in, egestas eget quam. Morbi leo risus, porta ac consectetur ac, vestibulum at eros."}),e.jsx("p",{children:"Praesent commodo cursus magna, vel scelerisque nisl consectetur et. Vivamus sagittis lacus vel augue laoreet rutrum faucibus dolor auctor."}),e.jsx("p",{children:"Aenean lacinia bibendum nulla sed consectetur. Praesent commodo cursus magna, vel scelerisque nisl consectetur et. Donec sed odio dui. Donec ullamcorper nulla non metus auctor fringilla."}),e.jsx("p",{children:"Cras mattis consectetur purus sit amet fermentum. Cras justo odio, dapibus ac facilisis in, egestas eget quam. Morbi leo risus, porta ac consectetur ac, vestibulum at eros."}),e.jsx("p",{children:"Praesent commodo cursus magna, vel scelerisque nisl consectetur et. Vivamus sagittis lacus vel augue laoreet rutrum faucibus dolor auctor."})]}),e.jsxs(j,{children:[e.jsx(l,{color:"secondary",onClick:()=>s(!1),children:"Close"}),e.jsx(l,{color:"primary",children:"Save changes"})]})]})]})},I=()=>{const[r,s]=i.useState(!1);return e.jsxs(e.Fragment,{children:[e.jsx(l,{color:"primary",onClick:()=>s(!r),children:"Launch demo modal"}),e.jsxs(o,{alignment:"center",visible:r,onClose:()=>s(!1),children:[e.jsx(n,{children:e.jsx(c,{children:"Modal title"})}),e.jsxs(a,{children:[e.jsx("h5",{children:"Popover in a modal"}),e.jsxs("p",{children:["This",e.jsx(A,{title:"Popover title",content:"Popover body content is set in this property.",children:e.jsx(l,{color:"primary",children:"button"})})," ","triggers a popover on click."]}),e.jsx("hr",{}),e.jsx("h5",{children:"Tooltips in a modal"}),e.jsxs("p",{children:[e.jsx(S,{content:"Tooltip",children:e.jsx(w,{children:"This link"})})," ","and",e.jsx(S,{content:"Tooltip",children:e.jsx(w,{children:"that link"})})," ","have tooltips on hover."]})]}),e.jsxs(j,{children:[e.jsx(l,{color:"secondary",onClick:()=>s(!1),children:"Close"}),e.jsx(l,{color:"primary",children:"Save changes"})]})]})]})},W=()=>{const[r,s]=i.useState(!1),[p,d]=i.useState(!1),[b,g]=i.useState(!1);return e.jsxs(e.Fragment,{children:[e.jsx(l,{color:"primary",onClick:()=>s(!r),children:"Extra large modal"}),e.jsx(l,{color:"primary",onClick:()=>d(!p),children:"Large modal"}),e.jsx(l,{color:"primary",onClick:()=>g(!b),children:"Small large modal"}),e.jsxs(o,{size:"xl",visible:r,onClose:()=>s(!1),children:[e.jsx(n,{children:e.jsx(c,{children:"Extra large modal"})}),e.jsx(a,{children:"..."})]}),e.jsxs(o,{size:"lg",visible:p,onClose:()=>d(!1),children:[e.jsx(n,{children:e.jsx(c,{children:"Large modal"})}),e.jsx(a,{children:"..."})]}),e.jsxs(o,{size:"sm",visible:b,onClose:()=>g(!1),children:[e.jsx(n,{children:e.jsx(c,{children:"Small modal"})}),e.jsx(a,{children:"..."})]})]})},_=()=>{const[r,s]=i.useState(!1),[p,d]=i.useState(!1),[b,g]=i.useState(!1),[f,C]=i.useState(!1),[y,k]=i.useState(!1),[M,q]=i.useState(!1);return e.jsxs(e.Fragment,{children:[e.jsx(l,{color:"primary",onClick:()=>s(!r),children:"Full screen"}),e.jsx(l,{color:"primary",onClick:()=>d(!p),children:"Full screen below sm"}),e.jsx(l,{color:"primary",onClick:()=>g(!b),children:"Full screen below md"}),e.jsx(l,{color:"primary",onClick:()=>C(!f),children:"Full screen below lg"}),e.jsx(l,{color:"primary",onClick:()=>k(!y),children:"Full screen below xl"}),e.jsx(l,{color:"primary",onClick:()=>q(!M),children:"Full screen below xxl"}),e.jsxs(o,{fullscreen:!0,visible:r,onClose:()=>s(!1),children:[e.jsx(n,{children:e.jsx(c,{children:"Full screen"})}),e.jsx(a,{children:"..."})]}),e.jsxs(o,{fullscreen:"sm",visible:p,onClose:()=>d(!1),children:[e.jsx(n,{children:e.jsx(c,{children:"Full screen below sm"})}),e.jsx(a,{children:"..."})]}),e.jsxs(o,{fullscreen:"md",visible:b,onClose:()=>g(!1),children:[e.jsx(n,{children:e.jsx(c,{children:"Full screen below md"})}),e.jsx(a,{children:"..."})]}),e.jsxs(o,{fullscreen:"lg",visible:f,onClose:()=>C(!1),children:[e.jsx(n,{children:e.jsx(c,{children:"Full screen below lg"})}),e.jsx(a,{children:"..."})]}),e.jsxs(o,{fullscreen:"xl",visible:y,onClose:()=>k(!1),children:[e.jsx(n,{children:e.jsx(c,{children:"Full screen below xl"})}),e.jsx(a,{children:"..."})]}),e.jsxs(o,{fullscreen:"xxl",visible:M,onClose:()=>q(!1),children:[e.jsx(n,{children:e.jsx(c,{children:"Full screen below xxl"})}),e.jsx(a,{children:"..."})]})]})},le=()=>e.jsxs(L,{children:[e.jsxs(u,{xs:12,children:[e.jsx(F,{href:"components/modal/"}),e.jsxs(m,{className:"mb-4",children:[e.jsx(x,{children:e.jsx("strong",{children:"React Modal"})}),e.jsxs(h,{children:[e.jsxs("p",{className:"text-body-secondary small",children:["Below is a static modal example (meaning its ",e.jsx("code",{children:"position"})," and"," ",e.jsx("code",{children:"display"})," have been overridden). Included are the modal header, modal body (required for ",e.jsx("code",{children:"padding"}),"), and modal footer (optional). We ask that you include modal headers with dismiss actions whenever possible, or provide another explicit dismiss action."]}),e.jsx(t,{href:"components/modal",children:e.jsxs(o,{className:"show d-block position-static",backdrop:!1,keyboard:!1,portal:!1,visible:!0,children:[e.jsx(n,{children:e.jsx(c,{children:"Modal title"})}),e.jsx(a,{children:"Modal body text goes here."}),e.jsxs(j,{children:[e.jsx(l,{color:"secondary",children:"Close"}),e.jsx(l,{color:"primary",children:"Save changes"})]})]})})]})]})]}),e.jsx(u,{xs:12,children:e.jsxs(m,{className:"mb-4",children:[e.jsxs(x,{children:[e.jsx("strong",{children:"React Modal"})," ",e.jsx("small",{children:"Live demo"})]}),e.jsxs(h,{children:[e.jsx("p",{className:"text-body-secondary small",children:"Toggle a working modal demo by clicking the button below. It will slide down and fade in from the top of the page."}),e.jsx(t,{href:"components/modal#live-demo",children:T()})]})]})}),e.jsx(u,{xs:12,children:e.jsxs(m,{className:"mb-4",children:[e.jsxs(x,{children:[e.jsx("strong",{children:"React Modal"})," ",e.jsx("small",{children:"Static backdrop"})]}),e.jsxs(h,{children:[e.jsxs("p",{className:"text-body-secondary small",children:["If you don’t provide an ",e.jsx("code",{children:"onDimsiss"})," handler to the Modal component, your modal will behave as though the backdrop is static, meaning it will not close when clicking outside it. Click the button below to try it."]}),e.jsx(t,{href:"components/modal#static-backdrop",children:R()})]})]})}),e.jsx(u,{xs:12,children:e.jsxs(m,{className:"mb-4",children:[e.jsxs(x,{children:[e.jsx("strong",{children:"React Modal"})," ",e.jsx("small",{children:"Scrolling long content"})]}),e.jsxs(h,{children:[e.jsxs("p",{className:"text-body-secondary small",children:["If you don’t provide an ",e.jsx("code",{children:"onDimsiss"})," handler to the Modal component, your modal will behave as though the backdrop is static, meaning it will not close when clicking outside it. Click the button below to try it."]}),e.jsx(t,{href:"components/modal#scrolling-long-content",children:z()}),e.jsxs("p",{className:"text-body-secondary small",children:["You can also create a scrollable modal that allows scroll the modal body by adding"," ",e.jsx("code",{children:"scrollable"})," prop."]}),e.jsx(t,{href:"components/modal#scrolling-long-content",children:B()})]})]})}),e.jsx(u,{xs:12,children:e.jsxs(m,{className:"mb-4",children:[e.jsxs(x,{children:[e.jsx("strong",{children:"React Modal"})," ",e.jsx("small",{children:"Vertically centered"})]}),e.jsxs(h,{children:[e.jsxs("p",{className:"text-body-secondary small",children:["Add ",e.jsx("code",{children:'alignment="center"'})," to ",e.jsx("code",{children:"<CModal>"})," to vertically center the modal."]}),e.jsx(t,{href:"components/modal#vertically-centered",children:X()}),e.jsx(t,{href:"components/modal#vertically-centered",children:E()})]})]})}),e.jsx(u,{xs:12,children:e.jsxs(m,{className:"mb-4",children:[e.jsxs(x,{children:[e.jsx("strong",{children:"React Modal"})," ",e.jsx("small",{children:"Tooltips and popovers"})]}),e.jsxs(h,{children:[e.jsxs("p",{className:"text-body-secondary small",children:[e.jsx("code",{children:"<CTooltips>"})," and ",e.jsx("code",{children:"<CPopovers>"})," can be placed within modals as needed. When modals are closed, any tooltips and popovers within are also automatically dismissed."]}),e.jsx(t,{href:"components/modal#tooltips-and-popovers",children:I()})]})]})}),e.jsx(u,{xs:12,children:e.jsxs(m,{className:"mb-4",children:[e.jsxs(x,{children:[e.jsx("strong",{children:"React Modal"})," ",e.jsx("small",{children:"Optional sizes"})]}),e.jsxs(h,{children:[e.jsxs("p",{className:"text-body-secondary small",children:["Modals have three optional sizes, available via modifier classes to be placed on a"," ",e.jsx("code",{children:"<CModal>"}),". These sizes kick in at certain breakpoints to avoid horizontal scrollbars on narrower viewports."]}),e.jsxs("table",{className:"table",children:[e.jsx("thead",{children:e.jsxs("tr",{children:[e.jsx("th",{children:"Size"}),e.jsx("th",{children:"Property size"}),e.jsx("th",{children:"Modal max-width"})]})}),e.jsxs("tbody",{children:[e.jsxs("tr",{children:[e.jsx("td",{children:"Small"}),e.jsx("td",{children:e.jsx("code",{children:"'sm'"})}),e.jsx("td",{children:e.jsx("code",{children:"300px"})})]}),e.jsxs("tr",{children:[e.jsx("td",{children:"Default"}),e.jsx("td",{className:"text-body-secondary",children:"None"}),e.jsx("td",{children:e.jsx("code",{children:"500px"})})]}),e.jsxs("tr",{children:[e.jsx("td",{children:"Large"}),e.jsx("td",{children:e.jsx("code",{children:"'lg'"})}),e.jsx("td",{children:e.jsx("code",{children:"800px"})})]}),e.jsxs("tr",{children:[e.jsx("td",{children:"Extra large"}),e.jsx("td",{children:e.jsx("code",{children:"'xl'"})}),e.jsx("td",{children:e.jsx("code",{children:"1140px"})})]})]})]}),e.jsx(t,{href:"components/modal#optional-sizes",children:W()})]})]})}),e.jsx(u,{xs:12,children:e.jsxs(m,{className:"mb-4",children:[e.jsxs(x,{children:[e.jsx("strong",{children:"React Modal"})," ",e.jsx("small",{children:"Fullscreen Modal"})]}),e.jsxs(h,{children:[e.jsxs("p",{className:"text-body-secondary small",children:["Another override is the option to pop up a modal that covers the user viewport, available via property ",e.jsx("code",{children:"fullscrean"}),"."]}),e.jsxs("table",{className:"table",children:[e.jsx("thead",{children:e.jsxs("tr",{children:[e.jsx("th",{children:"Property fullscrean"}),e.jsx("th",{children:"Availability"})]})}),e.jsxs("tbody",{children:[e.jsxs("tr",{children:[e.jsx("td",{children:e.jsx("code",{children:"true"})}),e.jsx("td",{children:"Always"})]}),e.jsxs("tr",{children:[e.jsx("td",{children:e.jsx("code",{children:"'sm'"})}),e.jsxs("td",{children:["Below ",e.jsx("code",{children:"576px"})]})]}),e.jsxs("tr",{children:[e.jsx("td",{children:e.jsx("code",{children:"'md'"})}),e.jsxs("td",{children:["Below ",e.jsx("code",{children:"768px"})]})]}),e.jsxs("tr",{children:[e.jsx("td",{children:e.jsx("code",{children:"'lg'"})}),e.jsxs("td",{children:["Below ",e.jsx("code",{children:"992px"})]})]}),e.jsxs("tr",{children:[e.jsx("td",{children:e.jsx("code",{children:"'xl'"})}),e.jsxs("td",{children:["Below ",e.jsx("code",{children:"1200px"})]})]}),e.jsxs("tr",{children:[e.jsx("td",{children:e.jsx("code",{children:"'xxl'"})}),e.jsxs("td",{children:["Below ",e.jsx("code",{children:"1400px"})]})]})]})]}),e.jsx(t,{href:"components/modal#fullscreen-modal",children:_()})]})]})})]});export{le as default};
