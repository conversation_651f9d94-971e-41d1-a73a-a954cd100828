const jwt = require('jsonwebtoken');
const User = require('../models/userModel');
const config = require('../config');


// Middleware for checking authentication
const authenticate = (req, res, next) => {
    const authHeader = req.headers.authorization;
    
    const token =req.cookies.token || authHeader.split(' ')[1];

    if (!token) {
        return res.status(401).json({ message: 'No token provided' });
    }

    try {
        const decoded = jwt.verify(token, config.jwt.secret);
        req.user = decoded; // Attach the decoded user object to the request
        next();
    } catch (error) {
        res.status(401).json({ message: 'Invalid token', error });
    }
};

// Middleware for checking user roles
const authorize = (roles) => {
    return (req, res, next) => {
        User.findById(req.userId, (err, user) => {
            if (err || !user) {
                return res.status(401).json({ message: 'Unauthorized' });
            }
            if (!roles.includes(user.role)) {
                return res.status(403).json({ message: 'Forbidden' });
            }
            next();
        });
    };
};

const authorizeRole = (role) => {
    return (req, res, next) => {
        if (req.user && req.user.role === role) {
            next(); // User has the required role, proceed to the next middleware
        } else {
            res.status(403).json({ message: 'Access denied. Insufficient permissions.' });
        }
    };
};

const authorizeRoles = (...allowedRoles) => {
    return (req, res, next) => {
        if (req.user && allowedRoles.includes(req.user.role)) {
            next(); // User has one of the allowed roles, proceed
        } else {
            res.status(403).json({ message: 'Access denied. Insufficient permissions.' });
        }
    };
};

// Error handling middleware
const errorHandler = (err, req, res, next) => {
    console.error(err.stack);
    res.status(500).json({ message: 'Internal Server Error' });
};

module.exports = {
    authenticate,
    authorizeRole,
    authorizeRoles,
    authorize,
    errorHandler, // Add errorHandler to the exports
};