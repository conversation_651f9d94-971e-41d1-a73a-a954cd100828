import{j as e}from"./index-BDJ8oeCE.js";import{a as t,b as d}from"./DefaultLayout-BolUaDEE.js";import"./index.esm-DSzlmaRN.js";import{C as p,a as h}from"./CRow-C1o2zw34.js";import{C as o,a as x}from"./CCardBody-iimbKiZ7.js";import{C as j}from"./CCardHeader-CFnfD6gM.js";import{C as n,a as c,b as r,c as l,d as i,e as s,f as a}from"./CTable-BG2MPlsJ.js";import"./cil-user-Ddrdy7PS.js";const H=()=>e.jsxs(p,{children:[e.jsxs(h,{xs:12,children:[e.jsx(t,{href:"components/table/"}),e.jsxs(o,{className:"mb-4",children:[e.jsxs(j,{children:[e.jsx("strong",{children:"React Table"})," ",e.jsx("small",{children:"Basic example"})]}),e.jsxs(x,{children:[e.jsxs("p",{className:"text-body-secondary small",children:["Using the most basic table CoreUI, here's how ",e.jsx("code",{children:"<CTable>"}),"-based tables look in CoreUI."]}),e.jsx(d,{href:"components/table",children:e.jsxs(n,{children:[e.jsx(c,{children:e.jsxs(r,{children:[e.jsx(l,{scope:"col",children:"#"}),e.jsx(l,{scope:"col",children:"Class"}),e.jsx(l,{scope:"col",children:"Heading"}),e.jsx(l,{scope:"col",children:"Heading"})]})}),e.jsxs(i,{children:[e.jsxs(r,{children:[e.jsx(l,{scope:"row",children:"1"}),e.jsx(s,{children:"Mark"}),e.jsx(s,{children:"Otto"}),e.jsx(s,{children:"@mdo"})]}),e.jsxs(r,{children:[e.jsx(l,{scope:"row",children:"2"}),e.jsx(s,{children:"Jacob"}),e.jsx(s,{children:"Thornton"}),e.jsx(s,{children:"@fat"})]}),e.jsxs(r,{children:[e.jsx(l,{scope:"row",children:"3"}),e.jsx(s,{colSpan:2,children:"Larry the Bird"}),e.jsx(s,{children:"@twitter"})]})]})]})})]})]})]}),e.jsx(h,{xs:12,children:e.jsxs(o,{className:"mb-4",children:[e.jsxs(j,{children:[e.jsx("strong",{children:"React Table"})," ",e.jsx("small",{children:"Variants"})]}),e.jsxs(x,{children:[e.jsx("p",{className:"text-body-secondary small",children:"Use contextual classes to color tables, table rows or individual cells."}),e.jsx(d,{href:"components/table#variants",children:e.jsxs(n,{children:[e.jsx(c,{children:e.jsxs(r,{children:[e.jsx(l,{scope:"col",children:"Class"}),e.jsx(l,{scope:"col",children:"Heading"}),e.jsx(l,{scope:"col",children:"Heading"})]})}),e.jsxs(i,{children:[e.jsxs(r,{children:[e.jsx(l,{scope:"row",children:"Default"}),e.jsx(s,{children:"Cell"}),e.jsx(s,{children:"Cell"})]}),e.jsxs(r,{color:"primary",children:[e.jsx(l,{scope:"row",children:"Primary"}),e.jsx(s,{children:"Cell"}),e.jsx(s,{children:"Cell"})]}),e.jsxs(r,{color:"secondary",children:[e.jsx(l,{scope:"row",children:"Secondary"}),e.jsx(s,{children:"Cell"}),e.jsx(s,{children:"Cell"})]}),e.jsxs(r,{color:"success",children:[e.jsx(l,{scope:"row",children:"Success"}),e.jsx(s,{children:"Cell"}),e.jsx(s,{children:"Cell"})]}),e.jsxs(r,{color:"danger",children:[e.jsx(l,{scope:"row",children:"Danger"}),e.jsx(s,{children:"Cell"}),e.jsx(s,{children:"Cell"})]}),e.jsxs(r,{color:"warning",children:[e.jsx(l,{scope:"row",children:"Warning"}),e.jsx(s,{children:"Cell"}),e.jsx(s,{children:"Cell"})]}),e.jsxs(r,{color:"info",children:[e.jsx(l,{scope:"row",children:"Info"}),e.jsx(s,{children:"Cell"}),e.jsx(s,{children:"Cell"})]}),e.jsxs(r,{color:"light",children:[e.jsx(l,{scope:"row",children:"Light"}),e.jsx(s,{children:"Cell"}),e.jsx(s,{children:"Cell"})]}),e.jsxs(r,{color:"dark",children:[e.jsx(l,{scope:"row",children:"Dark"}),e.jsx(s,{children:"Cell"}),e.jsx(s,{children:"Cell"})]})]})]})})]})]})}),e.jsx(h,{xs:12,children:e.jsxs(o,{className:"mb-4",children:[e.jsxs(j,{children:[e.jsx("strong",{children:"React Table"})," ",e.jsx("small",{children:"Striped rows"})]}),e.jsxs(x,{children:[e.jsxs("p",{className:"text-body-secondary small",children:["Use ",e.jsx("code",{children:"striped"})," property to add zebra-striping to any table row within the"," ",e.jsx("code",{children:"<CTableBody>"}),"."]}),e.jsx(d,{href:"components/table#striped-rows",children:e.jsxs(n,{striped:!0,children:[e.jsx(c,{children:e.jsxs(r,{children:[e.jsx(l,{scope:"col",children:"#"}),e.jsx(l,{scope:"col",children:"Class"}),e.jsx(l,{scope:"col",children:"Heading"}),e.jsx(l,{scope:"col",children:"Heading"})]})}),e.jsxs(i,{children:[e.jsxs(r,{children:[e.jsx(l,{scope:"row",children:"1"}),e.jsx(s,{children:"Mark"}),e.jsx(s,{children:"Otto"}),e.jsx(s,{children:"@mdo"})]}),e.jsxs(r,{children:[e.jsx(l,{scope:"row",children:"2"}),e.jsx(s,{children:"Jacob"}),e.jsx(s,{children:"Thornton"}),e.jsx(s,{children:"@fat"})]}),e.jsxs(r,{children:[e.jsx(l,{scope:"row",children:"3"}),e.jsx(s,{colSpan:2,children:"Larry the Bird"}),e.jsx(s,{children:"@twitter"})]})]})]})}),e.jsx("p",{className:"text-body-secondary small",children:"These classes can also be added to table variants:"}),e.jsx(d,{href:"components/table#striped-rows",children:e.jsxs(n,{color:"dark",striped:!0,children:[e.jsx(c,{children:e.jsxs(r,{children:[e.jsx(l,{scope:"col",children:"#"}),e.jsx(l,{scope:"col",children:"Class"}),e.jsx(l,{scope:"col",children:"Heading"}),e.jsx(l,{scope:"col",children:"Heading"})]})}),e.jsxs(i,{children:[e.jsxs(r,{children:[e.jsx(l,{scope:"row",children:"1"}),e.jsx(s,{children:"Mark"}),e.jsx(s,{children:"Otto"}),e.jsx(s,{children:"@mdo"})]}),e.jsxs(r,{children:[e.jsx(l,{scope:"row",children:"2"}),e.jsx(s,{children:"Jacob"}),e.jsx(s,{children:"Thornton"}),e.jsx(s,{children:"@fat"})]}),e.jsxs(r,{children:[e.jsx(l,{scope:"row",children:"3"}),e.jsx(s,{colSpan:2,children:"Larry the Bird"}),e.jsx(s,{children:"@twitter"})]})]})]})}),e.jsx(d,{href:"components/table#striped-rows",children:e.jsxs(n,{color:"success",striped:!0,children:[e.jsx(c,{children:e.jsxs(r,{children:[e.jsx(l,{scope:"col",children:"#"}),e.jsx(l,{scope:"col",children:"Class"}),e.jsx(l,{scope:"col",children:"Heading"}),e.jsx(l,{scope:"col",children:"Heading"})]})}),e.jsxs(i,{children:[e.jsxs(r,{children:[e.jsx(l,{scope:"row",children:"1"}),e.jsx(s,{children:"Mark"}),e.jsx(s,{children:"Otto"}),e.jsx(s,{children:"@mdo"})]}),e.jsxs(r,{children:[e.jsx(l,{scope:"row",children:"2"}),e.jsx(s,{children:"Jacob"}),e.jsx(s,{children:"Thornton"}),e.jsx(s,{children:"@fat"})]}),e.jsxs(r,{children:[e.jsx(l,{scope:"row",children:"3"}),e.jsx(s,{colSpan:2,children:"Larry the Bird"}),e.jsx(s,{children:"@twitter"})]})]})]})})]})]})}),e.jsx(h,{xs:12,children:e.jsxs(o,{className:"mb-4",children:[e.jsxs(j,{children:[e.jsx("strong",{children:"React Table"})," ",e.jsx("small",{children:"Hoverable rows"})]}),e.jsxs(x,{children:[e.jsxs("p",{className:"text-body-secondary small",children:["Use ",e.jsx("code",{children:"hover"})," property to enable a hover state on table rows within a"," ",e.jsx("code",{children:"<CTableBody>"}),"."]}),e.jsx(d,{href:"components/table#hoverable-rows",children:e.jsxs(n,{hover:!0,children:[e.jsx(c,{children:e.jsxs(r,{children:[e.jsx(l,{scope:"col",children:"#"}),e.jsx(l,{scope:"col",children:"Class"}),e.jsx(l,{scope:"col",children:"Heading"}),e.jsx(l,{scope:"col",children:"Heading"})]})}),e.jsxs(i,{children:[e.jsxs(r,{children:[e.jsx(l,{scope:"row",children:"1"}),e.jsx(s,{children:"Mark"}),e.jsx(s,{children:"Otto"}),e.jsx(s,{children:"@mdo"})]}),e.jsxs(r,{children:[e.jsx(l,{scope:"row",children:"2"}),e.jsx(s,{children:"Jacob"}),e.jsx(s,{children:"Thornton"}),e.jsx(s,{children:"@fat"})]}),e.jsxs(r,{children:[e.jsx(l,{scope:"row",children:"3"}),e.jsx(s,{colSpan:2,children:"Larry the Bird"}),e.jsx(s,{children:"@twitter"})]})]})]})}),e.jsx(d,{href:"components/table#hoverable-rows",children:e.jsxs(n,{color:"dark",hover:!0,children:[e.jsx(c,{children:e.jsxs(r,{children:[e.jsx(l,{scope:"col",children:"#"}),e.jsx(l,{scope:"col",children:"Class"}),e.jsx(l,{scope:"col",children:"Heading"}),e.jsx(l,{scope:"col",children:"Heading"})]})}),e.jsxs(i,{children:[e.jsxs(r,{children:[e.jsx(l,{scope:"row",children:"1"}),e.jsx(s,{children:"Mark"}),e.jsx(s,{children:"Otto"}),e.jsx(s,{children:"@mdo"})]}),e.jsxs(r,{children:[e.jsx(l,{scope:"row",children:"2"}),e.jsx(s,{children:"Jacob"}),e.jsx(s,{children:"Thornton"}),e.jsx(s,{children:"@fat"})]}),e.jsxs(r,{children:[e.jsx(l,{scope:"row",children:"3"}),e.jsx(s,{colSpan:2,children:"Larry the Bird"}),e.jsx(s,{children:"@twitter"})]})]})]})}),e.jsx(d,{href:"components/table#hoverable-rows",children:e.jsxs(n,{striped:!0,hover:!0,children:[e.jsx(c,{children:e.jsxs(r,{children:[e.jsx(l,{scope:"col",children:"#"}),e.jsx(l,{scope:"col",children:"Class"}),e.jsx(l,{scope:"col",children:"Heading"}),e.jsx(l,{scope:"col",children:"Heading"})]})}),e.jsxs(i,{children:[e.jsxs(r,{children:[e.jsx(l,{scope:"row",children:"1"}),e.jsx(s,{children:"Mark"}),e.jsx(s,{children:"Otto"}),e.jsx(s,{children:"@mdo"})]}),e.jsxs(r,{children:[e.jsx(l,{scope:"row",children:"2"}),e.jsx(s,{children:"Jacob"}),e.jsx(s,{children:"Thornton"}),e.jsx(s,{children:"@fat"})]}),e.jsxs(r,{children:[e.jsx(l,{scope:"row",children:"3"}),e.jsx(s,{colSpan:2,children:"Larry the Bird"}),e.jsx(s,{children:"@twitter"})]})]})]})})]})]})}),e.jsx(h,{xs:12,children:e.jsxs(o,{className:"mb-4",children:[e.jsxs(j,{children:[e.jsx("strong",{children:"React Table"})," ",e.jsx("small",{children:"Active tables"})]}),e.jsxs(x,{children:[e.jsx(d,{href:"components/table#active-tables",children:e.jsxs(n,{children:[e.jsx(c,{children:e.jsxs(r,{children:[e.jsx(l,{scope:"col",children:"#"}),e.jsx(l,{scope:"col",children:"Class"}),e.jsx(l,{scope:"col",children:"Heading"}),e.jsx(l,{scope:"col",children:"Heading"})]})}),e.jsxs(i,{children:[e.jsxs(r,{active:!0,children:[e.jsx(l,{scope:"row",children:"1"}),e.jsx(s,{children:"Mark"}),e.jsx(s,{children:"Otto"}),e.jsx(s,{children:"@mdo"})]}),e.jsxs(r,{children:[e.jsx(l,{scope:"row",children:"2"}),e.jsx(s,{children:"Jacob"}),e.jsx(s,{children:"Thornton"}),e.jsx(s,{children:"@fat"})]}),e.jsxs(r,{children:[e.jsx(l,{scope:"row",children:"3"}),e.jsx(s,{colSpan:2,active:!0,children:"Larry the Bird"}),e.jsx(s,{children:"@twitter"})]})]})]})}),e.jsx(d,{href:"components/table#active-tables",children:e.jsxs(n,{color:"dark",children:[e.jsx(c,{children:e.jsxs(r,{children:[e.jsx(l,{scope:"col",children:"#"}),e.jsx(l,{scope:"col",children:"Class"}),e.jsx(l,{scope:"col",children:"Heading"}),e.jsx(l,{scope:"col",children:"Heading"})]})}),e.jsxs(i,{children:[e.jsxs(r,{active:!0,children:[e.jsx(l,{scope:"row",children:"1"}),e.jsx(s,{children:"Mark"}),e.jsx(s,{children:"Otto"}),e.jsx(s,{children:"@mdo"})]}),e.jsxs(r,{children:[e.jsx(l,{scope:"row",children:"2"}),e.jsx(s,{children:"Jacob"}),e.jsx(s,{children:"Thornton"}),e.jsx(s,{children:"@fat"})]}),e.jsxs(r,{children:[e.jsx(l,{scope:"row",children:"3"}),e.jsx(s,{colSpan:2,active:!0,children:"Larry the Bird"}),e.jsx(s,{children:"@twitter"})]})]})]})})]})]})}),e.jsx(h,{xs:12,children:e.jsxs(o,{className:"mb-4",children:[e.jsxs(j,{children:[e.jsx("strong",{children:"React Table"})," ",e.jsx("small",{children:"Bordered tables"})]}),e.jsxs(x,{children:[e.jsxs("p",{className:"text-body-secondary small",children:["Add ",e.jsx("code",{children:"bordered"})," property for borders on all sides of the table and cells."]}),e.jsx(d,{href:"components/table#bordered-tables",children:e.jsxs(n,{bordered:!0,children:[e.jsx(c,{children:e.jsxs(r,{children:[e.jsx(l,{scope:"col",children:"#"}),e.jsx(l,{scope:"col",children:"Class"}),e.jsx(l,{scope:"col",children:"Heading"}),e.jsx(l,{scope:"col",children:"Heading"})]})}),e.jsxs(i,{children:[e.jsxs(r,{children:[e.jsx(l,{scope:"row",children:"1"}),e.jsx(s,{children:"Mark"}),e.jsx(s,{children:"Otto"}),e.jsx(s,{children:"@mdo"})]}),e.jsxs(r,{children:[e.jsx(l,{scope:"row",children:"2"}),e.jsx(s,{children:"Jacob"}),e.jsx(s,{children:"Thornton"}),e.jsx(s,{children:"@fat"})]}),e.jsxs(r,{children:[e.jsx(l,{scope:"row",children:"3"}),e.jsx(s,{colSpan:2,children:"Larry the Bird"}),e.jsx(s,{children:"@twitter"})]})]})]})}),e.jsxs("p",{className:"text-body-secondary small",children:[e.jsx("a",{href:"https://coreui.io/docs/utilities/borders#border-color",children:"Border color utilities"})," ","can be added to change colors:"]}),e.jsx(d,{href:"components/table#bordered-tables",children:e.jsxs(n,{bordered:!0,borderColor:"primary",children:[e.jsx(c,{children:e.jsxs(r,{children:[e.jsx(l,{scope:"col",children:"#"}),e.jsx(l,{scope:"col",children:"Class"}),e.jsx(l,{scope:"col",children:"Heading"}),e.jsx(l,{scope:"col",children:"Heading"})]})}),e.jsxs(i,{children:[e.jsxs(r,{children:[e.jsx(l,{scope:"row",children:"1"}),e.jsx(s,{children:"Mark"}),e.jsx(s,{children:"Otto"}),e.jsx(s,{children:"@mdo"})]}),e.jsxs(r,{children:[e.jsx(l,{scope:"row",children:"2"}),e.jsx(s,{children:"Jacob"}),e.jsx(s,{children:"Thornton"}),e.jsx(s,{children:"@fat"})]}),e.jsxs(r,{children:[e.jsx(l,{scope:"row",children:"3"}),e.jsx(s,{colSpan:2,children:"Larry the Bird"}),e.jsx(s,{children:"@twitter"})]})]})]})})]})]})}),e.jsx(h,{xs:12,children:e.jsxs(o,{className:"mb-4",children:[e.jsxs(j,{children:[e.jsx("strong",{children:"React Table"})," ",e.jsx("small",{children:"Tables without borders"})]}),e.jsxs(x,{children:[e.jsxs("p",{className:"text-body-secondary small",children:["Add ",e.jsx("code",{children:"borderless"})," property for a table without borders."]}),e.jsx(d,{href:"components/table#tables-without-borders",children:e.jsxs(n,{borderless:!0,children:[e.jsx(c,{children:e.jsxs(r,{children:[e.jsx(l,{scope:"col",children:"#"}),e.jsx(l,{scope:"col",children:"Class"}),e.jsx(l,{scope:"col",children:"Heading"}),e.jsx(l,{scope:"col",children:"Heading"})]})}),e.jsxs(i,{children:[e.jsxs(r,{children:[e.jsx(l,{scope:"row",children:"1"}),e.jsx(s,{children:"Mark"}),e.jsx(s,{children:"Otto"}),e.jsx(s,{children:"@mdo"})]}),e.jsxs(r,{children:[e.jsx(l,{scope:"row",children:"2"}),e.jsx(s,{children:"Jacob"}),e.jsx(s,{children:"Thornton"}),e.jsx(s,{children:"@fat"})]}),e.jsxs(r,{children:[e.jsx(l,{scope:"row",children:"3"}),e.jsx(s,{colSpan:2,children:"Larry the Bird"}),e.jsx(s,{children:"@twitter"})]})]})]})}),e.jsx(d,{href:"components/table#tables-without-borders",children:e.jsxs(n,{color:"dark",borderless:!0,children:[e.jsx(c,{children:e.jsxs(r,{children:[e.jsx(l,{scope:"col",children:"#"}),e.jsx(l,{scope:"col",children:"Class"}),e.jsx(l,{scope:"col",children:"Heading"}),e.jsx(l,{scope:"col",children:"Heading"})]})}),e.jsxs(i,{children:[e.jsxs(r,{children:[e.jsx(l,{scope:"row",children:"1"}),e.jsx(s,{children:"Mark"}),e.jsx(s,{children:"Otto"}),e.jsx(s,{children:"@mdo"})]}),e.jsxs(r,{children:[e.jsx(l,{scope:"row",children:"2"}),e.jsx(s,{children:"Jacob"}),e.jsx(s,{children:"Thornton"}),e.jsx(s,{children:"@fat"})]}),e.jsxs(r,{children:[e.jsx(l,{scope:"row",children:"3"}),e.jsx(s,{colSpan:2,children:"Larry the Bird"}),e.jsx(s,{children:"@twitter"})]})]})]})})]})]})}),e.jsx(h,{xs:12,children:e.jsxs(o,{className:"mb-4",children:[e.jsxs(j,{children:[e.jsx("strong",{children:"React Table"})," ",e.jsx("small",{children:"Small tables"})]}),e.jsxs(x,{children:[e.jsxs("p",{className:"text-body-secondary small",children:["Add ",e.jsx("code",{children:"small"})," property to make any ",e.jsx("code",{children:"<CTable>"})," more compact by cutting all cell ",e.jsx("code",{children:"padding"})," in half."]}),e.jsx(d,{href:"components/table#small-tables",children:e.jsxs(n,{small:!0,children:[e.jsx(c,{children:e.jsxs(r,{children:[e.jsx(l,{scope:"col",children:"#"}),e.jsx(l,{scope:"col",children:"Class"}),e.jsx(l,{scope:"col",children:"Heading"}),e.jsx(l,{scope:"col",children:"Heading"})]})}),e.jsxs(i,{children:[e.jsxs(r,{children:[e.jsx(l,{scope:"row",children:"1"}),e.jsx(s,{children:"Mark"}),e.jsx(s,{children:"Otto"}),e.jsx(s,{children:"@mdo"})]}),e.jsxs(r,{children:[e.jsx(l,{scope:"row",children:"2"}),e.jsx(s,{children:"Jacob"}),e.jsx(s,{children:"Thornton"}),e.jsx(s,{children:"@fat"})]}),e.jsxs(r,{children:[e.jsx(l,{scope:"row",children:"3"}),e.jsx(s,{colSpan:2,children:"Larry the Bird"}),e.jsx(s,{children:"@twitter"})]})]})]})})]})]})}),e.jsx(h,{xs:12,children:e.jsxs(o,{className:"mb-4",children:[e.jsxs(j,{children:[e.jsx("strong",{children:"React Table"})," ",e.jsx("small",{children:"Vertical alignment"})]}),e.jsxs(x,{children:[e.jsxs("p",{className:"text-body-secondary small",children:["Table cells of ",e.jsx("code",{children:"<CTableHead>"})," are always vertical aligned to the bottom. Table cells in ",e.jsx("code",{children:"<CTableBody>"})," inherit their alignment from"," ",e.jsx("code",{children:"<CTable>"})," and are aligned to the the top by default. Use the align property to re-align where needed."]}),e.jsx(d,{href:"components/table#vertical-alignment",children:e.jsxs(n,{align:"middle",responsive:!0,children:[e.jsx(c,{children:e.jsxs(r,{children:[e.jsx(l,{scope:"col",className:"w-25",children:"Heading 1"}),e.jsx(l,{scope:"col",className:"w-25",children:"Heading 2"}),e.jsx(l,{scope:"col",className:"w-25",children:"Heading 3"}),e.jsx(l,{scope:"col",className:"w-25",children:"Heading 4"})]})}),e.jsxs(i,{children:[e.jsxs(r,{children:[e.jsxs(s,{children:["This cell inherits ",e.jsx("code",{children:"vertical-align: middle;"})," from the table"]}),e.jsxs(s,{children:["This cell inherits ",e.jsx("code",{children:"vertical-align: middle;"})," from the table"]}),e.jsxs(s,{children:["This cell inherits ",e.jsx("code",{children:"vertical-align: middle;"})," from the table"]}),e.jsx(s,{children:"This here is some placeholder text, intended to take up quite a bit of vertical space, to demonsCTableRowate how the vertical alignment works in the preceding cells."})]}),e.jsxs(r,{align:"bottom",children:[e.jsxs(s,{children:["This cell inherits ",e.jsx("code",{children:"vertical-align: bottom;"})," from the table row"]}),e.jsxs(s,{children:["This cell inherits ",e.jsx("code",{children:"vertical-align: bottom;"})," from the table row"]}),e.jsxs(s,{children:["This cell inherits ",e.jsx("code",{children:"vertical-align: bottom;"})," from the table row"]}),e.jsx(s,{children:"This here is some placeholder text, intended to take up quite a bit of vertical space, to demonsCTableRowate how the vertical alignment works in the preceding cells."})]}),e.jsxs(r,{children:[e.jsxs(s,{children:["This cell inherits ",e.jsx("code",{children:"vertical-align: middle;"})," from the table"]}),e.jsxs(s,{children:["This cell inherits ",e.jsx("code",{children:"vertical-align: middle;"})," from the table"]}),e.jsx(s,{align:"top",children:"This cell is aligned to the top."}),e.jsx(s,{children:"This here is some placeholder text, intended to take up quite a bit of vertical space, to demonsCTableRowate how the vertical alignment works in the preceding cells."})]})]})]})})]})]})}),e.jsx(h,{xs:12,children:e.jsxs(o,{className:"mb-4",children:[e.jsxs(j,{children:[e.jsx("strong",{children:"React Table"})," ",e.jsx("small",{children:"Nesting"})]}),e.jsxs(x,{children:[e.jsx("p",{className:"text-body-secondary small",children:"Border styles, active styles, and table variants are not inherited by nested tables."}),e.jsx(d,{href:"components/table#nesting",children:e.jsxs(n,{striped:!0,children:[e.jsx(c,{children:e.jsxs(r,{children:[e.jsx(l,{scope:"col",children:"#"}),e.jsx(l,{scope:"col",children:"Class"}),e.jsx(l,{scope:"col",children:"Heading"}),e.jsx(l,{scope:"col",children:"Heading"})]})}),e.jsxs(i,{children:[e.jsxs(r,{children:[e.jsx(l,{scope:"row",children:"1"}),e.jsx(s,{children:"Mark"}),e.jsx(s,{children:"Otto"}),e.jsx(s,{children:"@mdo"})]}),e.jsx(r,{children:e.jsx(l,{colSpan:4,children:e.jsxs(n,{children:[e.jsx(c,{children:e.jsxs(r,{children:[e.jsx(l,{scope:"col",children:"Header"}),e.jsx(l,{scope:"col",children:"Header"}),e.jsx(l,{scope:"col",children:"Header"})]})}),e.jsxs(i,{children:[e.jsxs(r,{children:[e.jsx(l,{scope:"row",children:"A"}),e.jsx(s,{children:"First"}),e.jsx(s,{children:"Last"})]}),e.jsxs(r,{children:[e.jsx(l,{scope:"row",children:"B"}),e.jsx(s,{children:"First"}),e.jsx(s,{children:"Last"})]}),e.jsxs(r,{children:[e.jsx(l,{scope:"row",children:"C"}),e.jsx(s,{children:"First"}),e.jsx(s,{children:"Last"})]})]})]})})}),e.jsxs(r,{children:[e.jsx(l,{scope:"row",children:"3"}),e.jsx(s,{colSpan:2,children:"Larry the Bird"}),e.jsx(s,{children:"@twitter"})]})]})]})})]})]})}),e.jsx(h,{xs:12,children:e.jsxs(o,{className:"mb-4",children:[e.jsxs(j,{children:[e.jsx("strong",{children:"React Table"})," ",e.jsx("small",{children:"Table head"})]}),e.jsxs(x,{children:[e.jsxs("p",{className:"text-body-secondary small",children:["Similar to tables and dark tables, use the modifier prop"," ",e.jsx("code",{children:'color="light"'})," or ",e.jsx("code",{children:'color="dark"'})," to make"," ",e.jsx("code",{children:"<CTableHead>"}),"s appear light or dark gray."]}),e.jsx(d,{href:"components/table#table-head",children:e.jsxs(n,{children:[e.jsx(c,{color:"light",children:e.jsxs(r,{children:[e.jsx(l,{scope:"col",children:"#"}),e.jsx(l,{scope:"col",children:"Class"}),e.jsx(l,{scope:"col",children:"Heading"}),e.jsx(l,{scope:"col",children:"Heading"})]})}),e.jsxs(i,{children:[e.jsxs(r,{children:[e.jsx(l,{scope:"row",children:"1"}),e.jsx(s,{children:"Mark"}),e.jsx(s,{children:"Otto"}),e.jsx(s,{children:"@mdo"})]}),e.jsxs(r,{children:[e.jsx(l,{scope:"row",children:"2"}),e.jsx(s,{children:"Jacob"}),e.jsx(s,{children:"Thornton"}),e.jsx(s,{children:"@fat"})]}),e.jsxs(r,{children:[e.jsx(l,{scope:"row",children:"3"}),e.jsx(s,{children:"Larry"}),e.jsx(s,{children:"the Bird"}),e.jsx(s,{children:"@twitter"})]})]})]})}),e.jsx(d,{href:"components/table#table-head",children:e.jsxs(n,{children:[e.jsx(c,{color:"dark",children:e.jsxs(r,{children:[e.jsx(l,{scope:"col",children:"#"}),e.jsx(l,{scope:"col",children:"Class"}),e.jsx(l,{scope:"col",children:"Heading"}),e.jsx(l,{scope:"col",children:"Heading"})]})}),e.jsxs(i,{children:[e.jsxs(r,{children:[e.jsx(l,{scope:"row",children:"1"}),e.jsx(s,{children:"Mark"}),e.jsx(s,{children:"Otto"}),e.jsx(s,{children:"@mdo"})]}),e.jsxs(r,{children:[e.jsx(l,{scope:"row",children:"2"}),e.jsx(s,{children:"Jacob"}),e.jsx(s,{children:"Thornton"}),e.jsx(s,{children:"@fat"})]}),e.jsxs(r,{children:[e.jsx(l,{scope:"row",children:"3"}),e.jsx(s,{colSpan:2,children:"Larry the Bird"}),e.jsx(s,{children:"@twitter"})]})]})]})})]})]})}),e.jsx(h,{xs:12,children:e.jsxs(o,{className:"mb-4",children:[e.jsxs(j,{children:[e.jsx("strong",{children:"React Table"})," ",e.jsx("small",{children:"Table foot"})]}),e.jsx(x,{children:e.jsx(d,{href:"components/table#table-foot",children:e.jsxs(n,{children:[e.jsx(c,{color:"light",children:e.jsxs(r,{children:[e.jsx(l,{scope:"col",children:"#"}),e.jsx(l,{scope:"col",children:"Class"}),e.jsx(l,{scope:"col",children:"Heading"}),e.jsx(l,{scope:"col",children:"Heading"})]})}),e.jsxs(i,{children:[e.jsxs(r,{children:[e.jsx(l,{scope:"row",children:"1"}),e.jsx(s,{children:"Mark"}),e.jsx(s,{children:"Otto"}),e.jsx(s,{children:"@mdo"})]}),e.jsxs(r,{children:[e.jsx(l,{scope:"row",children:"2"}),e.jsx(s,{children:"Jacob"}),e.jsx(s,{children:"Thornton"}),e.jsx(s,{children:"@fat"})]}),e.jsxs(r,{children:[e.jsx(l,{scope:"row",children:"3"}),e.jsx(s,{colSpan:2,children:"Larry the Bird"}),e.jsx(s,{children:"@twitter"})]})]}),e.jsx(c,{children:e.jsxs(r,{children:[e.jsx(s,{children:"Footer"}),e.jsx(s,{children:"Footer"}),e.jsx(s,{children:"Footer"}),e.jsx(s,{children:"Footer"})]})})]})})})]})}),e.jsx(h,{xs:12,children:e.jsxs(o,{className:"mb-4",children:[e.jsxs(j,{children:[e.jsx("strong",{children:"React Table"})," ",e.jsx("small",{children:"Captions"})]}),e.jsxs(x,{children:[e.jsxs("p",{className:"text-body-secondary small",children:["A ",e.jsx("code",{children:"<CTableCaption>"})," functions like a heading for a table. It helps users with screen readers to find a table and understand what it's about and decide if they want to read it."]}),e.jsx(d,{href:"components/table#captions",children:e.jsxs(n,{children:[e.jsx(a,{children:"List of users"}),e.jsx(c,{children:e.jsxs(r,{children:[e.jsx(l,{scope:"col",children:"#"}),e.jsx(l,{scope:"col",children:"Class"}),e.jsx(l,{scope:"col",children:"Heading"}),e.jsx(l,{scope:"col",children:"Heading"})]})}),e.jsxs(i,{children:[e.jsxs(r,{children:[e.jsx(l,{scope:"row",children:"1"}),e.jsx(s,{children:"Mark"}),e.jsx(s,{children:"Otto"}),e.jsx(s,{children:"@mdo"})]}),e.jsxs(r,{children:[e.jsx(l,{scope:"row",children:"2"}),e.jsx(s,{children:"Jacob"}),e.jsx(s,{children:"Thornton"}),e.jsx(s,{children:"@fat"})]}),e.jsxs(r,{children:[e.jsx(l,{scope:"row",children:"3"}),e.jsx(s,{children:"Larry"}),e.jsx(s,{children:"the Bird"}),e.jsx(s,{children:"@twitter"})]})]})]})}),e.jsxs("p",{className:"text-body-secondary small",children:["You can also put the ",e.jsx("code",{children:"<CTableCaption>"})," on the top of the table with"," ",e.jsx("code",{children:'caption="top"'}),"."]}),e.jsx(d,{href:"components/table#captions",children:e.jsxs(n,{caption:"top",children:[e.jsx(a,{children:"List of users"}),e.jsx(c,{children:e.jsxs(r,{children:[e.jsx(l,{scope:"col",children:"#"}),e.jsx(l,{scope:"col",children:"Class"}),e.jsx(l,{scope:"col",children:"Heading"}),e.jsx(l,{scope:"col",children:"Heading"})]})}),e.jsxs(i,{children:[e.jsxs(r,{children:[e.jsx(l,{scope:"row",children:"1"}),e.jsx(s,{children:"Mark"}),e.jsx(s,{children:"Otto"}),e.jsx(s,{children:"@mdo"})]}),e.jsxs(r,{children:[e.jsx(l,{scope:"row",children:"2"}),e.jsx(s,{children:"Jacob"}),e.jsx(s,{children:"Thornton"}),e.jsx(s,{children:"@fat"})]}),e.jsxs(r,{children:[e.jsx(l,{scope:"row",children:"3"}),e.jsx(s,{children:"Larry"}),e.jsx(s,{children:"the Bird"}),e.jsx(s,{children:"@twitter"})]})]})]})})]})]})})]});export{H as default};
