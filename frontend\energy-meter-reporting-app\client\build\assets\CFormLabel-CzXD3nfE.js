import{a as o,_ as n,R as i,b as N,c as d,P as e}from"./index-BDJ8oeCE.js";var r=o.forwardRef(function(s,l){var m=s.children,c=s.className,a=s.customClassName,t=n(s,["children","className","customClassName"]);return i.createElement("label",N({className:a??d("form-label",c)},t,{ref:l}),m)});r.propTypes={children:e.node,className:e.string,customClassName:e.string};r.displayName="CFormLabel";export{r as C};
