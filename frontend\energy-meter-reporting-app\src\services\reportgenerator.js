const sql = require('mssql');
const ExcelJS = require('exceljs');
const fs = require('fs');
const path = require('path');
const config = require('../config');
const { getdailyquery, getmonthlyquery, getyearlyquery, getmonthlyAllFeeders } = require('../utils/queries');
const { copyFormatAndText, copyMergedCells, addFormulaForRow,
    writeEMNames, expandMergedCells, applyThickBorderToSheet,
    safeUnmerge, copyColumnLength, moveproductioncells,
    applyProductionFormula, applyMergedCellFormula, protectsheetfn,
    finalTotalFormula, totalLastColumns, applyThinBorderToRange, applyThickToBottom, applyThickToRight } = require('../utils/excelformater');

// MSSQL Database Configuration,
const dbConfig = {
    ...config.db,
    options: {
        encrypt: false, // Change to true if using Azure
        trustServerCertificate: true
    },
    requestTimeout: 60000
};

// Function to Generate Excel Report
async function generateIncomerReport(start_time, em_ids, em_names, parameterArray, plant_name, report_type, protectsheet = false, arrayunlockedcells = [], filePath, templatePath) {
    try {
        const emIDArray = em_ids.split(','); // Convert to array
        const emNameArray = em_names.split(',');
        const dataColumnsLength = parameterArray.length;

        let endTime;
        const startDate = new Date(start_time); // Add 5 hours 30 minutes (330 minutes)
        // console.log(startDate);
        // console.log(report_type);
        if (report_type === 1) { // Daily
            endTime = startDate;
            endTime.setHours(23, 59, 59);
        } else if (report_type === 2) { // Monthly
            endTime = new Date(startDate.getFullYear(), startDate.getMonth() + 1, 0); // Last day of the month
            endTime.setHours(23, 59, 59);
        } else if (report_type === 3) { // Yearly
            endTime = new Date(startDate.getFullYear(), 11, 31); // December 31st
            endTime.setHours(23, 59, 59);
        }
        //  console.log(endTime);
        // Add 5 hours and 30 minutes to end_time
        endTime = new Date(endTime.getTime() + 330 * 60 * 1000);

        const end_time = endTime.toISOString();
        // console.log(end_time);

        // Connect to MSSQL
        await sql.connect(dbConfig);

        // Query to Fetch Data
        let query;

        if (report_type === 2) {
            query = getmonthlyquery(start_time, end_time, emIDArray, parameterArray);
        }
        else if (report_type === 3) {
            query = getyearlyquery(start_time, end_time, emIDArray, parameterArray);
        }
        else {
            query = getdailyquery(start_time, end_time, emIDArray, parameterArray);
        }

        const result = await sql.query(query);



        const data = result.recordset;
        if (data.length === 0) {
            console.log('No data found');
            return;
        }

        // Load Template
        const workbook = new ExcelJS.Workbook();
        await workbook.xlsx.readFile(templatePath);
        const worksheet = workbook.worksheets[report_type - 1]; // report_type =1 daily. 2 - monthly , 3- Yearly

        const selectedSheetName = worksheet.name;

        // Remove all other sheets except the selected one
        workbook.worksheets.forEach(sheet => {
            if (sheet.name !== selectedSheetName) {
                workbook.removeWorksheet(sheet.id);
            }
        });


        // Organize Data
        let groupedData = getgroupeddata(data, report_type);



        worksheet.getCell('D2').value = plant_name;
        switch (report_type) {
            case 1:
                worksheet.getCell('D3').value = 'Daily Report';
                worksheet.getCell('D4').value = 'Date: ' + start_time.split('T')[0];
                break;
            case 2:
                worksheet.getCell('D3').value = 'Monthly Report';
                worksheet.getCell('D4').value = 'Month: ' + start_time.split('T')[0].slice(0, -3);
                break;

            case 3:
                worksheet.getCell('D3').value = 'Yearly Report';
                worksheet.getCell('D4').value = 'Year: ' + start_time.split('T')[0].slice(0, -6);
                break;
            default:
        }

        let lastDataRowPerEM = Array();

        // Populate Rows
        let startRow = 8; // Assuming data starts from row 8 in template

        Object.keys(groupedData).forEach(datetime => {
            let row = worksheet.getRow(startRow++);

            // The datetime is already in IST from getgroupeddata
            // For display in Excel, we need to convert back to UTC
            // because Excel will apply the local timezone offset
            let dateValue = new Date(new Date(datetime).getTime() - 330 * 60 * 1000);

            // For yearly reports, we want to keep the IST time
            if (report_type === 3) {
                dateValue = new Date(datetime);
            }

            row.getCell(1).value = dateValue;
            if (report_type === 2)
                row.getCell(1).numFmt = 'yyyy-mm-dd';

            let colIndex = 2;

            emIDArray.forEach(em => {
                let hasData = false;

                let emValues = [
                    groupedData[datetime][`${em}_CD_KWH`]

                ];
                emValues.forEach(val => {
                    if (val !== null && val !== undefined) hasData = true;
                });
                row.getCell(colIndex++).value = groupedData[datetime][`${em}_RYPHASE_VOL`];
                row.getCell(colIndex++).value = groupedData[datetime][`${em}_YBPHASE_VOL`];
                row.getCell(colIndex++).value = groupedData[datetime][`${em}_BRPHASE_VOL`];
                row.getCell(colIndex++).value = groupedData[datetime][`${em}_RPHASE_CUR`];
                row.getCell(colIndex++).value = groupedData[datetime][`${em}_YPHASE_CUR`];
                row.getCell(colIndex++).value = groupedData[datetime][`${em}_BPHASE_CUR`];
                row.getCell(colIndex++).value = groupedData[datetime][`${em}_CD_KWH`];
                row.getCell(colIndex++).value = groupedData[datetime][`${em}_AVGPF`];
                if (hasData) lastDataRowPerEM[em] = startRow - 1;

            });
            row.commit();
        });
        // Delete extra rows for monthly report
        //  console.log(Object.keys(groupedData).length);
        // console.log('Last data row per EM:', lastDataRowPerEM);



        const firstEMColumn = 2; // B column is index 2
        if (emIDArray.length > 1) {
            copyFormatAndText(worksheet, worksheet, "B5:I7", firstEMColumn + dataColumnsLength, emIDArray.length - 1);
            if (report_type === 1) {
                copyFormatAndText(worksheet, worksheet, "B33:I33", firstEMColumn + dataColumnsLength, emIDArray.length - 1);
                copyFormatAndText(worksheet, worksheet, "B8:I32", firstEMColumn + dataColumnsLength, emIDArray.length - 1, true);
            } else if (report_type === 2) {
                copyFormatAndText(worksheet, worksheet, "B39:I39", firstEMColumn + dataColumnsLength, emIDArray.length - 1);
                copyFormatAndText(worksheet, worksheet, "B8:I38", firstEMColumn + dataColumnsLength, emIDArray.length - 1, true);
            } else if (report_type === 3) {
                copyFormatAndText(worksheet, worksheet, "B20:I20", firstEMColumn + dataColumnsLength, emIDArray.length - 1);
                copyFormatAndText(worksheet, worksheet, "B8:I19", firstEMColumn + dataColumnsLength, emIDArray.length - 1, true);
            }

            copyMergedCells(worksheet, worksheet, firstEMColumn, firstEMColumn + dataColumnsLength, emIDArray.length - 1);
        }

        /*const firstdatarow = 8;
        let maxrows = 0;

        for (let i = 0; i < emIDArray.length; i++) {
            let em = emIDArray[i];
            let lastRow = lastDataRowPerEM[em] || lastDatarowNo;
            if (lastRow > maxrows) {
                maxrows = lastRow;
            }
        }*/

        if (report_type === 1) {
            //  addFormulaForRow(worksheet, 32, 'upper',8,8,8,0);

            addFormulaForRow(worksheet, 32, 'upper', 8, 8, 8, 0, lastDataRowPerEM, emIDArray);

            /* let deleteCount = 32 - maxrows;
             let startRow = maxrows + 1;
             console.log('Deleting rows:', startRow, 'count', deleteCount);
             worksheet.spliceRows(startRow, deleteCount);*/
        }
        if (report_type === 2) {
            addFormulaForRow(worksheet, 38, 'sum', 8, 8, 8, 0, lastDataRowPerEM, emIDArray);

            const totalDaysInMonth = new Date(startDate.getFullYear(), startDate.getMonth() + 1, 0).getDate();
            if (totalDaysInMonth < 31) {
                for (let i = 8 + totalDaysInMonth; i <= 38; i++) {
                    worksheet.getRow(i).hidden = true;
                }
            }

            /*let deleteCount = 38 - maxrows;
            let startRow = maxrows + 1;
            console.log('Deleting rows:', startRow, 'count', deleteCount);
            worksheet.spliceRows(startRow, deleteCount);*/

        }
        if (report_type === 3) {
            addFormulaForRow(worksheet, 19, 'sum', 8, 8, 8, 0, lastDataRowPerEM, emIDArray);

            /*let deleteCount = 38 - maxrows;
            let startRow = maxrows + 1;
            console.log('Deleting rows:', startRow, 'count', deleteCount);
            worksheet.spliceRows(startRow, deleteCount);*/
        }



        writeEMNames(worksheet, emNameArray);
        expandMergedCells(worksheet, emIDArray);
        applyThickBorderToSheet(worksheet, 6);
        //
        if (protectsheet) {
            await protectsheetfn(worksheet, arrayunlockedcells);
        }


        // Save File
        await workbook.xlsx.writeFile(filePath); // Save to the provided filePath

        console.log(`Report generated successfully: ${filePath}`);
    } catch (error) {
        console.error('Error generating report:', error);
    }
}

async function generateShiftReport(start_time, em_ids, em_names, parameterArray, plant_name, report_type, protectsheet = false, arrayunlockedcells = [], filePath, templatePath) {
    try {
        const emIDArray = em_ids.split(','); // Convert to array
        const emNameArray = em_names.split(',');
        const dataColumnsLength = parameterArray.length;

        let endTime;
        const startDate = new Date(start_time); // Add 5 hours 30 minutes (330 minutes)

        if (report_type === 1) { // Daily
            endTime = startDate;
            endTime.setHours(23, 59, 59);
        } else if (report_type === 2) { // Monthly
            endTime = new Date(startDate.getFullYear(), startDate.getMonth() + 1, 0); // Last day of the month
            endTime.setHours(23, 59, 59);
        } else if (report_type === 3) { // Yearly
            endTime = new Date(startDate.getFullYear(), 11, 31); // December 31st
            endTime.setHours(23, 59, 59);
        }

        // Add 5 hours and 30 minutes to end_time
        endTime = new Date(endTime.getTime() + 330 * 60 * 1000);

        const end_time = endTime.toISOString();
        // console.log(end_time);

        // Connect to MSSQL
        await sql.connect(dbConfig);
        // Query to Fetch Data
        let query;

        if (report_type === 2) {
            query = getmonthlyquery(start_time, end_time, emIDArray, parameterArray);
        }
        else if (report_type === 3) {
            query = getyearlyquery(start_time, end_time, emIDArray, parameterArray);
        }
        else {
            query = getdailyquery(start_time, end_time, emIDArray, parameterArray);
        }



        const result = await sql.query(query);
        const data = result.recordset;
        // console.log(data);
        if (data.length === 0) {
            console.log('No data found');
            return;
        }

        // Load Template
        const workbook = new ExcelJS.Workbook();
        await workbook.xlsx.readFile(templatePath);
        const worksheet = workbook.worksheets[0]; // report_type =1 daily. 2 - monthly , 3- Yearly

        const selectedSheetName = worksheet.name;

        // Remove all other sheets except the selected one
        workbook.worksheets.forEach(sheet => {
            if (sheet.name !== selectedSheetName) {
                workbook.removeWorksheet(sheet.id);
            }
        });


        // Organize Data
        let groupedData = getgroupeddata(data, report_type);

        // console.log(groupedData);

        worksheet.getCell('C2').value = plant_name;
        switch (report_type) {
            case 1:
                worksheet.getCell('C3').value = 'Daily Report';
                worksheet.getCell('C4').value = 'Date: ' + start_time.split('T')[0];
                break;
            default:
        }

        const originalWidth = worksheet.getColumn(5).width; // Default width if undefined
        if (emIDArray.length > 1) {

            copyFormatAndText(worksheet, worksheet, "E5:E34", 5, ((emIDArray.length - 1) * 3) + 1, false, 1, 0, 5);
            // console.log(originalWidth);
            worksheet.getColumn(5 + ((dataColumnsLength + 1) * (emIDArray.length - 1))).width = originalWidth;
            //  console.log(worksheet.getColumn(5 + (dataColumnsLength * emIDArray.length)).width);

            copyMergedCells(worksheet, worksheet, 5, 5 + ((dataColumnsLength + 1) * (emIDArray.length - 1)), 1, 1, 5, 32);

            safeUnmerge(worksheet, 'E5:E32');
            //    worksheet.spliceColumns(5, 1); // remove  columns before total
        }
        // Populate Rows

        let startRow = 7; // Assuming data starts from row 7 in template
        Object.keys(groupedData).forEach(datetime => {
            let row = worksheet.getRow(startRow++);

            // The datetime is already in IST from getgroupeddata
            // For display in Excel, we need to convert back to UTC
            // because Excel will apply the local timezone offset
            const dateValue = new Date(new Date(datetime).getTime() - 330 * 60 * 1000);

            row.getCell(1).value = dateValue;


            let colIndex = 2;
            emIDArray.forEach(em => {
                row.getCell(colIndex++).value = groupedData[datetime][`${em}_CD_KWH`];
                colIndex++;
                row.getCell(colIndex++).value = groupedData[datetime][`${em}_AVGPF`];
            });
            row.commit();
        });

        // Modify this inside your function before populating rows
        if (emIDArray.length > 1) {

            const firstEMColumn = 2; // B column is index
            //  console.log(dataColumnsLength);
            copyFormatAndText(worksheet, worksheet, "B5:D6", firstEMColumn + dataColumnsLength + 1, emIDArray.length - 1, false, dataColumnsLength + 1, 0);
            if (report_type === 1) {
                copyFormatAndText(worksheet, worksheet, "B32:D32", firstEMColumn + dataColumnsLength + 1, emIDArray.length - 1, false, dataColumnsLength + 1, 0);
                copyFormatAndText(worksheet, worksheet, "B7:D31", firstEMColumn + dataColumnsLength + 1, emIDArray.length - 1, true, dataColumnsLength + 1, 0);
            }
            copyMergedCells(worksheet, worksheet, firstEMColumn, firstEMColumn + dataColumnsLength + 1, emIDArray.length - 1, 2, 5, 6, 1);
            copyMergedCells(worksheet, worksheet, firstEMColumn, firstEMColumn + dataColumnsLength + 1, emIDArray.length - 1, 2, 7, 31, 1);

            copyColumnLength(worksheet, 2, 4, emIDArray.length - 1);

        }

        

        writeEMNames(worksheet, emNameArray, dataColumnsLength + 1, 5);

        expandMergedCells(worksheet, emIDArray, 3, 'D', 'C', 1);
        moveproductioncells(worksheet, 2, 3, 33, 34, emIDArray);


        applyProductionFormula(worksheet, 1 + (emIDArray.length * 3), 34, 32, 33);

        applyMergedCellFormula(worksheet, 3, 7, 34, (emIDArray.length - 1), 3);

        const lastcolumnId = 2 + (emIDArray.length * 3);
        applyMergedCellFormula(worksheet, lastcolumnId, 7, 31, (emIDArray.length - 1), dataColumnsLength + 1, 'sum', lastcolumnId);


        
        console.log('report_type',  report_type);
        if (report_type === 1) {
        addFormulaForRow(worksheet,31 , 'sum', 3, dataColumnsLength + 1, 7, 0,null,null,8-1);
       
        }

        finalTotalFormula(worksheet, lastcolumnId, 32, 7);

        const lastColumnLetter = worksheet.getColumn(lastcolumnId - 1).letter;
        worksheet.getCell(`${lastColumnLetter}32`).style = { ...worksheet.getCell(`${lastColumnLetter}33`).style };

        applyThickBorderToSheet(worksheet, 7, 0);


        arrayunlockedcells = [`${worksheet.getColumn(lastcolumnId - 1).letter}33`];

        if (protectsheet) {
            await protectsheetfn(worksheet, arrayunlockedcells);
        }


        // Save File
        await workbook.xlsx.writeFile(filePath); // Save to the provided filePath

        console.log(`Report generated successfully: ${filePath}`);
    } catch (error) {
        console.error('Error generating report:', error);
    }
}

async function generateOutgoingReport(start_time, em_ids, em_names, parameterArray, plant_name, report_type, protectsheet = false, arrayunlockedcells = [], filePath, templatePath) {
    try {
        const emIDArray = em_ids.split(','); // Convert to array
        const emNameArray = em_names.split(',');
        const dataColumnsLength = parameterArray.length;

        let endTime;
        const startDate = new Date(start_time); // Add 5 hours 30 minutes (330 minutes)

        if (report_type === 1) { // Daily
            endTime = startDate;
            endTime.setHours(23, 59, 59);
        } else if (report_type === 2) { // Monthly
            endTime = new Date(startDate.getFullYear(), startDate.getMonth() + 1, 0); // Last day of the month
            endTime.setHours(23, 59, 59);
        } else if (report_type === 3) { // Yearly
            endTime = new Date(startDate.getFullYear(), 11, 31); // December 31st
            endTime.setHours(23, 59, 59);
        }

        // Add 5 hours and 30 minutes to end_time
        endTime = new Date(endTime.getTime() + 330 * 60 * 1000);

        const end_time = endTime.toISOString();
        // console.log(end_time);

        // Connect to MSSQL
        await sql.connect(dbConfig);
        // Query to Fetch Data
        let query;

        if (report_type === 2) {
            query = getmonthlyquery(start_time, end_time, emIDArray, parameterArray);
        }
        else if (report_type === 3) {
            query = getyearlyquery(start_time, end_time, emIDArray, parameterArray);
        }
        else {
            query = getdailyquery(start_time, end_time, emIDArray, parameterArray);
        }



        const result = await sql.query(query);
        const data = result.recordset;
        if (data.length === 0) {
            console.log('No data found');
            return;
        }

        // Load Template
        const workbook = new ExcelJS.Workbook();
        await workbook.xlsx.readFile(templatePath);
        const worksheet = workbook.worksheets[report_type - 2]; // report_type =1 daily. 2 - monthly , 3- Yearly

        const selectedSheetName = worksheet.name;

        // Remove all other sheets except the selected one
        workbook.worksheets.forEach(sheet => {
            if (sheet.name !== selectedSheetName) {
                workbook.removeWorksheet(sheet.id);
            }
        });

        // Organize Data
        let groupedData = getgroupeddata(data, report_type);

        worksheet.getCell('C2').value = plant_name;
        switch (report_type) {
            case 1:
                worksheet.getCell('C3').value = 'Daily Report';
                worksheet.getCell('C4').value = 'Date: ' + start_time.split('T')[0];
                break;
            case 2:
                worksheet.getCell('C3').value = 'Monthly Report';
                worksheet.getCell('C4').value = 'Month: ' + start_time.split('T')[0].slice(0, -3);
                break;

            case 3:
                worksheet.getCell('C3').value = 'Yearly Report';
                worksheet.getCell('C4').value = 'Year: ' + start_time.split('T')[0].slice(0, -6);
                break;
            default:
        }


        const originalWidth = worksheet.getColumn(3).width; // Default width if undefined
        if (emIDArray.length > 1) {
            if (report_type == 2) {
                copyFormatAndText(worksheet, worksheet, "C5:C37", 3, ((emIDArray.length - 1) * 1) + 1, false, 1, 0, 3);
            }
            else if (report_type == 3) {
                copyFormatAndText(worksheet, worksheet, "C5:C18", 3, ((emIDArray.length - 1) * 1) + 1, false, 1, 0, 3);
            }
            // console.log(originalWidth);
            worksheet.getColumn(3 + ((dataColumnsLength) * (emIDArray.length - 1))).width = originalWidth;
            //  console.log(worksheet.getColumn(3 + ((dataColumnsLength ) * (emIDArray.length - 1))),originalWidth);

            copyMergedCells(worksheet, worksheet, 3, 3 + ((dataColumnsLength) * (emIDArray.length - 1)), 1, 1, 5, 37);

            safeUnmerge(worksheet, 'C5:C37');
        }


        // Populate Rows
        let startRow = 7; // Assuming data starts from row 7 in template
        Object.keys(groupedData).forEach(datetime => {
            let row = worksheet.getRow(startRow++);

            // The datetime is already in IST from getgroupeddata
            // For display in Excel, we need to convert back to UTC
            // because Excel will apply the local timezone offset
            let dateValue = new Date(new Date(datetime).getTime() - 330 * 60 * 1000);

            // For yearly reports, we want to keep the IST time
            if (report_type === 3) {
                dateValue = new Date(datetime);
            }

            row.getCell(1).value = dateValue;
            if (report_type === 2)
                row.getCell(1).numFmt = 'yyyy-mm-dd';

            let colIndex = 2;
            emIDArray.forEach(em => {
                row.getCell(colIndex++).value = groupedData[datetime][`${em}_CD_KWH`];
            });
            row.commit();
        });

        if (emIDArray.length > 1) {

            const firstEMColumn = 2; // B column is index 2
            copyFormatAndText(worksheet, worksheet, "B5:B6", firstEMColumn + dataColumnsLength, emIDArray.length - 1, false, dataColumnsLength, 0, 2);
            if (report_type === 2) {
                copyFormatAndText(worksheet, worksheet, "B7:B37", firstEMColumn + dataColumnsLength, emIDArray.length, true, dataColumnsLength, 0, 2);
            } else if (report_type === 3) {
                copyFormatAndText(worksheet, worksheet, "B7:B18", firstEMColumn + dataColumnsLength, emIDArray.length, true, dataColumnsLength, 0, 2);

            }
            copyColumnLength(worksheet, 2, 2, emIDArray.length - 1);

        }


        if (report_type === 2) {

            const totalDaysInMonth = new Date(startDate.getFullYear(), startDate.getMonth() + 1, 0).getDate();
            if (totalDaysInMonth < 31) {
                //for (let i = 7 + totalDaysInMonth; i <= 37; i++) {
                //  worksheet.getRow(i).hidden = true;
                //}
                const startRow = 7 + totalDaysInMonth;
                const deleteCount = 37 - startRow + 1;
                console.log('Deleting rows:', startRow, 'count', deleteCount);


                worksheet.spliceRows(startRow, deleteCount, [], []);
                //}
            }

        }
        if (report_type === 3) {
            //   addFormulaForRow(worksheet, 19, 'sum');

        }
        writeEMNames(worksheet, emNameArray, 1, 5);
        expandMergedCells(worksheet, emIDArray, dataColumnsLength, 'C', 'C', 1);
        const lastcolumnId = 2 + (emIDArray.length * dataColumnsLength);

        let noofDatarows;
        if (report_type == 2) {
            const totalDaysInMonth = new Date(startDate.getFullYear(), startDate.getMonth() + 1, 0).getDate();
            noofDatarows = totalDaysInMonth;
            for (let rowIndex = 7; rowIndex <= 7 + noofDatarows - 1; rowIndex++) {
                const cell = worksheet.getCell(`A${rowIndex}`);
                cell.numFmt = 'yyyy-mm-dd';
            }
        } else if (report_type == 3) {
            noofDatarows = Object.keys(groupedData).length;
            for (let rowIndex = 7; rowIndex <= 7 + noofDatarows - 1; rowIndex++) {
                const cell = worksheet.getCell(`A${rowIndex}`);
                cell.numFmt = 'mmmm-yyyy';
            }
        }

        totalLastColumns(worksheet, emIDArray, 7, 7 + noofDatarows - 1, lastcolumnId);
        applyThickBorderToSheet(worksheet, 7, 0, 7 + noofDatarows - 1);
        applyThinBorderToRange(worksheet, 1, 7, 2, 7 + noofDatarows - 2);

        const lastRow = 7 + noofDatarows - 1;
        const cellsArray = [`A${lastRow}`, `B${lastRow}`];
        applyThickToBottom(worksheet, cellsArray);



        if (protectsheet) {
            await protectsheetfn(worksheet, []);
        }


        // Save File
        await workbook.xlsx.writeFile(filePath); // Save to the provided filePath

        console.log(`Report generated successfully: ${filePath}`);
    } catch (error) {
        console.error('Error generating report:', error);
    }
}


async function generateAllFeederReport(start_time, em_ids, em_names, parameterArray, plant_name, report_type, protectsheet = false, arrayunlockedcells = [], filePath, templatePath) {
    try {
        const emIDArray = em_ids.split(','); // Convert to array
        const emNameArray = em_names.split(',');
        const dataColumnsLength = parameterArray.length;

        let endTime;
        const startDate = new Date(start_time); // Add 5 hours 30 minutes (330 minutes)

        if (report_type === 1) { // Daily
            endTime = startDate;
            endTime.setHours(23, 59, 59);
        } else if (report_type === 2) { // Monthly
            endTime = new Date(startDate.getFullYear(), startDate.getMonth() + 1, 0); // Last day of the month
            endTime.setHours(23, 59, 59);
        } else if (report_type === 3) { // Yearly
            endTime = new Date(startDate.getFullYear(), 11, 31); // December 31st
            endTime.setHours(23, 59, 59);
        }

        // Add 5 hours and 30 minutes to end_time
        endTime = new Date(endTime.getTime() + 330 * 60 * 1000);

        const end_time = endTime.toISOString();
        // console.log(end_time);

        // Connect to MSSQL
        await sql.connect(dbConfig);
        // Query to Fetch Data
        let query;

        query = getmonthlyAllFeeders(start_time, end_time, emIDArray, parameterArray);



        const result = await sql.query(query);
        const data = result.recordset;
        if (data.length === 0) {
            console.log('No data found');
            return;
        }

        // Load Template
        const workbook = new ExcelJS.Workbook();
        await workbook.xlsx.readFile(templatePath);
        const worksheet = workbook.worksheets[report_type - 1]; // report_type =1 daily. 2 - monthly , 3- Yearly


        // Organize Data
        let groupedData = getgroupeddata(data, report_type);

        worksheet.getCell('C2').value = plant_name;
        switch (report_type) {
            case 1:
                worksheet.getCell('C3').value = 'Daily Report';
                worksheet.getCell('C4').value = 'Date: ' + start_time.split('T')[0];
                break;
            case 2:
                worksheet.getCell('C3').value = 'Monthly Report';
                worksheet.getCell('C4').value = 'Month: ' + start_time.split('T')[0].slice(0, -3);
                break;

            case 3:
                worksheet.getCell('C3').value = 'Yearly Report';
                worksheet.getCell('C4').value = 'Year: ' + start_time.split('T')[0].slice(0, -6);
                break;
            default:
        }


        const originalWidth = worksheet.getColumn(2).width; // Default width if undefined
        if (emIDArray.length > 1) {
            if (report_type == 2) {
                copyFormatAndText(worksheet, worksheet, "B5:C37", 2 + dataColumnsLength, (emIDArray.length - 1), false, dataColumnsLength, 0, 2);
            }
            worksheet.getColumn(3 + ((dataColumnsLength) * (emIDArray.length - 1))).width = originalWidth;
            copyMergedCells(worksheet, worksheet, 2, 2 + (dataColumnsLength), dataColumnsLength * (emIDArray.length - 1), dataColumnsLength, 5, 6, 0);
        }


        // Populate Rowsconst
        let worksheet2 = workbook.worksheets[0];

        let startRow = 7; // Assuming data starts from row 7 in template
        Object.keys(groupedData).forEach(datetime => {
            let row = worksheet.getRow(startRow++);

            // The datetime is already in IST from getgroupeddata
            // For display in Excel, we need to convert back to UTC
            // because Excel will apply the local timezone offset
            let dateValue = new Date(new Date(datetime).getTime() - 330 * 60 * 1000);

            // For yearly reports, we want to keep the IST time
            if (report_type === 3) {
                dateValue = new Date(datetime);
            }

            row.getCell(1).value = dateValue;
            if (report_type === 2)
                row.getCell(1).numFmt = 'yyyy-mm-dd';


            let colIndex = 2;
            emIDArray.forEach(em => {
                row.getCell(colIndex++).value = groupedData[datetime][`${em}_CD_KWH`];
                row.getCell(colIndex++).value = groupedData[datetime][`${em}_AVGPF`];

            });
            row.commit();
        });

        if (emIDArray.length > 1) {

            const firstEMColumn = 2; // B column is index 2
            copyFormatAndText(worksheet, worksheet, "B5:C6", firstEMColumn + dataColumnsLength, emIDArray.length - 1, false, dataColumnsLength, 0, 2);
            if (report_type === 2) {
                copyFormatAndText(worksheet, worksheet, "B7:C37", firstEMColumn + dataColumnsLength, emIDArray.length - 1, true, dataColumnsLength, 0, 2);
            }
            copyColumnLength(worksheet, 2, 3, emIDArray.length - 1);

        }

        const totalDaysInMonth = new Date(startDate.getFullYear(), startDate.getMonth() + 1, 0).getDate();

        if (report_type === 2) {

            if (totalDaysInMonth < 31) {
                //for (let i = 7 + totalDaysInMonth; i <= 37; i++) {
                //  worksheet.getRow(i).hidden = true;
                //}
                const startRow = 7 + totalDaysInMonth;
                const deleteCount = 37 - startRow + 1;
                //                console.log('Deleting rows:', startRow, 'count', deleteCount);


                worksheet.spliceRows(startRow, deleteCount, [], []);
                //}
            }

        }

        writeEMNames(worksheet, emNameArray, dataColumnsLength, 5);
        expandMergedCells(worksheet, emIDArray, dataColumnsLength, 'C', 'C', 0);
        const lastcolumnId = 2 + (emIDArray.length * dataColumnsLength);

        let noofDatarows;
        if (report_type == 2) {
            const totalDaysInMonth = new Date(startDate.getFullYear(), startDate.getMonth() + 1, 0).getDate();
            noofDatarows = totalDaysInMonth;
            for (let rowIndex = 7; rowIndex <= 7 + noofDatarows - 1; rowIndex++) {
                const cell = worksheet.getCell(`A${rowIndex}`);
                const cell2 = worksheet2.getCell(`A${rowIndex}`);
                cell.numFmt = 'yyyy-mm-dd';
                cell2.numFmt = 'yyyy-mm-dd';
            }
        }

        //  totalLastColumns(worksheet, emIDArray, 7, 7 + noofDatarows - 1, lastcolumnId);

        worksheet.getCell('C5').style = {
            ...worksheet.getCell('C5').style,
            border: {
                ...worksheet.getCell('C5').style.border,
                right: { style: 'thin' }
            }
        };

        applyThickBorderToSheet(worksheet, 7 - 1, 0, 7 + noofDatarows - 1);
        applyThinBorderToRange(worksheet, 1, 7, 3, 7 + noofDatarows - 2);

        const lastRow = 7 + noofDatarows - 1;
        const cellsArray = [`A${lastRow}`, `B${lastRow}`, `C${lastRow}`];
        applyThickToBottom(worksheet, cellsArray);


        const TotalFeeders = emIDArray.length;
        worksheet2.getCell('C4').value = 'Month: ' + start_time.split('T')[0].slice(0, -3);
        worksheet2.getCell('B5').value = `No. of Total Feeder = ${TotalFeeders}`;

        // Add formulas to columns B and C for rows 7 to 7 + noofDatarows - 1
        const startColumn = 'B';
        const startColumnIndex = startColumn.charCodeAt(0) - 64; // 'A' is 1, 'B' is 2, etc.
        const offset = (emIDArray.length * dataColumnsLength) - 1;
        const endColumnIndex = startColumnIndex + offset;
        const endColumn = getExcelColumnName(endColumnIndex);
        // console.log(startColumn, endColumn,startColumn.charCodeAt(0) + (emIDArray.length * dataColumnsLength) - 1);
        for (let rowIndex = 7; rowIndex <= 7 + noofDatarows - 1; rowIndex++) {
            // Formula for column B
            // endColumn.
            //
            worksheet2.getCell(`B${rowIndex}`).value = {
                formula: `IF(MAX(LEN(FeederWise!$B${rowIndex}:$${endColumn}${rowIndex}))>0,SUMIFS(FeederWise!$B${rowIndex}:$${endColumn}${rowIndex},FeederWise!$B$6:$${endColumn}$6,"KWH"),"")`
            };


            worksheet2.getCell(`A${rowIndex}`).value = {
                formula: `IF(ISNUMBER(FeederWise!$A${rowIndex}), FeederWise!$A${rowIndex}, "")`
            };




            // Formula for column C
            const startColumnIndex = startColumn.charCodeAt(0) - 64; // 'A' is 1, 'B' is 2, etc.

            let weightedSumFormula = '';
            for (let col = 0; col < emIDArray.length * dataColumnsLength; col += 2) {
                const col1Index = startColumnIndex + col;
                const col2Index = startColumnIndex + col + 1;
                const col1 = getExcelColumnName(col1Index);
                const col2 = getExcelColumnName(col2Index);
                weightedSumFormula += `(FeederWise!${col1}${rowIndex}*FeederWise!${col2}${rowIndex})+`;
            }

            weightedSumFormula = weightedSumFormula.slice(0, -1); // Remove trailing '+'
            // // Remove trailing '+'
            worksheet2.getCell(`C${rowIndex}`).value = {
                formula: `IF(ISNUMBER(ROUND((${weightedSumFormula})/B${rowIndex},3)),ROUND((${weightedSumFormula})/B${rowIndex},3),"")`
            };
        }

        if (report_type === 2) {

            if (totalDaysInMonth < 31) {
                //for (let i = 7 + totalDaysInMonth; i <= 37; i++) {
                //  worksheet.getRow(i).hidden = true;
                //}
                const startRow = 7 + totalDaysInMonth;
                const deleteCount = 37 - startRow + 1;
                //                console.log('Deleting rows:', startRow, 'count', deleteCount);


                worksheet2.spliceRows(startRow, deleteCount, [], []);
                //}
            }

        }


        applyThickBorderToSheet(worksheet2, 7, 0, 7 + noofDatarows - 1);
        applyThinBorderToRange(worksheet2, 1, 6, 3, 6 + noofDatarows - 1);

        applyThickToBottom(worksheet2, cellsArray);

        const lastColumnCells = [];
        for (let rowIndex = 6; rowIndex <= 6 + noofDatarows; rowIndex++) {
            lastColumnCells.push(`${String.fromCharCode('A'.charCodeAt(0) + (2))}${rowIndex}`);
        }

        applyThickToRight(worksheet2, lastColumnCells);


        if (protectsheet) {
            await protectsheetfn(worksheet, []);
            await protectsheetfn(worksheet2, []);
        }


        // Save File
        await workbook.xlsx.writeFile(filePath); // Save to the provided filePath

        console.log(`Report generated successfully: ${filePath}`);
    } catch (error) {
        console.error('Error generating report:', error);
    }
}

async function getIncomerReportData(start_time, em_ids, em_names, parameterArray, plant_name, report_type) {
    return await getReportData(start_time, em_ids, em_names, parameterArray, plant_name, report_type, 'Incomer');
}

async function getShiftReportData(start_time, em_ids, em_names, parameterArray, plant_name, report_type) {
    return await getReportData(start_time, em_ids, em_names, parameterArray, plant_name, report_type, 'Shift');
}

async function getOutgoingReportData(start_time, em_ids, em_names, parameterArray, plant_name, report_type) {
    return await getReportData(start_time, em_ids, em_names, parameterArray, plant_name, report_type, 'Outgoing');
}

async function getAllFeederReportData(start_time, em_ids, em_names, parameterArray, plant_name, report_type) {
    return await getReportData(start_time, em_ids, em_names, parameterArray, plant_name, report_type, 'All Feeder');
}

async function getReportData(start_time, em_ids, em_names, parameterArray, plant_name, report_type, report_name) {
    try {
        const emIDArray = em_ids.split(',');
        const emNameArray = em_names.split(',');
        const dataColumnsLength = parameterArray.length;

        let endTime;
        const startDate = new Date(start_time);

        if (report_type === 1) {
            endTime = startDate;
            endTime.setHours(23, 59, 59);
        } else if (report_type === 2) {
            endTime = new Date(startDate.getFullYear(), startDate.getMonth() + 1, 0);
            endTime.setHours(23, 59, 59);
        } else if (report_type === 3) {
            endTime = new Date(startDate.getFullYear(), 11, 31);
            endTime.setHours(23, 59, 59);
        }

        endTime = new Date(endTime.getTime() + 330 * 60 * 1000);
        const end_time = endTime.toISOString();

        await sql.connect(dbConfig);

        let query;
        if (report_name === 'All Feeder') {
            query = getmonthlyAllFeeders(start_time, end_time, emIDArray, parameterArray);
        } else if (report_type === 2) {
            query = getmonthlyquery(start_time, end_time, emIDArray, parameterArray);
        } else if (report_type === 3) {
            query = getyearlyquery(start_time, end_time, emIDArray, parameterArray);
        } else {
            query = getdailyquery(start_time, end_time, emIDArray, parameterArray);
        }

        const result = await sql.query(query);
        const data = result.recordset;

        if (data.length === 0) {
            console.log('No data found');
            return [];
        }

        // Title
        let title = `${plant_name} ${report_name} Report`;
        if (report_type === 1) {
            title += ` - Date: ${start_time.split('T')[0]}`;
        } else if (report_type === 2) {
            title += ` - Month: ${start_time.split('T')[0].slice(0, -3)}`;
        } else if (report_type === 3) {
            title += ` - Year: ${start_time.split('T')[0].slice(0, -6)}`;
        }

        // Header Rows
        const headerRow1 = [''];
        const headerRow2 = ['Date/Time'];

        emNameArray.forEach((emName) => {
            headerRow1.push(...Array(dataColumnsLength).fill(emName));
            headerRow2.push(...parameterArray);
        });

        // Data Rows
        const groupedData = {};
        data.forEach(row => {
            if (!groupedData[row.DateAndTime]) {
                groupedData[row.DateAndTime] = {};
            }
            let value = row.TagName.endsWith('PF') ? parseFloat(row.Val).toFixed(3) : parseFloat(row.Val).toFixed(2);

            groupedData[row.DateAndTime][row.TagName] = value;
        });

        const dataRows = [];
        Object.keys(groupedData).forEach(datetime => {
            const row = [];
            // The datetime is already in IST from getgroupeddata
            const dateValue = new Date(datetime);

            if (report_type === 1) {
                // For display, convert back to UTC to match Excel's behavior
                const adjustedDateValue = new Date(dateValue.getTime() - 330 * 60 * 1000);
                const formattedDate = `${adjustedDateValue.getFullYear()}-${String(adjustedDateValue.getMonth() + 1).padStart(2, '0')}-${String(adjustedDateValue.getDate()).padStart(2, '0')} ${String(adjustedDateValue.getHours()).padStart(2, '0')}:${String(adjustedDateValue.getMinutes()).padStart(2, '0')}`;
                row.push(formattedDate);
            } else if (report_type === 2) {
                // For monthly reports, just use the date part
                const adjustedDateValue = new Date(dateValue.getTime() - 330 * 60 * 1000);
                row.push(adjustedDateValue.toISOString().split('T')[0]);
            } else if (report_type === 3) {
                // For yearly reports, use the month and year
                const formattedDate = dateValue.toLocaleString('en-US', { month: 'short', year: 'numeric' });
                row.push(formattedDate);
            }

            emIDArray.forEach(em => {
                parameterArray.forEach(param => {
                    row.push(groupedData[datetime][`${em}_${param}`] || null);
                });
            });

            dataRows.push(row);
        });

        return {
            title,
            headers: [headerRow1, headerRow2],
            data: dataRows
        };
    } catch (error) {
        console.error('Error generating report data:', error);
        return [];
    }
}

function getgroupeddata(data, report_type) {
    const groupedData = {};
    data.forEach(row => {
        // Add 5 hours 30 minutes (330 minutes) to convert to IST
        const adjustedDateTime = new Date(new Date(row.DateAndTime).getTime() + 330 * 60 * 1000);

        if (!groupedData[adjustedDateTime]) {
            groupedData[adjustedDateTime] = {};
        }
        let value = parseFloat(row.Val);
        if (row.TagName.endsWith('PF')) {
            value = Math.round(value * 1000) / 1000;
        } else {
            value = Math.round(value * 100) / 100;
        }
        groupedData[adjustedDateTime][row.TagName] = value;
    });

    // Pass the IST-adjusted data to padAndSortGroupedData
    return padAndSortGroupedData(groupedData, report_type);
}


function padAndSortGroupedData(groupedData, report_type) {


    // 1. Find all unique keys
    const allKeysSet = new Set();
    Object.values(groupedData).forEach(obj => {
        Object.keys(obj).forEach(key => allKeysSet.add(key));
    });
    const allKeys = Array.from(allKeysSet);
    // console.log('allKeys',  allKeys);
    // 2. Set minDate and maxDate based on report_type, not just data
    let minDate, maxDate, step;

    const dateKeys = Object.keys(groupedData).map(key => new Date(key));
    if (dateKeys <= 0) {
        return groupedData;
    }
    minDate = new Date(Math.min(...dateKeys));
    maxDate = new Date(Math.max(...dateKeys));


    if (report_type === 1) { // Daily
        step = 60 * 60 * 1000; // 1 hour in milliseconds
    } else if (report_type === 2) { // Monthly
        step = 24 * 60 * 60 * 1000; // 1 day in milliseconds
    } else if (report_type === 3) { // Yearly
        step = 'month';
    }

    // 3. Pad missing times with all keys set to null
    let current = new Date(minDate.getTime());
    while (current <= maxDate) {
        // For monthly reports, ensure each day has the same time (00:00)
        let paddingDate = new Date(current);

        const displayKey = paddingDate.toString();
        if (!groupedData[displayKey]) {
            groupedData[displayKey] = {};
            allKeys.forEach(key => {
                groupedData[displayKey][key] = null;
            });
        } else {
            allKeys.forEach(key => {
                if (!(key in groupedData[displayKey])) {
                    groupedData[displayKey][key] = null;
                }
            });
        }

        // Just add the step without additional time adjustment
        // since we're already working with IST times
        current = new Date(current.getTime() + step);

    }

    // 4. Sort keys and rebuild object in order
    const sortedKeys = Object.keys(groupedData).sort((a, b) => new Date(a) - new Date(b));
    const sortedGroupedData = {};
    sortedKeys.forEach(key => {
        sortedGroupedData[key] = groupedData[key];
    });

    return sortedGroupedData;
}

function getExcelColumnName(colIndex) {
    let dividend = colIndex;
    let columnName = '';
    while (dividend > 0) {
        let modulo = (dividend - 1) % 26;
        columnName = String.fromCharCode(65 + modulo) + columnName;
        dividend = Math.floor((dividend - modulo) / 26);
    }
    return columnName;
}

module.exports = {
    generateIncomerReport,
    generateShiftReport,
    generateOutgoingReport,
    generateAllFeederReport,
    getIncomerReportData,
    getShiftReportData,
    getOutgoingReportData,
    getAllFeederReportData
};