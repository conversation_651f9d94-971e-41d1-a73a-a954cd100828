const Location = require('../models/locationModel');

// Add a new location
exports.addLocation = async (req, res) => {
    try {
        const { name, description } = req.body;
        const newLocation = new Location({ name, description });
        await newLocation.save();
        res.status(201).json({ message: 'Location added successfully', location: newLocation });
    } catch (error) {
        res.status(500).json({ message: 'Error adding location', error });
    }
};

// Get all locations
exports.getAllLocations = async (req, res) => {
    try {
        const locations = await Location.findAll();
        res.status(200).json({ data: locations });
    } catch (error) {
        res.status(500).json({ message: 'Error retrieving locations', error });
    }
};

// Get a location by ID
exports.getLocationById = async (req, res) => {
    try {
        const location = await Location.findById(req.params.id);
        if (!location) {
            return res.status(404).json({ message: 'Location not found' });
        }
        res.status(200).json(location);
    } catch (error) {
        res.status(500).json({ message: 'Error retrieving location', error });
    }
};

// Update a location
exports.updateLocation = async (req, res) => {
    try {
        const { name, description } = req.body;
        //  console.log(name, description);
        const [updatedRowsCount] = await Location.update(
            { name, description },
            { where: { id: req.params.id } }
        );

        if (updatedRowsCount === 0) {
            return res.status(404).json({ message: 'Location not found' });
        }

        // Fetch the updated record manually
        const updatedLocation = await Location.findByPk(req.params.id);

        res.status(200).json({ message: 'Location updated successfully', location: updatedLocation });

    } catch (error) {
        res.status(500).json({ message: 'Error updating location', error });
    }
};

// Delete a location
exports.deleteLocation = async (req, res) => {
    try {
        const deletedRowsCount = await Location.destroy({
            where: { id: req.params.id }
        });

        if (deletedRowsCount === 0) {
            return res.status(404).json({ message: 'Location not found' });
        }

        res.status(200).json({ message: 'Location deleted successfully' });
    } catch (error) {
        res.status(500).json({ message: 'Error deleting location', error });
    }
};

// Get plant name by location ID
exports.getPlantNameByLocationId = async (locationId) => {
    try {
        const location = await Location.findByPk(locationId);
        if (!location) {
            throw new Error('Location not found');
        }
        return location.name;
    } catch (error) {
        throw new Error(`Error retrieving plant name: ${error.message}`);
    }
};