import{a as i,_ as m,R as C,b as p,c as N,P as a,d as n}from"./index-BDJ8oeCE.js";var x=i.forwardRef(function(r,o){var e,s=r.children,t=r.className,c=r.color,l=r.textBgColor,d=r.textColor,f=m(r,["children","className","color","textBgColor","textColor"]);return C.createElement("div",p({className:N("card",(e={},e["bg-".concat(c)]=c,e["text-".concat(d)]=d,e["text-bg-".concat(l)]=l,e),t)},f,{ref:o}),s)});x.propTypes={children:a.node,className:a.string,color:n,textBgColor:n,textColor:a.string};x.displayName="CCard";var g=i.forwardRef(function(r,o){var e=r.children,s=r.className,t=m(r,["children","className"]);return C.createElement("div",p({className:N("card-body",s)},t,{ref:o}),e)});g.propTypes={children:a.node,className:a.string};g.displayName="CCardBody";export{x as C,g as a};
