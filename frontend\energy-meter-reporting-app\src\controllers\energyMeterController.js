const EnergyMeter = require('../models/energyMeterModel');
const { sequelize } = require('../utils/db'); // Assuming Sequelize is used for database connection

// Add a new energy meter
exports.addEnergyMeter = async (req, res) => {
    try {
        const { tagName, feederName } = req.body;
        const newEnergyMeter = await EnergyMeter.create({ tagName, feederName });
        res.status(201).json({ message: 'Energy meter added successfully', data: newEnergyMeter });
    } catch (error) {
        res.status(500).json({ message: 'Error adding energy meter', error });
    }
};

// Update an existing energy meter
exports.updateEnergyMeter = async (req, res) => {
    try {
        const { tagName, feederName } = req.body;
        const [updatedRowsCount] = await EnergyMeter.update(
            { tagName, feederName },
            { where: { id: req.params.id } }
        );

        if (updatedRowsCount === 0) {
            return res.status(404).json({ message: 'Energy meter not found' });
        }

        // Fetch the updated record manually
        const updatedEnergyMeter = await EnergyMeter.findByPk(req.params.id);

        res.status(200).json({ message: 'Energy meter updated successfully', data: updatedEnergyMeter });
    } catch (error) {
        res.status(500).json({ message: 'Error updating energy meter', error });
    }
};

// Retrieve all energy meters
exports.getAllEnergyMeters = async (req, res) => {
    try {
        const meters = await EnergyMeter.findAll();
        res.status(200).json({ message: 'Energy meters retrieved successfully', data: meters });
    } catch (error) {
        res.status(500).json({ message: 'Error retrieving energy meters', error });
    }
};

// Retrieve a specific energy meter by ID
exports.getEnergyMeterById = async (req, res) => {
    try {
        const meter = await EnergyMeter.findByPk(req.params.id);
        if (!meter) {
            return res.status(404).json({ message: 'Energy meter not found' });
        }
        res.status(200).json({ message: 'Energy meter retrieved successfully', data: meter });
    } catch (error) {
        res.status(500).json({ message: 'Error retrieving energy meter', error });
    }
};

// Delete an energy meter
exports.deleteEnergyMeter = async (req, res) => {
    try {
        const deletedRowsCount = await EnergyMeter.destroy({
            where: { id: req.params.id }
        });

        if (deletedRowsCount === 0) {
            return res.status(404).json({ message: 'Energy meter not found' });
        }

        res.status(200).json({ message: 'Energy meter deleted successfully' });
    } catch (error) {
        res.status(500).json({ message: 'Error deleting energy meter', error });
    }
};

// Get energy meter names by IDs
exports.getEnergyMeterandFeederNamesByIds = async (ids) => {
    try {
        const meters = await EnergyMeter.findAll({ where: { id: ids } });
        return meters.map(meter => ({
            tagName: meter.tagName,
            feederName: meter.feederName
        }));
    } catch (error) {
        throw new Error(`Error retrieving energy meter Feeder names: ${error.message}`);
    }
};

exports.getEnergyMeterKWHDataFromDB = async (req, res) => {
    try {
        const { locationId, timeStart, timeEnd } = req.body;

        if (!locationId || !timeStart || !timeEnd) {
            return res.status(400).json({ message: 'locationId, timeStart, and timeEnd are required' });
        }

        // Fetch energy meter IDs and tag names for the given locationId
        const energyMetersQuery = `
            SELECT em.[id] AS energyMeterId, em.[tagName]
            FROM [REPORTS_DB].[dbo].[Mappings] m
            JOIN [REPORTS_DB].[dbo].[EnergyMeters] em ON m.[energyMeterId] = em.[id]
            WHERE m.[locationId] = :locationId
        `;
        const energyMeters = await sequelize.query(energyMetersQuery, {
            replacements: { locationId },
            type: sequelize.QueryTypes.SELECT
        });

        if (energyMeters.length === 0) {
            return res.status(404).json({ message: 'No energy meters found for the given locationId' });
        }

        const tagNames = energyMeters.map(meter => meter.tagName.replace(/\s+/g, ''));

        if (tagNames.length === 0) {
            return res.status(404).json({ message: 'No tag names found for the energy meters' });
        }

        // Format tagNames into SQL-compatible values
        const tagNamesSQL = tagNames.map(tag => `('${tag}')`).join(', ');

        // Execute the main query to fetch CD_KWH data
        const kwhDataQuery = `
            DECLARE @TimeStart DATETIME = DATEADD(SECOND, -30, '${timeStart}');
            DECLARE @TimeEnd DATETIME = DATEADD(SECOND, +1, '${timeEnd}');
            DECLARE @EMIDs TABLE (EMID NVARCHAR(10));
            
            INSERT INTO @EMIDs (EMID) VALUES ${tagNamesSQL};
            
            DECLARE @Parameters TABLE (ParameterName NVARCHAR(50));
            INSERT INTO @Parameters (ParameterName) VALUES ('CD_KWH');
            
            WITH TagIndices AS (
                SELECT TagIndex, TagName FROM dbo.TagTable_01
                WHERE EXISTS (
                    SELECT 1 FROM @EMIDs e
                    JOIN @Parameters p ON TagName = e.EMID + '_' + p.ParameterName
                )
            ),
            HourlyData AS (
                SELECT 
                    CASE 
                        WHEN DATEPART(MINUTE, f.DateAndTime) >= 30 
                        THEN DATEADD(HOUR, DATEDIFF(HOUR, 0, f.DateAndTime) + 1, 0)  
                        ELSE DATEADD(HOUR, DATEDIFF(HOUR, 0, f.DateAndTime), 0)  
                    END AS RoundedDateTime,
                    f.DateAndTime AS ActualDateTime,
                    f.TagIndex,
                    f.Val,
                    ROW_NUMBER() OVER (
                        PARTITION BY t.TagName, 
                        CASE 
                            WHEN DATEPART(MINUTE, f.DateAndTime) >= 30 
                            THEN DATEADD(HOUR, DATEDIFF(HOUR, 0, f.DateAndTime) + 1, 0)  
                            ELSE DATEADD(HOUR, DATEDIFF(HOUR, 0, f.DateAndTime), 0)  
                        END 
                        ORDER BY ABS(DATEDIFF(SECOND, 
                            CASE 
                                WHEN DATEPART(MINUTE, f.DateAndTime) >= 30 
                                THEN DATEADD(HOUR, DATEDIFF(HOUR, 0, f.DateAndTime) + 1, 0)  
                                ELSE DATEADD(HOUR, DATEDIFF(HOUR, 0, f.DateAndTime), 0)  
                            END, f.DateAndTime))
                    ) AS rn
                FROM dbo.FloatTable_01 f
                JOIN TagIndices t ON f.TagIndex = t.TagIndex
                WHERE f.DateAndTime >= @TimeStart 
                AND f.DateAndTime < @TimeEnd
            ),
            FinalData AS (
                SELECT 
                    CASE 
                        WHEN DATEPART(MINUTE, h.ActualDateTime) >= 30 
                        THEN DATEADD(HOUR, DATEDIFF(HOUR, 0, h.ActualDateTime) + 1, 0)  
                        ELSE DATEADD(HOUR, DATEDIFF(HOUR, 0, h.ActualDateTime), 0)  
                    END AS DateAndTime, 
                    t.TagName, 
                    h.Val
                FROM HourlyData h
                JOIN TagIndices t ON h.TagIndex = t.TagIndex
                WHERE h.rn = 1
            )
            SELECT * FROM FinalData
            WHERE DateAndTime >= @TimeStart 
            AND DateAndTime < @TimeEnd
            ORDER BY DateAndTime, TagName;
        `;

        const kwhData = await sequelize.query(kwhDataQuery, {
            replacements: [timeStart, timeEnd],
            type: sequelize.QueryTypes.SELECT
        });

        res.status(200).json({ message: 'KWH data retrieved successfully', data: kwhData });
    } catch (error) {
        res.status(500).json({ message: 'Error retrieving KWH data', error });
    }
};