import{a as F,_ as y,R as s,b as r,c as I,P as a}from"./index-BDJ8oeCE.js";import{a as o}from"./CFormControlWrapper-CyzWU6Dg.js";var n=F.forwardRef(function(e,c){var b=e.children,f=e.className,p=e.feedback,m=e.feedbackInvalid,k=e.feedbackValid,v=e.floatingClassName,x=e.floatingLabel,l=e.id,i=e.invalid,g=e.label,N=e.plainText,C=e.text,T=e.tooltipFeedback,t=e.valid,d=y(e,["children","className","feedback","feedbackInvalid","feedbackValid","floatingClassName","floatingLabel","id","invalid","label","plainText","text","tooltipFeedback","valid"]);return s.createElement(o,{describedby:d["aria-describedby"],feedback:p,feedbackInvalid:m,feedbackValid:k,floatingClassName:v,floatingLabel:x,id:l,invalid:i,label:g,text:C,tooltipFeedback:T,valid:t},s.createElement("textarea",r({className:I(N?"form-control-plaintext":"form-control",{"is-invalid":i,"is-valid":t},f),id:l},d,{ref:c}),b))});n.propTypes=r({className:a.string,id:a.string,plainText:a.bool},o.propTypes);n.displayName="CFormTextarea";export{n as C};
