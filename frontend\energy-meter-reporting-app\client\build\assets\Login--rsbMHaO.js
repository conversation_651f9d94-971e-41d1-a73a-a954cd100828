import{a as t,A as y,j as e,q as h,v as b}from"./index-BDJ8oeCE.js";import{b as k,c as l,a as m}from"./index.esm-DSzlmaRN.js";import{C as d,a}from"./CRow-C1o2zw34.js";import{C as v}from"./CCardGroup-s55qtq2U.js";import{C as I,a as N}from"./CCardBody-iimbKiZ7.js";import{C as L}from"./CForm-C4rJo8l4.js";import{C as p,a as u}from"./CInputGroupText-BGHrT9V9.js";import{c as S}from"./cil-user-Ddrdy7PS.js";import{C as x}from"./CFormInput-LKfVdWds.js";import{c as E}from"./cil-lock-locked-DmxpJbVL.js";import"./CFormControlWrapper-CyzWU6Dg.js";import"./CFormControlValidation-_wBnnnml.js";import"./CFormLabel-CzXD3nfE.js";h.defaults.withCredentials=!0;const K=()=>{const[n,C]=t.useState(""),[i,j]=t.useState(""),[c,f]=t.useState(null),{setIsAuthenticated:r}=t.useContext(y);t.useEffect(()=>{localStorage.getItem("token")&&r(!0)},[r]);const g=async s=>{s.preventDefault();try{const o=await h.post(`${b.baseURL}/auth/login`,{username:n,password:i},{withCredentials:!0});if(o.status===200){const w=o.data.token;localStorage.setItem("token",w),r(!0);const U=new Date().getTime();window.location.replace("/")}}catch{f("Invalid username or password")}};return e.jsx("div",{className:"bg-body-tertiary min-vh-100 d-flex flex-row align-items-center",children:e.jsx(k,{children:e.jsx(d,{className:"justify-content-center",children:e.jsx(a,{md:4,children:e.jsx(v,{children:e.jsx(I,{className:"p-4",children:e.jsx(N,{children:e.jsxs(L,{onSubmit:g,children:[e.jsx("h1",{children:"Login"}),e.jsx("p",{className:"text-body-secondary",children:"Sign In to your account"}),e.jsxs(p,{className:"mb-3",children:[e.jsx(u,{children:e.jsx(l,{icon:S})}),e.jsx(x,{type:"text",placeholder:"Username",autoComplete:"username",value:n,onChange:s=>C(s.target.value),required:!0})]}),e.jsxs(p,{className:"mb-4",children:[e.jsx(u,{children:e.jsx(l,{icon:E})}),e.jsx(x,{type:"password",placeholder:"Password",autoComplete:"current-password",value:i,onChange:s=>j(s.target.value),required:!0})]}),c&&e.jsx("p",{style:{color:"red"},children:c}),e.jsxs(d,{children:[e.jsx(a,{xs:6,children:e.jsx(m,{type:"submit",color:"primary",className:"px-4",children:"Login"})}),e.jsx(a,{xs:6,className:"text-right",children:e.jsx(m,{color:"link",className:"px-0",children:"Forgot password?"})})]})]})})})})})})})})};export{K as default};
