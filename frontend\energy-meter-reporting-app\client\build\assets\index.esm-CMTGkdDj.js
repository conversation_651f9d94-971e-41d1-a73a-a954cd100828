var Ga=Object.defineProperty;var Xa=(e,t,i)=>t in e?Ga(e,t,{enumerable:!0,configurable:!0,writable:!0,value:i}):e[t]=i;var M=(e,t,i)=>Xa(e,typeof t!="symbol"?t+"":t,i);import{a as nt,R as bt}from"./index-BDJ8oeCE.js";/*!
 * @kurkle/color v0.3.4
 * https://github.com/kurkle/color#readme
 * (c) 2024 <PERSON><PERSON>
 * Released under the MIT License
 */function He(e){return e+.5|0}const Lt=(e,t,i)=>Math.max(Math.min(e,i),t);function Me(e){return Lt(He(e*2.55),0,255)}function It(e){return Lt(He(e*255),0,255)}function St(e){return Lt(He(e/2.55)/100,0,1)}function rs(e){return Lt(He(e*100),0,100)}const ct={0:0,1:1,2:2,3:3,4:4,5:5,6:6,7:7,8:8,9:9,A:10,B:11,C:12,D:13,E:14,F:15,a:10,b:11,c:12,d:13,e:14,f:15},cn=[..."0123456789ABCDEF"],Ka=e=>cn[e&15],qa=e=>cn[(e&240)>>4]+cn[e&15],Ue=e=>(e&240)>>4===(e&15),Za=e=>Ue(e.r)&&Ue(e.g)&&Ue(e.b)&&Ue(e.a);function Ja(e){var t=e.length,i;return e[0]==="#"&&(t===4||t===5?i={r:255&ct[e[1]]*17,g:255&ct[e[2]]*17,b:255&ct[e[3]]*17,a:t===5?ct[e[4]]*17:255}:(t===7||t===9)&&(i={r:ct[e[1]]<<4|ct[e[2]],g:ct[e[3]]<<4|ct[e[4]],b:ct[e[5]]<<4|ct[e[6]],a:t===9?ct[e[7]]<<4|ct[e[8]]:255})),i}const Qa=(e,t)=>e<255?t(e):"";function tl(e){var t=Za(e)?Ka:qa;return e?"#"+t(e.r)+t(e.g)+t(e.b)+Qa(e.a,t):void 0}const el=/^(hsla?|hwb|hsv)\(\s*([-+.e\d]+)(?:deg)?[\s,]+([-+.e\d]+)%[\s,]+([-+.e\d]+)%(?:[\s,]+([-+.e\d]+)(%)?)?\s*\)$/;function vr(e,t,i){const n=t*Math.min(i,1-i),s=(o,r=(o+e/30)%12)=>i-n*Math.max(Math.min(r-3,9-r,1),-1);return[s(0),s(8),s(4)]}function il(e,t,i){const n=(s,o=(s+e/60)%6)=>i-i*t*Math.max(Math.min(o,4-o,1),0);return[n(5),n(3),n(1)]}function nl(e,t,i){const n=vr(e,1,.5);let s;for(t+i>1&&(s=1/(t+i),t*=s,i*=s),s=0;s<3;s++)n[s]*=1-t-i,n[s]+=t;return n}function sl(e,t,i,n,s){return e===s?(t-i)/n+(t<i?6:0):t===s?(i-e)/n+2:(e-t)/n+4}function Tn(e){const i=e.r/255,n=e.g/255,s=e.b/255,o=Math.max(i,n,s),r=Math.min(i,n,s),a=(o+r)/2;let l,c,h;return o!==r&&(h=o-r,c=a>.5?h/(2-o-r):h/(o+r),l=sl(i,n,s,h,o),l=l*60+.5),[l|0,c||0,a]}function Dn(e,t,i,n){return(Array.isArray(t)?e(t[0],t[1],t[2]):e(t,i,n)).map(It)}function Ln(e,t,i){return Dn(vr,e,t,i)}function ol(e,t,i){return Dn(nl,e,t,i)}function rl(e,t,i){return Dn(il,e,t,i)}function Mr(e){return(e%360+360)%360}function al(e){const t=el.exec(e);let i=255,n;if(!t)return;t[5]!==n&&(i=t[6]?Me(+t[5]):It(+t[5]));const s=Mr(+t[2]),o=+t[3]/100,r=+t[4]/100;return t[1]==="hwb"?n=ol(s,o,r):t[1]==="hsv"?n=rl(s,o,r):n=Ln(s,o,r),{r:n[0],g:n[1],b:n[2],a:i}}function ll(e,t){var i=Tn(e);i[0]=Mr(i[0]+t),i=Ln(i),e.r=i[0],e.g=i[1],e.b=i[2]}function cl(e){if(!e)return;const t=Tn(e),i=t[0],n=rs(t[1]),s=rs(t[2]);return e.a<255?`hsla(${i}, ${n}%, ${s}%, ${St(e.a)})`:`hsl(${i}, ${n}%, ${s}%)`}const as={x:"dark",Z:"light",Y:"re",X:"blu",W:"gr",V:"medium",U:"slate",A:"ee",T:"ol",S:"or",B:"ra",C:"lateg",D:"ights",R:"in",Q:"turquois",E:"hi",P:"ro",O:"al",N:"le",M:"de",L:"yello",F:"en",K:"ch",G:"arks",H:"ea",I:"ightg",J:"wh"},ls={OiceXe:"f0f8ff",antiquewEte:"faebd7",aqua:"ffff",aquamarRe:"7fffd4",azuY:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"0",blanKedOmond:"ffebcd",Xe:"ff",XeviTet:"8a2be2",bPwn:"a52a2a",burlywood:"deb887",caMtXe:"5f9ea0",KartYuse:"7fff00",KocTate:"d2691e",cSO:"ff7f50",cSnflowerXe:"6495ed",cSnsilk:"fff8dc",crimson:"dc143c",cyan:"ffff",xXe:"8b",xcyan:"8b8b",xgTMnPd:"b8860b",xWay:"a9a9a9",xgYF:"6400",xgYy:"a9a9a9",xkhaki:"bdb76b",xmagFta:"8b008b",xTivegYF:"556b2f",xSange:"ff8c00",xScEd:"9932cc",xYd:"8b0000",xsOmon:"e9967a",xsHgYF:"8fbc8f",xUXe:"483d8b",xUWay:"2f4f4f",xUgYy:"2f4f4f",xQe:"ced1",xviTet:"9400d3",dAppRk:"ff1493",dApskyXe:"bfff",dimWay:"696969",dimgYy:"696969",dodgerXe:"1e90ff",fiYbrick:"b22222",flSOwEte:"fffaf0",foYstWAn:"228b22",fuKsia:"ff00ff",gaRsbSo:"dcdcdc",ghostwEte:"f8f8ff",gTd:"ffd700",gTMnPd:"daa520",Way:"808080",gYF:"8000",gYFLw:"adff2f",gYy:"808080",honeyMw:"f0fff0",hotpRk:"ff69b4",RdianYd:"cd5c5c",Rdigo:"4b0082",ivSy:"fffff0",khaki:"f0e68c",lavFMr:"e6e6fa",lavFMrXsh:"fff0f5",lawngYF:"7cfc00",NmoncEffon:"fffacd",ZXe:"add8e6",ZcSO:"f08080",Zcyan:"e0ffff",ZgTMnPdLw:"fafad2",ZWay:"d3d3d3",ZgYF:"90ee90",ZgYy:"d3d3d3",ZpRk:"ffb6c1",ZsOmon:"ffa07a",ZsHgYF:"20b2aa",ZskyXe:"87cefa",ZUWay:"778899",ZUgYy:"778899",ZstAlXe:"b0c4de",ZLw:"ffffe0",lime:"ff00",limegYF:"32cd32",lRF:"faf0e6",magFta:"ff00ff",maPon:"800000",VaquamarRe:"66cdaa",VXe:"cd",VScEd:"ba55d3",VpurpN:"9370db",VsHgYF:"3cb371",VUXe:"7b68ee",VsprRggYF:"fa9a",VQe:"48d1cc",VviTetYd:"c71585",midnightXe:"191970",mRtcYam:"f5fffa",mistyPse:"ffe4e1",moccasR:"ffe4b5",navajowEte:"ffdead",navy:"80",Tdlace:"fdf5e6",Tive:"808000",TivedBb:"6b8e23",Sange:"ffa500",SangeYd:"ff4500",ScEd:"da70d6",pOegTMnPd:"eee8aa",pOegYF:"98fb98",pOeQe:"afeeee",pOeviTetYd:"db7093",papayawEp:"ffefd5",pHKpuff:"ffdab9",peru:"cd853f",pRk:"ffc0cb",plum:"dda0dd",powMrXe:"b0e0e6",purpN:"800080",YbeccapurpN:"663399",Yd:"ff0000",Psybrown:"bc8f8f",PyOXe:"4169e1",saddNbPwn:"8b4513",sOmon:"fa8072",sandybPwn:"f4a460",sHgYF:"2e8b57",sHshell:"fff5ee",siFna:"a0522d",silver:"c0c0c0",skyXe:"87ceeb",UXe:"6a5acd",UWay:"708090",UgYy:"708090",snow:"fffafa",sprRggYF:"ff7f",stAlXe:"4682b4",tan:"d2b48c",teO:"8080",tEstN:"d8bfd8",tomato:"ff6347",Qe:"40e0d0",viTet:"ee82ee",JHt:"f5deb3",wEte:"ffffff",wEtesmoke:"f5f5f5",Lw:"ffff00",LwgYF:"9acd32"};function hl(){const e={},t=Object.keys(ls),i=Object.keys(as);let n,s,o,r,a;for(n=0;n<t.length;n++){for(r=a=t[n],s=0;s<i.length;s++)o=i[s],a=a.replace(o,as[o]);o=parseInt(ls[r],16),e[a]=[o>>16&255,o>>8&255,o&255]}return e}let Ge;function dl(e){Ge||(Ge=hl(),Ge.transparent=[0,0,0,0]);const t=Ge[e.toLowerCase()];return t&&{r:t[0],g:t[1],b:t[2],a:t.length===4?t[3]:255}}const ul=/^rgba?\(\s*([-+.\d]+)(%)?[\s,]+([-+.e\d]+)(%)?[\s,]+([-+.e\d]+)(%)?(?:[\s,/]+([-+.e\d]+)(%)?)?\s*\)$/;function fl(e){const t=ul.exec(e);let i=255,n,s,o;if(t){if(t[7]!==n){const r=+t[7];i=t[8]?Me(r):Lt(r*255,0,255)}return n=+t[1],s=+t[3],o=+t[5],n=255&(t[2]?Me(n):Lt(n,0,255)),s=255&(t[4]?Me(s):Lt(s,0,255)),o=255&(t[6]?Me(o):Lt(o,0,255)),{r:n,g:s,b:o,a:i}}}function gl(e){return e&&(e.a<255?`rgba(${e.r}, ${e.g}, ${e.b}, ${St(e.a)})`:`rgb(${e.r}, ${e.g}, ${e.b})`)}const Vi=e=>e<=.0031308?e*12.92:Math.pow(e,1/2.4)*1.055-.055,ne=e=>e<=.04045?e/12.92:Math.pow((e+.055)/1.055,2.4);function pl(e,t,i){const n=ne(St(e.r)),s=ne(St(e.g)),o=ne(St(e.b));return{r:It(Vi(n+i*(ne(St(t.r))-n))),g:It(Vi(s+i*(ne(St(t.g))-s))),b:It(Vi(o+i*(ne(St(t.b))-o))),a:e.a+i*(t.a-e.a)}}function Xe(e,t,i){if(e){let n=Tn(e);n[t]=Math.max(0,Math.min(n[t]+n[t]*i,t===0?360:1)),n=Ln(n),e.r=n[0],e.g=n[1],e.b=n[2]}}function Sr(e,t){return e&&Object.assign(t||{},e)}function cs(e){var t={r:0,g:0,b:0,a:255};return Array.isArray(e)?e.length>=3&&(t={r:e[0],g:e[1],b:e[2],a:255},e.length>3&&(t.a=It(e[3]))):(t=Sr(e,{r:0,g:0,b:0,a:1}),t.a=It(t.a)),t}function ml(e){return e.charAt(0)==="r"?fl(e):al(e)}class $e{constructor(t){if(t instanceof $e)return t;const i=typeof t;let n;i==="object"?n=cs(t):i==="string"&&(n=Ja(t)||dl(t)||ml(t)),this._rgb=n,this._valid=!!n}get valid(){return this._valid}get rgb(){var t=Sr(this._rgb);return t&&(t.a=St(t.a)),t}set rgb(t){this._rgb=cs(t)}rgbString(){return this._valid?gl(this._rgb):void 0}hexString(){return this._valid?tl(this._rgb):void 0}hslString(){return this._valid?cl(this._rgb):void 0}mix(t,i){if(t){const n=this.rgb,s=t.rgb;let o;const r=i===o?.5:i,a=2*r-1,l=n.a-s.a,c=((a*l===-1?a:(a+l)/(1+a*l))+1)/2;o=1-c,n.r=255&c*n.r+o*s.r+.5,n.g=255&c*n.g+o*s.g+.5,n.b=255&c*n.b+o*s.b+.5,n.a=r*n.a+(1-r)*s.a,this.rgb=n}return this}interpolate(t,i){return t&&(this._rgb=pl(this._rgb,t._rgb,i)),this}clone(){return new $e(this.rgb)}alpha(t){return this._rgb.a=It(t),this}clearer(t){const i=this._rgb;return i.a*=1-t,this}greyscale(){const t=this._rgb,i=He(t.r*.3+t.g*.59+t.b*.11);return t.r=t.g=t.b=i,this}opaquer(t){const i=this._rgb;return i.a*=1+t,this}negate(){const t=this._rgb;return t.r=255-t.r,t.g=255-t.g,t.b=255-t.b,this}lighten(t){return Xe(this._rgb,2,t),this}darken(t){return Xe(this._rgb,2,-t),this}saturate(t){return Xe(this._rgb,1,t),this}desaturate(t){return Xe(this._rgb,1,-t),this}rotate(t){return ll(this._rgb,t),this}}/*!
 * Chart.js v4.4.8
 * https://www.chartjs.org
 * (c) 2025 Chart.js Contributors
 * Released under the MIT License
 */function xt(){}const bl=(()=>{let e=0;return()=>e++})();function D(e){return e==null}function j(e){if(Array.isArray&&Array.isArray(e))return!0;const t=Object.prototype.toString.call(e);return t.slice(0,7)==="[object"&&t.slice(-6)==="Array]"}function L(e){return e!==null&&Object.prototype.toString.call(e)==="[object Object]"}function V(e){return(typeof e=="number"||e instanceof Number)&&isFinite(+e)}function at(e,t){return V(e)?e:t}function C(e,t){return typeof e>"u"?t:e}const _l=(e,t)=>typeof e=="string"&&e.endsWith("%")?parseFloat(e)/100:+e/t,wr=(e,t)=>typeof e=="string"&&e.endsWith("%")?parseFloat(e)/100*t:+e;function I(e,t,i){if(e&&typeof e.call=="function")return e.apply(i,t)}function $(e,t,i,n){let s,o,r;if(j(e))for(o=e.length,s=0;s<o;s++)t.call(i,e[s],s);else if(L(e))for(r=Object.keys(e),o=r.length,s=0;s<o;s++)t.call(i,e[r[s]],r[s])}function bi(e,t){let i,n,s,o;if(!e||!t||e.length!==t.length)return!1;for(i=0,n=e.length;i<n;++i)if(s=e[i],o=t[i],s.datasetIndex!==o.datasetIndex||s.index!==o.index)return!1;return!0}function _i(e){if(j(e))return e.map(_i);if(L(e)){const t=Object.create(null),i=Object.keys(e),n=i.length;let s=0;for(;s<n;++s)t[i[s]]=_i(e[i[s]]);return t}return e}function Pr(e){return["__proto__","prototype","constructor"].indexOf(e)===-1}function yl(e,t,i,n){if(!Pr(e))return;const s=t[e],o=i[e];L(s)&&L(o)?Re(s,o,n):t[e]=_i(o)}function Re(e,t,i){const n=j(t)?t:[t],s=n.length;if(!L(e))return e;i=i||{};const o=i.merger||yl;let r;for(let a=0;a<s;++a){if(r=n[a],!L(r))continue;const l=Object.keys(r);for(let c=0,h=l.length;c<h;++c)o(l[c],e,r,i)}return e}function Ce(e,t){return Re(e,t,{merger:xl})}function xl(e,t,i){if(!Pr(e))return;const n=t[e],s=i[e];L(n)&&L(s)?Ce(n,s):Object.prototype.hasOwnProperty.call(t,e)||(t[e]=_i(s))}const hs={"":e=>e,x:e=>e.x,y:e=>e.y};function vl(e){const t=e.split("."),i=[];let n="";for(const s of t)n+=s,n.endsWith("\\")?n=n.slice(0,-1)+".":(i.push(n),n="");return i}function Ml(e){const t=vl(e);return i=>{for(const n of t){if(n==="")break;i=i&&i[n]}return i}}function Ft(e,t){return(hs[t]||(hs[t]=Ml(t)))(e)}function En(e){return e.charAt(0).toUpperCase()+e.slice(1)}const Ie=e=>typeof e<"u",zt=e=>typeof e=="function",ds=(e,t)=>{if(e.size!==t.size)return!1;for(const i of e)if(!t.has(i))return!1;return!0};function Sl(e){return e.type==="mouseup"||e.type==="click"||e.type==="contextmenu"}const B=Math.PI,z=2*B,wl=z+B,yi=Number.POSITIVE_INFINITY,Pl=B/180,Y=B/2,Ht=B/4,us=B*2/3,Et=Math.log10,mt=Math.sign;function Te(e,t,i){return Math.abs(e-t)<i}function fs(e){const t=Math.round(e);e=Te(e,t,e/1e3)?t:e;const i=Math.pow(10,Math.floor(Et(e))),n=e/i;return(n<=1?1:n<=2?2:n<=5?5:10)*i}function Ol(e){const t=[],i=Math.sqrt(e);let n;for(n=1;n<i;n++)e%n===0&&(t.push(n),t.push(e/n));return i===(i|0)&&t.push(i),t.sort((s,o)=>s-o).pop(),t}function Al(e){return typeof e=="symbol"||typeof e=="object"&&e!==null&&!(Symbol.toPrimitive in e||"toString"in e||"valueOf"in e)}function re(e){return!Al(e)&&!isNaN(parseFloat(e))&&isFinite(e)}function kl(e,t){const i=Math.round(e);return i-t<=e&&i+t>=e}function Or(e,t,i){let n,s,o;for(n=0,s=e.length;n<s;n++)o=e[n][i],isNaN(o)||(t.min=Math.min(t.min,o),t.max=Math.max(t.max,o))}function ht(e){return e*(B/180)}function $n(e){return e*(180/B)}function gs(e){if(!V(e))return;let t=1,i=0;for(;Math.round(e*t)/t!==e;)t*=10,i++;return i}function Ar(e,t){const i=t.x-e.x,n=t.y-e.y,s=Math.sqrt(i*i+n*n);let o=Math.atan2(n,i);return o<-.5*B&&(o+=z),{angle:o,distance:s}}function hn(e,t){return Math.sqrt(Math.pow(t.x-e.x,2)+Math.pow(t.y-e.y,2))}function Cl(e,t){return(e-t+wl)%z-B}function lt(e){return(e%z+z)%z}function Fe(e,t,i,n){const s=lt(e),o=lt(t),r=lt(i),a=lt(o-s),l=lt(r-s),c=lt(s-o),h=lt(s-r);return s===o||s===r||n&&o===r||a>l&&c<h}function K(e,t,i){return Math.max(t,Math.min(i,e))}function Tl(e){return K(e,-32768,32767)}function wt(e,t,i,n=1e-6){return e>=Math.min(t,i)-n&&e<=Math.max(t,i)+n}function Rn(e,t,i){i=i||(r=>e[r]<t);let n=e.length-1,s=0,o;for(;n-s>1;)o=s+n>>1,i(o)?s=o:n=o;return{lo:s,hi:n}}const Pt=(e,t,i,n)=>Rn(e,i,n?s=>{const o=e[s][t];return o<i||o===i&&e[s+1][t]===i}:s=>e[s][t]<i),Dl=(e,t,i)=>Rn(e,i,n=>e[n][t]>=i);function Ll(e,t,i){let n=0,s=e.length;for(;n<s&&e[n]<t;)n++;for(;s>n&&e[s-1]>i;)s--;return n>0||s<e.length?e.slice(n,s):e}const kr=["push","pop","shift","splice","unshift"];function El(e,t){if(e._chartjs){e._chartjs.listeners.push(t);return}Object.defineProperty(e,"_chartjs",{configurable:!0,enumerable:!1,value:{listeners:[t]}}),kr.forEach(i=>{const n="_onData"+En(i),s=e[i];Object.defineProperty(e,i,{configurable:!0,enumerable:!1,value(...o){const r=s.apply(this,o);return e._chartjs.listeners.forEach(a=>{typeof a[n]=="function"&&a[n](...o)}),r}})})}function ps(e,t){const i=e._chartjs;if(!i)return;const n=i.listeners,s=n.indexOf(t);s!==-1&&n.splice(s,1),!(n.length>0)&&(kr.forEach(o=>{delete e[o]}),delete e._chartjs)}function Cr(e){const t=new Set(e);return t.size===e.length?e:Array.from(t)}const Tr=function(){return typeof window>"u"?function(e){return e()}:window.requestAnimationFrame}();function Dr(e,t){let i=[],n=!1;return function(...s){i=s,n||(n=!0,Tr.call(window,()=>{n=!1,e.apply(t,i)}))}}function $l(e,t){let i;return function(...n){return t?(clearTimeout(i),i=setTimeout(e,t,n)):e.apply(this,n),t}}const In=e=>e==="start"?"left":e==="end"?"right":"center",q=(e,t,i)=>e==="start"?t:e==="end"?i:(t+i)/2,Rl=(e,t,i,n)=>e===(n?"left":"right")?i:e==="center"?(t+i)/2:t;function Lr(e,t,i){const n=t.length;let s=0,o=n;if(e._sorted){const{iScale:r,vScale:a,_parsed:l}=e,c=e.dataset&&e.dataset.options?e.dataset.options.spanGaps:null,h=r.axis,{min:d,max:u,minDefined:f,maxDefined:g}=r.getUserBounds();if(f){if(s=Math.min(Pt(l,h,d).lo,i?n:Pt(t,h,r.getPixelForValue(d)).lo),c){const p=l.slice(0,s+1).reverse().findIndex(m=>!D(m[a.axis]));s-=Math.max(0,p)}s=K(s,0,n-1)}if(g){let p=Math.max(Pt(l,r.axis,u,!0).hi+1,i?0:Pt(t,h,r.getPixelForValue(u),!0).hi+1);if(c){const m=l.slice(p-1).findIndex(b=>!D(b[a.axis]));p+=Math.max(0,m)}o=K(p,s,n)-s}else o=n-s}return{start:s,count:o}}function Er(e){const{xScale:t,yScale:i,_scaleRanges:n}=e,s={xmin:t.min,xmax:t.max,ymin:i.min,ymax:i.max};if(!n)return e._scaleRanges=s,!0;const o=n.xmin!==t.min||n.xmax!==t.max||n.ymin!==i.min||n.ymax!==i.max;return Object.assign(n,s),o}const Ke=e=>e===0||e===1,ms=(e,t,i)=>-(Math.pow(2,10*(e-=1))*Math.sin((e-t)*z/i)),bs=(e,t,i)=>Math.pow(2,-10*e)*Math.sin((e-t)*z/i)+1,De={linear:e=>e,easeInQuad:e=>e*e,easeOutQuad:e=>-e*(e-2),easeInOutQuad:e=>(e/=.5)<1?.5*e*e:-.5*(--e*(e-2)-1),easeInCubic:e=>e*e*e,easeOutCubic:e=>(e-=1)*e*e+1,easeInOutCubic:e=>(e/=.5)<1?.5*e*e*e:.5*((e-=2)*e*e+2),easeInQuart:e=>e*e*e*e,easeOutQuart:e=>-((e-=1)*e*e*e-1),easeInOutQuart:e=>(e/=.5)<1?.5*e*e*e*e:-.5*((e-=2)*e*e*e-2),easeInQuint:e=>e*e*e*e*e,easeOutQuint:e=>(e-=1)*e*e*e*e+1,easeInOutQuint:e=>(e/=.5)<1?.5*e*e*e*e*e:.5*((e-=2)*e*e*e*e+2),easeInSine:e=>-Math.cos(e*Y)+1,easeOutSine:e=>Math.sin(e*Y),easeInOutSine:e=>-.5*(Math.cos(B*e)-1),easeInExpo:e=>e===0?0:Math.pow(2,10*(e-1)),easeOutExpo:e=>e===1?1:-Math.pow(2,-10*e)+1,easeInOutExpo:e=>Ke(e)?e:e<.5?.5*Math.pow(2,10*(e*2-1)):.5*(-Math.pow(2,-10*(e*2-1))+2),easeInCirc:e=>e>=1?e:-(Math.sqrt(1-e*e)-1),easeOutCirc:e=>Math.sqrt(1-(e-=1)*e),easeInOutCirc:e=>(e/=.5)<1?-.5*(Math.sqrt(1-e*e)-1):.5*(Math.sqrt(1-(e-=2)*e)+1),easeInElastic:e=>Ke(e)?e:ms(e,.075,.3),easeOutElastic:e=>Ke(e)?e:bs(e,.075,.3),easeInOutElastic(e){return Ke(e)?e:e<.5?.5*ms(e*2,.1125,.45):.5+.5*bs(e*2-1,.1125,.45)},easeInBack(e){return e*e*((1.70158+1)*e-1.70158)},easeOutBack(e){return(e-=1)*e*((1.70158+1)*e********)+1},easeInOutBack(e){let t=1.70158;return(e/=.5)<1?.5*(e*e*(((t*=1.525)+1)*e-t)):.5*((e-=2)*e*(((t*=1.525)+1)*e+t)+2)},easeInBounce:e=>1-De.easeOutBounce(1-e),easeOutBounce(e){return e<1/2.75?7.5625*e*e:e<2/2.75?7.5625*(e-=1.5/2.75)*e+.75:e<2.5/2.75?7.5625*(e-=2.25/2.75)*e+.9375:7.5625*(e-=2.625/2.75)*e+.984375},easeInOutBounce:e=>e<.5?De.easeInBounce(e*2)*.5:De.easeOutBounce(e*2-1)*.5+.5};function Fn(e){if(e&&typeof e=="object"){const t=e.toString();return t==="[object CanvasPattern]"||t==="[object CanvasGradient]"}return!1}function _s(e){return Fn(e)?e:new $e(e)}function Yi(e){return Fn(e)?e:new $e(e).saturate(.5).darken(.1).hexString()}const Il=["x","y","borderWidth","radius","tension"],Fl=["color","borderColor","backgroundColor"];function zl(e){e.set("animation",{delay:void 0,duration:1e3,easing:"easeOutQuart",fn:void 0,from:void 0,loop:void 0,to:void 0,type:void 0}),e.describe("animation",{_fallback:!1,_indexable:!1,_scriptable:t=>t!=="onProgress"&&t!=="onComplete"&&t!=="fn"}),e.set("animations",{colors:{type:"color",properties:Fl},numbers:{type:"number",properties:Il}}),e.describe("animations",{_fallback:"animation"}),e.set("transitions",{active:{animation:{duration:400}},resize:{animation:{duration:0}},show:{animations:{colors:{from:"transparent"},visible:{type:"boolean",duration:0}}},hide:{animations:{colors:{to:"transparent"},visible:{type:"boolean",easing:"linear",fn:t=>t|0}}}})}function Bl(e){e.set("layout",{autoPadding:!0,padding:{top:0,right:0,bottom:0,left:0}})}const ys=new Map;function Nl(e,t){t=t||{};const i=e+JSON.stringify(t);let n=ys.get(i);return n||(n=new Intl.NumberFormat(e,t),ys.set(i,n)),n}function We(e,t,i){return Nl(t,i).format(e)}const $r={values(e){return j(e)?e:""+e},numeric(e,t,i){if(e===0)return"0";const n=this.chart.options.locale;let s,o=e;if(i.length>1){const c=Math.max(Math.abs(i[0].value),Math.abs(i[i.length-1].value));(c<1e-4||c>1e15)&&(s="scientific"),o=jl(e,i)}const r=Et(Math.abs(o)),a=isNaN(r)?1:Math.max(Math.min(-1*Math.floor(r),20),0),l={notation:s,minimumFractionDigits:a,maximumFractionDigits:a};return Object.assign(l,this.options.ticks.format),We(e,n,l)},logarithmic(e,t,i){if(e===0)return"0";const n=i[t].significand||e/Math.pow(10,Math.floor(Et(e)));return[1,2,3,5,10,15].includes(n)||t>.8*i.length?$r.numeric.call(this,e,t,i):""}};function jl(e,t){let i=t.length>3?t[2].value-t[1].value:t[1].value-t[0].value;return Math.abs(i)>=1&&e!==Math.floor(e)&&(i=e-Math.floor(e)),i}var Ci={formatters:$r};function Hl(e){e.set("scale",{display:!0,offset:!1,reverse:!1,beginAtZero:!1,bounds:"ticks",clip:!0,grace:0,grid:{display:!0,lineWidth:1,drawOnChartArea:!0,drawTicks:!0,tickLength:8,tickWidth:(t,i)=>i.lineWidth,tickColor:(t,i)=>i.color,offset:!1},border:{display:!0,dash:[],dashOffset:0,width:1},title:{display:!1,text:"",padding:{top:4,bottom:4}},ticks:{minRotation:0,maxRotation:50,mirror:!1,textStrokeWidth:0,textStrokeColor:"",padding:3,display:!0,autoSkip:!0,autoSkipPadding:3,labelOffset:0,callback:Ci.formatters.values,minor:{},major:{},align:"center",crossAlign:"near",showLabelBackdrop:!1,backdropColor:"rgba(255, 255, 255, 0.75)",backdropPadding:2}}),e.route("scale.ticks","color","","color"),e.route("scale.grid","color","","borderColor"),e.route("scale.border","color","","borderColor"),e.route("scale.title","color","","color"),e.describe("scale",{_fallback:!1,_scriptable:t=>!t.startsWith("before")&&!t.startsWith("after")&&t!=="callback"&&t!=="parser",_indexable:t=>t!=="borderDash"&&t!=="tickBorderDash"&&t!=="dash"}),e.describe("scales",{_fallback:"scale"}),e.describe("scale.ticks",{_scriptable:t=>t!=="backdropPadding"&&t!=="callback",_indexable:t=>t!=="backdropPadding"})}const Jt=Object.create(null),dn=Object.create(null);function Le(e,t){if(!t)return e;const i=t.split(".");for(let n=0,s=i.length;n<s;++n){const o=i[n];e=e[o]||(e[o]=Object.create(null))}return e}function Ui(e,t,i){return typeof t=="string"?Re(Le(e,t),i):Re(Le(e,""),t)}class Wl{constructor(t,i){this.animation=void 0,this.backgroundColor="rgba(0,0,0,0.1)",this.borderColor="rgba(0,0,0,0.1)",this.color="#666",this.datasets={},this.devicePixelRatio=n=>n.chart.platform.getDevicePixelRatio(),this.elements={},this.events=["mousemove","mouseout","click","touchstart","touchmove"],this.font={family:"'Helvetica Neue', 'Helvetica', 'Arial', sans-serif",size:12,style:"normal",lineHeight:1.2,weight:null},this.hover={},this.hoverBackgroundColor=(n,s)=>Yi(s.backgroundColor),this.hoverBorderColor=(n,s)=>Yi(s.borderColor),this.hoverColor=(n,s)=>Yi(s.color),this.indexAxis="x",this.interaction={mode:"nearest",intersect:!0,includeInvisible:!1},this.maintainAspectRatio=!0,this.onHover=null,this.onClick=null,this.parsing=!0,this.plugins={},this.responsive=!0,this.scale=void 0,this.scales={},this.showLine=!0,this.drawActiveElementsOnTop=!0,this.describe(t),this.apply(i)}set(t,i){return Ui(this,t,i)}get(t){return Le(this,t)}describe(t,i){return Ui(dn,t,i)}override(t,i){return Ui(Jt,t,i)}route(t,i,n,s){const o=Le(this,t),r=Le(this,n),a="_"+i;Object.defineProperties(o,{[a]:{value:o[i],writable:!0},[i]:{enumerable:!0,get(){const l=this[a],c=r[s];return L(l)?Object.assign({},c,l):C(l,c)},set(l){this[a]=l}}})}apply(t){t.forEach(i=>i(this))}}var H=new Wl({_scriptable:e=>!e.startsWith("on"),_indexable:e=>e!=="events",hover:{_fallback:"interaction"},interaction:{_scriptable:!1,_indexable:!1}},[zl,Bl,Hl]);function Vl(e){return!e||D(e.size)||D(e.family)?null:(e.style?e.style+" ":"")+(e.weight?e.weight+" ":"")+e.size+"px "+e.family}function xi(e,t,i,n,s){let o=t[s];return o||(o=t[s]=e.measureText(s).width,i.push(s)),o>n&&(n=o),n}function Yl(e,t,i,n){n=n||{};let s=n.data=n.data||{},o=n.garbageCollect=n.garbageCollect||[];n.font!==t&&(s=n.data={},o=n.garbageCollect=[],n.font=t),e.save(),e.font=t;let r=0;const a=i.length;let l,c,h,d,u;for(l=0;l<a;l++)if(d=i[l],d!=null&&!j(d))r=xi(e,s,o,r,d);else if(j(d))for(c=0,h=d.length;c<h;c++)u=d[c],u!=null&&!j(u)&&(r=xi(e,s,o,r,u));e.restore();const f=o.length/2;if(f>i.length){for(l=0;l<f;l++)delete s[o[l]];o.splice(0,f)}return r}function Wt(e,t,i){const n=e.currentDevicePixelRatio,s=i!==0?Math.max(i/2,.5):0;return Math.round((t-s)*n)/n+s}function xs(e,t){!t&&!e||(t=t||e.getContext("2d"),t.save(),t.resetTransform(),t.clearRect(0,0,e.width,e.height),t.restore())}function un(e,t,i,n){Rr(e,t,i,n,null)}function Rr(e,t,i,n,s){let o,r,a,l,c,h,d,u;const f=t.pointStyle,g=t.rotation,p=t.radius;let m=(g||0)*Pl;if(f&&typeof f=="object"&&(o=f.toString(),o==="[object HTMLImageElement]"||o==="[object HTMLCanvasElement]")){e.save(),e.translate(i,n),e.rotate(m),e.drawImage(f,-f.width/2,-f.height/2,f.width,f.height),e.restore();return}if(!(isNaN(p)||p<=0)){switch(e.beginPath(),f){default:s?e.ellipse(i,n,s/2,p,0,0,z):e.arc(i,n,p,0,z),e.closePath();break;case"triangle":h=s?s/2:p,e.moveTo(i+Math.sin(m)*h,n-Math.cos(m)*p),m+=us,e.lineTo(i+Math.sin(m)*h,n-Math.cos(m)*p),m+=us,e.lineTo(i+Math.sin(m)*h,n-Math.cos(m)*p),e.closePath();break;case"rectRounded":c=p*.516,l=p-c,r=Math.cos(m+Ht)*l,d=Math.cos(m+Ht)*(s?s/2-c:l),a=Math.sin(m+Ht)*l,u=Math.sin(m+Ht)*(s?s/2-c:l),e.arc(i-d,n-a,c,m-B,m-Y),e.arc(i+u,n-r,c,m-Y,m),e.arc(i+d,n+a,c,m,m+Y),e.arc(i-u,n+r,c,m+Y,m+B),e.closePath();break;case"rect":if(!g){l=Math.SQRT1_2*p,h=s?s/2:l,e.rect(i-h,n-l,2*h,2*l);break}m+=Ht;case"rectRot":d=Math.cos(m)*(s?s/2:p),r=Math.cos(m)*p,a=Math.sin(m)*p,u=Math.sin(m)*(s?s/2:p),e.moveTo(i-d,n-a),e.lineTo(i+u,n-r),e.lineTo(i+d,n+a),e.lineTo(i-u,n+r),e.closePath();break;case"crossRot":m+=Ht;case"cross":d=Math.cos(m)*(s?s/2:p),r=Math.cos(m)*p,a=Math.sin(m)*p,u=Math.sin(m)*(s?s/2:p),e.moveTo(i-d,n-a),e.lineTo(i+d,n+a),e.moveTo(i+u,n-r),e.lineTo(i-u,n+r);break;case"star":d=Math.cos(m)*(s?s/2:p),r=Math.cos(m)*p,a=Math.sin(m)*p,u=Math.sin(m)*(s?s/2:p),e.moveTo(i-d,n-a),e.lineTo(i+d,n+a),e.moveTo(i+u,n-r),e.lineTo(i-u,n+r),m+=Ht,d=Math.cos(m)*(s?s/2:p),r=Math.cos(m)*p,a=Math.sin(m)*p,u=Math.sin(m)*(s?s/2:p),e.moveTo(i-d,n-a),e.lineTo(i+d,n+a),e.moveTo(i+u,n-r),e.lineTo(i-u,n+r);break;case"line":r=s?s/2:Math.cos(m)*p,a=Math.sin(m)*p,e.moveTo(i-r,n-a),e.lineTo(i+r,n+a);break;case"dash":e.moveTo(i,n),e.lineTo(i+Math.cos(m)*(s?s/2:p),n+Math.sin(m)*p);break;case!1:e.closePath();break}e.fill(),t.borderWidth>0&&e.stroke()}}function Ot(e,t,i){return i=i||.5,!t||e&&e.x>t.left-i&&e.x<t.right+i&&e.y>t.top-i&&e.y<t.bottom+i}function Ti(e,t){e.save(),e.beginPath(),e.rect(t.left,t.top,t.right-t.left,t.bottom-t.top),e.clip()}function Di(e){e.restore()}function Ul(e,t,i,n,s){if(!t)return e.lineTo(i.x,i.y);if(s==="middle"){const o=(t.x+i.x)/2;e.lineTo(o,t.y),e.lineTo(o,i.y)}else s==="after"!=!!n?e.lineTo(t.x,i.y):e.lineTo(i.x,t.y);e.lineTo(i.x,i.y)}function Gl(e,t,i,n){if(!t)return e.lineTo(i.x,i.y);e.bezierCurveTo(n?t.cp1x:t.cp2x,n?t.cp1y:t.cp2y,n?i.cp2x:i.cp1x,n?i.cp2y:i.cp1y,i.x,i.y)}function Xl(e,t){t.translation&&e.translate(t.translation[0],t.translation[1]),D(t.rotation)||e.rotate(t.rotation),t.color&&(e.fillStyle=t.color),t.textAlign&&(e.textAlign=t.textAlign),t.textBaseline&&(e.textBaseline=t.textBaseline)}function Kl(e,t,i,n,s){if(s.strikethrough||s.underline){const o=e.measureText(n),r=t-o.actualBoundingBoxLeft,a=t+o.actualBoundingBoxRight,l=i-o.actualBoundingBoxAscent,c=i+o.actualBoundingBoxDescent,h=s.strikethrough?(l+c)/2:c;e.strokeStyle=e.fillStyle,e.beginPath(),e.lineWidth=s.decorationWidth||2,e.moveTo(r,h),e.lineTo(a,h),e.stroke()}}function ql(e,t){const i=e.fillStyle;e.fillStyle=t.color,e.fillRect(t.left,t.top,t.width,t.height),e.fillStyle=i}function Qt(e,t,i,n,s,o={}){const r=j(t)?t:[t],a=o.strokeWidth>0&&o.strokeColor!=="";let l,c;for(e.save(),e.font=s.string,Xl(e,o),l=0;l<r.length;++l)c=r[l],o.backdrop&&ql(e,o.backdrop),a&&(o.strokeColor&&(e.strokeStyle=o.strokeColor),D(o.strokeWidth)||(e.lineWidth=o.strokeWidth),e.strokeText(c,i,n,o.maxWidth)),e.fillText(c,i,n,o.maxWidth),Kl(e,i,n,c,o),n+=Number(s.lineHeight);e.restore()}function ze(e,t){const{x:i,y:n,w:s,h:o,radius:r}=t;e.arc(i+r.topLeft,n+r.topLeft,r.topLeft,1.5*B,B,!0),e.lineTo(i,n+o-r.bottomLeft),e.arc(i+r.bottomLeft,n+o-r.bottomLeft,r.bottomLeft,B,Y,!0),e.lineTo(i+s-r.bottomRight,n+o),e.arc(i+s-r.bottomRight,n+o-r.bottomRight,r.bottomRight,Y,0,!0),e.lineTo(i+s,n+r.topRight),e.arc(i+s-r.topRight,n+r.topRight,r.topRight,0,-Y,!0),e.lineTo(i+r.topLeft,n)}const Zl=/^(normal|(\d+(?:\.\d+)?)(px|em|%)?)$/,Jl=/^(normal|italic|initial|inherit|unset|(oblique( -?[0-9]?[0-9]deg)?))$/;function Ql(e,t){const i=(""+e).match(Zl);if(!i||i[1]==="normal")return t*1.2;switch(e=+i[2],i[3]){case"px":return e;case"%":e/=100;break}return t*e}const tc=e=>+e||0;function zn(e,t){const i={},n=L(t),s=n?Object.keys(t):t,o=L(e)?n?r=>C(e[r],e[t[r]]):r=>e[r]:()=>e;for(const r of s)i[r]=tc(o(r));return i}function Ir(e){return zn(e,{top:"y",right:"x",bottom:"y",left:"x"})}function qt(e){return zn(e,["topLeft","topRight","bottomLeft","bottomRight"])}function Q(e){const t=Ir(e);return t.width=t.left+t.right,t.height=t.top+t.bottom,t}function X(e,t){e=e||{},t=t||H.font;let i=C(e.size,t.size);typeof i=="string"&&(i=parseInt(i,10));let n=C(e.style,t.style);n&&!(""+n).match(Jl)&&(console.warn('Invalid font style specified: "'+n+'"'),n=void 0);const s={family:C(e.family,t.family),lineHeight:Ql(C(e.lineHeight,t.lineHeight),i),size:i,style:n,weight:C(e.weight,t.weight),string:""};return s.string=Vl(s),s}function Se(e,t,i,n){let s,o,r;for(s=0,o=e.length;s<o;++s)if(r=e[s],r!==void 0&&r!==void 0)return r}function ec(e,t,i){const{min:n,max:s}=e,o=wr(t,(s-n)/2),r=(a,l)=>i&&a===0?0:a+l;return{min:r(n,-Math.abs(o)),max:r(s,o)}}function Bt(e,t){return Object.assign(Object.create(e),t)}function Bn(e,t=[""],i,n,s=()=>e[0]){const o=i||e;typeof n>"u"&&(n=Nr("_fallback",e));const r={[Symbol.toStringTag]:"Object",_cacheable:!0,_scopes:e,_rootScopes:o,_fallback:n,_getTarget:s,override:a=>Bn([a,...e],t,o,n)};return new Proxy(r,{deleteProperty(a,l){return delete a[l],delete a._keys,delete e[0][l],!0},get(a,l){return zr(a,l,()=>cc(l,t,e,a))},getOwnPropertyDescriptor(a,l){return Reflect.getOwnPropertyDescriptor(a._scopes[0],l)},getPrototypeOf(){return Reflect.getPrototypeOf(e[0])},has(a,l){return Ms(a).includes(l)},ownKeys(a){return Ms(a)},set(a,l,c){const h=a._storage||(a._storage=s());return a[l]=h[l]=c,delete a._keys,!0}})}function ae(e,t,i,n){const s={_cacheable:!1,_proxy:e,_context:t,_subProxy:i,_stack:new Set,_descriptors:Fr(e,n),setContext:o=>ae(e,o,i,n),override:o=>ae(e.override(o),t,i,n)};return new Proxy(s,{deleteProperty(o,r){return delete o[r],delete e[r],!0},get(o,r,a){return zr(o,r,()=>nc(o,r,a))},getOwnPropertyDescriptor(o,r){return o._descriptors.allKeys?Reflect.has(e,r)?{enumerable:!0,configurable:!0}:void 0:Reflect.getOwnPropertyDescriptor(e,r)},getPrototypeOf(){return Reflect.getPrototypeOf(e)},has(o,r){return Reflect.has(e,r)},ownKeys(){return Reflect.ownKeys(e)},set(o,r,a){return e[r]=a,delete o[r],!0}})}function Fr(e,t={scriptable:!0,indexable:!0}){const{_scriptable:i=t.scriptable,_indexable:n=t.indexable,_allKeys:s=t.allKeys}=e;return{allKeys:s,scriptable:i,indexable:n,isScriptable:zt(i)?i:()=>i,isIndexable:zt(n)?n:()=>n}}const ic=(e,t)=>e?e+En(t):t,Nn=(e,t)=>L(t)&&e!=="adapters"&&(Object.getPrototypeOf(t)===null||t.constructor===Object);function zr(e,t,i){if(Object.prototype.hasOwnProperty.call(e,t)||t==="constructor")return e[t];const n=i();return e[t]=n,n}function nc(e,t,i){const{_proxy:n,_context:s,_subProxy:o,_descriptors:r}=e;let a=n[t];return zt(a)&&r.isScriptable(t)&&(a=sc(t,a,e,i)),j(a)&&a.length&&(a=oc(t,a,e,r.isIndexable)),Nn(t,a)&&(a=ae(a,s,o&&o[t],r)),a}function sc(e,t,i,n){const{_proxy:s,_context:o,_subProxy:r,_stack:a}=i;if(a.has(e))throw new Error("Recursion detected: "+Array.from(a).join("->")+"->"+e);a.add(e);let l=t(o,r||n);return a.delete(e),Nn(e,l)&&(l=jn(s._scopes,s,e,l)),l}function oc(e,t,i,n){const{_proxy:s,_context:o,_subProxy:r,_descriptors:a}=i;if(typeof o.index<"u"&&n(e))return t[o.index%t.length];if(L(t[0])){const l=t,c=s._scopes.filter(h=>h!==l);t=[];for(const h of l){const d=jn(c,s,e,h);t.push(ae(d,o,r&&r[e],a))}}return t}function Br(e,t,i){return zt(e)?e(t,i):e}const rc=(e,t)=>e===!0?t:typeof e=="string"?Ft(t,e):void 0;function ac(e,t,i,n,s){for(const o of t){const r=rc(i,o);if(r){e.add(r);const a=Br(r._fallback,i,s);if(typeof a<"u"&&a!==i&&a!==n)return a}else if(r===!1&&typeof n<"u"&&i!==n)return null}return!1}function jn(e,t,i,n){const s=t._rootScopes,o=Br(t._fallback,i,n),r=[...e,...s],a=new Set;a.add(n);let l=vs(a,r,i,o||i,n);return l===null||typeof o<"u"&&o!==i&&(l=vs(a,r,o,l,n),l===null)?!1:Bn(Array.from(a),[""],s,o,()=>lc(t,i,n))}function vs(e,t,i,n,s){for(;i;)i=ac(e,t,i,n,s);return i}function lc(e,t,i){const n=e._getTarget();t in n||(n[t]={});const s=n[t];return j(s)&&L(i)?i:s||{}}function cc(e,t,i,n){let s;for(const o of t)if(s=Nr(ic(o,e),i),typeof s<"u")return Nn(e,s)?jn(i,n,e,s):s}function Nr(e,t){for(const i of t){if(!i)continue;const n=i[e];if(typeof n<"u")return n}}function Ms(e){let t=e._keys;return t||(t=e._keys=hc(e._scopes)),t}function hc(e){const t=new Set;for(const i of e)for(const n of Object.keys(i).filter(s=>!s.startsWith("_")))t.add(n);return Array.from(t)}function jr(e,t,i,n){const{iScale:s}=e,{key:o="r"}=this._parsing,r=new Array(n);let a,l,c,h;for(a=0,l=n;a<l;++a)c=a+i,h=t[c],r[a]={r:s.parse(Ft(h,o),c)};return r}const dc=Number.EPSILON||1e-14,le=(e,t)=>t<e.length&&!e[t].skip&&e[t],Hr=e=>e==="x"?"y":"x";function uc(e,t,i,n){const s=e.skip?t:e,o=t,r=i.skip?t:i,a=hn(o,s),l=hn(r,o);let c=a/(a+l),h=l/(a+l);c=isNaN(c)?0:c,h=isNaN(h)?0:h;const d=n*c,u=n*h;return{previous:{x:o.x-d*(r.x-s.x),y:o.y-d*(r.y-s.y)},next:{x:o.x+u*(r.x-s.x),y:o.y+u*(r.y-s.y)}}}function fc(e,t,i){const n=e.length;let s,o,r,a,l,c=le(e,0);for(let h=0;h<n-1;++h)if(l=c,c=le(e,h+1),!(!l||!c)){if(Te(t[h],0,dc)){i[h]=i[h+1]=0;continue}s=i[h]/t[h],o=i[h+1]/t[h],a=Math.pow(s,2)+Math.pow(o,2),!(a<=9)&&(r=3/Math.sqrt(a),i[h]=s*r*t[h],i[h+1]=o*r*t[h])}}function gc(e,t,i="x"){const n=Hr(i),s=e.length;let o,r,a,l=le(e,0);for(let c=0;c<s;++c){if(r=a,a=l,l=le(e,c+1),!a)continue;const h=a[i],d=a[n];r&&(o=(h-r[i])/3,a[`cp1${i}`]=h-o,a[`cp1${n}`]=d-o*t[c]),l&&(o=(l[i]-h)/3,a[`cp2${i}`]=h+o,a[`cp2${n}`]=d+o*t[c])}}function pc(e,t="x"){const i=Hr(t),n=e.length,s=Array(n).fill(0),o=Array(n);let r,a,l,c=le(e,0);for(r=0;r<n;++r)if(a=l,l=c,c=le(e,r+1),!!l){if(c){const h=c[t]-l[t];s[r]=h!==0?(c[i]-l[i])/h:0}o[r]=a?c?mt(s[r-1])!==mt(s[r])?0:(s[r-1]+s[r])/2:s[r-1]:s[r]}fc(e,s,o),gc(e,o,t)}function qe(e,t,i){return Math.max(Math.min(e,i),t)}function mc(e,t){let i,n,s,o,r,a=Ot(e[0],t);for(i=0,n=e.length;i<n;++i)r=o,o=a,a=i<n-1&&Ot(e[i+1],t),o&&(s=e[i],r&&(s.cp1x=qe(s.cp1x,t.left,t.right),s.cp1y=qe(s.cp1y,t.top,t.bottom)),a&&(s.cp2x=qe(s.cp2x,t.left,t.right),s.cp2y=qe(s.cp2y,t.top,t.bottom)))}function bc(e,t,i,n,s){let o,r,a,l;if(t.spanGaps&&(e=e.filter(c=>!c.skip)),t.cubicInterpolationMode==="monotone")pc(e,s);else{let c=n?e[e.length-1]:e[0];for(o=0,r=e.length;o<r;++o)a=e[o],l=uc(c,a,e[Math.min(o+1,r-(n?0:1))%r],t.tension),a.cp1x=l.previous.x,a.cp1y=l.previous.y,a.cp2x=l.next.x,a.cp2y=l.next.y,c=a}t.capBezierPoints&&mc(e,i)}function Hn(){return typeof window<"u"&&typeof document<"u"}function Wn(e){let t=e.parentNode;return t&&t.toString()==="[object ShadowRoot]"&&(t=t.host),t}function vi(e,t,i){let n;return typeof e=="string"?(n=parseInt(e,10),e.indexOf("%")!==-1&&(n=n/100*t.parentNode[i])):n=e,n}const Li=e=>e.ownerDocument.defaultView.getComputedStyle(e,null);function _c(e,t){return Li(e).getPropertyValue(t)}const yc=["top","right","bottom","left"];function Zt(e,t,i){const n={};i=i?"-"+i:"";for(let s=0;s<4;s++){const o=yc[s];n[o]=parseFloat(e[t+"-"+o+i])||0}return n.width=n.left+n.right,n.height=n.top+n.bottom,n}const xc=(e,t,i)=>(e>0||t>0)&&(!i||!i.shadowRoot);function vc(e,t){const i=e.touches,n=i&&i.length?i[0]:e,{offsetX:s,offsetY:o}=n;let r=!1,a,l;if(xc(s,o,e.target))a=s,l=o;else{const c=t.getBoundingClientRect();a=n.clientX-c.left,l=n.clientY-c.top,r=!0}return{x:a,y:l,box:r}}function Ut(e,t){if("native"in e)return e;const{canvas:i,currentDevicePixelRatio:n}=t,s=Li(i),o=s.boxSizing==="border-box",r=Zt(s,"padding"),a=Zt(s,"border","width"),{x:l,y:c,box:h}=vc(e,i),d=r.left+(h&&a.left),u=r.top+(h&&a.top);let{width:f,height:g}=t;return o&&(f-=r.width+a.width,g-=r.height+a.height),{x:Math.round((l-d)/f*i.width/n),y:Math.round((c-u)/g*i.height/n)}}function Mc(e,t,i){let n,s;if(t===void 0||i===void 0){const o=e&&Wn(e);if(!o)t=e.clientWidth,i=e.clientHeight;else{const r=o.getBoundingClientRect(),a=Li(o),l=Zt(a,"border","width"),c=Zt(a,"padding");t=r.width-c.width-l.width,i=r.height-c.height-l.height,n=vi(a.maxWidth,o,"clientWidth"),s=vi(a.maxHeight,o,"clientHeight")}}return{width:t,height:i,maxWidth:n||yi,maxHeight:s||yi}}const Ze=e=>Math.round(e*10)/10;function Sc(e,t,i,n){const s=Li(e),o=Zt(s,"margin"),r=vi(s.maxWidth,e,"clientWidth")||yi,a=vi(s.maxHeight,e,"clientHeight")||yi,l=Mc(e,t,i);let{width:c,height:h}=l;if(s.boxSizing==="content-box"){const u=Zt(s,"border","width"),f=Zt(s,"padding");c-=f.width+u.width,h-=f.height+u.height}return c=Math.max(0,c-o.width),h=Math.max(0,n?c/n:h-o.height),c=Ze(Math.min(c,r,l.maxWidth)),h=Ze(Math.min(h,a,l.maxHeight)),c&&!h&&(h=Ze(c/2)),(t!==void 0||i!==void 0)&&n&&l.height&&h>l.height&&(h=l.height,c=Ze(Math.floor(h*n))),{width:c,height:h}}function Ss(e,t,i){const n=t||1,s=Math.floor(e.height*n),o=Math.floor(e.width*n);e.height=Math.floor(e.height),e.width=Math.floor(e.width);const r=e.canvas;return r.style&&(i||!r.style.height&&!r.style.width)&&(r.style.height=`${e.height}px`,r.style.width=`${e.width}px`),e.currentDevicePixelRatio!==n||r.height!==s||r.width!==o?(e.currentDevicePixelRatio=n,r.height=s,r.width=o,e.ctx.setTransform(n,0,0,n,0,0),!0):!1}const wc=function(){let e=!1;try{const t={get passive(){return e=!0,!1}};Hn()&&(window.addEventListener("test",null,t),window.removeEventListener("test",null,t))}catch{}return e}();function ws(e,t){const i=_c(e,t),n=i&&i.match(/^(\d+)(\.\d+)?px$/);return n?+n[1]:void 0}function Gt(e,t,i,n){return{x:e.x+i*(t.x-e.x),y:e.y+i*(t.y-e.y)}}function Pc(e,t,i,n){return{x:e.x+i*(t.x-e.x),y:n==="middle"?i<.5?e.y:t.y:n==="after"?i<1?e.y:t.y:i>0?t.y:e.y}}function Oc(e,t,i,n){const s={x:e.cp2x,y:e.cp2y},o={x:t.cp1x,y:t.cp1y},r=Gt(e,s,i),a=Gt(s,o,i),l=Gt(o,t,i),c=Gt(r,a,i),h=Gt(a,l,i);return Gt(c,h,i)}const Ac=function(e,t){return{x(i){return e+e+t-i},setWidth(i){t=i},textAlign(i){return i==="center"?i:i==="right"?"left":"right"},xPlus(i,n){return i-n},leftForLtr(i,n){return i-n}}},kc=function(){return{x(e){return e},setWidth(e){},textAlign(e){return e},xPlus(e,t){return e+t},leftForLtr(e,t){return e}}};function oe(e,t,i){return e?Ac(t,i):kc()}function Wr(e,t){let i,n;(t==="ltr"||t==="rtl")&&(i=e.canvas.style,n=[i.getPropertyValue("direction"),i.getPropertyPriority("direction")],i.setProperty("direction",t,"important"),e.prevTextDirection=n)}function Vr(e,t){t!==void 0&&(delete e.prevTextDirection,e.canvas.style.setProperty("direction",t[0],t[1]))}function Yr(e){return e==="angle"?{between:Fe,compare:Cl,normalize:lt}:{between:wt,compare:(t,i)=>t-i,normalize:t=>t}}function Ps({start:e,end:t,count:i,loop:n,style:s}){return{start:e%i,end:t%i,loop:n&&(t-e+1)%i===0,style:s}}function Cc(e,t,i){const{property:n,start:s,end:o}=i,{between:r,normalize:a}=Yr(n),l=t.length;let{start:c,end:h,loop:d}=e,u,f;if(d){for(c+=l,h+=l,u=0,f=l;u<f&&r(a(t[c%l][n]),s,o);++u)c--,h--;c%=l,h%=l}return h<c&&(h+=l),{start:c,end:h,loop:d,style:e.style}}function Ur(e,t,i){if(!i)return[e];const{property:n,start:s,end:o}=i,r=t.length,{compare:a,between:l,normalize:c}=Yr(n),{start:h,end:d,loop:u,style:f}=Cc(e,t,i),g=[];let p=!1,m=null,b,_,v;const x=()=>l(s,v,b)&&a(s,v)!==0,y=()=>a(o,b)===0||l(o,v,b),S=()=>p||x(),w=()=>!p||y();for(let P=h,O=h;P<=d;++P)_=t[P%r],!_.skip&&(b=c(_[n]),b!==v&&(p=l(b,s,o),m===null&&S()&&(m=a(b,s)===0?P:O),m!==null&&w()&&(g.push(Ps({start:m,end:P,loop:u,count:r,style:f})),m=null),O=P,v=b));return m!==null&&g.push(Ps({start:m,end:d,loop:u,count:r,style:f})),g}function Gr(e,t){const i=[],n=e.segments;for(let s=0;s<n.length;s++){const o=Ur(n[s],e.points,t);o.length&&i.push(...o)}return i}function Tc(e,t,i,n){let s=0,o=t-1;if(i&&!n)for(;s<t&&!e[s].skip;)s++;for(;s<t&&e[s].skip;)s++;for(s%=t,i&&(o+=s);o>s&&e[o%t].skip;)o--;return o%=t,{start:s,end:o}}function Dc(e,t,i,n){const s=e.length,o=[];let r=t,a=e[t],l;for(l=t+1;l<=i;++l){const c=e[l%s];c.skip||c.stop?a.skip||(n=!1,o.push({start:t%s,end:(l-1)%s,loop:n}),t=r=c.stop?l:null):(r=l,a.skip&&(t=l)),a=c}return r!==null&&o.push({start:t%s,end:r%s,loop:n}),o}function Lc(e,t){const i=e.points,n=e.options.spanGaps,s=i.length;if(!s)return[];const o=!!e._loop,{start:r,end:a}=Tc(i,s,o,n);if(n===!0)return Os(e,[{start:r,end:a,loop:o}],i,t);const l=a<r?a+s:a,c=!!e._fullLoop&&r===0&&a===s-1;return Os(e,Dc(i,r,l,c),i,t)}function Os(e,t,i,n){return!n||!n.setContext||!i?t:Ec(e,t,i,n)}function Ec(e,t,i,n){const s=e._chart.getContext(),o=As(e.options),{_datasetIndex:r,options:{spanGaps:a}}=e,l=i.length,c=[];let h=o,d=t[0].start,u=d;function f(g,p,m,b){const _=a?-1:1;if(g!==p){for(g+=l;i[g%l].skip;)g-=_;for(;i[p%l].skip;)p+=_;g%l!==p%l&&(c.push({start:g%l,end:p%l,loop:m,style:b}),h=b,d=p%l)}}for(const g of t){d=a?d:g.start;let p=i[d%l],m;for(u=d+1;u<=g.end;u++){const b=i[u%l];m=As(n.setContext(Bt(s,{type:"segment",p0:p,p1:b,p0DataIndex:(u-1)%l,p1DataIndex:u%l,datasetIndex:r}))),$c(m,h)&&f(d,u-1,g.loop,h),p=b,h=m}d<u-1&&f(d,u-1,g.loop,h)}return c}function As(e){return{backgroundColor:e.backgroundColor,borderCapStyle:e.borderCapStyle,borderDash:e.borderDash,borderDashOffset:e.borderDashOffset,borderJoinStyle:e.borderJoinStyle,borderWidth:e.borderWidth,borderColor:e.borderColor}}function $c(e,t){if(!t)return!1;const i=[],n=function(s,o){return Fn(o)?(i.includes(o)||i.push(o),i.indexOf(o)):o};return JSON.stringify(e,n)!==JSON.stringify(t,n)}/*!
 * Chart.js v4.4.8
 * https://www.chartjs.org
 * (c) 2025 Chart.js Contributors
 * Released under the MIT License
 */class Rc{constructor(){this._request=null,this._charts=new Map,this._running=!1,this._lastDate=void 0}_notify(t,i,n,s){const o=i.listeners[s],r=i.duration;o.forEach(a=>a({chart:t,initial:i.initial,numSteps:r,currentStep:Math.min(n-i.start,r)}))}_refresh(){this._request||(this._running=!0,this._request=Tr.call(window,()=>{this._update(),this._request=null,this._running&&this._refresh()}))}_update(t=Date.now()){let i=0;this._charts.forEach((n,s)=>{if(!n.running||!n.items.length)return;const o=n.items;let r=o.length-1,a=!1,l;for(;r>=0;--r)l=o[r],l._active?(l._total>n.duration&&(n.duration=l._total),l.tick(t),a=!0):(o[r]=o[o.length-1],o.pop());a&&(s.draw(),this._notify(s,n,t,"progress")),o.length||(n.running=!1,this._notify(s,n,t,"complete"),n.initial=!1),i+=o.length}),this._lastDate=t,i===0&&(this._running=!1)}_getAnims(t){const i=this._charts;let n=i.get(t);return n||(n={running:!1,initial:!0,items:[],listeners:{complete:[],progress:[]}},i.set(t,n)),n}listen(t,i,n){this._getAnims(t).listeners[i].push(n)}add(t,i){!i||!i.length||this._getAnims(t).items.push(...i)}has(t){return this._getAnims(t).items.length>0}start(t){const i=this._charts.get(t);i&&(i.running=!0,i.start=Date.now(),i.duration=i.items.reduce((n,s)=>Math.max(n,s._duration),0),this._refresh())}running(t){if(!this._running)return!1;const i=this._charts.get(t);return!(!i||!i.running||!i.items.length)}stop(t){const i=this._charts.get(t);if(!i||!i.items.length)return;const n=i.items;let s=n.length-1;for(;s>=0;--s)n[s].cancel();i.items=[],this._notify(t,i,Date.now(),"complete")}remove(t){return this._charts.delete(t)}}var vt=new Rc;const ks="transparent",Ic={boolean(e,t,i){return i>.5?t:e},color(e,t,i){const n=_s(e||ks),s=n.valid&&_s(t||ks);return s&&s.valid?s.mix(n,i).hexString():t},number(e,t,i){return e+(t-e)*i}};class Fc{constructor(t,i,n,s){const o=i[n];s=Se([t.to,s,o,t.from]);const r=Se([t.from,o,s]);this._active=!0,this._fn=t.fn||Ic[t.type||typeof r],this._easing=De[t.easing]||De.linear,this._start=Math.floor(Date.now()+(t.delay||0)),this._duration=this._total=Math.floor(t.duration),this._loop=!!t.loop,this._target=i,this._prop=n,this._from=r,this._to=s,this._promises=void 0}active(){return this._active}update(t,i,n){if(this._active){this._notify(!1);const s=this._target[this._prop],o=n-this._start,r=this._duration-o;this._start=n,this._duration=Math.floor(Math.max(r,t.duration)),this._total+=o,this._loop=!!t.loop,this._to=Se([t.to,i,s,t.from]),this._from=Se([t.from,s,i])}}cancel(){this._active&&(this.tick(Date.now()),this._active=!1,this._notify(!1))}tick(t){const i=t-this._start,n=this._duration,s=this._prop,o=this._from,r=this._loop,a=this._to;let l;if(this._active=o!==a&&(r||i<n),!this._active){this._target[s]=a,this._notify(!0);return}if(i<0){this._target[s]=o;return}l=i/n%2,l=r&&l>1?2-l:l,l=this._easing(Math.min(1,Math.max(0,l))),this._target[s]=this._fn(o,a,l)}wait(){const t=this._promises||(this._promises=[]);return new Promise((i,n)=>{t.push({res:i,rej:n})})}_notify(t){const i=t?"res":"rej",n=this._promises||[];for(let s=0;s<n.length;s++)n[s][i]()}}class Xr{constructor(t,i){this._chart=t,this._properties=new Map,this.configure(i)}configure(t){if(!L(t))return;const i=Object.keys(H.animation),n=this._properties;Object.getOwnPropertyNames(t).forEach(s=>{const o=t[s];if(!L(o))return;const r={};for(const a of i)r[a]=o[a];(j(o.properties)&&o.properties||[s]).forEach(a=>{(a===s||!n.has(a))&&n.set(a,r)})})}_animateOptions(t,i){const n=i.options,s=Bc(t,n);if(!s)return[];const o=this._createAnimations(s,n);return n.$shared&&zc(t.options.$animations,n).then(()=>{t.options=n},()=>{}),o}_createAnimations(t,i){const n=this._properties,s=[],o=t.$animations||(t.$animations={}),r=Object.keys(i),a=Date.now();let l;for(l=r.length-1;l>=0;--l){const c=r[l];if(c.charAt(0)==="$")continue;if(c==="options"){s.push(...this._animateOptions(t,i));continue}const h=i[c];let d=o[c];const u=n.get(c);if(d)if(u&&d.active()){d.update(u,h,a);continue}else d.cancel();if(!u||!u.duration){t[c]=h;continue}o[c]=d=new Fc(u,t,c,h),s.push(d)}return s}update(t,i){if(this._properties.size===0){Object.assign(t,i);return}const n=this._createAnimations(t,i);if(n.length)return vt.add(this._chart,n),!0}}function zc(e,t){const i=[],n=Object.keys(t);for(let s=0;s<n.length;s++){const o=e[n[s]];o&&o.active()&&i.push(o.wait())}return Promise.all(i)}function Bc(e,t){if(!t)return;let i=e.options;if(!i){e.options=t;return}return i.$shared&&(e.options=i=Object.assign({},i,{$shared:!1,$animations:{}})),i}function Cs(e,t){const i=e&&e.options||{},n=i.reverse,s=i.min===void 0?t:0,o=i.max===void 0?t:0;return{start:n?o:s,end:n?s:o}}function Nc(e,t,i){if(i===!1)return!1;const n=Cs(e,i),s=Cs(t,i);return{top:s.end,right:n.end,bottom:s.start,left:n.start}}function jc(e){let t,i,n,s;return L(e)?(t=e.top,i=e.right,n=e.bottom,s=e.left):t=i=n=s=e,{top:t,right:i,bottom:n,left:s,disabled:e===!1}}function Kr(e,t){const i=[],n=e._getSortedDatasetMetas(t);let s,o;for(s=0,o=n.length;s<o;++s)i.push(n[s].index);return i}function Ts(e,t,i,n={}){const s=e.keys,o=n.mode==="single";let r,a,l,c;if(t===null)return;let h=!1;for(r=0,a=s.length;r<a;++r){if(l=+s[r],l===i){if(h=!0,n.all)continue;break}c=e.values[l],V(c)&&(o||t===0||mt(t)===mt(c))&&(t+=c)}return!h&&!n.all?0:t}function Hc(e,t){const{iScale:i,vScale:n}=t,s=i.axis==="x"?"x":"y",o=n.axis==="x"?"x":"y",r=Object.keys(e),a=new Array(r.length);let l,c,h;for(l=0,c=r.length;l<c;++l)h=r[l],a[l]={[s]:h,[o]:e[h]};return a}function Gi(e,t){const i=e&&e.options.stacked;return i||i===void 0&&t.stack!==void 0}function Wc(e,t,i){return`${e.id}.${t.id}.${i.stack||i.type}`}function Vc(e){const{min:t,max:i,minDefined:n,maxDefined:s}=e.getUserBounds();return{min:n?t:Number.NEGATIVE_INFINITY,max:s?i:Number.POSITIVE_INFINITY}}function Yc(e,t,i){const n=e[t]||(e[t]={});return n[i]||(n[i]={})}function Ds(e,t,i,n){for(const s of t.getMatchingVisibleMetas(n).reverse()){const o=e[s.index];if(i&&o>0||!i&&o<0)return s.index}return null}function Ls(e,t){const{chart:i,_cachedMeta:n}=e,s=i._stacks||(i._stacks={}),{iScale:o,vScale:r,index:a}=n,l=o.axis,c=r.axis,h=Wc(o,r,n),d=t.length;let u;for(let f=0;f<d;++f){const g=t[f],{[l]:p,[c]:m}=g,b=g._stacks||(g._stacks={});u=b[c]=Yc(s,h,p),u[a]=m,u._top=Ds(u,r,!0,n.type),u._bottom=Ds(u,r,!1,n.type);const _=u._visualValues||(u._visualValues={});_[a]=m}}function Xi(e,t){const i=e.scales;return Object.keys(i).filter(n=>i[n].axis===t).shift()}function Uc(e,t){return Bt(e,{active:!1,dataset:void 0,datasetIndex:t,index:t,mode:"default",type:"dataset"})}function Gc(e,t,i){return Bt(e,{active:!1,dataIndex:t,parsed:void 0,raw:void 0,element:i,index:t,mode:"default",type:"data"})}function me(e,t){const i=e.controller.index,n=e.vScale&&e.vScale.axis;if(n){t=t||e._parsed;for(const s of t){const o=s._stacks;if(!o||o[n]===void 0||o[n][i]===void 0)return;delete o[n][i],o[n]._visualValues!==void 0&&o[n]._visualValues[i]!==void 0&&delete o[n]._visualValues[i]}}}const Ki=e=>e==="reset"||e==="none",Es=(e,t)=>t?e:Object.assign({},e),Xc=(e,t,i)=>e&&!t.hidden&&t._stacked&&{keys:Kr(i,!0),values:null};class dt{constructor(t,i){this.chart=t,this._ctx=t.ctx,this.index=i,this._cachedDataOpts={},this._cachedMeta=this.getMeta(),this._type=this._cachedMeta.type,this.options=void 0,this._parsing=!1,this._data=void 0,this._objectData=void 0,this._sharedOptions=void 0,this._drawStart=void 0,this._drawCount=void 0,this.enableOptionSharing=!1,this.supportsDecimation=!1,this.$context=void 0,this._syncList=[],this.datasetElementType=new.target.datasetElementType,this.dataElementType=new.target.dataElementType,this.initialize()}initialize(){const t=this._cachedMeta;this.configure(),this.linkScales(),t._stacked=Gi(t.vScale,t),this.addElements(),this.options.fill&&!this.chart.isPluginEnabled("filler")&&console.warn("Tried to use the 'fill' option without the 'Filler' plugin enabled. Please import and register the 'Filler' plugin and make sure it is not disabled in the options")}updateIndex(t){this.index!==t&&me(this._cachedMeta),this.index=t}linkScales(){const t=this.chart,i=this._cachedMeta,n=this.getDataset(),s=(d,u,f,g)=>d==="x"?u:d==="r"?g:f,o=i.xAxisID=C(n.xAxisID,Xi(t,"x")),r=i.yAxisID=C(n.yAxisID,Xi(t,"y")),a=i.rAxisID=C(n.rAxisID,Xi(t,"r")),l=i.indexAxis,c=i.iAxisID=s(l,o,r,a),h=i.vAxisID=s(l,r,o,a);i.xScale=this.getScaleForId(o),i.yScale=this.getScaleForId(r),i.rScale=this.getScaleForId(a),i.iScale=this.getScaleForId(c),i.vScale=this.getScaleForId(h)}getDataset(){return this.chart.data.datasets[this.index]}getMeta(){return this.chart.getDatasetMeta(this.index)}getScaleForId(t){return this.chart.scales[t]}_getOtherScale(t){const i=this._cachedMeta;return t===i.iScale?i.vScale:i.iScale}reset(){this._update("reset")}_destroy(){const t=this._cachedMeta;this._data&&ps(this._data,this),t._stacked&&me(t)}_dataCheck(){const t=this.getDataset(),i=t.data||(t.data=[]),n=this._data;if(L(i)){const s=this._cachedMeta;this._data=Hc(i,s)}else if(n!==i){if(n){ps(n,this);const s=this._cachedMeta;me(s),s._parsed=[]}i&&Object.isExtensible(i)&&El(i,this),this._syncList=[],this._data=i}}addElements(){const t=this._cachedMeta;this._dataCheck(),this.datasetElementType&&(t.dataset=new this.datasetElementType)}buildOrUpdateElements(t){const i=this._cachedMeta,n=this.getDataset();let s=!1;this._dataCheck();const o=i._stacked;i._stacked=Gi(i.vScale,i),i.stack!==n.stack&&(s=!0,me(i),i.stack=n.stack),this._resyncElements(t),(s||o!==i._stacked)&&(Ls(this,i._parsed),i._stacked=Gi(i.vScale,i))}configure(){const t=this.chart.config,i=t.datasetScopeKeys(this._type),n=t.getOptionScopes(this.getDataset(),i,!0);this.options=t.createResolver(n,this.getContext()),this._parsing=this.options.parsing,this._cachedDataOpts={}}parse(t,i){const{_cachedMeta:n,_data:s}=this,{iScale:o,_stacked:r}=n,a=o.axis;let l=t===0&&i===s.length?!0:n._sorted,c=t>0&&n._parsed[t-1],h,d,u;if(this._parsing===!1)n._parsed=s,n._sorted=!0,u=s;else{j(s[t])?u=this.parseArrayData(n,s,t,i):L(s[t])?u=this.parseObjectData(n,s,t,i):u=this.parsePrimitiveData(n,s,t,i);const f=()=>d[a]===null||c&&d[a]<c[a];for(h=0;h<i;++h)n._parsed[h+t]=d=u[h],l&&(f()&&(l=!1),c=d);n._sorted=l}r&&Ls(this,u)}parsePrimitiveData(t,i,n,s){const{iScale:o,vScale:r}=t,a=o.axis,l=r.axis,c=o.getLabels(),h=o===r,d=new Array(s);let u,f,g;for(u=0,f=s;u<f;++u)g=u+n,d[u]={[a]:h||o.parse(c[g],g),[l]:r.parse(i[g],g)};return d}parseArrayData(t,i,n,s){const{xScale:o,yScale:r}=t,a=new Array(s);let l,c,h,d;for(l=0,c=s;l<c;++l)h=l+n,d=i[h],a[l]={x:o.parse(d[0],h),y:r.parse(d[1],h)};return a}parseObjectData(t,i,n,s){const{xScale:o,yScale:r}=t,{xAxisKey:a="x",yAxisKey:l="y"}=this._parsing,c=new Array(s);let h,d,u,f;for(h=0,d=s;h<d;++h)u=h+n,f=i[u],c[h]={x:o.parse(Ft(f,a),u),y:r.parse(Ft(f,l),u)};return c}getParsed(t){return this._cachedMeta._parsed[t]}getDataElement(t){return this._cachedMeta.data[t]}applyStack(t,i,n){const s=this.chart,o=this._cachedMeta,r=i[t.axis],a={keys:Kr(s,!0),values:i._stacks[t.axis]._visualValues};return Ts(a,r,o.index,{mode:n})}updateRangeFromParsed(t,i,n,s){const o=n[i.axis];let r=o===null?NaN:o;const a=s&&n._stacks[i.axis];s&&a&&(s.values=a,r=Ts(s,o,this._cachedMeta.index)),t.min=Math.min(t.min,r),t.max=Math.max(t.max,r)}getMinMax(t,i){const n=this._cachedMeta,s=n._parsed,o=n._sorted&&t===n.iScale,r=s.length,a=this._getOtherScale(t),l=Xc(i,n,this.chart),c={min:Number.POSITIVE_INFINITY,max:Number.NEGATIVE_INFINITY},{min:h,max:d}=Vc(a);let u,f;function g(){f=s[u];const p=f[a.axis];return!V(f[t.axis])||h>p||d<p}for(u=0;u<r&&!(!g()&&(this.updateRangeFromParsed(c,t,f,l),o));++u);if(o){for(u=r-1;u>=0;--u)if(!g()){this.updateRangeFromParsed(c,t,f,l);break}}return c}getAllParsedValues(t){const i=this._cachedMeta._parsed,n=[];let s,o,r;for(s=0,o=i.length;s<o;++s)r=i[s][t.axis],V(r)&&n.push(r);return n}getMaxOverflow(){return!1}getLabelAndValue(t){const i=this._cachedMeta,n=i.iScale,s=i.vScale,o=this.getParsed(t);return{label:n?""+n.getLabelForValue(o[n.axis]):"",value:s?""+s.getLabelForValue(o[s.axis]):""}}_update(t){const i=this._cachedMeta;this.update(t||"default"),i._clip=jc(C(this.options.clip,Nc(i.xScale,i.yScale,this.getMaxOverflow())))}update(t){}draw(){const t=this._ctx,i=this.chart,n=this._cachedMeta,s=n.data||[],o=i.chartArea,r=[],a=this._drawStart||0,l=this._drawCount||s.length-a,c=this.options.drawActiveElementsOnTop;let h;for(n.dataset&&n.dataset.draw(t,o,a,l),h=a;h<a+l;++h){const d=s[h];d.hidden||(d.active&&c?r.push(d):d.draw(t,o))}for(h=0;h<r.length;++h)r[h].draw(t,o)}getStyle(t,i){const n=i?"active":"default";return t===void 0&&this._cachedMeta.dataset?this.resolveDatasetElementOptions(n):this.resolveDataElementOptions(t||0,n)}getContext(t,i,n){const s=this.getDataset();let o;if(t>=0&&t<this._cachedMeta.data.length){const r=this._cachedMeta.data[t];o=r.$context||(r.$context=Gc(this.getContext(),t,r)),o.parsed=this.getParsed(t),o.raw=s.data[t],o.index=o.dataIndex=t}else o=this.$context||(this.$context=Uc(this.chart.getContext(),this.index)),o.dataset=s,o.index=o.datasetIndex=this.index;return o.active=!!i,o.mode=n,o}resolveDatasetElementOptions(t){return this._resolveElementOptions(this.datasetElementType.id,t)}resolveDataElementOptions(t,i){return this._resolveElementOptions(this.dataElementType.id,i,t)}_resolveElementOptions(t,i="default",n){const s=i==="active",o=this._cachedDataOpts,r=t+"-"+i,a=o[r],l=this.enableOptionSharing&&Ie(n);if(a)return Es(a,l);const c=this.chart.config,h=c.datasetElementScopeKeys(this._type,t),d=s?[`${t}Hover`,"hover",t,""]:[t,""],u=c.getOptionScopes(this.getDataset(),h),f=Object.keys(H.elements[t]),g=()=>this.getContext(n,s,i),p=c.resolveNamedOptions(u,f,g,d);return p.$shared&&(p.$shared=l,o[r]=Object.freeze(Es(p,l))),p}_resolveAnimations(t,i,n){const s=this.chart,o=this._cachedDataOpts,r=`animation-${i}`,a=o[r];if(a)return a;let l;if(s.options.animation!==!1){const h=this.chart.config,d=h.datasetAnimationScopeKeys(this._type,i),u=h.getOptionScopes(this.getDataset(),d);l=h.createResolver(u,this.getContext(t,n,i))}const c=new Xr(s,l&&l.animations);return l&&l._cacheable&&(o[r]=Object.freeze(c)),c}getSharedOptions(t){if(t.$shared)return this._sharedOptions||(this._sharedOptions=Object.assign({},t))}includeOptions(t,i){return!i||Ki(t)||this.chart._animationsDisabled}_getSharedOptions(t,i){const n=this.resolveDataElementOptions(t,i),s=this._sharedOptions,o=this.getSharedOptions(n),r=this.includeOptions(i,o)||o!==s;return this.updateSharedOptions(o,i,n),{sharedOptions:o,includeOptions:r}}updateElement(t,i,n,s){Ki(s)?Object.assign(t,n):this._resolveAnimations(i,s).update(t,n)}updateSharedOptions(t,i,n){t&&!Ki(i)&&this._resolveAnimations(void 0,i).update(t,n)}_setStyle(t,i,n,s){t.active=s;const o=this.getStyle(i,s);this._resolveAnimations(i,n,s).update(t,{options:!s&&this.getSharedOptions(o)||o})}removeHoverStyle(t,i,n){this._setStyle(t,n,"active",!1)}setHoverStyle(t,i,n){this._setStyle(t,n,"active",!0)}_removeDatasetHoverStyle(){const t=this._cachedMeta.dataset;t&&this._setStyle(t,void 0,"active",!1)}_setDatasetHoverStyle(){const t=this._cachedMeta.dataset;t&&this._setStyle(t,void 0,"active",!0)}_resyncElements(t){const i=this._data,n=this._cachedMeta.data;for(const[a,l,c]of this._syncList)this[a](l,c);this._syncList=[];const s=n.length,o=i.length,r=Math.min(o,s);r&&this.parse(0,r),o>s?this._insertElements(s,o-s,t):o<s&&this._removeElements(o,s-o)}_insertElements(t,i,n=!0){const s=this._cachedMeta,o=s.data,r=t+i;let a;const l=c=>{for(c.length+=i,a=c.length-1;a>=r;a--)c[a]=c[a-i]};for(l(o),a=t;a<r;++a)o[a]=new this.dataElementType;this._parsing&&l(s._parsed),this.parse(t,i),n&&this.updateElements(o,t,i,"reset")}updateElements(t,i,n,s){}_removeElements(t,i){const n=this._cachedMeta;if(this._parsing){const s=n._parsed.splice(t,i);n._stacked&&me(n,s)}n.data.splice(t,i)}_sync(t){if(this._parsing)this._syncList.push(t);else{const[i,n,s]=t;this[i](n,s)}this.chart._dataChanges.push([this.index,...t])}_onDataPush(){const t=arguments.length;this._sync(["_insertElements",this.getDataset().data.length-t,t])}_onDataPop(){this._sync(["_removeElements",this._cachedMeta.data.length-1,1])}_onDataShift(){this._sync(["_removeElements",0,1])}_onDataSplice(t,i){i&&this._sync(["_removeElements",t,i]);const n=arguments.length-2;n&&this._sync(["_insertElements",t,n])}_onDataUnshift(){this._sync(["_insertElements",0,arguments.length])}}M(dt,"defaults",{}),M(dt,"datasetElementType",null),M(dt,"dataElementType",null);function Kc(e,t){if(!e._cache.$bar){const i=e.getMatchingVisibleMetas(t);let n=[];for(let s=0,o=i.length;s<o;s++)n=n.concat(i[s].controller.getAllParsedValues(e));e._cache.$bar=Cr(n.sort((s,o)=>s-o))}return e._cache.$bar}function qc(e){const t=e.iScale,i=Kc(t,e.type);let n=t._length,s,o,r,a;const l=()=>{r===32767||r===-32768||(Ie(a)&&(n=Math.min(n,Math.abs(r-a)||n)),a=r)};for(s=0,o=i.length;s<o;++s)r=t.getPixelForValue(i[s]),l();for(a=void 0,s=0,o=t.ticks.length;s<o;++s)r=t.getPixelForTick(s),l();return n}function Zc(e,t,i,n){const s=i.barThickness;let o,r;return D(s)?(o=t.min*i.categoryPercentage,r=i.barPercentage):(o=s*n,r=1),{chunk:o/n,ratio:r,start:t.pixels[e]-o/2}}function Jc(e,t,i,n){const s=t.pixels,o=s[e];let r=e>0?s[e-1]:null,a=e<s.length-1?s[e+1]:null;const l=i.categoryPercentage;r===null&&(r=o-(a===null?t.end-t.start:a-o)),a===null&&(a=o+o-r);const c=o-(o-Math.min(r,a))/2*l;return{chunk:Math.abs(a-r)/2*l/n,ratio:i.barPercentage,start:c}}function Qc(e,t,i,n){const s=i.parse(e[0],n),o=i.parse(e[1],n),r=Math.min(s,o),a=Math.max(s,o);let l=r,c=a;Math.abs(r)>Math.abs(a)&&(l=a,c=r),t[i.axis]=c,t._custom={barStart:l,barEnd:c,start:s,end:o,min:r,max:a}}function qr(e,t,i,n){return j(e)?Qc(e,t,i,n):t[i.axis]=i.parse(e,n),t}function $s(e,t,i,n){const s=e.iScale,o=e.vScale,r=s.getLabels(),a=s===o,l=[];let c,h,d,u;for(c=i,h=i+n;c<h;++c)u=t[c],d={},d[s.axis]=a||s.parse(r[c],c),l.push(qr(u,d,o,c));return l}function qi(e){return e&&e.barStart!==void 0&&e.barEnd!==void 0}function th(e,t,i){return e!==0?mt(e):(t.isHorizontal()?1:-1)*(t.min>=i?1:-1)}function eh(e){let t,i,n,s,o;return e.horizontal?(t=e.base>e.x,i="left",n="right"):(t=e.base<e.y,i="bottom",n="top"),t?(s="end",o="start"):(s="start",o="end"),{start:i,end:n,reverse:t,top:s,bottom:o}}function ih(e,t,i,n){let s=t.borderSkipped;const o={};if(!s){e.borderSkipped=o;return}if(s===!0){e.borderSkipped={top:!0,right:!0,bottom:!0,left:!0};return}const{start:r,end:a,reverse:l,top:c,bottom:h}=eh(e);s==="middle"&&i&&(e.enableBorderRadius=!0,(i._top||0)===n?s=c:(i._bottom||0)===n?s=h:(o[Rs(h,r,a,l)]=!0,s=c)),o[Rs(s,r,a,l)]=!0,e.borderSkipped=o}function Rs(e,t,i,n){return n?(e=nh(e,t,i),e=Is(e,i,t)):e=Is(e,t,i),e}function nh(e,t,i){return e===t?i:e===i?t:e}function Is(e,t,i){return e==="start"?t:e==="end"?i:e}function sh(e,{inflateAmount:t},i){e.inflateAmount=t==="auto"?i===1?.33:0:t}class li extends dt{parsePrimitiveData(t,i,n,s){return $s(t,i,n,s)}parseArrayData(t,i,n,s){return $s(t,i,n,s)}parseObjectData(t,i,n,s){const{iScale:o,vScale:r}=t,{xAxisKey:a="x",yAxisKey:l="y"}=this._parsing,c=o.axis==="x"?a:l,h=r.axis==="x"?a:l,d=[];let u,f,g,p;for(u=n,f=n+s;u<f;++u)p=i[u],g={},g[o.axis]=o.parse(Ft(p,c),u),d.push(qr(Ft(p,h),g,r,u));return d}updateRangeFromParsed(t,i,n,s){super.updateRangeFromParsed(t,i,n,s);const o=n._custom;o&&i===this._cachedMeta.vScale&&(t.min=Math.min(t.min,o.min),t.max=Math.max(t.max,o.max))}getMaxOverflow(){return 0}getLabelAndValue(t){const i=this._cachedMeta,{iScale:n,vScale:s}=i,o=this.getParsed(t),r=o._custom,a=qi(r)?"["+r.start+", "+r.end+"]":""+s.getLabelForValue(o[s.axis]);return{label:""+n.getLabelForValue(o[n.axis]),value:a}}initialize(){this.enableOptionSharing=!0,super.initialize();const t=this._cachedMeta;t.stack=this.getDataset().stack}update(t){const i=this._cachedMeta;this.updateElements(i.data,0,i.data.length,t)}updateElements(t,i,n,s){const o=s==="reset",{index:r,_cachedMeta:{vScale:a}}=this,l=a.getBasePixel(),c=a.isHorizontal(),h=this._getRuler(),{sharedOptions:d,includeOptions:u}=this._getSharedOptions(i,s);for(let f=i;f<i+n;f++){const g=this.getParsed(f),p=o||D(g[a.axis])?{base:l,head:l}:this._calculateBarValuePixels(f),m=this._calculateBarIndexPixels(f,h),b=(g._stacks||{})[a.axis],_={horizontal:c,base:p.base,enableBorderRadius:!b||qi(g._custom)||r===b._top||r===b._bottom,x:c?p.head:m.center,y:c?m.center:p.head,height:c?m.size:Math.abs(p.size),width:c?Math.abs(p.size):m.size};u&&(_.options=d||this.resolveDataElementOptions(f,t[f].active?"active":s));const v=_.options||t[f].options;ih(_,v,b,r),sh(_,v,h.ratio),this.updateElement(t[f],f,_,s)}}_getStacks(t,i){const{iScale:n}=this._cachedMeta,s=n.getMatchingVisibleMetas(this._type).filter(h=>h.controller.options.grouped),o=n.options.stacked,r=[],a=this._cachedMeta.controller.getParsed(i),l=a&&a[n.axis],c=h=>{const d=h._parsed.find(f=>f[n.axis]===l),u=d&&d[h.vScale.axis];if(D(u)||isNaN(u))return!0};for(const h of s)if(!(i!==void 0&&c(h))&&((o===!1||r.indexOf(h.stack)===-1||o===void 0&&h.stack===void 0)&&r.push(h.stack),h.index===t))break;return r.length||r.push(void 0),r}_getStackCount(t){return this._getStacks(void 0,t).length}_getStackIndex(t,i,n){const s=this._getStacks(t,n),o=i!==void 0?s.indexOf(i):-1;return o===-1?s.length-1:o}_getRuler(){const t=this.options,i=this._cachedMeta,n=i.iScale,s=[];let o,r;for(o=0,r=i.data.length;o<r;++o)s.push(n.getPixelForValue(this.getParsed(o)[n.axis],o));const a=t.barThickness;return{min:a||qc(i),pixels:s,start:n._startPixel,end:n._endPixel,stackCount:this._getStackCount(),scale:n,grouped:t.grouped,ratio:a?1:t.categoryPercentage*t.barPercentage}}_calculateBarValuePixels(t){const{_cachedMeta:{vScale:i,_stacked:n,index:s},options:{base:o,minBarLength:r}}=this,a=o||0,l=this.getParsed(t),c=l._custom,h=qi(c);let d=l[i.axis],u=0,f=n?this.applyStack(i,l,n):d,g,p;f!==d&&(u=f-d,f=d),h&&(d=c.barStart,f=c.barEnd-c.barStart,d!==0&&mt(d)!==mt(c.barEnd)&&(u=0),u+=d);const m=!D(o)&&!h?o:u;let b=i.getPixelForValue(m);if(this.chart.getDataVisibility(t)?g=i.getPixelForValue(u+f):g=b,p=g-b,Math.abs(p)<r){p=th(p,i,a)*r,d===a&&(b-=p/2);const _=i.getPixelForDecimal(0),v=i.getPixelForDecimal(1),x=Math.min(_,v),y=Math.max(_,v);b=Math.max(Math.min(b,y),x),g=b+p,n&&!h&&(l._stacks[i.axis]._visualValues[s]=i.getValueForPixel(g)-i.getValueForPixel(b))}if(b===i.getPixelForValue(a)){const _=mt(p)*i.getLineWidthForValue(a)/2;b+=_,p-=_}return{size:p,base:b,head:g,center:g+p/2}}_calculateBarIndexPixels(t,i){const n=i.scale,s=this.options,o=s.skipNull,r=C(s.maxBarThickness,1/0);let a,l;if(i.grouped){const c=o?this._getStackCount(t):i.stackCount,h=s.barThickness==="flex"?Jc(t,i,s,c):Zc(t,i,s,c),d=this._getStackIndex(this.index,this._cachedMeta.stack,o?t:void 0);a=h.start+h.chunk*d+h.chunk/2,l=Math.min(r,h.chunk*h.ratio)}else a=n.getPixelForValue(this.getParsed(t)[n.axis],t),l=Math.min(r,i.min*i.ratio);return{base:a-l/2,head:a+l/2,center:a,size:l}}draw(){const t=this._cachedMeta,i=t.vScale,n=t.data,s=n.length;let o=0;for(;o<s;++o)this.getParsed(o)[i.axis]!==null&&!n[o].hidden&&n[o].draw(this._ctx)}}M(li,"id","bar"),M(li,"defaults",{datasetElementType:!1,dataElementType:"bar",categoryPercentage:.8,barPercentage:.9,grouped:!0,animations:{numbers:{type:"number",properties:["x","y","base","width","height"]}}}),M(li,"overrides",{scales:{_index_:{type:"category",offset:!0,grid:{offset:!0}},_value_:{type:"linear",beginAtZero:!0}}});class ci extends dt{initialize(){this.enableOptionSharing=!0,super.initialize()}parsePrimitiveData(t,i,n,s){const o=super.parsePrimitiveData(t,i,n,s);for(let r=0;r<o.length;r++)o[r]._custom=this.resolveDataElementOptions(r+n).radius;return o}parseArrayData(t,i,n,s){const o=super.parseArrayData(t,i,n,s);for(let r=0;r<o.length;r++){const a=i[n+r];o[r]._custom=C(a[2],this.resolveDataElementOptions(r+n).radius)}return o}parseObjectData(t,i,n,s){const o=super.parseObjectData(t,i,n,s);for(let r=0;r<o.length;r++){const a=i[n+r];o[r]._custom=C(a&&a.r&&+a.r,this.resolveDataElementOptions(r+n).radius)}return o}getMaxOverflow(){const t=this._cachedMeta.data;let i=0;for(let n=t.length-1;n>=0;--n)i=Math.max(i,t[n].size(this.resolveDataElementOptions(n))/2);return i>0&&i}getLabelAndValue(t){const i=this._cachedMeta,n=this.chart.data.labels||[],{xScale:s,yScale:o}=i,r=this.getParsed(t),a=s.getLabelForValue(r.x),l=o.getLabelForValue(r.y),c=r._custom;return{label:n[t]||"",value:"("+a+", "+l+(c?", "+c:"")+")"}}update(t){const i=this._cachedMeta.data;this.updateElements(i,0,i.length,t)}updateElements(t,i,n,s){const o=s==="reset",{iScale:r,vScale:a}=this._cachedMeta,{sharedOptions:l,includeOptions:c}=this._getSharedOptions(i,s),h=r.axis,d=a.axis;for(let u=i;u<i+n;u++){const f=t[u],g=!o&&this.getParsed(u),p={},m=p[h]=o?r.getPixelForDecimal(.5):r.getPixelForValue(g[h]),b=p[d]=o?a.getBasePixel():a.getPixelForValue(g[d]);p.skip=isNaN(m)||isNaN(b),c&&(p.options=l||this.resolveDataElementOptions(u,f.active?"active":s),o&&(p.options.radius=0)),this.updateElement(f,u,p,s)}}resolveDataElementOptions(t,i){const n=this.getParsed(t);let s=super.resolveDataElementOptions(t,i);s.$shared&&(s=Object.assign({},s,{$shared:!1}));const o=s.radius;return i!=="active"&&(s.radius=0),s.radius+=C(n&&n._custom,o),s}}M(ci,"id","bubble"),M(ci,"defaults",{datasetElementType:!1,dataElementType:"point",animations:{numbers:{type:"number",properties:["x","y","borderWidth","radius"]}}}),M(ci,"overrides",{scales:{x:{type:"linear"},y:{type:"linear"}}});function oh(e,t,i){let n=1,s=1,o=0,r=0;if(t<z){const a=e,l=a+t,c=Math.cos(a),h=Math.sin(a),d=Math.cos(l),u=Math.sin(l),f=(v,x,y)=>Fe(v,a,l,!0)?1:Math.max(x,x*i,y,y*i),g=(v,x,y)=>Fe(v,a,l,!0)?-1:Math.min(x,x*i,y,y*i),p=f(0,c,d),m=f(Y,h,u),b=g(B,c,d),_=g(B+Y,h,u);n=(p-b)/2,s=(m-_)/2,o=-(p+b)/2,r=-(m+_)/2}return{ratioX:n,ratioY:s,offsetX:o,offsetY:r}}class Kt extends dt{constructor(t,i){super(t,i),this.enableOptionSharing=!0,this.innerRadius=void 0,this.outerRadius=void 0,this.offsetX=void 0,this.offsetY=void 0}linkScales(){}parse(t,i){const n=this.getDataset().data,s=this._cachedMeta;if(this._parsing===!1)s._parsed=n;else{let o=l=>+n[l];if(L(n[t])){const{key:l="value"}=this._parsing;o=c=>+Ft(n[c],l)}let r,a;for(r=t,a=t+i;r<a;++r)s._parsed[r]=o(r)}}_getRotation(){return ht(this.options.rotation-90)}_getCircumference(){return ht(this.options.circumference)}_getRotationExtents(){let t=z,i=-z;for(let n=0;n<this.chart.data.datasets.length;++n)if(this.chart.isDatasetVisible(n)&&this.chart.getDatasetMeta(n).type===this._type){const s=this.chart.getDatasetMeta(n).controller,o=s._getRotation(),r=s._getCircumference();t=Math.min(t,o),i=Math.max(i,o+r)}return{rotation:t,circumference:i-t}}update(t){const i=this.chart,{chartArea:n}=i,s=this._cachedMeta,o=s.data,r=this.getMaxBorderWidth()+this.getMaxOffset(o)+this.options.spacing,a=Math.max((Math.min(n.width,n.height)-r)/2,0),l=Math.min(_l(this.options.cutout,a),1),c=this._getRingWeight(this.index),{circumference:h,rotation:d}=this._getRotationExtents(),{ratioX:u,ratioY:f,offsetX:g,offsetY:p}=oh(d,h,l),m=(n.width-r)/u,b=(n.height-r)/f,_=Math.max(Math.min(m,b)/2,0),v=wr(this.options.radius,_),x=Math.max(v*l,0),y=(v-x)/this._getVisibleDatasetWeightTotal();this.offsetX=g*v,this.offsetY=p*v,s.total=this.calculateTotal(),this.outerRadius=v-y*this._getRingWeightOffset(this.index),this.innerRadius=Math.max(this.outerRadius-y*c,0),this.updateElements(o,0,o.length,t)}_circumference(t,i){const n=this.options,s=this._cachedMeta,o=this._getCircumference();return i&&n.animation.animateRotate||!this.chart.getDataVisibility(t)||s._parsed[t]===null||s.data[t].hidden?0:this.calculateCircumference(s._parsed[t]*o/z)}updateElements(t,i,n,s){const o=s==="reset",r=this.chart,a=r.chartArea,c=r.options.animation,h=(a.left+a.right)/2,d=(a.top+a.bottom)/2,u=o&&c.animateScale,f=u?0:this.innerRadius,g=u?0:this.outerRadius,{sharedOptions:p,includeOptions:m}=this._getSharedOptions(i,s);let b=this._getRotation(),_;for(_=0;_<i;++_)b+=this._circumference(_,o);for(_=i;_<i+n;++_){const v=this._circumference(_,o),x=t[_],y={x:h+this.offsetX,y:d+this.offsetY,startAngle:b,endAngle:b+v,circumference:v,outerRadius:g,innerRadius:f};m&&(y.options=p||this.resolveDataElementOptions(_,x.active?"active":s)),b+=v,this.updateElement(x,_,y,s)}}calculateTotal(){const t=this._cachedMeta,i=t.data;let n=0,s;for(s=0;s<i.length;s++){const o=t._parsed[s];o!==null&&!isNaN(o)&&this.chart.getDataVisibility(s)&&!i[s].hidden&&(n+=Math.abs(o))}return n}calculateCircumference(t){const i=this._cachedMeta.total;return i>0&&!isNaN(t)?z*(Math.abs(t)/i):0}getLabelAndValue(t){const i=this._cachedMeta,n=this.chart,s=n.data.labels||[],o=We(i._parsed[t],n.options.locale);return{label:s[t]||"",value:o}}getMaxBorderWidth(t){let i=0;const n=this.chart;let s,o,r,a,l;if(!t){for(s=0,o=n.data.datasets.length;s<o;++s)if(n.isDatasetVisible(s)){r=n.getDatasetMeta(s),t=r.data,a=r.controller;break}}if(!t)return 0;for(s=0,o=t.length;s<o;++s)l=a.resolveDataElementOptions(s),l.borderAlign!=="inner"&&(i=Math.max(i,l.borderWidth||0,l.hoverBorderWidth||0));return i}getMaxOffset(t){let i=0;for(let n=0,s=t.length;n<s;++n){const o=this.resolveDataElementOptions(n);i=Math.max(i,o.offset||0,o.hoverOffset||0)}return i}_getRingWeightOffset(t){let i=0;for(let n=0;n<t;++n)this.chart.isDatasetVisible(n)&&(i+=this._getRingWeight(n));return i}_getRingWeight(t){return Math.max(C(this.chart.data.datasets[t].weight,1),0)}_getVisibleDatasetWeightTotal(){return this._getRingWeightOffset(this.chart.data.datasets.length)||1}}M(Kt,"id","doughnut"),M(Kt,"defaults",{datasetElementType:!1,dataElementType:"arc",animation:{animateRotate:!0,animateScale:!1},animations:{numbers:{type:"number",properties:["circumference","endAngle","innerRadius","outerRadius","startAngle","x","y","offset","borderWidth","spacing"]}},cutout:"50%",rotation:0,circumference:360,radius:"100%",spacing:0,indexAxis:"r"}),M(Kt,"descriptors",{_scriptable:t=>t!=="spacing",_indexable:t=>t!=="spacing"&&!t.startsWith("borderDash")&&!t.startsWith("hoverBorderDash")}),M(Kt,"overrides",{aspectRatio:1,plugins:{legend:{labels:{generateLabels(t){const i=t.data;if(i.labels.length&&i.datasets.length){const{labels:{pointStyle:n,color:s}}=t.legend.options;return i.labels.map((o,r)=>{const l=t.getDatasetMeta(0).controller.getStyle(r);return{text:o,fillStyle:l.backgroundColor,strokeStyle:l.borderColor,fontColor:s,lineWidth:l.borderWidth,pointStyle:n,hidden:!t.getDataVisibility(r),index:r}})}return[]}},onClick(t,i,n){n.chart.toggleDataVisibility(i.index),n.chart.update()}}}});class hi extends dt{initialize(){this.enableOptionSharing=!0,this.supportsDecimation=!0,super.initialize()}update(t){const i=this._cachedMeta,{dataset:n,data:s=[],_dataset:o}=i,r=this.chart._animationsDisabled;let{start:a,count:l}=Lr(i,s,r);this._drawStart=a,this._drawCount=l,Er(i)&&(a=0,l=s.length),n._chart=this.chart,n._datasetIndex=this.index,n._decimated=!!o._decimated,n.points=s;const c=this.resolveDatasetElementOptions(t);this.options.showLine||(c.borderWidth=0),c.segment=this.options.segment,this.updateElement(n,void 0,{animated:!r,options:c},t),this.updateElements(s,a,l,t)}updateElements(t,i,n,s){const o=s==="reset",{iScale:r,vScale:a,_stacked:l,_dataset:c}=this._cachedMeta,{sharedOptions:h,includeOptions:d}=this._getSharedOptions(i,s),u=r.axis,f=a.axis,{spanGaps:g,segment:p}=this.options,m=re(g)?g:Number.POSITIVE_INFINITY,b=this.chart._animationsDisabled||o||s==="none",_=i+n,v=t.length;let x=i>0&&this.getParsed(i-1);for(let y=0;y<v;++y){const S=t[y],w=b?S:{};if(y<i||y>=_){w.skip=!0;continue}const P=this.getParsed(y),O=D(P[f]),A=w[u]=r.getPixelForValue(P[u],y),k=w[f]=o||O?a.getBasePixel():a.getPixelForValue(l?this.applyStack(a,P,l):P[f],y);w.skip=isNaN(A)||isNaN(k)||O,w.stop=y>0&&Math.abs(P[u]-x[u])>m,p&&(w.parsed=P,w.raw=c.data[y]),d&&(w.options=h||this.resolveDataElementOptions(y,S.active?"active":s)),b||this.updateElement(S,y,w,s),x=P}}getMaxOverflow(){const t=this._cachedMeta,i=t.dataset,n=i.options&&i.options.borderWidth||0,s=t.data||[];if(!s.length)return n;const o=s[0].size(this.resolveDataElementOptions(0)),r=s[s.length-1].size(this.resolveDataElementOptions(s.length-1));return Math.max(n,o,r)/2}draw(){const t=this._cachedMeta;t.dataset.updateControlPoints(this.chart.chartArea,t.iScale.axis),super.draw()}}M(hi,"id","line"),M(hi,"defaults",{datasetElementType:"line",dataElementType:"point",showLine:!0,spanGaps:!1}),M(hi,"overrides",{scales:{_index_:{type:"category"},_value_:{type:"linear"}}});class Ee extends dt{constructor(t,i){super(t,i),this.innerRadius=void 0,this.outerRadius=void 0}getLabelAndValue(t){const i=this._cachedMeta,n=this.chart,s=n.data.labels||[],o=We(i._parsed[t].r,n.options.locale);return{label:s[t]||"",value:o}}parseObjectData(t,i,n,s){return jr.bind(this)(t,i,n,s)}update(t){const i=this._cachedMeta.data;this._updateRadius(),this.updateElements(i,0,i.length,t)}getMinMax(){const t=this._cachedMeta,i={min:Number.POSITIVE_INFINITY,max:Number.NEGATIVE_INFINITY};return t.data.forEach((n,s)=>{const o=this.getParsed(s).r;!isNaN(o)&&this.chart.getDataVisibility(s)&&(o<i.min&&(i.min=o),o>i.max&&(i.max=o))}),i}_updateRadius(){const t=this.chart,i=t.chartArea,n=t.options,s=Math.min(i.right-i.left,i.bottom-i.top),o=Math.max(s/2,0),r=Math.max(n.cutoutPercentage?o/100*n.cutoutPercentage:1,0),a=(o-r)/t.getVisibleDatasetCount();this.outerRadius=o-a*this.index,this.innerRadius=this.outerRadius-a}updateElements(t,i,n,s){const o=s==="reset",r=this.chart,l=r.options.animation,c=this._cachedMeta.rScale,h=c.xCenter,d=c.yCenter,u=c.getIndexAngle(0)-.5*B;let f=u,g;const p=360/this.countVisibleElements();for(g=0;g<i;++g)f+=this._computeAngle(g,s,p);for(g=i;g<i+n;g++){const m=t[g];let b=f,_=f+this._computeAngle(g,s,p),v=r.getDataVisibility(g)?c.getDistanceFromCenterForValue(this.getParsed(g).r):0;f=_,o&&(l.animateScale&&(v=0),l.animateRotate&&(b=_=u));const x={x:h,y:d,innerRadius:0,outerRadius:v,startAngle:b,endAngle:_,options:this.resolveDataElementOptions(g,m.active?"active":s)};this.updateElement(m,g,x,s)}}countVisibleElements(){const t=this._cachedMeta;let i=0;return t.data.forEach((n,s)=>{!isNaN(this.getParsed(s).r)&&this.chart.getDataVisibility(s)&&i++}),i}_computeAngle(t,i,n){return this.chart.getDataVisibility(t)?ht(this.resolveDataElementOptions(t,i).angle||n):0}}M(Ee,"id","polarArea"),M(Ee,"defaults",{dataElementType:"arc",animation:{animateRotate:!0,animateScale:!0},animations:{numbers:{type:"number",properties:["x","y","startAngle","endAngle","innerRadius","outerRadius"]}},indexAxis:"r",startAngle:0}),M(Ee,"overrides",{aspectRatio:1,plugins:{legend:{labels:{generateLabels(t){const i=t.data;if(i.labels.length&&i.datasets.length){const{labels:{pointStyle:n,color:s}}=t.legend.options;return i.labels.map((o,r)=>{const l=t.getDatasetMeta(0).controller.getStyle(r);return{text:o,fillStyle:l.backgroundColor,strokeStyle:l.borderColor,fontColor:s,lineWidth:l.borderWidth,pointStyle:n,hidden:!t.getDataVisibility(r),index:r}})}return[]}},onClick(t,i,n){n.chart.toggleDataVisibility(i.index),n.chart.update()}}},scales:{r:{type:"radialLinear",angleLines:{display:!1},beginAtZero:!0,grid:{circular:!0},pointLabels:{display:!1},startAngle:0}}});class fn extends Kt{}M(fn,"id","pie"),M(fn,"defaults",{cutout:0,rotation:0,circumference:360,radius:"100%"});class di extends dt{getLabelAndValue(t){const i=this._cachedMeta.vScale,n=this.getParsed(t);return{label:i.getLabels()[t],value:""+i.getLabelForValue(n[i.axis])}}parseObjectData(t,i,n,s){return jr.bind(this)(t,i,n,s)}update(t){const i=this._cachedMeta,n=i.dataset,s=i.data||[],o=i.iScale.getLabels();if(n.points=s,t!=="resize"){const r=this.resolveDatasetElementOptions(t);this.options.showLine||(r.borderWidth=0);const a={_loop:!0,_fullLoop:o.length===s.length,options:r};this.updateElement(n,void 0,a,t)}this.updateElements(s,0,s.length,t)}updateElements(t,i,n,s){const o=this._cachedMeta.rScale,r=s==="reset";for(let a=i;a<i+n;a++){const l=t[a],c=this.resolveDataElementOptions(a,l.active?"active":s),h=o.getPointPositionForValue(a,this.getParsed(a).r),d=r?o.xCenter:h.x,u=r?o.yCenter:h.y,f={x:d,y:u,angle:h.angle,skip:isNaN(d)||isNaN(u),options:c};this.updateElement(l,a,f,s)}}}M(di,"id","radar"),M(di,"defaults",{datasetElementType:"line",dataElementType:"point",indexAxis:"r",showLine:!0,elements:{line:{fill:"start"}}}),M(di,"overrides",{aspectRatio:1,scales:{r:{type:"radialLinear"}}});class ui extends dt{getLabelAndValue(t){const i=this._cachedMeta,n=this.chart.data.labels||[],{xScale:s,yScale:o}=i,r=this.getParsed(t),a=s.getLabelForValue(r.x),l=o.getLabelForValue(r.y);return{label:n[t]||"",value:"("+a+", "+l+")"}}update(t){const i=this._cachedMeta,{data:n=[]}=i,s=this.chart._animationsDisabled;let{start:o,count:r}=Lr(i,n,s);if(this._drawStart=o,this._drawCount=r,Er(i)&&(o=0,r=n.length),this.options.showLine){this.datasetElementType||this.addElements();const{dataset:a,_dataset:l}=i;a._chart=this.chart,a._datasetIndex=this.index,a._decimated=!!l._decimated,a.points=n;const c=this.resolveDatasetElementOptions(t);c.segment=this.options.segment,this.updateElement(a,void 0,{animated:!s,options:c},t)}else this.datasetElementType&&(delete i.dataset,this.datasetElementType=!1);this.updateElements(n,o,r,t)}addElements(){const{showLine:t}=this.options;!this.datasetElementType&&t&&(this.datasetElementType=this.chart.registry.getElement("line")),super.addElements()}updateElements(t,i,n,s){const o=s==="reset",{iScale:r,vScale:a,_stacked:l,_dataset:c}=this._cachedMeta,h=this.resolveDataElementOptions(i,s),d=this.getSharedOptions(h),u=this.includeOptions(s,d),f=r.axis,g=a.axis,{spanGaps:p,segment:m}=this.options,b=re(p)?p:Number.POSITIVE_INFINITY,_=this.chart._animationsDisabled||o||s==="none";let v=i>0&&this.getParsed(i-1);for(let x=i;x<i+n;++x){const y=t[x],S=this.getParsed(x),w=_?y:{},P=D(S[g]),O=w[f]=r.getPixelForValue(S[f],x),A=w[g]=o||P?a.getBasePixel():a.getPixelForValue(l?this.applyStack(a,S,l):S[g],x);w.skip=isNaN(O)||isNaN(A)||P,w.stop=x>0&&Math.abs(S[f]-v[f])>b,m&&(w.parsed=S,w.raw=c.data[x]),u&&(w.options=d||this.resolveDataElementOptions(x,y.active?"active":s)),_||this.updateElement(y,x,w,s),v=S}this.updateSharedOptions(d,s,h)}getMaxOverflow(){const t=this._cachedMeta,i=t.data||[];if(!this.options.showLine){let a=0;for(let l=i.length-1;l>=0;--l)a=Math.max(a,i[l].size(this.resolveDataElementOptions(l))/2);return a>0&&a}const n=t.dataset,s=n.options&&n.options.borderWidth||0;if(!i.length)return s;const o=i[0].size(this.resolveDataElementOptions(0)),r=i[i.length-1].size(this.resolveDataElementOptions(i.length-1));return Math.max(s,o,r)/2}}M(ui,"id","scatter"),M(ui,"defaults",{datasetElementType:!1,dataElementType:"point",showLine:!1,fill:!1}),M(ui,"overrides",{interaction:{mode:"point"},scales:{x:{type:"linear"},y:{type:"linear"}}});var rh=Object.freeze({__proto__:null,BarController:li,BubbleController:ci,DoughnutController:Kt,LineController:hi,PieController:fn,PolarAreaController:Ee,RadarController:di,ScatterController:ui});function Vt(){throw new Error("This method is not implemented: Check that a complete date adapter is provided.")}class Vn{constructor(t){M(this,"options");this.options=t||{}}static override(t){Object.assign(Vn.prototype,t)}init(){}formats(){return Vt()}parse(){return Vt()}format(){return Vt()}add(){return Vt()}diff(){return Vt()}startOf(){return Vt()}endOf(){return Vt()}}var ah={_date:Vn};function lh(e,t,i,n){const{controller:s,data:o,_sorted:r}=e,a=s._cachedMeta.iScale,l=e.dataset&&e.dataset.options?e.dataset.options.spanGaps:null;if(a&&t===a.axis&&t!=="r"&&r&&o.length){const c=a._reversePixels?Dl:Pt;if(n){if(s._sharedOptions){const h=o[0],d=typeof h.getRange=="function"&&h.getRange(t);if(d){const u=c(o,t,i-d),f=c(o,t,i+d);return{lo:u.lo,hi:f.hi}}}}else{const h=c(o,t,i);if(l){const{vScale:d}=s._cachedMeta,{_parsed:u}=e,f=u.slice(0,h.lo+1).reverse().findIndex(p=>!D(p[d.axis]));h.lo-=Math.max(0,f);const g=u.slice(h.hi).findIndex(p=>!D(p[d.axis]));h.hi+=Math.max(0,g)}return h}}return{lo:0,hi:o.length-1}}function Ei(e,t,i,n,s){const o=e.getSortedVisibleDatasetMetas(),r=i[t];for(let a=0,l=o.length;a<l;++a){const{index:c,data:h}=o[a],{lo:d,hi:u}=lh(o[a],t,r,s);for(let f=d;f<=u;++f){const g=h[f];g.skip||n(g,c,f)}}}function ch(e){const t=e.indexOf("x")!==-1,i=e.indexOf("y")!==-1;return function(n,s){const o=t?Math.abs(n.x-s.x):0,r=i?Math.abs(n.y-s.y):0;return Math.sqrt(Math.pow(o,2)+Math.pow(r,2))}}function Zi(e,t,i,n,s){const o=[];return!s&&!e.isPointInArea(t)||Ei(e,i,t,function(a,l,c){!s&&!Ot(a,e.chartArea,0)||a.inRange(t.x,t.y,n)&&o.push({element:a,datasetIndex:l,index:c})},!0),o}function hh(e,t,i,n){let s=[];function o(r,a,l){const{startAngle:c,endAngle:h}=r.getProps(["startAngle","endAngle"],n),{angle:d}=Ar(r,{x:t.x,y:t.y});Fe(d,c,h)&&s.push({element:r,datasetIndex:a,index:l})}return Ei(e,i,t,o),s}function dh(e,t,i,n,s,o){let r=[];const a=ch(i);let l=Number.POSITIVE_INFINITY;function c(h,d,u){const f=h.inRange(t.x,t.y,s);if(n&&!f)return;const g=h.getCenterPoint(s);if(!(!!o||e.isPointInArea(g))&&!f)return;const m=a(t,g);m<l?(r=[{element:h,datasetIndex:d,index:u}],l=m):m===l&&r.push({element:h,datasetIndex:d,index:u})}return Ei(e,i,t,c),r}function Ji(e,t,i,n,s,o){return!o&&!e.isPointInArea(t)?[]:i==="r"&&!n?hh(e,t,i,s):dh(e,t,i,n,s,o)}function Fs(e,t,i,n,s){const o=[],r=i==="x"?"inXRange":"inYRange";let a=!1;return Ei(e,i,t,(l,c,h)=>{l[r]&&l[r](t[i],s)&&(o.push({element:l,datasetIndex:c,index:h}),a=a||l.inRange(t.x,t.y,s))}),n&&!a?[]:o}var uh={modes:{index(e,t,i,n){const s=Ut(t,e),o=i.axis||"x",r=i.includeInvisible||!1,a=i.intersect?Zi(e,s,o,n,r):Ji(e,s,o,!1,n,r),l=[];return a.length?(e.getSortedVisibleDatasetMetas().forEach(c=>{const h=a[0].index,d=c.data[h];d&&!d.skip&&l.push({element:d,datasetIndex:c.index,index:h})}),l):[]},dataset(e,t,i,n){const s=Ut(t,e),o=i.axis||"xy",r=i.includeInvisible||!1;let a=i.intersect?Zi(e,s,o,n,r):Ji(e,s,o,!1,n,r);if(a.length>0){const l=a[0].datasetIndex,c=e.getDatasetMeta(l).data;a=[];for(let h=0;h<c.length;++h)a.push({element:c[h],datasetIndex:l,index:h})}return a},point(e,t,i,n){const s=Ut(t,e),o=i.axis||"xy",r=i.includeInvisible||!1;return Zi(e,s,o,n,r)},nearest(e,t,i,n){const s=Ut(t,e),o=i.axis||"xy",r=i.includeInvisible||!1;return Ji(e,s,o,i.intersect,n,r)},x(e,t,i,n){const s=Ut(t,e);return Fs(e,s,"x",i.intersect,n)},y(e,t,i,n){const s=Ut(t,e);return Fs(e,s,"y",i.intersect,n)}}};const Zr=["left","top","right","bottom"];function be(e,t){return e.filter(i=>i.pos===t)}function zs(e,t){return e.filter(i=>Zr.indexOf(i.pos)===-1&&i.box.axis===t)}function _e(e,t){return e.sort((i,n)=>{const s=t?n:i,o=t?i:n;return s.weight===o.weight?s.index-o.index:s.weight-o.weight})}function fh(e){const t=[];let i,n,s,o,r,a;for(i=0,n=(e||[]).length;i<n;++i)s=e[i],{position:o,options:{stack:r,stackWeight:a=1}}=s,t.push({index:i,box:s,pos:o,horizontal:s.isHorizontal(),weight:s.weight,stack:r&&o+r,stackWeight:a});return t}function gh(e){const t={};for(const i of e){const{stack:n,pos:s,stackWeight:o}=i;if(!n||!Zr.includes(s))continue;const r=t[n]||(t[n]={count:0,placed:0,weight:0,size:0});r.count++,r.weight+=o}return t}function ph(e,t){const i=gh(e),{vBoxMaxWidth:n,hBoxMaxHeight:s}=t;let o,r,a;for(o=0,r=e.length;o<r;++o){a=e[o];const{fullSize:l}=a.box,c=i[a.stack],h=c&&a.stackWeight/c.weight;a.horizontal?(a.width=h?h*n:l&&t.availableWidth,a.height=s):(a.width=n,a.height=h?h*s:l&&t.availableHeight)}return i}function mh(e){const t=fh(e),i=_e(t.filter(c=>c.box.fullSize),!0),n=_e(be(t,"left"),!0),s=_e(be(t,"right")),o=_e(be(t,"top"),!0),r=_e(be(t,"bottom")),a=zs(t,"x"),l=zs(t,"y");return{fullSize:i,leftAndTop:n.concat(o),rightAndBottom:s.concat(l).concat(r).concat(a),chartArea:be(t,"chartArea"),vertical:n.concat(s).concat(l),horizontal:o.concat(r).concat(a)}}function Bs(e,t,i,n){return Math.max(e[i],t[i])+Math.max(e[n],t[n])}function Jr(e,t){e.top=Math.max(e.top,t.top),e.left=Math.max(e.left,t.left),e.bottom=Math.max(e.bottom,t.bottom),e.right=Math.max(e.right,t.right)}function bh(e,t,i,n){const{pos:s,box:o}=i,r=e.maxPadding;if(!L(s)){i.size&&(e[s]-=i.size);const d=n[i.stack]||{size:0,count:1};d.size=Math.max(d.size,i.horizontal?o.height:o.width),i.size=d.size/d.count,e[s]+=i.size}o.getPadding&&Jr(r,o.getPadding());const a=Math.max(0,t.outerWidth-Bs(r,e,"left","right")),l=Math.max(0,t.outerHeight-Bs(r,e,"top","bottom")),c=a!==e.w,h=l!==e.h;return e.w=a,e.h=l,i.horizontal?{same:c,other:h}:{same:h,other:c}}function _h(e){const t=e.maxPadding;function i(n){const s=Math.max(t[n]-e[n],0);return e[n]+=s,s}e.y+=i("top"),e.x+=i("left"),i("right"),i("bottom")}function yh(e,t){const i=t.maxPadding;function n(s){const o={left:0,top:0,right:0,bottom:0};return s.forEach(r=>{o[r]=Math.max(t[r],i[r])}),o}return n(e?["left","right"]:["top","bottom"])}function we(e,t,i,n){const s=[];let o,r,a,l,c,h;for(o=0,r=e.length,c=0;o<r;++o){a=e[o],l=a.box,l.update(a.width||t.w,a.height||t.h,yh(a.horizontal,t));const{same:d,other:u}=bh(t,i,a,n);c|=d&&s.length,h=h||u,l.fullSize||s.push(a)}return c&&we(s,t,i,n)||h}function Je(e,t,i,n,s){e.top=i,e.left=t,e.right=t+n,e.bottom=i+s,e.width=n,e.height=s}function Ns(e,t,i,n){const s=i.padding;let{x:o,y:r}=t;for(const a of e){const l=a.box,c=n[a.stack]||{placed:0,weight:1},h=a.stackWeight/c.weight||1;if(a.horizontal){const d=t.w*h,u=c.size||l.height;Ie(c.start)&&(r=c.start),l.fullSize?Je(l,s.left,r,i.outerWidth-s.right-s.left,u):Je(l,t.left+c.placed,r,d,u),c.start=r,c.placed+=d,r=l.bottom}else{const d=t.h*h,u=c.size||l.width;Ie(c.start)&&(o=c.start),l.fullSize?Je(l,o,s.top,u,i.outerHeight-s.bottom-s.top):Je(l,o,t.top+c.placed,u,d),c.start=o,c.placed+=d,o=l.right}}t.x=o,t.y=r}var J={addBox(e,t){e.boxes||(e.boxes=[]),t.fullSize=t.fullSize||!1,t.position=t.position||"top",t.weight=t.weight||0,t._layers=t._layers||function(){return[{z:0,draw(i){t.draw(i)}}]},e.boxes.push(t)},removeBox(e,t){const i=e.boxes?e.boxes.indexOf(t):-1;i!==-1&&e.boxes.splice(i,1)},configure(e,t,i){t.fullSize=i.fullSize,t.position=i.position,t.weight=i.weight},update(e,t,i,n){if(!e)return;const s=Q(e.options.layout.padding),o=Math.max(t-s.width,0),r=Math.max(i-s.height,0),a=mh(e.boxes),l=a.vertical,c=a.horizontal;$(e.boxes,p=>{typeof p.beforeLayout=="function"&&p.beforeLayout()});const h=l.reduce((p,m)=>m.box.options&&m.box.options.display===!1?p:p+1,0)||1,d=Object.freeze({outerWidth:t,outerHeight:i,padding:s,availableWidth:o,availableHeight:r,vBoxMaxWidth:o/2/h,hBoxMaxHeight:r/2}),u=Object.assign({},s);Jr(u,Q(n));const f=Object.assign({maxPadding:u,w:o,h:r,x:s.left,y:s.top},s),g=ph(l.concat(c),d);we(a.fullSize,f,d,g),we(l,f,d,g),we(c,f,d,g)&&we(l,f,d,g),_h(f),Ns(a.leftAndTop,f,d,g),f.x+=f.w,f.y+=f.h,Ns(a.rightAndBottom,f,d,g),e.chartArea={left:f.left,top:f.top,right:f.left+f.w,bottom:f.top+f.h,height:f.h,width:f.w},$(a.chartArea,p=>{const m=p.box;Object.assign(m,e.chartArea),m.update(f.w,f.h,{left:0,top:0,right:0,bottom:0})})}};class Qr{acquireContext(t,i){}releaseContext(t){return!1}addEventListener(t,i,n){}removeEventListener(t,i,n){}getDevicePixelRatio(){return 1}getMaximumSize(t,i,n,s){return i=Math.max(0,i||t.width),n=n||t.height,{width:i,height:Math.max(0,s?Math.floor(i/s):n)}}isAttached(t){return!0}updateConfig(t){}}class xh extends Qr{acquireContext(t){return t&&t.getContext&&t.getContext("2d")||null}updateConfig(t){t.options.animation=!1}}const fi="$chartjs",vh={touchstart:"mousedown",touchmove:"mousemove",touchend:"mouseup",pointerenter:"mouseenter",pointerdown:"mousedown",pointermove:"mousemove",pointerup:"mouseup",pointerleave:"mouseout",pointerout:"mouseout"},js=e=>e===null||e==="";function Mh(e,t){const i=e.style,n=e.getAttribute("height"),s=e.getAttribute("width");if(e[fi]={initial:{height:n,width:s,style:{display:i.display,height:i.height,width:i.width}}},i.display=i.display||"block",i.boxSizing=i.boxSizing||"border-box",js(s)){const o=ws(e,"width");o!==void 0&&(e.width=o)}if(js(n))if(e.style.height==="")e.height=e.width/(t||2);else{const o=ws(e,"height");o!==void 0&&(e.height=o)}return e}const ta=wc?{passive:!0}:!1;function Sh(e,t,i){e&&e.addEventListener(t,i,ta)}function wh(e,t,i){e&&e.canvas&&e.canvas.removeEventListener(t,i,ta)}function Ph(e,t){const i=vh[e.type]||e.type,{x:n,y:s}=Ut(e,t);return{type:i,chart:t,native:e,x:n!==void 0?n:null,y:s!==void 0?s:null}}function Mi(e,t){for(const i of e)if(i===t||i.contains(t))return!0}function Oh(e,t,i){const n=e.canvas,s=new MutationObserver(o=>{let r=!1;for(const a of o)r=r||Mi(a.addedNodes,n),r=r&&!Mi(a.removedNodes,n);r&&i()});return s.observe(document,{childList:!0,subtree:!0}),s}function Ah(e,t,i){const n=e.canvas,s=new MutationObserver(o=>{let r=!1;for(const a of o)r=r||Mi(a.removedNodes,n),r=r&&!Mi(a.addedNodes,n);r&&i()});return s.observe(document,{childList:!0,subtree:!0}),s}const Be=new Map;let Hs=0;function ea(){const e=window.devicePixelRatio;e!==Hs&&(Hs=e,Be.forEach((t,i)=>{i.currentDevicePixelRatio!==e&&t()}))}function kh(e,t){Be.size||window.addEventListener("resize",ea),Be.set(e,t)}function Ch(e){Be.delete(e),Be.size||window.removeEventListener("resize",ea)}function Th(e,t,i){const n=e.canvas,s=n&&Wn(n);if(!s)return;const o=Dr((a,l)=>{const c=s.clientWidth;i(a,l),c<s.clientWidth&&i()},window),r=new ResizeObserver(a=>{const l=a[0],c=l.contentRect.width,h=l.contentRect.height;c===0&&h===0||o(c,h)});return r.observe(s),kh(e,o),r}function Qi(e,t,i){i&&i.disconnect(),t==="resize"&&Ch(e)}function Dh(e,t,i){const n=e.canvas,s=Dr(o=>{e.ctx!==null&&i(Ph(o,e))},e);return Sh(n,t,s),s}class Lh extends Qr{acquireContext(t,i){const n=t&&t.getContext&&t.getContext("2d");return n&&n.canvas===t?(Mh(t,i),n):null}releaseContext(t){const i=t.canvas;if(!i[fi])return!1;const n=i[fi].initial;["height","width"].forEach(o=>{const r=n[o];D(r)?i.removeAttribute(o):i.setAttribute(o,r)});const s=n.style||{};return Object.keys(s).forEach(o=>{i.style[o]=s[o]}),i.width=i.width,delete i[fi],!0}addEventListener(t,i,n){this.removeEventListener(t,i);const s=t.$proxies||(t.$proxies={}),r={attach:Oh,detach:Ah,resize:Th}[i]||Dh;s[i]=r(t,i,n)}removeEventListener(t,i){const n=t.$proxies||(t.$proxies={}),s=n[i];if(!s)return;({attach:Qi,detach:Qi,resize:Qi}[i]||wh)(t,i,s),n[i]=void 0}getDevicePixelRatio(){return window.devicePixelRatio}getMaximumSize(t,i,n,s){return Sc(t,i,n,s)}isAttached(t){const i=t&&Wn(t);return!!(i&&i.isConnected)}}function Eh(e){return!Hn()||typeof OffscreenCanvas<"u"&&e instanceof OffscreenCanvas?xh:Lh}class ut{constructor(){M(this,"x");M(this,"y");M(this,"active",!1);M(this,"options");M(this,"$animations")}tooltipPosition(t){const{x:i,y:n}=this.getProps(["x","y"],t);return{x:i,y:n}}hasValue(){return re(this.x)&&re(this.y)}getProps(t,i){const n=this.$animations;if(!i||!n)return this;const s={};return t.forEach(o=>{s[o]=n[o]&&n[o].active()?n[o]._to:this[o]}),s}}M(ut,"defaults",{}),M(ut,"defaultRoutes");function $h(e,t){const i=e.options.ticks,n=Rh(e),s=Math.min(i.maxTicksLimit||n,n),o=i.major.enabled?Fh(t):[],r=o.length,a=o[0],l=o[r-1],c=[];if(r>s)return zh(t,c,o,r/s),c;const h=Ih(o,t,s);if(r>0){let d,u;const f=r>1?Math.round((l-a)/(r-1)):null;for(Qe(t,c,h,D(f)?0:a-f,a),d=0,u=r-1;d<u;d++)Qe(t,c,h,o[d],o[d+1]);return Qe(t,c,h,l,D(f)?t.length:l+f),c}return Qe(t,c,h),c}function Rh(e){const t=e.options.offset,i=e._tickSize(),n=e._length/i+(t?0:1),s=e._maxLength/i;return Math.floor(Math.min(n,s))}function Ih(e,t,i){const n=Bh(e),s=t.length/i;if(!n)return Math.max(s,1);const o=Ol(n);for(let r=0,a=o.length-1;r<a;r++){const l=o[r];if(l>s)return l}return Math.max(s,1)}function Fh(e){const t=[];let i,n;for(i=0,n=e.length;i<n;i++)e[i].major&&t.push(i);return t}function zh(e,t,i,n){let s=0,o=i[0],r;for(n=Math.ceil(n),r=0;r<e.length;r++)r===o&&(t.push(e[r]),s++,o=i[s*n])}function Qe(e,t,i,n,s){const o=C(n,0),r=Math.min(C(s,e.length),e.length);let a=0,l,c,h;for(i=Math.ceil(i),s&&(l=s-n,i=l/Math.floor(l/i)),h=o;h<0;)a++,h=Math.round(o+a*i);for(c=Math.max(o,0);c<r;c++)c===h&&(t.push(e[c]),a++,h=Math.round(o+a*i))}function Bh(e){const t=e.length;let i,n;if(t<2)return!1;for(n=e[0],i=1;i<t;++i)if(e[i]-e[i-1]!==n)return!1;return n}const Nh=e=>e==="left"?"right":e==="right"?"left":e,Ws=(e,t,i)=>t==="top"||t==="left"?e[t]+i:e[t]-i,Vs=(e,t)=>Math.min(t||e,e);function Ys(e,t){const i=[],n=e.length/t,s=e.length;let o=0;for(;o<s;o+=n)i.push(e[Math.floor(o)]);return i}function jh(e,t,i){const n=e.ticks.length,s=Math.min(t,n-1),o=e._startPixel,r=e._endPixel,a=1e-6;let l=e.getPixelForTick(s),c;if(!(i&&(n===1?c=Math.max(l-o,r-l):t===0?c=(e.getPixelForTick(1)-l)/2:c=(l-e.getPixelForTick(s-1))/2,l+=s<t?c:-c,l<o-a||l>r+a)))return l}function Hh(e,t){$(e,i=>{const n=i.gc,s=n.length/2;let o;if(s>t){for(o=0;o<s;++o)delete i.data[n[o]];n.splice(0,s)}})}function ye(e){return e.drawTicks?e.tickLength:0}function Us(e,t){if(!e.display)return 0;const i=X(e.font,t),n=Q(e.padding);return(j(e.text)?e.text.length:1)*i.lineHeight+n.height}function Wh(e,t){return Bt(e,{scale:t,type:"scale"})}function Vh(e,t,i){return Bt(e,{tick:i,index:t,type:"tick"})}function Yh(e,t,i){let n=In(e);return(i&&t!=="right"||!i&&t==="right")&&(n=Nh(n)),n}function Uh(e,t,i,n){const{top:s,left:o,bottom:r,right:a,chart:l}=e,{chartArea:c,scales:h}=l;let d=0,u,f,g;const p=r-s,m=a-o;if(e.isHorizontal()){if(f=q(n,o,a),L(i)){const b=Object.keys(i)[0],_=i[b];g=h[b].getPixelForValue(_)+p-t}else i==="center"?g=(c.bottom+c.top)/2+p-t:g=Ws(e,i,t);u=a-o}else{if(L(i)){const b=Object.keys(i)[0],_=i[b];f=h[b].getPixelForValue(_)-m+t}else i==="center"?f=(c.left+c.right)/2-m+t:f=Ws(e,i,t);g=q(n,r,s),d=i==="left"?-Y:Y}return{titleX:f,titleY:g,maxWidth:u,rotation:d}}class te extends ut{constructor(t){super(),this.id=t.id,this.type=t.type,this.options=void 0,this.ctx=t.ctx,this.chart=t.chart,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.width=void 0,this.height=void 0,this._margins={left:0,right:0,top:0,bottom:0},this.maxWidth=void 0,this.maxHeight=void 0,this.paddingTop=void 0,this.paddingBottom=void 0,this.paddingLeft=void 0,this.paddingRight=void 0,this.axis=void 0,this.labelRotation=void 0,this.min=void 0,this.max=void 0,this._range=void 0,this.ticks=[],this._gridLineItems=null,this._labelItems=null,this._labelSizes=null,this._length=0,this._maxLength=0,this._longestTextCache={},this._startPixel=void 0,this._endPixel=void 0,this._reversePixels=!1,this._userMax=void 0,this._userMin=void 0,this._suggestedMax=void 0,this._suggestedMin=void 0,this._ticksLength=0,this._borderValue=0,this._cache={},this._dataLimitsCached=!1,this.$context=void 0}init(t){this.options=t.setContext(this.getContext()),this.axis=t.axis,this._userMin=this.parse(t.min),this._userMax=this.parse(t.max),this._suggestedMin=this.parse(t.suggestedMin),this._suggestedMax=this.parse(t.suggestedMax)}parse(t,i){return t}getUserBounds(){let{_userMin:t,_userMax:i,_suggestedMin:n,_suggestedMax:s}=this;return t=at(t,Number.POSITIVE_INFINITY),i=at(i,Number.NEGATIVE_INFINITY),n=at(n,Number.POSITIVE_INFINITY),s=at(s,Number.NEGATIVE_INFINITY),{min:at(t,n),max:at(i,s),minDefined:V(t),maxDefined:V(i)}}getMinMax(t){let{min:i,max:n,minDefined:s,maxDefined:o}=this.getUserBounds(),r;if(s&&o)return{min:i,max:n};const a=this.getMatchingVisibleMetas();for(let l=0,c=a.length;l<c;++l)r=a[l].controller.getMinMax(this,t),s||(i=Math.min(i,r.min)),o||(n=Math.max(n,r.max));return i=o&&i>n?n:i,n=s&&i>n?i:n,{min:at(i,at(n,i)),max:at(n,at(i,n))}}getPadding(){return{left:this.paddingLeft||0,top:this.paddingTop||0,right:this.paddingRight||0,bottom:this.paddingBottom||0}}getTicks(){return this.ticks}getLabels(){const t=this.chart.data;return this.options.labels||(this.isHorizontal()?t.xLabels:t.yLabels)||t.labels||[]}getLabelItems(t=this.chart.chartArea){return this._labelItems||(this._labelItems=this._computeLabelItems(t))}beforeLayout(){this._cache={},this._dataLimitsCached=!1}beforeUpdate(){I(this.options.beforeUpdate,[this])}update(t,i,n){const{beginAtZero:s,grace:o,ticks:r}=this.options,a=r.sampleSize;this.beforeUpdate(),this.maxWidth=t,this.maxHeight=i,this._margins=n=Object.assign({left:0,right:0,top:0,bottom:0},n),this.ticks=null,this._labelSizes=null,this._gridLineItems=null,this._labelItems=null,this.beforeSetDimensions(),this.setDimensions(),this.afterSetDimensions(),this._maxLength=this.isHorizontal()?this.width+n.left+n.right:this.height+n.top+n.bottom,this._dataLimitsCached||(this.beforeDataLimits(),this.determineDataLimits(),this.afterDataLimits(),this._range=ec(this,o,s),this._dataLimitsCached=!0),this.beforeBuildTicks(),this.ticks=this.buildTicks()||[],this.afterBuildTicks();const l=a<this.ticks.length;this._convertTicksToLabels(l?Ys(this.ticks,a):this.ticks),this.configure(),this.beforeCalculateLabelRotation(),this.calculateLabelRotation(),this.afterCalculateLabelRotation(),r.display&&(r.autoSkip||r.source==="auto")&&(this.ticks=$h(this,this.ticks),this._labelSizes=null,this.afterAutoSkip()),l&&this._convertTicksToLabels(this.ticks),this.beforeFit(),this.fit(),this.afterFit(),this.afterUpdate()}configure(){let t=this.options.reverse,i,n;this.isHorizontal()?(i=this.left,n=this.right):(i=this.top,n=this.bottom,t=!t),this._startPixel=i,this._endPixel=n,this._reversePixels=t,this._length=n-i,this._alignToPixels=this.options.alignToPixels}afterUpdate(){I(this.options.afterUpdate,[this])}beforeSetDimensions(){I(this.options.beforeSetDimensions,[this])}setDimensions(){this.isHorizontal()?(this.width=this.maxWidth,this.left=0,this.right=this.width):(this.height=this.maxHeight,this.top=0,this.bottom=this.height),this.paddingLeft=0,this.paddingTop=0,this.paddingRight=0,this.paddingBottom=0}afterSetDimensions(){I(this.options.afterSetDimensions,[this])}_callHooks(t){this.chart.notifyPlugins(t,this.getContext()),I(this.options[t],[this])}beforeDataLimits(){this._callHooks("beforeDataLimits")}determineDataLimits(){}afterDataLimits(){this._callHooks("afterDataLimits")}beforeBuildTicks(){this._callHooks("beforeBuildTicks")}buildTicks(){return[]}afterBuildTicks(){this._callHooks("afterBuildTicks")}beforeTickToLabelConversion(){I(this.options.beforeTickToLabelConversion,[this])}generateTickLabels(t){const i=this.options.ticks;let n,s,o;for(n=0,s=t.length;n<s;n++)o=t[n],o.label=I(i.callback,[o.value,n,t],this)}afterTickToLabelConversion(){I(this.options.afterTickToLabelConversion,[this])}beforeCalculateLabelRotation(){I(this.options.beforeCalculateLabelRotation,[this])}calculateLabelRotation(){const t=this.options,i=t.ticks,n=Vs(this.ticks.length,t.ticks.maxTicksLimit),s=i.minRotation||0,o=i.maxRotation;let r=s,a,l,c;if(!this._isVisible()||!i.display||s>=o||n<=1||!this.isHorizontal()){this.labelRotation=s;return}const h=this._getLabelSizes(),d=h.widest.width,u=h.highest.height,f=K(this.chart.width-d,0,this.maxWidth);a=t.offset?this.maxWidth/n:f/(n-1),d+6>a&&(a=f/(n-(t.offset?.5:1)),l=this.maxHeight-ye(t.grid)-i.padding-Us(t.title,this.chart.options.font),c=Math.sqrt(d*d+u*u),r=$n(Math.min(Math.asin(K((h.highest.height+6)/a,-1,1)),Math.asin(K(l/c,-1,1))-Math.asin(K(u/c,-1,1)))),r=Math.max(s,Math.min(o,r))),this.labelRotation=r}afterCalculateLabelRotation(){I(this.options.afterCalculateLabelRotation,[this])}afterAutoSkip(){}beforeFit(){I(this.options.beforeFit,[this])}fit(){const t={width:0,height:0},{chart:i,options:{ticks:n,title:s,grid:o}}=this,r=this._isVisible(),a=this.isHorizontal();if(r){const l=Us(s,i.options.font);if(a?(t.width=this.maxWidth,t.height=ye(o)+l):(t.height=this.maxHeight,t.width=ye(o)+l),n.display&&this.ticks.length){const{first:c,last:h,widest:d,highest:u}=this._getLabelSizes(),f=n.padding*2,g=ht(this.labelRotation),p=Math.cos(g),m=Math.sin(g);if(a){const b=n.mirror?0:m*d.width+p*u.height;t.height=Math.min(this.maxHeight,t.height+b+f)}else{const b=n.mirror?0:p*d.width+m*u.height;t.width=Math.min(this.maxWidth,t.width+b+f)}this._calculatePadding(c,h,m,p)}}this._handleMargins(),a?(this.width=this._length=i.width-this._margins.left-this._margins.right,this.height=t.height):(this.width=t.width,this.height=this._length=i.height-this._margins.top-this._margins.bottom)}_calculatePadding(t,i,n,s){const{ticks:{align:o,padding:r},position:a}=this.options,l=this.labelRotation!==0,c=a!=="top"&&this.axis==="x";if(this.isHorizontal()){const h=this.getPixelForTick(0)-this.left,d=this.right-this.getPixelForTick(this.ticks.length-1);let u=0,f=0;l?c?(u=s*t.width,f=n*i.height):(u=n*t.height,f=s*i.width):o==="start"?f=i.width:o==="end"?u=t.width:o!=="inner"&&(u=t.width/2,f=i.width/2),this.paddingLeft=Math.max((u-h+r)*this.width/(this.width-h),0),this.paddingRight=Math.max((f-d+r)*this.width/(this.width-d),0)}else{let h=i.height/2,d=t.height/2;o==="start"?(h=0,d=t.height):o==="end"&&(h=i.height,d=0),this.paddingTop=h+r,this.paddingBottom=d+r}}_handleMargins(){this._margins&&(this._margins.left=Math.max(this.paddingLeft,this._margins.left),this._margins.top=Math.max(this.paddingTop,this._margins.top),this._margins.right=Math.max(this.paddingRight,this._margins.right),this._margins.bottom=Math.max(this.paddingBottom,this._margins.bottom))}afterFit(){I(this.options.afterFit,[this])}isHorizontal(){const{axis:t,position:i}=this.options;return i==="top"||i==="bottom"||t==="x"}isFullSize(){return this.options.fullSize}_convertTicksToLabels(t){this.beforeTickToLabelConversion(),this.generateTickLabels(t);let i,n;for(i=0,n=t.length;i<n;i++)D(t[i].label)&&(t.splice(i,1),n--,i--);this.afterTickToLabelConversion()}_getLabelSizes(){let t=this._labelSizes;if(!t){const i=this.options.ticks.sampleSize;let n=this.ticks;i<n.length&&(n=Ys(n,i)),this._labelSizes=t=this._computeLabelSizes(n,n.length,this.options.ticks.maxTicksLimit)}return t}_computeLabelSizes(t,i,n){const{ctx:s,_longestTextCache:o}=this,r=[],a=[],l=Math.floor(i/Vs(i,n));let c=0,h=0,d,u,f,g,p,m,b,_,v,x,y;for(d=0;d<i;d+=l){if(g=t[d].label,p=this._resolveTickFontOptions(d),s.font=m=p.string,b=o[m]=o[m]||{data:{},gc:[]},_=p.lineHeight,v=x=0,!D(g)&&!j(g))v=xi(s,b.data,b.gc,v,g),x=_;else if(j(g))for(u=0,f=g.length;u<f;++u)y=g[u],!D(y)&&!j(y)&&(v=xi(s,b.data,b.gc,v,y),x+=_);r.push(v),a.push(x),c=Math.max(v,c),h=Math.max(x,h)}Hh(o,i);const S=r.indexOf(c),w=a.indexOf(h),P=O=>({width:r[O]||0,height:a[O]||0});return{first:P(0),last:P(i-1),widest:P(S),highest:P(w),widths:r,heights:a}}getLabelForValue(t){return t}getPixelForValue(t,i){return NaN}getValueForPixel(t){}getPixelForTick(t){const i=this.ticks;return t<0||t>i.length-1?null:this.getPixelForValue(i[t].value)}getPixelForDecimal(t){this._reversePixels&&(t=1-t);const i=this._startPixel+t*this._length;return Tl(this._alignToPixels?Wt(this.chart,i,0):i)}getDecimalForPixel(t){const i=(t-this._startPixel)/this._length;return this._reversePixels?1-i:i}getBasePixel(){return this.getPixelForValue(this.getBaseValue())}getBaseValue(){const{min:t,max:i}=this;return t<0&&i<0?i:t>0&&i>0?t:0}getContext(t){const i=this.ticks||[];if(t>=0&&t<i.length){const n=i[t];return n.$context||(n.$context=Vh(this.getContext(),t,n))}return this.$context||(this.$context=Wh(this.chart.getContext(),this))}_tickSize(){const t=this.options.ticks,i=ht(this.labelRotation),n=Math.abs(Math.cos(i)),s=Math.abs(Math.sin(i)),o=this._getLabelSizes(),r=t.autoSkipPadding||0,a=o?o.widest.width+r:0,l=o?o.highest.height+r:0;return this.isHorizontal()?l*n>a*s?a/n:l/s:l*s<a*n?l/n:a/s}_isVisible(){const t=this.options.display;return t!=="auto"?!!t:this.getMatchingVisibleMetas().length>0}_computeGridLineItems(t){const i=this.axis,n=this.chart,s=this.options,{grid:o,position:r,border:a}=s,l=o.offset,c=this.isHorizontal(),d=this.ticks.length+(l?1:0),u=ye(o),f=[],g=a.setContext(this.getContext()),p=g.display?g.width:0,m=p/2,b=function(R){return Wt(n,R,p)};let _,v,x,y,S,w,P,O,A,k,T,U;if(r==="top")_=b(this.bottom),w=this.bottom-u,O=_-m,k=b(t.top)+m,U=t.bottom;else if(r==="bottom")_=b(this.top),k=t.top,U=b(t.bottom)-m,w=_+m,O=this.top+u;else if(r==="left")_=b(this.right),S=this.right-u,P=_-m,A=b(t.left)+m,T=t.right;else if(r==="right")_=b(this.left),A=t.left,T=b(t.right)-m,S=_+m,P=this.left+u;else if(i==="x"){if(r==="center")_=b((t.top+t.bottom)/2+.5);else if(L(r)){const R=Object.keys(r)[0],N=r[R];_=b(this.chart.scales[R].getPixelForValue(N))}k=t.top,U=t.bottom,w=_+m,O=w+u}else if(i==="y"){if(r==="center")_=b((t.left+t.right)/2);else if(L(r)){const R=Object.keys(r)[0],N=r[R];_=b(this.chart.scales[R].getPixelForValue(N))}S=_-m,P=S-u,A=t.left,T=t.right}const tt=C(s.ticks.maxTicksLimit,d),E=Math.max(1,Math.ceil(d/tt));for(v=0;v<d;v+=E){const R=this.getContext(v),N=o.setContext(R),W=a.setContext(R),G=N.lineWidth,Ct=N.color,Nt=W.dash||[],Tt=W.dashOffset,rt=N.tickWidth,et=N.tickColor,Dt=N.tickBorderDash||[],jt=N.tickBorderDashOffset;x=jh(this,v,l),x!==void 0&&(y=Wt(n,x,G),c?S=P=A=T=y:w=O=k=U=y,f.push({tx1:S,ty1:w,tx2:P,ty2:O,x1:A,y1:k,x2:T,y2:U,width:G,color:Ct,borderDash:Nt,borderDashOffset:Tt,tickWidth:rt,tickColor:et,tickBorderDash:Dt,tickBorderDashOffset:jt}))}return this._ticksLength=d,this._borderValue=_,f}_computeLabelItems(t){const i=this.axis,n=this.options,{position:s,ticks:o}=n,r=this.isHorizontal(),a=this.ticks,{align:l,crossAlign:c,padding:h,mirror:d}=o,u=ye(n.grid),f=u+h,g=d?-h:f,p=-ht(this.labelRotation),m=[];let b,_,v,x,y,S,w,P,O,A,k,T,U="middle";if(s==="top")S=this.bottom-g,w=this._getXAxisLabelAlignment();else if(s==="bottom")S=this.top+g,w=this._getXAxisLabelAlignment();else if(s==="left"){const E=this._getYAxisLabelAlignment(u);w=E.textAlign,y=E.x}else if(s==="right"){const E=this._getYAxisLabelAlignment(u);w=E.textAlign,y=E.x}else if(i==="x"){if(s==="center")S=(t.top+t.bottom)/2+f;else if(L(s)){const E=Object.keys(s)[0],R=s[E];S=this.chart.scales[E].getPixelForValue(R)+f}w=this._getXAxisLabelAlignment()}else if(i==="y"){if(s==="center")y=(t.left+t.right)/2-f;else if(L(s)){const E=Object.keys(s)[0],R=s[E];y=this.chart.scales[E].getPixelForValue(R)}w=this._getYAxisLabelAlignment(u).textAlign}i==="y"&&(l==="start"?U="top":l==="end"&&(U="bottom"));const tt=this._getLabelSizes();for(b=0,_=a.length;b<_;++b){v=a[b],x=v.label;const E=o.setContext(this.getContext(b));P=this.getPixelForTick(b)+o.labelOffset,O=this._resolveTickFontOptions(b),A=O.lineHeight,k=j(x)?x.length:1;const R=k/2,N=E.color,W=E.textStrokeColor,G=E.textStrokeWidth;let Ct=w;r?(y=P,w==="inner"&&(b===_-1?Ct=this.options.reverse?"left":"right":b===0?Ct=this.options.reverse?"right":"left":Ct="center"),s==="top"?c==="near"||p!==0?T=-k*A+A/2:c==="center"?T=-tt.highest.height/2-R*A+A:T=-tt.highest.height+A/2:c==="near"||p!==0?T=A/2:c==="center"?T=tt.highest.height/2-R*A:T=tt.highest.height-k*A,d&&(T*=-1),p!==0&&!E.showLabelBackdrop&&(y+=A/2*Math.sin(p))):(S=P,T=(1-k)*A/2);let Nt;if(E.showLabelBackdrop){const Tt=Q(E.backdropPadding),rt=tt.heights[b],et=tt.widths[b];let Dt=T-Tt.top,jt=0-Tt.left;switch(U){case"middle":Dt-=rt/2;break;case"bottom":Dt-=rt;break}switch(w){case"center":jt-=et/2;break;case"right":jt-=et;break;case"inner":b===_-1?jt-=et:b>0&&(jt-=et/2);break}Nt={left:jt,top:Dt,width:et+Tt.width,height:rt+Tt.height,color:E.backdropColor}}m.push({label:x,font:O,textOffset:T,options:{rotation:p,color:N,strokeColor:W,strokeWidth:G,textAlign:Ct,textBaseline:U,translation:[y,S],backdrop:Nt}})}return m}_getXAxisLabelAlignment(){const{position:t,ticks:i}=this.options;if(-ht(this.labelRotation))return t==="top"?"left":"right";let s="center";return i.align==="start"?s="left":i.align==="end"?s="right":i.align==="inner"&&(s="inner"),s}_getYAxisLabelAlignment(t){const{position:i,ticks:{crossAlign:n,mirror:s,padding:o}}=this.options,r=this._getLabelSizes(),a=t+o,l=r.widest.width;let c,h;return i==="left"?s?(h=this.right+o,n==="near"?c="left":n==="center"?(c="center",h+=l/2):(c="right",h+=l)):(h=this.right-a,n==="near"?c="right":n==="center"?(c="center",h-=l/2):(c="left",h=this.left)):i==="right"?s?(h=this.left+o,n==="near"?c="right":n==="center"?(c="center",h-=l/2):(c="left",h-=l)):(h=this.left+a,n==="near"?c="left":n==="center"?(c="center",h+=l/2):(c="right",h=this.right)):c="right",{textAlign:c,x:h}}_computeLabelArea(){if(this.options.ticks.mirror)return;const t=this.chart,i=this.options.position;if(i==="left"||i==="right")return{top:0,left:this.left,bottom:t.height,right:this.right};if(i==="top"||i==="bottom")return{top:this.top,left:0,bottom:this.bottom,right:t.width}}drawBackground(){const{ctx:t,options:{backgroundColor:i},left:n,top:s,width:o,height:r}=this;i&&(t.save(),t.fillStyle=i,t.fillRect(n,s,o,r),t.restore())}getLineWidthForValue(t){const i=this.options.grid;if(!this._isVisible()||!i.display)return 0;const s=this.ticks.findIndex(o=>o.value===t);return s>=0?i.setContext(this.getContext(s)).lineWidth:0}drawGrid(t){const i=this.options.grid,n=this.ctx,s=this._gridLineItems||(this._gridLineItems=this._computeGridLineItems(t));let o,r;const a=(l,c,h)=>{!h.width||!h.color||(n.save(),n.lineWidth=h.width,n.strokeStyle=h.color,n.setLineDash(h.borderDash||[]),n.lineDashOffset=h.borderDashOffset,n.beginPath(),n.moveTo(l.x,l.y),n.lineTo(c.x,c.y),n.stroke(),n.restore())};if(i.display)for(o=0,r=s.length;o<r;++o){const l=s[o];i.drawOnChartArea&&a({x:l.x1,y:l.y1},{x:l.x2,y:l.y2},l),i.drawTicks&&a({x:l.tx1,y:l.ty1},{x:l.tx2,y:l.ty2},{color:l.tickColor,width:l.tickWidth,borderDash:l.tickBorderDash,borderDashOffset:l.tickBorderDashOffset})}}drawBorder(){const{chart:t,ctx:i,options:{border:n,grid:s}}=this,o=n.setContext(this.getContext()),r=n.display?o.width:0;if(!r)return;const a=s.setContext(this.getContext(0)).lineWidth,l=this._borderValue;let c,h,d,u;this.isHorizontal()?(c=Wt(t,this.left,r)-r/2,h=Wt(t,this.right,a)+a/2,d=u=l):(d=Wt(t,this.top,r)-r/2,u=Wt(t,this.bottom,a)+a/2,c=h=l),i.save(),i.lineWidth=o.width,i.strokeStyle=o.color,i.beginPath(),i.moveTo(c,d),i.lineTo(h,u),i.stroke(),i.restore()}drawLabels(t){if(!this.options.ticks.display)return;const n=this.ctx,s=this._computeLabelArea();s&&Ti(n,s);const o=this.getLabelItems(t);for(const r of o){const a=r.options,l=r.font,c=r.label,h=r.textOffset;Qt(n,c,0,h,l,a)}s&&Di(n)}drawTitle(){const{ctx:t,options:{position:i,title:n,reverse:s}}=this;if(!n.display)return;const o=X(n.font),r=Q(n.padding),a=n.align;let l=o.lineHeight/2;i==="bottom"||i==="center"||L(i)?(l+=r.bottom,j(n.text)&&(l+=o.lineHeight*(n.text.length-1))):l+=r.top;const{titleX:c,titleY:h,maxWidth:d,rotation:u}=Uh(this,l,i,a);Qt(t,n.text,0,0,o,{color:n.color,maxWidth:d,rotation:u,textAlign:Yh(a,i,s),textBaseline:"middle",translation:[c,h]})}draw(t){this._isVisible()&&(this.drawBackground(),this.drawGrid(t),this.drawBorder(),this.drawTitle(),this.drawLabels(t))}_layers(){const t=this.options,i=t.ticks&&t.ticks.z||0,n=C(t.grid&&t.grid.z,-1),s=C(t.border&&t.border.z,0);return!this._isVisible()||this.draw!==te.prototype.draw?[{z:i,draw:o=>{this.draw(o)}}]:[{z:n,draw:o=>{this.drawBackground(),this.drawGrid(o),this.drawTitle()}},{z:s,draw:()=>{this.drawBorder()}},{z:i,draw:o=>{this.drawLabels(o)}}]}getMatchingVisibleMetas(t){const i=this.chart.getSortedVisibleDatasetMetas(),n=this.axis+"AxisID",s=[];let o,r;for(o=0,r=i.length;o<r;++o){const a=i[o];a[n]===this.id&&(!t||a.type===t)&&s.push(a)}return s}_resolveTickFontOptions(t){const i=this.options.ticks.setContext(this.getContext(t));return X(i.font)}_maxDigits(){const t=this._resolveTickFontOptions(0).lineHeight;return(this.isHorizontal()?this.width:this.height)/t}}class ti{constructor(t,i,n){this.type=t,this.scope=i,this.override=n,this.items=Object.create(null)}isForType(t){return Object.prototype.isPrototypeOf.call(this.type.prototype,t.prototype)}register(t){const i=Object.getPrototypeOf(t);let n;Kh(i)&&(n=this.register(i));const s=this.items,o=t.id,r=this.scope+"."+o;if(!o)throw new Error("class does not have id: "+t);return o in s||(s[o]=t,Gh(t,r,n),this.override&&H.override(t.id,t.overrides)),r}get(t){return this.items[t]}unregister(t){const i=this.items,n=t.id,s=this.scope;n in i&&delete i[n],s&&n in H[s]&&(delete H[s][n],this.override&&delete Jt[n])}}function Gh(e,t,i){const n=Re(Object.create(null),[i?H.get(i):{},H.get(t),e.defaults]);H.set(t,n),e.defaultRoutes&&Xh(t,e.defaultRoutes),e.descriptors&&H.describe(t,e.descriptors)}function Xh(e,t){Object.keys(t).forEach(i=>{const n=i.split("."),s=n.pop(),o=[e].concat(n).join("."),r=t[i].split("."),a=r.pop(),l=r.join(".");H.route(o,s,l,a)})}function Kh(e){return"id"in e&&"defaults"in e}class qh{constructor(){this.controllers=new ti(dt,"datasets",!0),this.elements=new ti(ut,"elements"),this.plugins=new ti(Object,"plugins"),this.scales=new ti(te,"scales"),this._typedRegistries=[this.controllers,this.scales,this.elements]}add(...t){this._each("register",t)}remove(...t){this._each("unregister",t)}addControllers(...t){this._each("register",t,this.controllers)}addElements(...t){this._each("register",t,this.elements)}addPlugins(...t){this._each("register",t,this.plugins)}addScales(...t){this._each("register",t,this.scales)}getController(t){return this._get(t,this.controllers,"controller")}getElement(t){return this._get(t,this.elements,"element")}getPlugin(t){return this._get(t,this.plugins,"plugin")}getScale(t){return this._get(t,this.scales,"scale")}removeControllers(...t){this._each("unregister",t,this.controllers)}removeElements(...t){this._each("unregister",t,this.elements)}removePlugins(...t){this._each("unregister",t,this.plugins)}removeScales(...t){this._each("unregister",t,this.scales)}_each(t,i,n){[...i].forEach(s=>{const o=n||this._getRegistryForType(s);n||o.isForType(s)||o===this.plugins&&s.id?this._exec(t,o,s):$(s,r=>{const a=n||this._getRegistryForType(r);this._exec(t,a,r)})})}_exec(t,i,n){const s=En(t);I(n["before"+s],[],n),i[t](n),I(n["after"+s],[],n)}_getRegistryForType(t){for(let i=0;i<this._typedRegistries.length;i++){const n=this._typedRegistries[i];if(n.isForType(t))return n}return this.plugins}_get(t,i,n){const s=i.get(t);if(s===void 0)throw new Error('"'+t+'" is not a registered '+n+".");return s}}var gt=new qh;class Zh{constructor(){this._init=[]}notify(t,i,n,s){i==="beforeInit"&&(this._init=this._createDescriptors(t,!0),this._notify(this._init,t,"install"));const o=s?this._descriptors(t).filter(s):this._descriptors(t),r=this._notify(o,t,i,n);return i==="afterDestroy"&&(this._notify(o,t,"stop"),this._notify(this._init,t,"uninstall")),r}_notify(t,i,n,s){s=s||{};for(const o of t){const r=o.plugin,a=r[n],l=[i,s,o.options];if(I(a,l,r)===!1&&s.cancelable)return!1}return!0}invalidate(){D(this._cache)||(this._oldCache=this._cache,this._cache=void 0)}_descriptors(t){if(this._cache)return this._cache;const i=this._cache=this._createDescriptors(t);return this._notifyStateChanges(t),i}_createDescriptors(t,i){const n=t&&t.config,s=C(n.options&&n.options.plugins,{}),o=Jh(n);return s===!1&&!i?[]:td(t,o,s,i)}_notifyStateChanges(t){const i=this._oldCache||[],n=this._cache,s=(o,r)=>o.filter(a=>!r.some(l=>a.plugin.id===l.plugin.id));this._notify(s(i,n),t,"stop"),this._notify(s(n,i),t,"start")}}function Jh(e){const t={},i=[],n=Object.keys(gt.plugins.items);for(let o=0;o<n.length;o++)i.push(gt.getPlugin(n[o]));const s=e.plugins||[];for(let o=0;o<s.length;o++){const r=s[o];i.indexOf(r)===-1&&(i.push(r),t[r.id]=!0)}return{plugins:i,localIds:t}}function Qh(e,t){return!t&&e===!1?null:e===!0?{}:e}function td(e,{plugins:t,localIds:i},n,s){const o=[],r=e.getContext();for(const a of t){const l=a.id,c=Qh(n[l],s);c!==null&&o.push({plugin:a,options:ed(e.config,{plugin:a,local:i[l]},c,r)})}return o}function ed(e,{plugin:t,local:i},n,s){const o=e.pluginScopeKeys(t),r=e.getOptionScopes(n,o);return i&&t.defaults&&r.push(t.defaults),e.createResolver(r,s,[""],{scriptable:!1,indexable:!1,allKeys:!0})}function gn(e,t){const i=H.datasets[e]||{};return((t.datasets||{})[e]||{}).indexAxis||t.indexAxis||i.indexAxis||"x"}function id(e,t){let i=e;return e==="_index_"?i=t:e==="_value_"&&(i=t==="x"?"y":"x"),i}function nd(e,t){return e===t?"_index_":"_value_"}function Gs(e){if(e==="x"||e==="y"||e==="r")return e}function sd(e){if(e==="top"||e==="bottom")return"x";if(e==="left"||e==="right")return"y"}function pn(e,...t){if(Gs(e))return e;for(const i of t){const n=i.axis||sd(i.position)||e.length>1&&Gs(e[0].toLowerCase());if(n)return n}throw new Error(`Cannot determine type of '${e}' axis. Please provide 'axis' or 'position' option.`)}function Xs(e,t,i){if(i[t+"AxisID"]===e)return{axis:t}}function od(e,t){if(t.data&&t.data.datasets){const i=t.data.datasets.filter(n=>n.xAxisID===e||n.yAxisID===e);if(i.length)return Xs(e,"x",i[0])||Xs(e,"y",i[0])}return{}}function rd(e,t){const i=Jt[e.type]||{scales:{}},n=t.scales||{},s=gn(e.type,t),o=Object.create(null);return Object.keys(n).forEach(r=>{const a=n[r];if(!L(a))return console.error(`Invalid scale configuration for scale: ${r}`);if(a._proxy)return console.warn(`Ignoring resolver passed as options for scale: ${r}`);const l=pn(r,a,od(r,e),H.scales[a.type]),c=nd(l,s),h=i.scales||{};o[r]=Ce(Object.create(null),[{axis:l},a,h[l],h[c]])}),e.data.datasets.forEach(r=>{const a=r.type||e.type,l=r.indexAxis||gn(a,t),h=(Jt[a]||{}).scales||{};Object.keys(h).forEach(d=>{const u=id(d,l),f=r[u+"AxisID"]||u;o[f]=o[f]||Object.create(null),Ce(o[f],[{axis:u},n[f],h[d]])})}),Object.keys(o).forEach(r=>{const a=o[r];Ce(a,[H.scales[a.type],H.scale])}),o}function ia(e){const t=e.options||(e.options={});t.plugins=C(t.plugins,{}),t.scales=rd(e,t)}function na(e){return e=e||{},e.datasets=e.datasets||[],e.labels=e.labels||[],e}function ad(e){return e=e||{},e.data=na(e.data),ia(e),e}const Ks=new Map,sa=new Set;function ei(e,t){let i=Ks.get(e);return i||(i=t(),Ks.set(e,i),sa.add(i)),i}const xe=(e,t,i)=>{const n=Ft(t,i);n!==void 0&&e.add(n)};class ld{constructor(t){this._config=ad(t),this._scopeCache=new Map,this._resolverCache=new Map}get platform(){return this._config.platform}get type(){return this._config.type}set type(t){this._config.type=t}get data(){return this._config.data}set data(t){this._config.data=na(t)}get options(){return this._config.options}set options(t){this._config.options=t}get plugins(){return this._config.plugins}update(){const t=this._config;this.clearCache(),ia(t)}clearCache(){this._scopeCache.clear(),this._resolverCache.clear()}datasetScopeKeys(t){return ei(t,()=>[[`datasets.${t}`,""]])}datasetAnimationScopeKeys(t,i){return ei(`${t}.transition.${i}`,()=>[[`datasets.${t}.transitions.${i}`,`transitions.${i}`],[`datasets.${t}`,""]])}datasetElementScopeKeys(t,i){return ei(`${t}-${i}`,()=>[[`datasets.${t}.elements.${i}`,`datasets.${t}`,`elements.${i}`,""]])}pluginScopeKeys(t){const i=t.id,n=this.type;return ei(`${n}-plugin-${i}`,()=>[[`plugins.${i}`,...t.additionalOptionScopes||[]]])}_cachedScopes(t,i){const n=this._scopeCache;let s=n.get(t);return(!s||i)&&(s=new Map,n.set(t,s)),s}getOptionScopes(t,i,n){const{options:s,type:o}=this,r=this._cachedScopes(t,n),a=r.get(i);if(a)return a;const l=new Set;i.forEach(h=>{t&&(l.add(t),h.forEach(d=>xe(l,t,d))),h.forEach(d=>xe(l,s,d)),h.forEach(d=>xe(l,Jt[o]||{},d)),h.forEach(d=>xe(l,H,d)),h.forEach(d=>xe(l,dn,d))});const c=Array.from(l);return c.length===0&&c.push(Object.create(null)),sa.has(i)&&r.set(i,c),c}chartOptionScopes(){const{options:t,type:i}=this;return[t,Jt[i]||{},H.datasets[i]||{},{type:i},H,dn]}resolveNamedOptions(t,i,n,s=[""]){const o={$shared:!0},{resolver:r,subPrefixes:a}=qs(this._resolverCache,t,s);let l=r;if(hd(r,i)){o.$shared=!1,n=zt(n)?n():n;const c=this.createResolver(t,n,a);l=ae(r,n,c)}for(const c of i)o[c]=l[c];return o}createResolver(t,i,n=[""],s){const{resolver:o}=qs(this._resolverCache,t,n);return L(i)?ae(o,i,void 0,s):o}}function qs(e,t,i){let n=e.get(t);n||(n=new Map,e.set(t,n));const s=i.join();let o=n.get(s);return o||(o={resolver:Bn(t,i),subPrefixes:i.filter(a=>!a.toLowerCase().includes("hover"))},n.set(s,o)),o}const cd=e=>L(e)&&Object.getOwnPropertyNames(e).some(t=>zt(e[t]));function hd(e,t){const{isScriptable:i,isIndexable:n}=Fr(e);for(const s of t){const o=i(s),r=n(s),a=(r||o)&&e[s];if(o&&(zt(a)||cd(a))||r&&j(a))return!0}return!1}var dd="4.4.8";const ud=["top","bottom","left","right","chartArea"];function Zs(e,t){return e==="top"||e==="bottom"||ud.indexOf(e)===-1&&t==="x"}function Js(e,t){return function(i,n){return i[e]===n[e]?i[t]-n[t]:i[e]-n[e]}}function Qs(e){const t=e.chart,i=t.options.animation;t.notifyPlugins("afterRender"),I(i&&i.onComplete,[e],t)}function fd(e){const t=e.chart,i=t.options.animation;I(i&&i.onProgress,[e],t)}function oa(e){return Hn()&&typeof e=="string"?e=document.getElementById(e):e&&e.length&&(e=e[0]),e&&e.canvas&&(e=e.canvas),e}const gi={},to=e=>{const t=oa(e);return Object.values(gi).filter(i=>i.canvas===t).pop()};function gd(e,t,i){const n=Object.keys(e);for(const s of n){const o=+s;if(o>=t){const r=e[s];delete e[s],(i>0||o>t)&&(e[o+i]=r)}}}function pd(e,t,i,n){return!i||e.type==="mouseout"?null:n?t:e}function ii(e,t,i){return e.options.clip?e[i]:t[i]}function md(e,t){const{xScale:i,yScale:n}=e;return i&&n?{left:ii(i,t,"left"),right:ii(i,t,"right"),top:ii(n,t,"top"),bottom:ii(n,t,"bottom")}:t}class pt{static register(...t){gt.add(...t),eo()}static unregister(...t){gt.remove(...t),eo()}constructor(t,i){const n=this.config=new ld(i),s=oa(t),o=to(s);if(o)throw new Error("Canvas is already in use. Chart with ID '"+o.id+"' must be destroyed before the canvas with ID '"+o.canvas.id+"' can be reused.");const r=n.createResolver(n.chartOptionScopes(),this.getContext());this.platform=new(n.platform||Eh(s)),this.platform.updateConfig(n);const a=this.platform.acquireContext(s,r.aspectRatio),l=a&&a.canvas,c=l&&l.height,h=l&&l.width;if(this.id=bl(),this.ctx=a,this.canvas=l,this.width=h,this.height=c,this._options=r,this._aspectRatio=this.aspectRatio,this._layers=[],this._metasets=[],this._stacks=void 0,this.boxes=[],this.currentDevicePixelRatio=void 0,this.chartArea=void 0,this._active=[],this._lastEvent=void 0,this._listeners={},this._responsiveListeners=void 0,this._sortedMetasets=[],this.scales={},this._plugins=new Zh,this.$proxies={},this._hiddenIndices={},this.attached=!1,this._animationsDisabled=void 0,this.$context=void 0,this._doResize=$l(d=>this.update(d),r.resizeDelay||0),this._dataChanges=[],gi[this.id]=this,!a||!l){console.error("Failed to create chart: can't acquire context from the given item");return}vt.listen(this,"complete",Qs),vt.listen(this,"progress",fd),this._initialize(),this.attached&&this.update()}get aspectRatio(){const{options:{aspectRatio:t,maintainAspectRatio:i},width:n,height:s,_aspectRatio:o}=this;return D(t)?i&&o?o:s?n/s:null:t}get data(){return this.config.data}set data(t){this.config.data=t}get options(){return this._options}set options(t){this.config.options=t}get registry(){return gt}_initialize(){return this.notifyPlugins("beforeInit"),this.options.responsive?this.resize():Ss(this,this.options.devicePixelRatio),this.bindEvents(),this.notifyPlugins("afterInit"),this}clear(){return xs(this.canvas,this.ctx),this}stop(){return vt.stop(this),this}resize(t,i){vt.running(this)?this._resizeBeforeDraw={width:t,height:i}:this._resize(t,i)}_resize(t,i){const n=this.options,s=this.canvas,o=n.maintainAspectRatio&&this.aspectRatio,r=this.platform.getMaximumSize(s,t,i,o),a=n.devicePixelRatio||this.platform.getDevicePixelRatio(),l=this.width?"resize":"attach";this.width=r.width,this.height=r.height,this._aspectRatio=this.aspectRatio,Ss(this,a,!0)&&(this.notifyPlugins("resize",{size:r}),I(n.onResize,[this,r],this),this.attached&&this._doResize(l)&&this.render())}ensureScalesHaveIDs(){const i=this.options.scales||{};$(i,(n,s)=>{n.id=s})}buildOrUpdateScales(){const t=this.options,i=t.scales,n=this.scales,s=Object.keys(n).reduce((r,a)=>(r[a]=!1,r),{});let o=[];i&&(o=o.concat(Object.keys(i).map(r=>{const a=i[r],l=pn(r,a),c=l==="r",h=l==="x";return{options:a,dposition:c?"chartArea":h?"bottom":"left",dtype:c?"radialLinear":h?"category":"linear"}}))),$(o,r=>{const a=r.options,l=a.id,c=pn(l,a),h=C(a.type,r.dtype);(a.position===void 0||Zs(a.position,c)!==Zs(r.dposition))&&(a.position=r.dposition),s[l]=!0;let d=null;if(l in n&&n[l].type===h)d=n[l];else{const u=gt.getScale(h);d=new u({id:l,type:h,ctx:this.ctx,chart:this}),n[d.id]=d}d.init(a,t)}),$(s,(r,a)=>{r||delete n[a]}),$(n,r=>{J.configure(this,r,r.options),J.addBox(this,r)})}_updateMetasets(){const t=this._metasets,i=this.data.datasets.length,n=t.length;if(t.sort((s,o)=>s.index-o.index),n>i){for(let s=i;s<n;++s)this._destroyDatasetMeta(s);t.splice(i,n-i)}this._sortedMetasets=t.slice(0).sort(Js("order","index"))}_removeUnreferencedMetasets(){const{_metasets:t,data:{datasets:i}}=this;t.length>i.length&&delete this._stacks,t.forEach((n,s)=>{i.filter(o=>o===n._dataset).length===0&&this._destroyDatasetMeta(s)})}buildOrUpdateControllers(){const t=[],i=this.data.datasets;let n,s;for(this._removeUnreferencedMetasets(),n=0,s=i.length;n<s;n++){const o=i[n];let r=this.getDatasetMeta(n);const a=o.type||this.config.type;if(r.type&&r.type!==a&&(this._destroyDatasetMeta(n),r=this.getDatasetMeta(n)),r.type=a,r.indexAxis=o.indexAxis||gn(a,this.options),r.order=o.order||0,r.index=n,r.label=""+o.label,r.visible=this.isDatasetVisible(n),r.controller)r.controller.updateIndex(n),r.controller.linkScales();else{const l=gt.getController(a),{datasetElementType:c,dataElementType:h}=H.datasets[a];Object.assign(l,{dataElementType:gt.getElement(h),datasetElementType:c&&gt.getElement(c)}),r.controller=new l(this,n),t.push(r.controller)}}return this._updateMetasets(),t}_resetElements(){$(this.data.datasets,(t,i)=>{this.getDatasetMeta(i).controller.reset()},this)}reset(){this._resetElements(),this.notifyPlugins("reset")}update(t){const i=this.config;i.update();const n=this._options=i.createResolver(i.chartOptionScopes(),this.getContext()),s=this._animationsDisabled=!n.animation;if(this._updateScales(),this._checkEventBindings(),this._updateHiddenIndices(),this._plugins.invalidate(),this.notifyPlugins("beforeUpdate",{mode:t,cancelable:!0})===!1)return;const o=this.buildOrUpdateControllers();this.notifyPlugins("beforeElementsUpdate");let r=0;for(let c=0,h=this.data.datasets.length;c<h;c++){const{controller:d}=this.getDatasetMeta(c),u=!s&&o.indexOf(d)===-1;d.buildOrUpdateElements(u),r=Math.max(+d.getMaxOverflow(),r)}r=this._minPadding=n.layout.autoPadding?r:0,this._updateLayout(r),s||$(o,c=>{c.reset()}),this._updateDatasets(t),this.notifyPlugins("afterUpdate",{mode:t}),this._layers.sort(Js("z","_idx"));const{_active:a,_lastEvent:l}=this;l?this._eventHandler(l,!0):a.length&&this._updateHoverStyles(a,a,!0),this.render()}_updateScales(){$(this.scales,t=>{J.removeBox(this,t)}),this.ensureScalesHaveIDs(),this.buildOrUpdateScales()}_checkEventBindings(){const t=this.options,i=new Set(Object.keys(this._listeners)),n=new Set(t.events);(!ds(i,n)||!!this._responsiveListeners!==t.responsive)&&(this.unbindEvents(),this.bindEvents())}_updateHiddenIndices(){const{_hiddenIndices:t}=this,i=this._getUniformDataChanges()||[];for(const{method:n,start:s,count:o}of i){const r=n==="_removeElements"?-o:o;gd(t,s,r)}}_getUniformDataChanges(){const t=this._dataChanges;if(!t||!t.length)return;this._dataChanges=[];const i=this.data.datasets.length,n=o=>new Set(t.filter(r=>r[0]===o).map((r,a)=>a+","+r.splice(1).join(","))),s=n(0);for(let o=1;o<i;o++)if(!ds(s,n(o)))return;return Array.from(s).map(o=>o.split(",")).map(o=>({method:o[1],start:+o[2],count:+o[3]}))}_updateLayout(t){if(this.notifyPlugins("beforeLayout",{cancelable:!0})===!1)return;J.update(this,this.width,this.height,t);const i=this.chartArea,n=i.width<=0||i.height<=0;this._layers=[],$(this.boxes,s=>{n&&s.position==="chartArea"||(s.configure&&s.configure(),this._layers.push(...s._layers()))},this),this._layers.forEach((s,o)=>{s._idx=o}),this.notifyPlugins("afterLayout")}_updateDatasets(t){if(this.notifyPlugins("beforeDatasetsUpdate",{mode:t,cancelable:!0})!==!1){for(let i=0,n=this.data.datasets.length;i<n;++i)this.getDatasetMeta(i).controller.configure();for(let i=0,n=this.data.datasets.length;i<n;++i)this._updateDataset(i,zt(t)?t({datasetIndex:i}):t);this.notifyPlugins("afterDatasetsUpdate",{mode:t})}}_updateDataset(t,i){const n=this.getDatasetMeta(t),s={meta:n,index:t,mode:i,cancelable:!0};this.notifyPlugins("beforeDatasetUpdate",s)!==!1&&(n.controller._update(i),s.cancelable=!1,this.notifyPlugins("afterDatasetUpdate",s))}render(){this.notifyPlugins("beforeRender",{cancelable:!0})!==!1&&(vt.has(this)?this.attached&&!vt.running(this)&&vt.start(this):(this.draw(),Qs({chart:this})))}draw(){let t;if(this._resizeBeforeDraw){const{width:n,height:s}=this._resizeBeforeDraw;this._resizeBeforeDraw=null,this._resize(n,s)}if(this.clear(),this.width<=0||this.height<=0||this.notifyPlugins("beforeDraw",{cancelable:!0})===!1)return;const i=this._layers;for(t=0;t<i.length&&i[t].z<=0;++t)i[t].draw(this.chartArea);for(this._drawDatasets();t<i.length;++t)i[t].draw(this.chartArea);this.notifyPlugins("afterDraw")}_getSortedDatasetMetas(t){const i=this._sortedMetasets,n=[];let s,o;for(s=0,o=i.length;s<o;++s){const r=i[s];(!t||r.visible)&&n.push(r)}return n}getSortedVisibleDatasetMetas(){return this._getSortedDatasetMetas(!0)}_drawDatasets(){if(this.notifyPlugins("beforeDatasetsDraw",{cancelable:!0})===!1)return;const t=this.getSortedVisibleDatasetMetas();for(let i=t.length-1;i>=0;--i)this._drawDataset(t[i]);this.notifyPlugins("afterDatasetsDraw")}_drawDataset(t){const i=this.ctx,n=t._clip,s=!n.disabled,o=md(t,this.chartArea),r={meta:t,index:t.index,cancelable:!0};this.notifyPlugins("beforeDatasetDraw",r)!==!1&&(s&&Ti(i,{left:n.left===!1?0:o.left-n.left,right:n.right===!1?this.width:o.right+n.right,top:n.top===!1?0:o.top-n.top,bottom:n.bottom===!1?this.height:o.bottom+n.bottom}),t.controller.draw(),s&&Di(i),r.cancelable=!1,this.notifyPlugins("afterDatasetDraw",r))}isPointInArea(t){return Ot(t,this.chartArea,this._minPadding)}getElementsAtEventForMode(t,i,n,s){const o=uh.modes[i];return typeof o=="function"?o(this,t,n,s):[]}getDatasetMeta(t){const i=this.data.datasets[t],n=this._metasets;let s=n.filter(o=>o&&o._dataset===i).pop();return s||(s={type:null,data:[],dataset:null,controller:null,hidden:null,xAxisID:null,yAxisID:null,order:i&&i.order||0,index:t,_dataset:i,_parsed:[],_sorted:!1},n.push(s)),s}getContext(){return this.$context||(this.$context=Bt(null,{chart:this,type:"chart"}))}getVisibleDatasetCount(){return this.getSortedVisibleDatasetMetas().length}isDatasetVisible(t){const i=this.data.datasets[t];if(!i)return!1;const n=this.getDatasetMeta(t);return typeof n.hidden=="boolean"?!n.hidden:!i.hidden}setDatasetVisibility(t,i){const n=this.getDatasetMeta(t);n.hidden=!i}toggleDataVisibility(t){this._hiddenIndices[t]=!this._hiddenIndices[t]}getDataVisibility(t){return!this._hiddenIndices[t]}_updateVisibility(t,i,n){const s=n?"show":"hide",o=this.getDatasetMeta(t),r=o.controller._resolveAnimations(void 0,s);Ie(i)?(o.data[i].hidden=!n,this.update()):(this.setDatasetVisibility(t,n),r.update(o,{visible:n}),this.update(a=>a.datasetIndex===t?s:void 0))}hide(t,i){this._updateVisibility(t,i,!1)}show(t,i){this._updateVisibility(t,i,!0)}_destroyDatasetMeta(t){const i=this._metasets[t];i&&i.controller&&i.controller._destroy(),delete this._metasets[t]}_stop(){let t,i;for(this.stop(),vt.remove(this),t=0,i=this.data.datasets.length;t<i;++t)this._destroyDatasetMeta(t)}destroy(){this.notifyPlugins("beforeDestroy");const{canvas:t,ctx:i}=this;this._stop(),this.config.clearCache(),t&&(this.unbindEvents(),xs(t,i),this.platform.releaseContext(i),this.canvas=null,this.ctx=null),delete gi[this.id],this.notifyPlugins("afterDestroy")}toBase64Image(...t){return this.canvas.toDataURL(...t)}bindEvents(){this.bindUserEvents(),this.options.responsive?this.bindResponsiveEvents():this.attached=!0}bindUserEvents(){const t=this._listeners,i=this.platform,n=(o,r)=>{i.addEventListener(this,o,r),t[o]=r},s=(o,r,a)=>{o.offsetX=r,o.offsetY=a,this._eventHandler(o)};$(this.options.events,o=>n(o,s))}bindResponsiveEvents(){this._responsiveListeners||(this._responsiveListeners={});const t=this._responsiveListeners,i=this.platform,n=(l,c)=>{i.addEventListener(this,l,c),t[l]=c},s=(l,c)=>{t[l]&&(i.removeEventListener(this,l,c),delete t[l])},o=(l,c)=>{this.canvas&&this.resize(l,c)};let r;const a=()=>{s("attach",a),this.attached=!0,this.resize(),n("resize",o),n("detach",r)};r=()=>{this.attached=!1,s("resize",o),this._stop(),this._resize(0,0),n("attach",a)},i.isAttached(this.canvas)?a():r()}unbindEvents(){$(this._listeners,(t,i)=>{this.platform.removeEventListener(this,i,t)}),this._listeners={},$(this._responsiveListeners,(t,i)=>{this.platform.removeEventListener(this,i,t)}),this._responsiveListeners=void 0}updateHoverStyle(t,i,n){const s=n?"set":"remove";let o,r,a,l;for(i==="dataset"&&(o=this.getDatasetMeta(t[0].datasetIndex),o.controller["_"+s+"DatasetHoverStyle"]()),a=0,l=t.length;a<l;++a){r=t[a];const c=r&&this.getDatasetMeta(r.datasetIndex).controller;c&&c[s+"HoverStyle"](r.element,r.datasetIndex,r.index)}}getActiveElements(){return this._active||[]}setActiveElements(t){const i=this._active||[],n=t.map(({datasetIndex:o,index:r})=>{const a=this.getDatasetMeta(o);if(!a)throw new Error("No dataset found at index "+o);return{datasetIndex:o,element:a.data[r],index:r}});!bi(n,i)&&(this._active=n,this._lastEvent=null,this._updateHoverStyles(n,i))}notifyPlugins(t,i,n){return this._plugins.notify(this,t,i,n)}isPluginEnabled(t){return this._plugins._cache.filter(i=>i.plugin.id===t).length===1}_updateHoverStyles(t,i,n){const s=this.options.hover,o=(l,c)=>l.filter(h=>!c.some(d=>h.datasetIndex===d.datasetIndex&&h.index===d.index)),r=o(i,t),a=n?t:o(t,i);r.length&&this.updateHoverStyle(r,s.mode,!1),a.length&&s.mode&&this.updateHoverStyle(a,s.mode,!0)}_eventHandler(t,i){const n={event:t,replay:i,cancelable:!0,inChartArea:this.isPointInArea(t)},s=r=>(r.options.events||this.options.events).includes(t.native.type);if(this.notifyPlugins("beforeEvent",n,s)===!1)return;const o=this._handleEvent(t,i,n.inChartArea);return n.cancelable=!1,this.notifyPlugins("afterEvent",n,s),(o||n.changed)&&this.render(),this}_handleEvent(t,i,n){const{_active:s=[],options:o}=this,r=i,a=this._getActiveElements(t,s,n,r),l=Sl(t),c=pd(t,this._lastEvent,n,l);n&&(this._lastEvent=null,I(o.onHover,[t,a,this],this),l&&I(o.onClick,[t,a,this],this));const h=!bi(a,s);return(h||i)&&(this._active=a,this._updateHoverStyles(a,s,i)),this._lastEvent=c,h}_getActiveElements(t,i,n,s){if(t.type==="mouseout")return[];if(!n)return i;const o=this.options.hover;return this.getElementsAtEventForMode(t,o.mode,o,s)}}M(pt,"defaults",H),M(pt,"instances",gi),M(pt,"overrides",Jt),M(pt,"registry",gt),M(pt,"version",dd),M(pt,"getChart",to);function eo(){return $(pt.instances,e=>e._plugins.invalidate())}function bd(e,t,i){const{startAngle:n,pixelMargin:s,x:o,y:r,outerRadius:a,innerRadius:l}=t;let c=s/a;e.beginPath(),e.arc(o,r,a,n-c,i+c),l>s?(c=s/l,e.arc(o,r,l,i+c,n-c,!0)):e.arc(o,r,s,i+Y,n-Y),e.closePath(),e.clip()}function _d(e){return zn(e,["outerStart","outerEnd","innerStart","innerEnd"])}function yd(e,t,i,n){const s=_d(e.options.borderRadius),o=(i-t)/2,r=Math.min(o,n*t/2),a=l=>{const c=(i-Math.min(o,l))*n/2;return K(l,0,Math.min(o,c))};return{outerStart:a(s.outerStart),outerEnd:a(s.outerEnd),innerStart:K(s.innerStart,0,r),innerEnd:K(s.innerEnd,0,r)}}function se(e,t,i,n){return{x:i+e*Math.cos(t),y:n+e*Math.sin(t)}}function Si(e,t,i,n,s,o){const{x:r,y:a,startAngle:l,pixelMargin:c,innerRadius:h}=t,d=Math.max(t.outerRadius+n+i-c,0),u=h>0?h+n+i+c:0;let f=0;const g=s-l;if(n){const E=h>0?h-n:0,R=d>0?d-n:0,N=(E+R)/2,W=N!==0?g*N/(N+n):g;f=(g-W)/2}const p=Math.max(.001,g*d-i/B)/d,m=(g-p)/2,b=l+m+f,_=s-m-f,{outerStart:v,outerEnd:x,innerStart:y,innerEnd:S}=yd(t,u,d,_-b),w=d-v,P=d-x,O=b+v/w,A=_-x/P,k=u+y,T=u+S,U=b+y/k,tt=_-S/T;if(e.beginPath(),o){const E=(O+A)/2;if(e.arc(r,a,d,O,E),e.arc(r,a,d,E,A),x>0){const G=se(P,A,r,a);e.arc(G.x,G.y,x,A,_+Y)}const R=se(T,_,r,a);if(e.lineTo(R.x,R.y),S>0){const G=se(T,tt,r,a);e.arc(G.x,G.y,S,_+Y,tt+Math.PI)}const N=(_-S/u+(b+y/u))/2;if(e.arc(r,a,u,_-S/u,N,!0),e.arc(r,a,u,N,b+y/u,!0),y>0){const G=se(k,U,r,a);e.arc(G.x,G.y,y,U+Math.PI,b-Y)}const W=se(w,b,r,a);if(e.lineTo(W.x,W.y),v>0){const G=se(w,O,r,a);e.arc(G.x,G.y,v,b-Y,O)}}else{e.moveTo(r,a);const E=Math.cos(O)*d+r,R=Math.sin(O)*d+a;e.lineTo(E,R);const N=Math.cos(A)*d+r,W=Math.sin(A)*d+a;e.lineTo(N,W)}e.closePath()}function xd(e,t,i,n,s){const{fullCircles:o,startAngle:r,circumference:a}=t;let l=t.endAngle;if(o){Si(e,t,i,n,l,s);for(let c=0;c<o;++c)e.fill();isNaN(a)||(l=r+(a%z||z))}return Si(e,t,i,n,l,s),e.fill(),l}function vd(e,t,i,n,s){const{fullCircles:o,startAngle:r,circumference:a,options:l}=t,{borderWidth:c,borderJoinStyle:h,borderDash:d,borderDashOffset:u}=l,f=l.borderAlign==="inner";if(!c)return;e.setLineDash(d||[]),e.lineDashOffset=u,f?(e.lineWidth=c*2,e.lineJoin=h||"round"):(e.lineWidth=c,e.lineJoin=h||"bevel");let g=t.endAngle;if(o){Si(e,t,i,n,g,s);for(let p=0;p<o;++p)e.stroke();isNaN(a)||(g=r+(a%z||z))}f&&bd(e,t,g),o||(Si(e,t,i,n,g,s),e.stroke())}class Pe extends ut{constructor(i){super();M(this,"circumference");M(this,"endAngle");M(this,"fullCircles");M(this,"innerRadius");M(this,"outerRadius");M(this,"pixelMargin");M(this,"startAngle");this.options=void 0,this.circumference=void 0,this.startAngle=void 0,this.endAngle=void 0,this.innerRadius=void 0,this.outerRadius=void 0,this.pixelMargin=0,this.fullCircles=0,i&&Object.assign(this,i)}inRange(i,n,s){const o=this.getProps(["x","y"],s),{angle:r,distance:a}=Ar(o,{x:i,y:n}),{startAngle:l,endAngle:c,innerRadius:h,outerRadius:d,circumference:u}=this.getProps(["startAngle","endAngle","innerRadius","outerRadius","circumference"],s),f=(this.options.spacing+this.options.borderWidth)/2,g=C(u,c-l),p=Fe(r,l,c)&&l!==c,m=g>=z||p,b=wt(a,h+f,d+f);return m&&b}getCenterPoint(i){const{x:n,y:s,startAngle:o,endAngle:r,innerRadius:a,outerRadius:l}=this.getProps(["x","y","startAngle","endAngle","innerRadius","outerRadius"],i),{offset:c,spacing:h}=this.options,d=(o+r)/2,u=(a+l+h+c)/2;return{x:n+Math.cos(d)*u,y:s+Math.sin(d)*u}}tooltipPosition(i){return this.getCenterPoint(i)}draw(i){const{options:n,circumference:s}=this,o=(n.offset||0)/4,r=(n.spacing||0)/2,a=n.circular;if(this.pixelMargin=n.borderAlign==="inner"?.33:0,this.fullCircles=s>z?Math.floor(s/z):0,s===0||this.innerRadius<0||this.outerRadius<0)return;i.save();const l=(this.startAngle+this.endAngle)/2;i.translate(Math.cos(l)*o,Math.sin(l)*o);const c=1-Math.sin(Math.min(B,s||0)),h=o*c;i.fillStyle=n.backgroundColor,i.strokeStyle=n.borderColor,xd(i,this,h,r,a),vd(i,this,h,r,a),i.restore()}}M(Pe,"id","arc"),M(Pe,"defaults",{borderAlign:"center",borderColor:"#fff",borderDash:[],borderDashOffset:0,borderJoinStyle:void 0,borderRadius:0,borderWidth:2,offset:0,spacing:0,angle:void 0,circular:!0}),M(Pe,"defaultRoutes",{backgroundColor:"backgroundColor"}),M(Pe,"descriptors",{_scriptable:!0,_indexable:i=>i!=="borderDash"});function ra(e,t,i=t){e.lineCap=C(i.borderCapStyle,t.borderCapStyle),e.setLineDash(C(i.borderDash,t.borderDash)),e.lineDashOffset=C(i.borderDashOffset,t.borderDashOffset),e.lineJoin=C(i.borderJoinStyle,t.borderJoinStyle),e.lineWidth=C(i.borderWidth,t.borderWidth),e.strokeStyle=C(i.borderColor,t.borderColor)}function Md(e,t,i){e.lineTo(i.x,i.y)}function Sd(e){return e.stepped?Ul:e.tension||e.cubicInterpolationMode==="monotone"?Gl:Md}function aa(e,t,i={}){const n=e.length,{start:s=0,end:o=n-1}=i,{start:r,end:a}=t,l=Math.max(s,r),c=Math.min(o,a),h=s<r&&o<r||s>a&&o>a;return{count:n,start:l,loop:t.loop,ilen:c<l&&!h?n+c-l:c-l}}function wd(e,t,i,n){const{points:s,options:o}=t,{count:r,start:a,loop:l,ilen:c}=aa(s,i,n),h=Sd(o);let{move:d=!0,reverse:u}=n||{},f,g,p;for(f=0;f<=c;++f)g=s[(a+(u?c-f:f))%r],!g.skip&&(d?(e.moveTo(g.x,g.y),d=!1):h(e,p,g,u,o.stepped),p=g);return l&&(g=s[(a+(u?c:0))%r],h(e,p,g,u,o.stepped)),!!l}function Pd(e,t,i,n){const s=t.points,{count:o,start:r,ilen:a}=aa(s,i,n),{move:l=!0,reverse:c}=n||{};let h=0,d=0,u,f,g,p,m,b;const _=x=>(r+(c?a-x:x))%o,v=()=>{p!==m&&(e.lineTo(h,m),e.lineTo(h,p),e.lineTo(h,b))};for(l&&(f=s[_(0)],e.moveTo(f.x,f.y)),u=0;u<=a;++u){if(f=s[_(u)],f.skip)continue;const x=f.x,y=f.y,S=x|0;S===g?(y<p?p=y:y>m&&(m=y),h=(d*h+x)/++d):(v(),e.lineTo(x,y),g=S,d=0,p=m=y),b=y}v()}function mn(e){const t=e.options,i=t.borderDash&&t.borderDash.length;return!e._decimated&&!e._loop&&!t.tension&&t.cubicInterpolationMode!=="monotone"&&!t.stepped&&!i?Pd:wd}function Od(e){return e.stepped?Pc:e.tension||e.cubicInterpolationMode==="monotone"?Oc:Gt}function Ad(e,t,i,n){let s=t._path;s||(s=t._path=new Path2D,t.path(s,i,n)&&s.closePath()),ra(e,t.options),e.stroke(s)}function kd(e,t,i,n){const{segments:s,options:o}=t,r=mn(t);for(const a of s)ra(e,o,a.style),e.beginPath(),r(e,t,a,{start:i,end:i+n-1})&&e.closePath(),e.stroke()}const Cd=typeof Path2D=="function";function Td(e,t,i,n){Cd&&!t.options.segment?Ad(e,t,i,n):kd(e,t,i,n)}class $t extends ut{constructor(t){super(),this.animated=!0,this.options=void 0,this._chart=void 0,this._loop=void 0,this._fullLoop=void 0,this._path=void 0,this._points=void 0,this._segments=void 0,this._decimated=!1,this._pointsUpdated=!1,this._datasetIndex=void 0,t&&Object.assign(this,t)}updateControlPoints(t,i){const n=this.options;if((n.tension||n.cubicInterpolationMode==="monotone")&&!n.stepped&&!this._pointsUpdated){const s=n.spanGaps?this._loop:this._fullLoop;bc(this._points,n,t,s,i),this._pointsUpdated=!0}}set points(t){this._points=t,delete this._segments,delete this._path,this._pointsUpdated=!1}get points(){return this._points}get segments(){return this._segments||(this._segments=Lc(this,this.options.segment))}first(){const t=this.segments,i=this.points;return t.length&&i[t[0].start]}last(){const t=this.segments,i=this.points,n=t.length;return n&&i[t[n-1].end]}interpolate(t,i){const n=this.options,s=t[i],o=this.points,r=Gr(this,{property:i,start:s,end:s});if(!r.length)return;const a=[],l=Od(n);let c,h;for(c=0,h=r.length;c<h;++c){const{start:d,end:u}=r[c],f=o[d],g=o[u];if(f===g){a.push(f);continue}const p=Math.abs((s-f[i])/(g[i]-f[i])),m=l(f,g,p,n.stepped);m[i]=t[i],a.push(m)}return a.length===1?a[0]:a}pathSegment(t,i,n){return mn(this)(t,this,i,n)}path(t,i,n){const s=this.segments,o=mn(this);let r=this._loop;i=i||0,n=n||this.points.length-i;for(const a of s)r&=o(t,this,a,{start:i,end:i+n-1});return!!r}draw(t,i,n,s){const o=this.options||{};(this.points||[]).length&&o.borderWidth&&(t.save(),Td(t,this,n,s),t.restore()),this.animated&&(this._pointsUpdated=!1,this._path=void 0)}}M($t,"id","line"),M($t,"defaults",{borderCapStyle:"butt",borderDash:[],borderDashOffset:0,borderJoinStyle:"miter",borderWidth:3,capBezierPoints:!0,cubicInterpolationMode:"default",fill:!1,spanGaps:!1,stepped:!1,tension:0}),M($t,"defaultRoutes",{backgroundColor:"backgroundColor",borderColor:"borderColor"}),M($t,"descriptors",{_scriptable:!0,_indexable:t=>t!=="borderDash"&&t!=="fill"});function io(e,t,i,n){const s=e.options,{[i]:o}=e.getProps([i],n);return Math.abs(t-o)<s.radius+s.hitRadius}class pi extends ut{constructor(i){super();M(this,"parsed");M(this,"skip");M(this,"stop");this.options=void 0,this.parsed=void 0,this.skip=void 0,this.stop=void 0,i&&Object.assign(this,i)}inRange(i,n,s){const o=this.options,{x:r,y:a}=this.getProps(["x","y"],s);return Math.pow(i-r,2)+Math.pow(n-a,2)<Math.pow(o.hitRadius+o.radius,2)}inXRange(i,n){return io(this,i,"x",n)}inYRange(i,n){return io(this,i,"y",n)}getCenterPoint(i){const{x:n,y:s}=this.getProps(["x","y"],i);return{x:n,y:s}}size(i){i=i||this.options||{};let n=i.radius||0;n=Math.max(n,n&&i.hoverRadius||0);const s=n&&i.borderWidth||0;return(n+s)*2}draw(i,n){const s=this.options;this.skip||s.radius<.1||!Ot(this,n,this.size(s)/2)||(i.strokeStyle=s.borderColor,i.lineWidth=s.borderWidth,i.fillStyle=s.backgroundColor,un(i,s,this.x,this.y))}getRange(){const i=this.options||{};return i.radius+i.hitRadius}}M(pi,"id","point"),M(pi,"defaults",{borderWidth:1,hitRadius:1,hoverBorderWidth:1,hoverRadius:4,pointStyle:"circle",radius:3,rotation:0}),M(pi,"defaultRoutes",{backgroundColor:"backgroundColor",borderColor:"borderColor"});function la(e,t){const{x:i,y:n,base:s,width:o,height:r}=e.getProps(["x","y","base","width","height"],t);let a,l,c,h,d;return e.horizontal?(d=r/2,a=Math.min(i,s),l=Math.max(i,s),c=n-d,h=n+d):(d=o/2,a=i-d,l=i+d,c=Math.min(n,s),h=Math.max(n,s)),{left:a,top:c,right:l,bottom:h}}function Rt(e,t,i,n){return e?0:K(t,i,n)}function Dd(e,t,i){const n=e.options.borderWidth,s=e.borderSkipped,o=Ir(n);return{t:Rt(s.top,o.top,0,i),r:Rt(s.right,o.right,0,t),b:Rt(s.bottom,o.bottom,0,i),l:Rt(s.left,o.left,0,t)}}function Ld(e,t,i){const{enableBorderRadius:n}=e.getProps(["enableBorderRadius"]),s=e.options.borderRadius,o=qt(s),r=Math.min(t,i),a=e.borderSkipped,l=n||L(s);return{topLeft:Rt(!l||a.top||a.left,o.topLeft,0,r),topRight:Rt(!l||a.top||a.right,o.topRight,0,r),bottomLeft:Rt(!l||a.bottom||a.left,o.bottomLeft,0,r),bottomRight:Rt(!l||a.bottom||a.right,o.bottomRight,0,r)}}function Ed(e){const t=la(e),i=t.right-t.left,n=t.bottom-t.top,s=Dd(e,i/2,n/2),o=Ld(e,i/2,n/2);return{outer:{x:t.left,y:t.top,w:i,h:n,radius:o},inner:{x:t.left+s.l,y:t.top+s.t,w:i-s.l-s.r,h:n-s.t-s.b,radius:{topLeft:Math.max(0,o.topLeft-Math.max(s.t,s.l)),topRight:Math.max(0,o.topRight-Math.max(s.t,s.r)),bottomLeft:Math.max(0,o.bottomLeft-Math.max(s.b,s.l)),bottomRight:Math.max(0,o.bottomRight-Math.max(s.b,s.r))}}}}function tn(e,t,i,n){const s=t===null,o=i===null,a=e&&!(s&&o)&&la(e,n);return a&&(s||wt(t,a.left,a.right))&&(o||wt(i,a.top,a.bottom))}function $d(e){return e.topLeft||e.topRight||e.bottomLeft||e.bottomRight}function Rd(e,t){e.rect(t.x,t.y,t.w,t.h)}function en(e,t,i={}){const n=e.x!==i.x?-t:0,s=e.y!==i.y?-t:0,o=(e.x+e.w!==i.x+i.w?t:0)-n,r=(e.y+e.h!==i.y+i.h?t:0)-s;return{x:e.x+n,y:e.y+s,w:e.w+o,h:e.h+r,radius:e.radius}}class mi extends ut{constructor(t){super(),this.options=void 0,this.horizontal=void 0,this.base=void 0,this.width=void 0,this.height=void 0,this.inflateAmount=void 0,t&&Object.assign(this,t)}draw(t){const{inflateAmount:i,options:{borderColor:n,backgroundColor:s}}=this,{inner:o,outer:r}=Ed(this),a=$d(r.radius)?ze:Rd;t.save(),(r.w!==o.w||r.h!==o.h)&&(t.beginPath(),a(t,en(r,i,o)),t.clip(),a(t,en(o,-i,r)),t.fillStyle=n,t.fill("evenodd")),t.beginPath(),a(t,en(o,i)),t.fillStyle=s,t.fill(),t.restore()}inRange(t,i,n){return tn(this,t,i,n)}inXRange(t,i){return tn(this,t,null,i)}inYRange(t,i){return tn(this,null,t,i)}getCenterPoint(t){const{x:i,y:n,base:s,horizontal:o}=this.getProps(["x","y","base","horizontal"],t);return{x:o?(i+s)/2:i,y:o?n:(n+s)/2}}getRange(t){return t==="x"?this.width/2:this.height/2}}M(mi,"id","bar"),M(mi,"defaults",{borderSkipped:"start",borderWidth:0,borderRadius:0,inflateAmount:"auto",pointStyle:void 0}),M(mi,"defaultRoutes",{backgroundColor:"backgroundColor",borderColor:"borderColor"});var Id=Object.freeze({__proto__:null,ArcElement:Pe,BarElement:mi,LineElement:$t,PointElement:pi});const bn=["rgb(54, 162, 235)","rgb(255, 99, 132)","rgb(255, 159, 64)","rgb(255, 205, 86)","rgb(75, 192, 192)","rgb(153, 102, 255)","rgb(201, 203, 207)"],no=bn.map(e=>e.replace("rgb(","rgba(").replace(")",", 0.5)"));function ca(e){return bn[e%bn.length]}function ha(e){return no[e%no.length]}function Fd(e,t){return e.borderColor=ca(t),e.backgroundColor=ha(t),++t}function zd(e,t){return e.backgroundColor=e.data.map(()=>ca(t++)),t}function Bd(e,t){return e.backgroundColor=e.data.map(()=>ha(t++)),t}function Nd(e){let t=0;return(i,n)=>{const s=e.getDatasetMeta(n).controller;s instanceof Kt?t=zd(i,t):s instanceof Ee?t=Bd(i,t):s&&(t=Fd(i,t))}}function so(e){let t;for(t in e)if(e[t].borderColor||e[t].backgroundColor)return!0;return!1}function jd(e){return e&&(e.borderColor||e.backgroundColor)}function Hd(){return H.borderColor!=="rgba(0,0,0,0.1)"||H.backgroundColor!=="rgba(0,0,0,0.1)"}var Wd={id:"colors",defaults:{enabled:!0,forceOverride:!1},beforeLayout(e,t,i){if(!i.enabled)return;const{data:{datasets:n},options:s}=e.config,{elements:o}=s,r=so(n)||jd(s)||o&&so(o)||Hd();if(!i.forceOverride&&r)return;const a=Nd(e);n.forEach(a)}};function Vd(e,t,i,n,s){const o=s.samples||n;if(o>=i)return e.slice(t,t+i);const r=[],a=(i-2)/(o-2);let l=0;const c=t+i-1;let h=t,d,u,f,g,p;for(r[l++]=e[h],d=0;d<o-2;d++){let m=0,b=0,_;const v=Math.floor((d+1)*a)+1+t,x=Math.min(Math.floor((d+2)*a)+1,i)+t,y=x-v;for(_=v;_<x;_++)m+=e[_].x,b+=e[_].y;m/=y,b/=y;const S=Math.floor(d*a)+1+t,w=Math.min(Math.floor((d+1)*a)+1,i)+t,{x:P,y:O}=e[h];for(f=g=-1,_=S;_<w;_++)g=.5*Math.abs((P-m)*(e[_].y-O)-(P-e[_].x)*(b-O)),g>f&&(f=g,u=e[_],p=_);r[l++]=u,h=p}return r[l++]=e[c],r}function Yd(e,t,i,n){let s=0,o=0,r,a,l,c,h,d,u,f,g,p;const m=[],b=t+i-1,_=e[t].x,x=e[b].x-_;for(r=t;r<t+i;++r){a=e[r],l=(a.x-_)/x*n,c=a.y;const y=l|0;if(y===h)c<g?(g=c,d=r):c>p&&(p=c,u=r),s=(o*s+a.x)/++o;else{const S=r-1;if(!D(d)&&!D(u)){const w=Math.min(d,u),P=Math.max(d,u);w!==f&&w!==S&&m.push({...e[w],x:s}),P!==f&&P!==S&&m.push({...e[P],x:s})}r>0&&S!==f&&m.push(e[S]),m.push(a),h=y,o=0,g=p=c,d=u=f=r}}return m}function da(e){if(e._decimated){const t=e._data;delete e._decimated,delete e._data,Object.defineProperty(e,"data",{configurable:!0,enumerable:!0,writable:!0,value:t})}}function oo(e){e.data.datasets.forEach(t=>{da(t)})}function Ud(e,t){const i=t.length;let n=0,s;const{iScale:o}=e,{min:r,max:a,minDefined:l,maxDefined:c}=o.getUserBounds();return l&&(n=K(Pt(t,o.axis,r).lo,0,i-1)),c?s=K(Pt(t,o.axis,a).hi+1,n,i)-n:s=i-n,{start:n,count:s}}var Gd={id:"decimation",defaults:{algorithm:"min-max",enabled:!1},beforeElementsUpdate:(e,t,i)=>{if(!i.enabled){oo(e);return}const n=e.width;e.data.datasets.forEach((s,o)=>{const{_data:r,indexAxis:a}=s,l=e.getDatasetMeta(o),c=r||s.data;if(Se([a,e.options.indexAxis])==="y"||!l.controller.supportsDecimation)return;const h=e.scales[l.xAxisID];if(h.type!=="linear"&&h.type!=="time"||e.options.parsing)return;let{start:d,count:u}=Ud(l,c);const f=i.threshold||4*n;if(u<=f){da(s);return}D(r)&&(s._data=c,delete s.data,Object.defineProperty(s,"data",{configurable:!0,enumerable:!0,get:function(){return this._decimated},set:function(p){this._data=p}}));let g;switch(i.algorithm){case"lttb":g=Vd(c,d,u,n,i);break;case"min-max":g=Yd(c,d,u,n);break;default:throw new Error(`Unsupported decimation algorithm '${i.algorithm}'`)}s._decimated=g})},destroy(e){oo(e)}};function Xd(e,t,i){const n=e.segments,s=e.points,o=t.points,r=[];for(const a of n){let{start:l,end:c}=a;c=Yn(l,c,s);const h=_n(i,s[l],s[c],a.loop);if(!t.segments){r.push({source:a,target:h,start:s[l],end:s[c]});continue}const d=Gr(t,h);for(const u of d){const f=_n(i,o[u.start],o[u.end],u.loop),g=Ur(a,s,f);for(const p of g)r.push({source:p,target:u,start:{[i]:ro(h,f,"start",Math.max)},end:{[i]:ro(h,f,"end",Math.min)}})}}return r}function _n(e,t,i,n){if(n)return;let s=t[e],o=i[e];return e==="angle"&&(s=lt(s),o=lt(o)),{property:e,start:s,end:o}}function Kd(e,t){const{x:i=null,y:n=null}=e||{},s=t.points,o=[];return t.segments.forEach(({start:r,end:a})=>{a=Yn(r,a,s);const l=s[r],c=s[a];n!==null?(o.push({x:l.x,y:n}),o.push({x:c.x,y:n})):i!==null&&(o.push({x:i,y:l.y}),o.push({x:i,y:c.y}))}),o}function Yn(e,t,i){for(;t>e;t--){const n=i[t];if(!isNaN(n.x)&&!isNaN(n.y))break}return t}function ro(e,t,i,n){return e&&t?n(e[i],t[i]):e?e[i]:t?t[i]:0}function ua(e,t){let i=[],n=!1;return j(e)?(n=!0,i=e):i=Kd(e,t),i.length?new $t({points:i,options:{tension:0},_loop:n,_fullLoop:n}):null}function ao(e){return e&&e.fill!==!1}function qd(e,t,i){let s=e[t].fill;const o=[t];let r;if(!i)return s;for(;s!==!1&&o.indexOf(s)===-1;){if(!V(s))return s;if(r=e[s],!r)return!1;if(r.visible)return s;o.push(s),s=r.fill}return!1}function Zd(e,t,i){const n=eu(e);if(L(n))return isNaN(n.value)?!1:n;let s=parseFloat(n);return V(s)&&Math.floor(s)===s?Jd(n[0],t,s,i):["origin","start","end","stack","shape"].indexOf(n)>=0&&n}function Jd(e,t,i,n){return(e==="-"||e==="+")&&(i=t+i),i===t||i<0||i>=n?!1:i}function Qd(e,t){let i=null;return e==="start"?i=t.bottom:e==="end"?i=t.top:L(e)?i=t.getPixelForValue(e.value):t.getBasePixel&&(i=t.getBasePixel()),i}function tu(e,t,i){let n;return e==="start"?n=i:e==="end"?n=t.options.reverse?t.min:t.max:L(e)?n=e.value:n=t.getBaseValue(),n}function eu(e){const t=e.options,i=t.fill;let n=C(i&&i.target,i);return n===void 0&&(n=!!t.backgroundColor),n===!1||n===null?!1:n===!0?"origin":n}function iu(e){const{scale:t,index:i,line:n}=e,s=[],o=n.segments,r=n.points,a=nu(t,i);a.push(ua({x:null,y:t.bottom},n));for(let l=0;l<o.length;l++){const c=o[l];for(let h=c.start;h<=c.end;h++)su(s,r[h],a)}return new $t({points:s,options:{}})}function nu(e,t){const i=[],n=e.getMatchingVisibleMetas("line");for(let s=0;s<n.length;s++){const o=n[s];if(o.index===t)break;o.hidden||i.unshift(o.dataset)}return i}function su(e,t,i){const n=[];for(let s=0;s<i.length;s++){const o=i[s],{first:r,last:a,point:l}=ou(o,t,"x");if(!(!l||r&&a)){if(r)n.unshift(l);else if(e.push(l),!a)break}}e.push(...n)}function ou(e,t,i){const n=e.interpolate(t,i);if(!n)return{};const s=n[i],o=e.segments,r=e.points;let a=!1,l=!1;for(let c=0;c<o.length;c++){const h=o[c],d=r[h.start][i],u=r[h.end][i];if(wt(s,d,u)){a=s===d,l=s===u;break}}return{first:a,last:l,point:n}}class fa{constructor(t){this.x=t.x,this.y=t.y,this.radius=t.radius}pathSegment(t,i,n){const{x:s,y:o,radius:r}=this;return i=i||{start:0,end:z},t.arc(s,o,r,i.end,i.start,!0),!n.bounds}interpolate(t){const{x:i,y:n,radius:s}=this,o=t.angle;return{x:i+Math.cos(o)*s,y:n+Math.sin(o)*s,angle:o}}}function ru(e){const{chart:t,fill:i,line:n}=e;if(V(i))return au(t,i);if(i==="stack")return iu(e);if(i==="shape")return!0;const s=lu(e);return s instanceof fa?s:ua(s,n)}function au(e,t){const i=e.getDatasetMeta(t);return i&&e.isDatasetVisible(t)?i.dataset:null}function lu(e){return(e.scale||{}).getPointPositionForValue?hu(e):cu(e)}function cu(e){const{scale:t={},fill:i}=e,n=Qd(i,t);if(V(n)){const s=t.isHorizontal();return{x:s?n:null,y:s?null:n}}return null}function hu(e){const{scale:t,fill:i}=e,n=t.options,s=t.getLabels().length,o=n.reverse?t.max:t.min,r=tu(i,t,o),a=[];if(n.grid.circular){const l=t.getPointPositionForValue(0,o);return new fa({x:l.x,y:l.y,radius:t.getDistanceFromCenterForValue(r)})}for(let l=0;l<s;++l)a.push(t.getPointPositionForValue(l,r));return a}function nn(e,t,i){const n=ru(t),{line:s,scale:o,axis:r}=t,a=s.options,l=a.fill,c=a.backgroundColor,{above:h=c,below:d=c}=l||{};n&&s.points.length&&(Ti(e,i),du(e,{line:s,target:n,above:h,below:d,area:i,scale:o,axis:r}),Di(e))}function du(e,t){const{line:i,target:n,above:s,below:o,area:r,scale:a}=t,l=i._loop?"angle":t.axis;e.save(),l==="x"&&o!==s&&(lo(e,n,r.top),co(e,{line:i,target:n,color:s,scale:a,property:l}),e.restore(),e.save(),lo(e,n,r.bottom)),co(e,{line:i,target:n,color:o,scale:a,property:l}),e.restore()}function lo(e,t,i){const{segments:n,points:s}=t;let o=!0,r=!1;e.beginPath();for(const a of n){const{start:l,end:c}=a,h=s[l],d=s[Yn(l,c,s)];o?(e.moveTo(h.x,h.y),o=!1):(e.lineTo(h.x,i),e.lineTo(h.x,h.y)),r=!!t.pathSegment(e,a,{move:r}),r?e.closePath():e.lineTo(d.x,i)}e.lineTo(t.first().x,i),e.closePath(),e.clip()}function co(e,t){const{line:i,target:n,property:s,color:o,scale:r}=t,a=Xd(i,n,s);for(const{source:l,target:c,start:h,end:d}of a){const{style:{backgroundColor:u=o}={}}=l,f=n!==!0;e.save(),e.fillStyle=u,uu(e,r,f&&_n(s,h,d)),e.beginPath();const g=!!i.pathSegment(e,l);let p;if(f){g?e.closePath():ho(e,n,d,s);const m=!!n.pathSegment(e,c,{move:g,reverse:!0});p=g&&m,p||ho(e,n,h,s)}e.closePath(),e.fill(p?"evenodd":"nonzero"),e.restore()}}function uu(e,t,i){const{top:n,bottom:s}=t.chart.chartArea,{property:o,start:r,end:a}=i||{};o==="x"&&(e.beginPath(),e.rect(r,n,a-r,s-n),e.clip())}function ho(e,t,i,n){const s=t.interpolate(i,n);s&&e.lineTo(s.x,s.y)}var fu={id:"filler",afterDatasetsUpdate(e,t,i){const n=(e.data.datasets||[]).length,s=[];let o,r,a,l;for(r=0;r<n;++r)o=e.getDatasetMeta(r),a=o.dataset,l=null,a&&a.options&&a instanceof $t&&(l={visible:e.isDatasetVisible(r),index:r,fill:Zd(a,r,n),chart:e,axis:o.controller.options.indexAxis,scale:o.vScale,line:a}),o.$filler=l,s.push(l);for(r=0;r<n;++r)l=s[r],!(!l||l.fill===!1)&&(l.fill=qd(s,r,i.propagate))},beforeDraw(e,t,i){const n=i.drawTime==="beforeDraw",s=e.getSortedVisibleDatasetMetas(),o=e.chartArea;for(let r=s.length-1;r>=0;--r){const a=s[r].$filler;a&&(a.line.updateControlPoints(o,a.axis),n&&a.fill&&nn(e.ctx,a,o))}},beforeDatasetsDraw(e,t,i){if(i.drawTime!=="beforeDatasetsDraw")return;const n=e.getSortedVisibleDatasetMetas();for(let s=n.length-1;s>=0;--s){const o=n[s].$filler;ao(o)&&nn(e.ctx,o,e.chartArea)}},beforeDatasetDraw(e,t,i){const n=t.meta.$filler;!ao(n)||i.drawTime!=="beforeDatasetDraw"||nn(e.ctx,n,e.chartArea)},defaults:{propagate:!0,drawTime:"beforeDatasetDraw"}};const uo=(e,t)=>{let{boxHeight:i=t,boxWidth:n=t}=e;return e.usePointStyle&&(i=Math.min(i,t),n=e.pointStyleWidth||Math.min(n,t)),{boxWidth:n,boxHeight:i,itemHeight:Math.max(t,i)}},gu=(e,t)=>e!==null&&t!==null&&e.datasetIndex===t.datasetIndex&&e.index===t.index;class fo extends ut{constructor(t){super(),this._added=!1,this.legendHitBoxes=[],this._hoveredItem=null,this.doughnutMode=!1,this.chart=t.chart,this.options=t.options,this.ctx=t.ctx,this.legendItems=void 0,this.columnSizes=void 0,this.lineWidths=void 0,this.maxHeight=void 0,this.maxWidth=void 0,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.height=void 0,this.width=void 0,this._margins=void 0,this.position=void 0,this.weight=void 0,this.fullSize=void 0}update(t,i,n){this.maxWidth=t,this.maxHeight=i,this._margins=n,this.setDimensions(),this.buildLabels(),this.fit()}setDimensions(){this.isHorizontal()?(this.width=this.maxWidth,this.left=this._margins.left,this.right=this.width):(this.height=this.maxHeight,this.top=this._margins.top,this.bottom=this.height)}buildLabels(){const t=this.options.labels||{};let i=I(t.generateLabels,[this.chart],this)||[];t.filter&&(i=i.filter(n=>t.filter(n,this.chart.data))),t.sort&&(i=i.sort((n,s)=>t.sort(n,s,this.chart.data))),this.options.reverse&&i.reverse(),this.legendItems=i}fit(){const{options:t,ctx:i}=this;if(!t.display){this.width=this.height=0;return}const n=t.labels,s=X(n.font),o=s.size,r=this._computeTitleHeight(),{boxWidth:a,itemHeight:l}=uo(n,o);let c,h;i.font=s.string,this.isHorizontal()?(c=this.maxWidth,h=this._fitRows(r,o,a,l)+10):(h=this.maxHeight,c=this._fitCols(r,s,a,l)+10),this.width=Math.min(c,t.maxWidth||this.maxWidth),this.height=Math.min(h,t.maxHeight||this.maxHeight)}_fitRows(t,i,n,s){const{ctx:o,maxWidth:r,options:{labels:{padding:a}}}=this,l=this.legendHitBoxes=[],c=this.lineWidths=[0],h=s+a;let d=t;o.textAlign="left",o.textBaseline="middle";let u=-1,f=-h;return this.legendItems.forEach((g,p)=>{const m=n+i/2+o.measureText(g.text).width;(p===0||c[c.length-1]+m+2*a>r)&&(d+=h,c[c.length-(p>0?0:1)]=0,f+=h,u++),l[p]={left:0,top:f,row:u,width:m,height:s},c[c.length-1]+=m+a}),d}_fitCols(t,i,n,s){const{ctx:o,maxHeight:r,options:{labels:{padding:a}}}=this,l=this.legendHitBoxes=[],c=this.columnSizes=[],h=r-t;let d=a,u=0,f=0,g=0,p=0;return this.legendItems.forEach((m,b)=>{const{itemWidth:_,itemHeight:v}=pu(n,i,o,m,s);b>0&&f+v+2*a>h&&(d+=u+a,c.push({width:u,height:f}),g+=u+a,p++,u=f=0),l[b]={left:g,top:f,col:p,width:_,height:v},u=Math.max(u,_),f+=v+a}),d+=u,c.push({width:u,height:f}),d}adjustHitBoxes(){if(!this.options.display)return;const t=this._computeTitleHeight(),{legendHitBoxes:i,options:{align:n,labels:{padding:s},rtl:o}}=this,r=oe(o,this.left,this.width);if(this.isHorizontal()){let a=0,l=q(n,this.left+s,this.right-this.lineWidths[a]);for(const c of i)a!==c.row&&(a=c.row,l=q(n,this.left+s,this.right-this.lineWidths[a])),c.top+=this.top+t+s,c.left=r.leftForLtr(r.x(l),c.width),l+=c.width+s}else{let a=0,l=q(n,this.top+t+s,this.bottom-this.columnSizes[a].height);for(const c of i)c.col!==a&&(a=c.col,l=q(n,this.top+t+s,this.bottom-this.columnSizes[a].height)),c.top=l,c.left+=this.left+s,c.left=r.leftForLtr(r.x(c.left),c.width),l+=c.height+s}}isHorizontal(){return this.options.position==="top"||this.options.position==="bottom"}draw(){if(this.options.display){const t=this.ctx;Ti(t,this),this._draw(),Di(t)}}_draw(){const{options:t,columnSizes:i,lineWidths:n,ctx:s}=this,{align:o,labels:r}=t,a=H.color,l=oe(t.rtl,this.left,this.width),c=X(r.font),{padding:h}=r,d=c.size,u=d/2;let f;this.drawTitle(),s.textAlign=l.textAlign("left"),s.textBaseline="middle",s.lineWidth=.5,s.font=c.string;const{boxWidth:g,boxHeight:p,itemHeight:m}=uo(r,d),b=function(S,w,P){if(isNaN(g)||g<=0||isNaN(p)||p<0)return;s.save();const O=C(P.lineWidth,1);if(s.fillStyle=C(P.fillStyle,a),s.lineCap=C(P.lineCap,"butt"),s.lineDashOffset=C(P.lineDashOffset,0),s.lineJoin=C(P.lineJoin,"miter"),s.lineWidth=O,s.strokeStyle=C(P.strokeStyle,a),s.setLineDash(C(P.lineDash,[])),r.usePointStyle){const A={radius:p*Math.SQRT2/2,pointStyle:P.pointStyle,rotation:P.rotation,borderWidth:O},k=l.xPlus(S,g/2),T=w+u;Rr(s,A,k,T,r.pointStyleWidth&&g)}else{const A=w+Math.max((d-p)/2,0),k=l.leftForLtr(S,g),T=qt(P.borderRadius);s.beginPath(),Object.values(T).some(U=>U!==0)?ze(s,{x:k,y:A,w:g,h:p,radius:T}):s.rect(k,A,g,p),s.fill(),O!==0&&s.stroke()}s.restore()},_=function(S,w,P){Qt(s,P.text,S,w+m/2,c,{strikethrough:P.hidden,textAlign:l.textAlign(P.textAlign)})},v=this.isHorizontal(),x=this._computeTitleHeight();v?f={x:q(o,this.left+h,this.right-n[0]),y:this.top+h+x,line:0}:f={x:this.left+h,y:q(o,this.top+x+h,this.bottom-i[0].height),line:0},Wr(this.ctx,t.textDirection);const y=m+h;this.legendItems.forEach((S,w)=>{s.strokeStyle=S.fontColor,s.fillStyle=S.fontColor;const P=s.measureText(S.text).width,O=l.textAlign(S.textAlign||(S.textAlign=r.textAlign)),A=g+u+P;let k=f.x,T=f.y;l.setWidth(this.width),v?w>0&&k+A+h>this.right&&(T=f.y+=y,f.line++,k=f.x=q(o,this.left+h,this.right-n[f.line])):w>0&&T+y>this.bottom&&(k=f.x=k+i[f.line].width+h,f.line++,T=f.y=q(o,this.top+x+h,this.bottom-i[f.line].height));const U=l.x(k);if(b(U,T,S),k=Rl(O,k+g+u,v?k+A:this.right,t.rtl),_(l.x(k),T,S),v)f.x+=A+h;else if(typeof S.text!="string"){const tt=c.lineHeight;f.y+=ga(S,tt)+h}else f.y+=y}),Vr(this.ctx,t.textDirection)}drawTitle(){const t=this.options,i=t.title,n=X(i.font),s=Q(i.padding);if(!i.display)return;const o=oe(t.rtl,this.left,this.width),r=this.ctx,a=i.position,l=n.size/2,c=s.top+l;let h,d=this.left,u=this.width;if(this.isHorizontal())u=Math.max(...this.lineWidths),h=this.top+c,d=q(t.align,d,this.right-u);else{const g=this.columnSizes.reduce((p,m)=>Math.max(p,m.height),0);h=c+q(t.align,this.top,this.bottom-g-t.labels.padding-this._computeTitleHeight())}const f=q(a,d,d+u);r.textAlign=o.textAlign(In(a)),r.textBaseline="middle",r.strokeStyle=i.color,r.fillStyle=i.color,r.font=n.string,Qt(r,i.text,f,h,n)}_computeTitleHeight(){const t=this.options.title,i=X(t.font),n=Q(t.padding);return t.display?i.lineHeight+n.height:0}_getLegendItemAt(t,i){let n,s,o;if(wt(t,this.left,this.right)&&wt(i,this.top,this.bottom)){for(o=this.legendHitBoxes,n=0;n<o.length;++n)if(s=o[n],wt(t,s.left,s.left+s.width)&&wt(i,s.top,s.top+s.height))return this.legendItems[n]}return null}handleEvent(t){const i=this.options;if(!_u(t.type,i))return;const n=this._getLegendItemAt(t.x,t.y);if(t.type==="mousemove"||t.type==="mouseout"){const s=this._hoveredItem,o=gu(s,n);s&&!o&&I(i.onLeave,[t,s,this],this),this._hoveredItem=n,n&&!o&&I(i.onHover,[t,n,this],this)}else n&&I(i.onClick,[t,n,this],this)}}function pu(e,t,i,n,s){const o=mu(n,e,t,i),r=bu(s,n,t.lineHeight);return{itemWidth:o,itemHeight:r}}function mu(e,t,i,n){let s=e.text;return s&&typeof s!="string"&&(s=s.reduce((o,r)=>o.length>r.length?o:r)),t+i.size/2+n.measureText(s).width}function bu(e,t,i){let n=e;return typeof t.text!="string"&&(n=ga(t,i)),n}function ga(e,t){const i=e.text?e.text.length:0;return t*i}function _u(e,t){return!!((e==="mousemove"||e==="mouseout")&&(t.onHover||t.onLeave)||t.onClick&&(e==="click"||e==="mouseup"))}var yu={id:"legend",_element:fo,start(e,t,i){const n=e.legend=new fo({ctx:e.ctx,options:i,chart:e});J.configure(e,n,i),J.addBox(e,n)},stop(e){J.removeBox(e,e.legend),delete e.legend},beforeUpdate(e,t,i){const n=e.legend;J.configure(e,n,i),n.options=i},afterUpdate(e){const t=e.legend;t.buildLabels(),t.adjustHitBoxes()},afterEvent(e,t){t.replay||e.legend.handleEvent(t.event)},defaults:{display:!0,position:"top",align:"center",fullSize:!0,reverse:!1,weight:1e3,onClick(e,t,i){const n=t.datasetIndex,s=i.chart;s.isDatasetVisible(n)?(s.hide(n),t.hidden=!0):(s.show(n),t.hidden=!1)},onHover:null,onLeave:null,labels:{color:e=>e.chart.options.color,boxWidth:40,padding:10,generateLabels(e){const t=e.data.datasets,{labels:{usePointStyle:i,pointStyle:n,textAlign:s,color:o,useBorderRadius:r,borderRadius:a}}=e.legend.options;return e._getSortedDatasetMetas().map(l=>{const c=l.controller.getStyle(i?0:void 0),h=Q(c.borderWidth);return{text:t[l.index].label,fillStyle:c.backgroundColor,fontColor:o,hidden:!l.visible,lineCap:c.borderCapStyle,lineDash:c.borderDash,lineDashOffset:c.borderDashOffset,lineJoin:c.borderJoinStyle,lineWidth:(h.width+h.height)/4,strokeStyle:c.borderColor,pointStyle:n||c.pointStyle,rotation:c.rotation,textAlign:s||c.textAlign,borderRadius:r&&(a||c.borderRadius),datasetIndex:l.index}},this)}},title:{color:e=>e.chart.options.color,display:!1,position:"center",text:""}},descriptors:{_scriptable:e=>!e.startsWith("on"),labels:{_scriptable:e=>!["generateLabels","filter","sort"].includes(e)}}};class Un extends ut{constructor(t){super(),this.chart=t.chart,this.options=t.options,this.ctx=t.ctx,this._padding=void 0,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.width=void 0,this.height=void 0,this.position=void 0,this.weight=void 0,this.fullSize=void 0}update(t,i){const n=this.options;if(this.left=0,this.top=0,!n.display){this.width=this.height=this.right=this.bottom=0;return}this.width=this.right=t,this.height=this.bottom=i;const s=j(n.text)?n.text.length:1;this._padding=Q(n.padding);const o=s*X(n.font).lineHeight+this._padding.height;this.isHorizontal()?this.height=o:this.width=o}isHorizontal(){const t=this.options.position;return t==="top"||t==="bottom"}_drawArgs(t){const{top:i,left:n,bottom:s,right:o,options:r}=this,a=r.align;let l=0,c,h,d;return this.isHorizontal()?(h=q(a,n,o),d=i+t,c=o-n):(r.position==="left"?(h=n+t,d=q(a,s,i),l=B*-.5):(h=o-t,d=q(a,i,s),l=B*.5),c=s-i),{titleX:h,titleY:d,maxWidth:c,rotation:l}}draw(){const t=this.ctx,i=this.options;if(!i.display)return;const n=X(i.font),o=n.lineHeight/2+this._padding.top,{titleX:r,titleY:a,maxWidth:l,rotation:c}=this._drawArgs(o);Qt(t,i.text,0,0,n,{color:i.color,maxWidth:l,rotation:c,textAlign:In(i.align),textBaseline:"middle",translation:[r,a]})}}function xu(e,t){const i=new Un({ctx:e.ctx,options:t,chart:e});J.configure(e,i,t),J.addBox(e,i),e.titleBlock=i}var vu={id:"title",_element:Un,start(e,t,i){xu(e,i)},stop(e){const t=e.titleBlock;J.removeBox(e,t),delete e.titleBlock},beforeUpdate(e,t,i){const n=e.titleBlock;J.configure(e,n,i),n.options=i},defaults:{align:"center",display:!1,font:{weight:"bold"},fullSize:!0,padding:10,position:"top",text:"",weight:2e3},defaultRoutes:{color:"color"},descriptors:{_scriptable:!0,_indexable:!1}};const ni=new WeakMap;var Mu={id:"subtitle",start(e,t,i){const n=new Un({ctx:e.ctx,options:i,chart:e});J.configure(e,n,i),J.addBox(e,n),ni.set(e,n)},stop(e){J.removeBox(e,ni.get(e)),ni.delete(e)},beforeUpdate(e,t,i){const n=ni.get(e);J.configure(e,n,i),n.options=i},defaults:{align:"center",display:!1,font:{weight:"normal"},fullSize:!0,padding:0,position:"top",text:"",weight:1500},defaultRoutes:{color:"color"},descriptors:{_scriptable:!0,_indexable:!1}};const Oe={average(e){if(!e.length)return!1;let t,i,n=new Set,s=0,o=0;for(t=0,i=e.length;t<i;++t){const a=e[t].element;if(a&&a.hasValue()){const l=a.tooltipPosition();n.add(l.x),s+=l.y,++o}}return o===0||n.size===0?!1:{x:[...n].reduce((a,l)=>a+l)/n.size,y:s/o}},nearest(e,t){if(!e.length)return!1;let i=t.x,n=t.y,s=Number.POSITIVE_INFINITY,o,r,a;for(o=0,r=e.length;o<r;++o){const l=e[o].element;if(l&&l.hasValue()){const c=l.getCenterPoint(),h=hn(t,c);h<s&&(s=h,a=l)}}if(a){const l=a.tooltipPosition();i=l.x,n=l.y}return{x:i,y:n}}};function ft(e,t){return t&&(j(t)?Array.prototype.push.apply(e,t):e.push(t)),e}function Mt(e){return(typeof e=="string"||e instanceof String)&&e.indexOf(`
`)>-1?e.split(`
`):e}function Su(e,t){const{element:i,datasetIndex:n,index:s}=t,o=e.getDatasetMeta(n).controller,{label:r,value:a}=o.getLabelAndValue(s);return{chart:e,label:r,parsed:o.getParsed(s),raw:e.data.datasets[n].data[s],formattedValue:a,dataset:o.getDataset(),dataIndex:s,datasetIndex:n,element:i}}function go(e,t){const i=e.chart.ctx,{body:n,footer:s,title:o}=e,{boxWidth:r,boxHeight:a}=t,l=X(t.bodyFont),c=X(t.titleFont),h=X(t.footerFont),d=o.length,u=s.length,f=n.length,g=Q(t.padding);let p=g.height,m=0,b=n.reduce((x,y)=>x+y.before.length+y.lines.length+y.after.length,0);if(b+=e.beforeBody.length+e.afterBody.length,d&&(p+=d*c.lineHeight+(d-1)*t.titleSpacing+t.titleMarginBottom),b){const x=t.displayColors?Math.max(a,l.lineHeight):l.lineHeight;p+=f*x+(b-f)*l.lineHeight+(b-1)*t.bodySpacing}u&&(p+=t.footerMarginTop+u*h.lineHeight+(u-1)*t.footerSpacing);let _=0;const v=function(x){m=Math.max(m,i.measureText(x).width+_)};return i.save(),i.font=c.string,$(e.title,v),i.font=l.string,$(e.beforeBody.concat(e.afterBody),v),_=t.displayColors?r+2+t.boxPadding:0,$(n,x=>{$(x.before,v),$(x.lines,v),$(x.after,v)}),_=0,i.font=h.string,$(e.footer,v),i.restore(),m+=g.width,{width:m,height:p}}function wu(e,t){const{y:i,height:n}=t;return i<n/2?"top":i>e.height-n/2?"bottom":"center"}function Pu(e,t,i,n){const{x:s,width:o}=n,r=i.caretSize+i.caretPadding;if(e==="left"&&s+o+r>t.width||e==="right"&&s-o-r<0)return!0}function Ou(e,t,i,n){const{x:s,width:o}=i,{width:r,chartArea:{left:a,right:l}}=e;let c="center";return n==="center"?c=s<=(a+l)/2?"left":"right":s<=o/2?c="left":s>=r-o/2&&(c="right"),Pu(c,e,t,i)&&(c="center"),c}function po(e,t,i){const n=i.yAlign||t.yAlign||wu(e,i);return{xAlign:i.xAlign||t.xAlign||Ou(e,t,i,n),yAlign:n}}function Au(e,t){let{x:i,width:n}=e;return t==="right"?i-=n:t==="center"&&(i-=n/2),i}function ku(e,t,i){let{y:n,height:s}=e;return t==="top"?n+=i:t==="bottom"?n-=s+i:n-=s/2,n}function mo(e,t,i,n){const{caretSize:s,caretPadding:o,cornerRadius:r}=e,{xAlign:a,yAlign:l}=i,c=s+o,{topLeft:h,topRight:d,bottomLeft:u,bottomRight:f}=qt(r);let g=Au(t,a);const p=ku(t,l,c);return l==="center"?a==="left"?g+=c:a==="right"&&(g-=c):a==="left"?g-=Math.max(h,u)+s:a==="right"&&(g+=Math.max(d,f)+s),{x:K(g,0,n.width-t.width),y:K(p,0,n.height-t.height)}}function si(e,t,i){const n=Q(i.padding);return t==="center"?e.x+e.width/2:t==="right"?e.x+e.width-n.right:e.x+n.left}function bo(e){return ft([],Mt(e))}function Cu(e,t,i){return Bt(e,{tooltip:t,tooltipItems:i,type:"tooltip"})}function _o(e,t){const i=t&&t.dataset&&t.dataset.tooltip&&t.dataset.tooltip.callbacks;return i?e.override(i):e}const pa={beforeTitle:xt,title(e){if(e.length>0){const t=e[0],i=t.chart.data.labels,n=i?i.length:0;if(this&&this.options&&this.options.mode==="dataset")return t.dataset.label||"";if(t.label)return t.label;if(n>0&&t.dataIndex<n)return i[t.dataIndex]}return""},afterTitle:xt,beforeBody:xt,beforeLabel:xt,label(e){if(this&&this.options&&this.options.mode==="dataset")return e.label+": "+e.formattedValue||e.formattedValue;let t=e.dataset.label||"";t&&(t+=": ");const i=e.formattedValue;return D(i)||(t+=i),t},labelColor(e){const i=e.chart.getDatasetMeta(e.datasetIndex).controller.getStyle(e.dataIndex);return{borderColor:i.borderColor,backgroundColor:i.backgroundColor,borderWidth:i.borderWidth,borderDash:i.borderDash,borderDashOffset:i.borderDashOffset,borderRadius:0}},labelTextColor(){return this.options.bodyColor},labelPointStyle(e){const i=e.chart.getDatasetMeta(e.datasetIndex).controller.getStyle(e.dataIndex);return{pointStyle:i.pointStyle,rotation:i.rotation}},afterLabel:xt,afterBody:xt,beforeFooter:xt,footer:xt,afterFooter:xt};function st(e,t,i,n){const s=e[t].call(i,n);return typeof s>"u"?pa[t].call(i,n):s}class yn extends ut{constructor(t){super(),this.opacity=0,this._active=[],this._eventPosition=void 0,this._size=void 0,this._cachedAnimations=void 0,this._tooltipItems=[],this.$animations=void 0,this.$context=void 0,this.chart=t.chart,this.options=t.options,this.dataPoints=void 0,this.title=void 0,this.beforeBody=void 0,this.body=void 0,this.afterBody=void 0,this.footer=void 0,this.xAlign=void 0,this.yAlign=void 0,this.x=void 0,this.y=void 0,this.height=void 0,this.width=void 0,this.caretX=void 0,this.caretY=void 0,this.labelColors=void 0,this.labelPointStyles=void 0,this.labelTextColors=void 0}initialize(t){this.options=t,this._cachedAnimations=void 0,this.$context=void 0}_resolveAnimations(){const t=this._cachedAnimations;if(t)return t;const i=this.chart,n=this.options.setContext(this.getContext()),s=n.enabled&&i.options.animation&&n.animations,o=new Xr(this.chart,s);return s._cacheable&&(this._cachedAnimations=Object.freeze(o)),o}getContext(){return this.$context||(this.$context=Cu(this.chart.getContext(),this,this._tooltipItems))}getTitle(t,i){const{callbacks:n}=i,s=st(n,"beforeTitle",this,t),o=st(n,"title",this,t),r=st(n,"afterTitle",this,t);let a=[];return a=ft(a,Mt(s)),a=ft(a,Mt(o)),a=ft(a,Mt(r)),a}getBeforeBody(t,i){return bo(st(i.callbacks,"beforeBody",this,t))}getBody(t,i){const{callbacks:n}=i,s=[];return $(t,o=>{const r={before:[],lines:[],after:[]},a=_o(n,o);ft(r.before,Mt(st(a,"beforeLabel",this,o))),ft(r.lines,st(a,"label",this,o)),ft(r.after,Mt(st(a,"afterLabel",this,o))),s.push(r)}),s}getAfterBody(t,i){return bo(st(i.callbacks,"afterBody",this,t))}getFooter(t,i){const{callbacks:n}=i,s=st(n,"beforeFooter",this,t),o=st(n,"footer",this,t),r=st(n,"afterFooter",this,t);let a=[];return a=ft(a,Mt(s)),a=ft(a,Mt(o)),a=ft(a,Mt(r)),a}_createItems(t){const i=this._active,n=this.chart.data,s=[],o=[],r=[];let a=[],l,c;for(l=0,c=i.length;l<c;++l)a.push(Su(this.chart,i[l]));return t.filter&&(a=a.filter((h,d,u)=>t.filter(h,d,u,n))),t.itemSort&&(a=a.sort((h,d)=>t.itemSort(h,d,n))),$(a,h=>{const d=_o(t.callbacks,h);s.push(st(d,"labelColor",this,h)),o.push(st(d,"labelPointStyle",this,h)),r.push(st(d,"labelTextColor",this,h))}),this.labelColors=s,this.labelPointStyles=o,this.labelTextColors=r,this.dataPoints=a,a}update(t,i){const n=this.options.setContext(this.getContext()),s=this._active;let o,r=[];if(!s.length)this.opacity!==0&&(o={opacity:0});else{const a=Oe[n.position].call(this,s,this._eventPosition);r=this._createItems(n),this.title=this.getTitle(r,n),this.beforeBody=this.getBeforeBody(r,n),this.body=this.getBody(r,n),this.afterBody=this.getAfterBody(r,n),this.footer=this.getFooter(r,n);const l=this._size=go(this,n),c=Object.assign({},a,l),h=po(this.chart,n,c),d=mo(n,c,h,this.chart);this.xAlign=h.xAlign,this.yAlign=h.yAlign,o={opacity:1,x:d.x,y:d.y,width:l.width,height:l.height,caretX:a.x,caretY:a.y}}this._tooltipItems=r,this.$context=void 0,o&&this._resolveAnimations().update(this,o),t&&n.external&&n.external.call(this,{chart:this.chart,tooltip:this,replay:i})}drawCaret(t,i,n,s){const o=this.getCaretPosition(t,n,s);i.lineTo(o.x1,o.y1),i.lineTo(o.x2,o.y2),i.lineTo(o.x3,o.y3)}getCaretPosition(t,i,n){const{xAlign:s,yAlign:o}=this,{caretSize:r,cornerRadius:a}=n,{topLeft:l,topRight:c,bottomLeft:h,bottomRight:d}=qt(a),{x:u,y:f}=t,{width:g,height:p}=i;let m,b,_,v,x,y;return o==="center"?(x=f+p/2,s==="left"?(m=u,b=m-r,v=x+r,y=x-r):(m=u+g,b=m+r,v=x-r,y=x+r),_=m):(s==="left"?b=u+Math.max(l,h)+r:s==="right"?b=u+g-Math.max(c,d)-r:b=this.caretX,o==="top"?(v=f,x=v-r,m=b-r,_=b+r):(v=f+p,x=v+r,m=b+r,_=b-r),y=v),{x1:m,x2:b,x3:_,y1:v,y2:x,y3:y}}drawTitle(t,i,n){const s=this.title,o=s.length;let r,a,l;if(o){const c=oe(n.rtl,this.x,this.width);for(t.x=si(this,n.titleAlign,n),i.textAlign=c.textAlign(n.titleAlign),i.textBaseline="middle",r=X(n.titleFont),a=n.titleSpacing,i.fillStyle=n.titleColor,i.font=r.string,l=0;l<o;++l)i.fillText(s[l],c.x(t.x),t.y+r.lineHeight/2),t.y+=r.lineHeight+a,l+1===o&&(t.y+=n.titleMarginBottom-a)}}_drawColorBox(t,i,n,s,o){const r=this.labelColors[n],a=this.labelPointStyles[n],{boxHeight:l,boxWidth:c}=o,h=X(o.bodyFont),d=si(this,"left",o),u=s.x(d),f=l<h.lineHeight?(h.lineHeight-l)/2:0,g=i.y+f;if(o.usePointStyle){const p={radius:Math.min(c,l)/2,pointStyle:a.pointStyle,rotation:a.rotation,borderWidth:1},m=s.leftForLtr(u,c)+c/2,b=g+l/2;t.strokeStyle=o.multiKeyBackground,t.fillStyle=o.multiKeyBackground,un(t,p,m,b),t.strokeStyle=r.borderColor,t.fillStyle=r.backgroundColor,un(t,p,m,b)}else{t.lineWidth=L(r.borderWidth)?Math.max(...Object.values(r.borderWidth)):r.borderWidth||1,t.strokeStyle=r.borderColor,t.setLineDash(r.borderDash||[]),t.lineDashOffset=r.borderDashOffset||0;const p=s.leftForLtr(u,c),m=s.leftForLtr(s.xPlus(u,1),c-2),b=qt(r.borderRadius);Object.values(b).some(_=>_!==0)?(t.beginPath(),t.fillStyle=o.multiKeyBackground,ze(t,{x:p,y:g,w:c,h:l,radius:b}),t.fill(),t.stroke(),t.fillStyle=r.backgroundColor,t.beginPath(),ze(t,{x:m,y:g+1,w:c-2,h:l-2,radius:b}),t.fill()):(t.fillStyle=o.multiKeyBackground,t.fillRect(p,g,c,l),t.strokeRect(p,g,c,l),t.fillStyle=r.backgroundColor,t.fillRect(m,g+1,c-2,l-2))}t.fillStyle=this.labelTextColors[n]}drawBody(t,i,n){const{body:s}=this,{bodySpacing:o,bodyAlign:r,displayColors:a,boxHeight:l,boxWidth:c,boxPadding:h}=n,d=X(n.bodyFont);let u=d.lineHeight,f=0;const g=oe(n.rtl,this.x,this.width),p=function(P){i.fillText(P,g.x(t.x+f),t.y+u/2),t.y+=u+o},m=g.textAlign(r);let b,_,v,x,y,S,w;for(i.textAlign=r,i.textBaseline="middle",i.font=d.string,t.x=si(this,m,n),i.fillStyle=n.bodyColor,$(this.beforeBody,p),f=a&&m!=="right"?r==="center"?c/2+h:c+2+h:0,x=0,S=s.length;x<S;++x){for(b=s[x],_=this.labelTextColors[x],i.fillStyle=_,$(b.before,p),v=b.lines,a&&v.length&&(this._drawColorBox(i,t,x,g,n),u=Math.max(d.lineHeight,l)),y=0,w=v.length;y<w;++y)p(v[y]),u=d.lineHeight;$(b.after,p)}f=0,u=d.lineHeight,$(this.afterBody,p),t.y-=o}drawFooter(t,i,n){const s=this.footer,o=s.length;let r,a;if(o){const l=oe(n.rtl,this.x,this.width);for(t.x=si(this,n.footerAlign,n),t.y+=n.footerMarginTop,i.textAlign=l.textAlign(n.footerAlign),i.textBaseline="middle",r=X(n.footerFont),i.fillStyle=n.footerColor,i.font=r.string,a=0;a<o;++a)i.fillText(s[a],l.x(t.x),t.y+r.lineHeight/2),t.y+=r.lineHeight+n.footerSpacing}}drawBackground(t,i,n,s){const{xAlign:o,yAlign:r}=this,{x:a,y:l}=t,{width:c,height:h}=n,{topLeft:d,topRight:u,bottomLeft:f,bottomRight:g}=qt(s.cornerRadius);i.fillStyle=s.backgroundColor,i.strokeStyle=s.borderColor,i.lineWidth=s.borderWidth,i.beginPath(),i.moveTo(a+d,l),r==="top"&&this.drawCaret(t,i,n,s),i.lineTo(a+c-u,l),i.quadraticCurveTo(a+c,l,a+c,l+u),r==="center"&&o==="right"&&this.drawCaret(t,i,n,s),i.lineTo(a+c,l+h-g),i.quadraticCurveTo(a+c,l+h,a+c-g,l+h),r==="bottom"&&this.drawCaret(t,i,n,s),i.lineTo(a+f,l+h),i.quadraticCurveTo(a,l+h,a,l+h-f),r==="center"&&o==="left"&&this.drawCaret(t,i,n,s),i.lineTo(a,l+d),i.quadraticCurveTo(a,l,a+d,l),i.closePath(),i.fill(),s.borderWidth>0&&i.stroke()}_updateAnimationTarget(t){const i=this.chart,n=this.$animations,s=n&&n.x,o=n&&n.y;if(s||o){const r=Oe[t.position].call(this,this._active,this._eventPosition);if(!r)return;const a=this._size=go(this,t),l=Object.assign({},r,this._size),c=po(i,t,l),h=mo(t,l,c,i);(s._to!==h.x||o._to!==h.y)&&(this.xAlign=c.xAlign,this.yAlign=c.yAlign,this.width=a.width,this.height=a.height,this.caretX=r.x,this.caretY=r.y,this._resolveAnimations().update(this,h))}}_willRender(){return!!this.opacity}draw(t){const i=this.options.setContext(this.getContext());let n=this.opacity;if(!n)return;this._updateAnimationTarget(i);const s={width:this.width,height:this.height},o={x:this.x,y:this.y};n=Math.abs(n)<.001?0:n;const r=Q(i.padding),a=this.title.length||this.beforeBody.length||this.body.length||this.afterBody.length||this.footer.length;i.enabled&&a&&(t.save(),t.globalAlpha=n,this.drawBackground(o,t,s,i),Wr(t,i.textDirection),o.y+=r.top,this.drawTitle(o,t,i),this.drawBody(o,t,i),this.drawFooter(o,t,i),Vr(t,i.textDirection),t.restore())}getActiveElements(){return this._active||[]}setActiveElements(t,i){const n=this._active,s=t.map(({datasetIndex:a,index:l})=>{const c=this.chart.getDatasetMeta(a);if(!c)throw new Error("Cannot find a dataset at index "+a);return{datasetIndex:a,element:c.data[l],index:l}}),o=!bi(n,s),r=this._positionChanged(s,i);(o||r)&&(this._active=s,this._eventPosition=i,this._ignoreReplayEvents=!0,this.update(!0))}handleEvent(t,i,n=!0){if(i&&this._ignoreReplayEvents)return!1;this._ignoreReplayEvents=!1;const s=this.options,o=this._active||[],r=this._getActiveElements(t,o,i,n),a=this._positionChanged(r,t),l=i||!bi(r,o)||a;return l&&(this._active=r,(s.enabled||s.external)&&(this._eventPosition={x:t.x,y:t.y},this.update(!0,i))),l}_getActiveElements(t,i,n,s){const o=this.options;if(t.type==="mouseout")return[];if(!s)return i.filter(a=>this.chart.data.datasets[a.datasetIndex]&&this.chart.getDatasetMeta(a.datasetIndex).controller.getParsed(a.index)!==void 0);const r=this.chart.getElementsAtEventForMode(t,o.mode,o,n);return o.reverse&&r.reverse(),r}_positionChanged(t,i){const{caretX:n,caretY:s,options:o}=this,r=Oe[o.position].call(this,t,i);return r!==!1&&(n!==r.x||s!==r.y)}}M(yn,"positioners",Oe);var Tu={id:"tooltip",_element:yn,positioners:Oe,afterInit(e,t,i){i&&(e.tooltip=new yn({chart:e,options:i}))},beforeUpdate(e,t,i){e.tooltip&&e.tooltip.initialize(i)},reset(e,t,i){e.tooltip&&e.tooltip.initialize(i)},afterDraw(e){const t=e.tooltip;if(t&&t._willRender()){const i={tooltip:t};if(e.notifyPlugins("beforeTooltipDraw",{...i,cancelable:!0})===!1)return;t.draw(e.ctx),e.notifyPlugins("afterTooltipDraw",i)}},afterEvent(e,t){if(e.tooltip){const i=t.replay;e.tooltip.handleEvent(t.event,i,t.inChartArea)&&(t.changed=!0)}},defaults:{enabled:!0,external:null,position:"average",backgroundColor:"rgba(0,0,0,0.8)",titleColor:"#fff",titleFont:{weight:"bold"},titleSpacing:2,titleMarginBottom:6,titleAlign:"left",bodyColor:"#fff",bodySpacing:2,bodyFont:{},bodyAlign:"left",footerColor:"#fff",footerSpacing:2,footerMarginTop:6,footerFont:{weight:"bold"},footerAlign:"left",padding:6,caretPadding:2,caretSize:5,cornerRadius:6,boxHeight:(e,t)=>t.bodyFont.size,boxWidth:(e,t)=>t.bodyFont.size,multiKeyBackground:"#fff",displayColors:!0,boxPadding:0,borderColor:"rgba(0,0,0,0)",borderWidth:0,animation:{duration:400,easing:"easeOutQuart"},animations:{numbers:{type:"number",properties:["x","y","width","height","caretX","caretY"]},opacity:{easing:"linear",duration:200}},callbacks:pa},defaultRoutes:{bodyFont:"font",footerFont:"font",titleFont:"font"},descriptors:{_scriptable:e=>e!=="filter"&&e!=="itemSort"&&e!=="external",_indexable:!1,callbacks:{_scriptable:!1,_indexable:!1},animation:{_fallback:!1},animations:{_fallback:"animation"}},additionalOptionScopes:["interaction"]},Du=Object.freeze({__proto__:null,Colors:Wd,Decimation:Gd,Filler:fu,Legend:yu,SubTitle:Mu,Title:vu,Tooltip:Tu});const Lu=(e,t,i,n)=>(typeof t=="string"?(i=e.push(t)-1,n.unshift({index:i,label:t})):isNaN(t)&&(i=null),i);function Eu(e,t,i,n){const s=e.indexOf(t);if(s===-1)return Lu(e,t,i,n);const o=e.lastIndexOf(t);return s!==o?i:s}const $u=(e,t)=>e===null?null:K(Math.round(e),0,t);function yo(e){const t=this.getLabels();return e>=0&&e<t.length?t[e]:e}class xn extends te{constructor(t){super(t),this._startValue=void 0,this._valueRange=0,this._addedLabels=[]}init(t){const i=this._addedLabels;if(i.length){const n=this.getLabels();for(const{index:s,label:o}of i)n[s]===o&&n.splice(s,1);this._addedLabels=[]}super.init(t)}parse(t,i){if(D(t))return null;const n=this.getLabels();return i=isFinite(i)&&n[i]===t?i:Eu(n,t,C(i,t),this._addedLabels),$u(i,n.length-1)}determineDataLimits(){const{minDefined:t,maxDefined:i}=this.getUserBounds();let{min:n,max:s}=this.getMinMax(!0);this.options.bounds==="ticks"&&(t||(n=0),i||(s=this.getLabels().length-1)),this.min=n,this.max=s}buildTicks(){const t=this.min,i=this.max,n=this.options.offset,s=[];let o=this.getLabels();o=t===0&&i===o.length-1?o:o.slice(t,i+1),this._valueRange=Math.max(o.length-(n?0:1),1),this._startValue=this.min-(n?.5:0);for(let r=t;r<=i;r++)s.push({value:r});return s}getLabelForValue(t){return yo.call(this,t)}configure(){super.configure(),this.isHorizontal()||(this._reversePixels=!this._reversePixels)}getPixelForValue(t){return typeof t!="number"&&(t=this.parse(t)),t===null?NaN:this.getPixelForDecimal((t-this._startValue)/this._valueRange)}getPixelForTick(t){const i=this.ticks;return t<0||t>i.length-1?null:this.getPixelForValue(i[t].value)}getValueForPixel(t){return Math.round(this._startValue+this.getDecimalForPixel(t)*this._valueRange)}getBasePixel(){return this.bottom}}M(xn,"id","category"),M(xn,"defaults",{ticks:{callback:yo}});function Ru(e,t){const i=[],{bounds:s,step:o,min:r,max:a,precision:l,count:c,maxTicks:h,maxDigits:d,includeBounds:u}=e,f=o||1,g=h-1,{min:p,max:m}=t,b=!D(r),_=!D(a),v=!D(c),x=(m-p)/(d+1);let y=fs((m-p)/g/f)*f,S,w,P,O;if(y<1e-14&&!b&&!_)return[{value:p},{value:m}];O=Math.ceil(m/y)-Math.floor(p/y),O>g&&(y=fs(O*y/g/f)*f),D(l)||(S=Math.pow(10,l),y=Math.ceil(y*S)/S),s==="ticks"?(w=Math.floor(p/y)*y,P=Math.ceil(m/y)*y):(w=p,P=m),b&&_&&o&&kl((a-r)/o,y/1e3)?(O=Math.round(Math.min((a-r)/y,h)),y=(a-r)/O,w=r,P=a):v?(w=b?r:w,P=_?a:P,O=c-1,y=(P-w)/O):(O=(P-w)/y,Te(O,Math.round(O),y/1e3)?O=Math.round(O):O=Math.ceil(O));const A=Math.max(gs(y),gs(w));S=Math.pow(10,D(l)?A:l),w=Math.round(w*S)/S,P=Math.round(P*S)/S;let k=0;for(b&&(u&&w!==r?(i.push({value:r}),w<r&&k++,Te(Math.round((w+k*y)*S)/S,r,xo(r,x,e))&&k++):w<r&&k++);k<O;++k){const T=Math.round((w+k*y)*S)/S;if(_&&T>a)break;i.push({value:T})}return _&&u&&P!==a?i.length&&Te(i[i.length-1].value,a,xo(a,x,e))?i[i.length-1].value=a:i.push({value:a}):(!_||P===a)&&i.push({value:P}),i}function xo(e,t,{horizontal:i,minRotation:n}){const s=ht(n),o=(i?Math.sin(s):Math.cos(s))||.001,r=.75*t*(""+e).length;return Math.min(t/o,r)}class wi extends te{constructor(t){super(t),this.start=void 0,this.end=void 0,this._startValue=void 0,this._endValue=void 0,this._valueRange=0}parse(t,i){return D(t)||(typeof t=="number"||t instanceof Number)&&!isFinite(+t)?null:+t}handleTickRangeOptions(){const{beginAtZero:t}=this.options,{minDefined:i,maxDefined:n}=this.getUserBounds();let{min:s,max:o}=this;const r=l=>s=i?s:l,a=l=>o=n?o:l;if(t){const l=mt(s),c=mt(o);l<0&&c<0?a(0):l>0&&c>0&&r(0)}if(s===o){let l=o===0?1:Math.abs(o*.05);a(o+l),t||r(s-l)}this.min=s,this.max=o}getTickLimit(){const t=this.options.ticks;let{maxTicksLimit:i,stepSize:n}=t,s;return n?(s=Math.ceil(this.max/n)-Math.floor(this.min/n)+1,s>1e3&&(console.warn(`scales.${this.id}.ticks.stepSize: ${n} would result generating up to ${s} ticks. Limiting to 1000.`),s=1e3)):(s=this.computeTickLimit(),i=i||11),i&&(s=Math.min(i,s)),s}computeTickLimit(){return Number.POSITIVE_INFINITY}buildTicks(){const t=this.options,i=t.ticks;let n=this.getTickLimit();n=Math.max(2,n);const s={maxTicks:n,bounds:t.bounds,min:t.min,max:t.max,precision:i.precision,step:i.stepSize,count:i.count,maxDigits:this._maxDigits(),horizontal:this.isHorizontal(),minRotation:i.minRotation||0,includeBounds:i.includeBounds!==!1},o=this._range||this,r=Ru(s,o);return t.bounds==="ticks"&&Or(r,this,"value"),t.reverse?(r.reverse(),this.start=this.max,this.end=this.min):(this.start=this.min,this.end=this.max),r}configure(){const t=this.ticks;let i=this.min,n=this.max;if(super.configure(),this.options.offset&&t.length){const s=(n-i)/Math.max(t.length-1,1)/2;i-=s,n+=s}this._startValue=i,this._endValue=n,this._valueRange=n-i}getLabelForValue(t){return We(t,this.chart.options.locale,this.options.ticks.format)}}class vn extends wi{determineDataLimits(){const{min:t,max:i}=this.getMinMax(!0);this.min=V(t)?t:0,this.max=V(i)?i:1,this.handleTickRangeOptions()}computeTickLimit(){const t=this.isHorizontal(),i=t?this.width:this.height,n=ht(this.options.ticks.minRotation),s=(t?Math.sin(n):Math.cos(n))||.001,o=this._resolveTickFontOptions(0);return Math.ceil(i/Math.min(40,o.lineHeight/s))}getPixelForValue(t){return t===null?NaN:this.getPixelForDecimal((t-this._startValue)/this._valueRange)}getValueForPixel(t){return this._startValue+this.getDecimalForPixel(t)*this._valueRange}}M(vn,"id","linear"),M(vn,"defaults",{ticks:{callback:Ci.formatters.numeric}});const Ne=e=>Math.floor(Et(e)),Yt=(e,t)=>Math.pow(10,Ne(e)+t);function vo(e){return e/Math.pow(10,Ne(e))===1}function Mo(e,t,i){const n=Math.pow(10,i),s=Math.floor(e/n);return Math.ceil(t/n)-s}function Iu(e,t){const i=t-e;let n=Ne(i);for(;Mo(e,t,n)>10;)n++;for(;Mo(e,t,n)<10;)n--;return Math.min(n,Ne(e))}function Fu(e,{min:t,max:i}){t=at(e.min,t);const n=[],s=Ne(t);let o=Iu(t,i),r=o<0?Math.pow(10,Math.abs(o)):1;const a=Math.pow(10,o),l=s>o?Math.pow(10,s):0,c=Math.round((t-l)*r)/r,h=Math.floor((t-l)/a/10)*a*10;let d=Math.floor((c-h)/Math.pow(10,o)),u=at(e.min,Math.round((l+h+d*Math.pow(10,o))*r)/r);for(;u<i;)n.push({value:u,major:vo(u),significand:d}),d>=10?d=d<15?15:20:d++,d>=20&&(o++,d=2,r=o>=0?1:r),u=Math.round((l+h+d*Math.pow(10,o))*r)/r;const f=at(e.max,u);return n.push({value:f,major:vo(f),significand:d}),n}class Mn extends te{constructor(t){super(t),this.start=void 0,this.end=void 0,this._startValue=void 0,this._valueRange=0}parse(t,i){const n=wi.prototype.parse.apply(this,[t,i]);if(n===0){this._zero=!0;return}return V(n)&&n>0?n:null}determineDataLimits(){const{min:t,max:i}=this.getMinMax(!0);this.min=V(t)?Math.max(0,t):null,this.max=V(i)?Math.max(0,i):null,this.options.beginAtZero&&(this._zero=!0),this._zero&&this.min!==this._suggestedMin&&!V(this._userMin)&&(this.min=t===Yt(this.min,0)?Yt(this.min,-1):Yt(this.min,0)),this.handleTickRangeOptions()}handleTickRangeOptions(){const{minDefined:t,maxDefined:i}=this.getUserBounds();let n=this.min,s=this.max;const o=a=>n=t?n:a,r=a=>s=i?s:a;n===s&&(n<=0?(o(1),r(10)):(o(Yt(n,-1)),r(Yt(s,1)))),n<=0&&o(Yt(s,-1)),s<=0&&r(Yt(n,1)),this.min=n,this.max=s}buildTicks(){const t=this.options,i={min:this._userMin,max:this._userMax},n=Fu(i,this);return t.bounds==="ticks"&&Or(n,this,"value"),t.reverse?(n.reverse(),this.start=this.max,this.end=this.min):(this.start=this.min,this.end=this.max),n}getLabelForValue(t){return t===void 0?"0":We(t,this.chart.options.locale,this.options.ticks.format)}configure(){const t=this.min;super.configure(),this._startValue=Et(t),this._valueRange=Et(this.max)-Et(t)}getPixelForValue(t){return(t===void 0||t===0)&&(t=this.min),t===null||isNaN(t)?NaN:this.getPixelForDecimal(t===this.min?0:(Et(t)-this._startValue)/this._valueRange)}getValueForPixel(t){const i=this.getDecimalForPixel(t);return Math.pow(10,this._startValue+i*this._valueRange)}}M(Mn,"id","logarithmic"),M(Mn,"defaults",{ticks:{callback:Ci.formatters.logarithmic,major:{enabled:!0}}});function Sn(e){const t=e.ticks;if(t.display&&e.display){const i=Q(t.backdropPadding);return C(t.font&&t.font.size,H.font.size)+i.height}return 0}function zu(e,t,i){return i=j(i)?i:[i],{w:Yl(e,t.string,i),h:i.length*t.lineHeight}}function So(e,t,i,n,s){return e===n||e===s?{start:t-i/2,end:t+i/2}:e<n||e>s?{start:t-i,end:t}:{start:t,end:t+i}}function Bu(e){const t={l:e.left+e._padding.left,r:e.right-e._padding.right,t:e.top+e._padding.top,b:e.bottom-e._padding.bottom},i=Object.assign({},t),n=[],s=[],o=e._pointLabels.length,r=e.options.pointLabels,a=r.centerPointLabels?B/o:0;for(let l=0;l<o;l++){const c=r.setContext(e.getPointLabelContext(l));s[l]=c.padding;const h=e.getPointPosition(l,e.drawingArea+s[l],a),d=X(c.font),u=zu(e.ctx,d,e._pointLabels[l]);n[l]=u;const f=lt(e.getIndexAngle(l)+a),g=Math.round($n(f)),p=So(g,h.x,u.w,0,180),m=So(g,h.y,u.h,90,270);Nu(i,t,f,p,m)}e.setCenterPoint(t.l-i.l,i.r-t.r,t.t-i.t,i.b-t.b),e._pointLabelItems=Wu(e,n,s)}function Nu(e,t,i,n,s){const o=Math.abs(Math.sin(i)),r=Math.abs(Math.cos(i));let a=0,l=0;n.start<t.l?(a=(t.l-n.start)/o,e.l=Math.min(e.l,t.l-a)):n.end>t.r&&(a=(n.end-t.r)/o,e.r=Math.max(e.r,t.r+a)),s.start<t.t?(l=(t.t-s.start)/r,e.t=Math.min(e.t,t.t-l)):s.end>t.b&&(l=(s.end-t.b)/r,e.b=Math.max(e.b,t.b+l))}function ju(e,t,i){const n=e.drawingArea,{extra:s,additionalAngle:o,padding:r,size:a}=i,l=e.getPointPosition(t,n+s+r,o),c=Math.round($n(lt(l.angle+Y))),h=Uu(l.y,a.h,c),d=Vu(c),u=Yu(l.x,a.w,d);return{visible:!0,x:l.x,y:h,textAlign:d,left:u,top:h,right:u+a.w,bottom:h+a.h}}function Hu(e,t){if(!t)return!0;const{left:i,top:n,right:s,bottom:o}=e;return!(Ot({x:i,y:n},t)||Ot({x:i,y:o},t)||Ot({x:s,y:n},t)||Ot({x:s,y:o},t))}function Wu(e,t,i){const n=[],s=e._pointLabels.length,o=e.options,{centerPointLabels:r,display:a}=o.pointLabels,l={extra:Sn(o)/2,additionalAngle:r?B/s:0};let c;for(let h=0;h<s;h++){l.padding=i[h],l.size=t[h];const d=ju(e,h,l);n.push(d),a==="auto"&&(d.visible=Hu(d,c),d.visible&&(c=d))}return n}function Vu(e){return e===0||e===180?"center":e<180?"left":"right"}function Yu(e,t,i){return i==="right"?e-=t:i==="center"&&(e-=t/2),e}function Uu(e,t,i){return i===90||i===270?e-=t/2:(i>270||i<90)&&(e-=t),e}function Gu(e,t,i){const{left:n,top:s,right:o,bottom:r}=i,{backdropColor:a}=t;if(!D(a)){const l=qt(t.borderRadius),c=Q(t.backdropPadding);e.fillStyle=a;const h=n-c.left,d=s-c.top,u=o-n+c.width,f=r-s+c.height;Object.values(l).some(g=>g!==0)?(e.beginPath(),ze(e,{x:h,y:d,w:u,h:f,radius:l}),e.fill()):e.fillRect(h,d,u,f)}}function Xu(e,t){const{ctx:i,options:{pointLabels:n}}=e;for(let s=t-1;s>=0;s--){const o=e._pointLabelItems[s];if(!o.visible)continue;const r=n.setContext(e.getPointLabelContext(s));Gu(i,r,o);const a=X(r.font),{x:l,y:c,textAlign:h}=o;Qt(i,e._pointLabels[s],l,c+a.lineHeight/2,a,{color:r.color,textAlign:h,textBaseline:"middle"})}}function ma(e,t,i,n){const{ctx:s}=e;if(i)s.arc(e.xCenter,e.yCenter,t,0,z);else{let o=e.getPointPosition(0,t);s.moveTo(o.x,o.y);for(let r=1;r<n;r++)o=e.getPointPosition(r,t),s.lineTo(o.x,o.y)}}function Ku(e,t,i,n,s){const o=e.ctx,r=t.circular,{color:a,lineWidth:l}=t;!r&&!n||!a||!l||i<0||(o.save(),o.strokeStyle=a,o.lineWidth=l,o.setLineDash(s.dash||[]),o.lineDashOffset=s.dashOffset,o.beginPath(),ma(e,i,r,n),o.closePath(),o.stroke(),o.restore())}function qu(e,t,i){return Bt(e,{label:i,index:t,type:"pointLabel"})}class Ae extends wi{constructor(t){super(t),this.xCenter=void 0,this.yCenter=void 0,this.drawingArea=void 0,this._pointLabels=[],this._pointLabelItems=[]}setDimensions(){const t=this._padding=Q(Sn(this.options)/2),i=this.width=this.maxWidth-t.width,n=this.height=this.maxHeight-t.height;this.xCenter=Math.floor(this.left+i/2+t.left),this.yCenter=Math.floor(this.top+n/2+t.top),this.drawingArea=Math.floor(Math.min(i,n)/2)}determineDataLimits(){const{min:t,max:i}=this.getMinMax(!1);this.min=V(t)&&!isNaN(t)?t:0,this.max=V(i)&&!isNaN(i)?i:0,this.handleTickRangeOptions()}computeTickLimit(){return Math.ceil(this.drawingArea/Sn(this.options))}generateTickLabels(t){wi.prototype.generateTickLabels.call(this,t),this._pointLabels=this.getLabels().map((i,n)=>{const s=I(this.options.pointLabels.callback,[i,n],this);return s||s===0?s:""}).filter((i,n)=>this.chart.getDataVisibility(n))}fit(){const t=this.options;t.display&&t.pointLabels.display?Bu(this):this.setCenterPoint(0,0,0,0)}setCenterPoint(t,i,n,s){this.xCenter+=Math.floor((t-i)/2),this.yCenter+=Math.floor((n-s)/2),this.drawingArea-=Math.min(this.drawingArea/2,Math.max(t,i,n,s))}getIndexAngle(t){const i=z/(this._pointLabels.length||1),n=this.options.startAngle||0;return lt(t*i+ht(n))}getDistanceFromCenterForValue(t){if(D(t))return NaN;const i=this.drawingArea/(this.max-this.min);return this.options.reverse?(this.max-t)*i:(t-this.min)*i}getValueForDistanceFromCenter(t){if(D(t))return NaN;const i=t/(this.drawingArea/(this.max-this.min));return this.options.reverse?this.max-i:this.min+i}getPointLabelContext(t){const i=this._pointLabels||[];if(t>=0&&t<i.length){const n=i[t];return qu(this.getContext(),t,n)}}getPointPosition(t,i,n=0){const s=this.getIndexAngle(t)-Y+n;return{x:Math.cos(s)*i+this.xCenter,y:Math.sin(s)*i+this.yCenter,angle:s}}getPointPositionForValue(t,i){return this.getPointPosition(t,this.getDistanceFromCenterForValue(i))}getBasePosition(t){return this.getPointPositionForValue(t||0,this.getBaseValue())}getPointLabelPosition(t){const{left:i,top:n,right:s,bottom:o}=this._pointLabelItems[t];return{left:i,top:n,right:s,bottom:o}}drawBackground(){const{backgroundColor:t,grid:{circular:i}}=this.options;if(t){const n=this.ctx;n.save(),n.beginPath(),ma(this,this.getDistanceFromCenterForValue(this._endValue),i,this._pointLabels.length),n.closePath(),n.fillStyle=t,n.fill(),n.restore()}}drawGrid(){const t=this.ctx,i=this.options,{angleLines:n,grid:s,border:o}=i,r=this._pointLabels.length;let a,l,c;if(i.pointLabels.display&&Xu(this,r),s.display&&this.ticks.forEach((h,d)=>{if(d!==0||d===0&&this.min<0){l=this.getDistanceFromCenterForValue(h.value);const u=this.getContext(d),f=s.setContext(u),g=o.setContext(u);Ku(this,f,l,r,g)}}),n.display){for(t.save(),a=r-1;a>=0;a--){const h=n.setContext(this.getPointLabelContext(a)),{color:d,lineWidth:u}=h;!u||!d||(t.lineWidth=u,t.strokeStyle=d,t.setLineDash(h.borderDash),t.lineDashOffset=h.borderDashOffset,l=this.getDistanceFromCenterForValue(i.reverse?this.min:this.max),c=this.getPointPosition(a,l),t.beginPath(),t.moveTo(this.xCenter,this.yCenter),t.lineTo(c.x,c.y),t.stroke())}t.restore()}}drawBorder(){}drawLabels(){const t=this.ctx,i=this.options,n=i.ticks;if(!n.display)return;const s=this.getIndexAngle(0);let o,r;t.save(),t.translate(this.xCenter,this.yCenter),t.rotate(s),t.textAlign="center",t.textBaseline="middle",this.ticks.forEach((a,l)=>{if(l===0&&this.min>=0&&!i.reverse)return;const c=n.setContext(this.getContext(l)),h=X(c.font);if(o=this.getDistanceFromCenterForValue(this.ticks[l].value),c.showLabelBackdrop){t.font=h.string,r=t.measureText(a.label).width,t.fillStyle=c.backdropColor;const d=Q(c.backdropPadding);t.fillRect(-r/2-d.left,-o-h.size/2-d.top,r+d.width,h.size+d.height)}Qt(t,a.label,0,-o,h,{color:c.color,strokeColor:c.textStrokeColor,strokeWidth:c.textStrokeWidth})}),t.restore()}drawTitle(){}}M(Ae,"id","radialLinear"),M(Ae,"defaults",{display:!0,animate:!0,position:"chartArea",angleLines:{display:!0,lineWidth:1,borderDash:[],borderDashOffset:0},grid:{circular:!1},startAngle:0,ticks:{showLabelBackdrop:!0,callback:Ci.formatters.numeric},pointLabels:{backdropColor:void 0,backdropPadding:2,display:!0,font:{size:10},callback(t){return t},padding:5,centerPointLabels:!1}}),M(Ae,"defaultRoutes",{"angleLines.color":"borderColor","pointLabels.color":"color","ticks.color":"color"}),M(Ae,"descriptors",{angleLines:{_fallback:"grid"}});const $i={millisecond:{common:!0,size:1,steps:1e3},second:{common:!0,size:1e3,steps:60},minute:{common:!0,size:6e4,steps:60},hour:{common:!0,size:36e5,steps:24},day:{common:!0,size:864e5,steps:30},week:{common:!1,size:6048e5,steps:4},month:{common:!0,size:2628e6,steps:12},quarter:{common:!1,size:7884e6,steps:4},year:{common:!0,size:3154e7}},ot=Object.keys($i);function wo(e,t){return e-t}function Po(e,t){if(D(t))return null;const i=e._adapter,{parser:n,round:s,isoWeekday:o}=e._parseOpts;let r=t;return typeof n=="function"&&(r=n(r)),V(r)||(r=typeof n=="string"?i.parse(r,n):i.parse(r)),r===null?null:(s&&(r=s==="week"&&(re(o)||o===!0)?i.startOf(r,"isoWeek",o):i.startOf(r,s)),+r)}function Oo(e,t,i,n){const s=ot.length;for(let o=ot.indexOf(e);o<s-1;++o){const r=$i[ot[o]],a=r.steps?r.steps:Number.MAX_SAFE_INTEGER;if(r.common&&Math.ceil((i-t)/(a*r.size))<=n)return ot[o]}return ot[s-1]}function Zu(e,t,i,n,s){for(let o=ot.length-1;o>=ot.indexOf(i);o--){const r=ot[o];if($i[r].common&&e._adapter.diff(s,n,r)>=t-1)return r}return ot[i?ot.indexOf(i):0]}function Ju(e){for(let t=ot.indexOf(e)+1,i=ot.length;t<i;++t)if($i[ot[t]].common)return ot[t]}function Ao(e,t,i){if(!i)e[t]=!0;else if(i.length){const{lo:n,hi:s}=Rn(i,t),o=i[n]>=t?i[n]:i[s];e[o]=!0}}function Qu(e,t,i,n){const s=e._adapter,o=+s.startOf(t[0].value,n),r=t[t.length-1].value;let a,l;for(a=o;a<=r;a=+s.add(a,1,n))l=i[a],l>=0&&(t[l].major=!0);return t}function ko(e,t,i){const n=[],s={},o=t.length;let r,a;for(r=0;r<o;++r)a=t[r],s[a]=r,n.push({value:a,major:!1});return o===0||!i?n:Qu(e,n,s,i)}class je extends te{constructor(t){super(t),this._cache={data:[],labels:[],all:[]},this._unit="day",this._majorUnit=void 0,this._offsets={},this._normalized=!1,this._parseOpts=void 0}init(t,i={}){const n=t.time||(t.time={}),s=this._adapter=new ah._date(t.adapters.date);s.init(i),Ce(n.displayFormats,s.formats()),this._parseOpts={parser:n.parser,round:n.round,isoWeekday:n.isoWeekday},super.init(t),this._normalized=i.normalized}parse(t,i){return t===void 0?null:Po(this,t)}beforeLayout(){super.beforeLayout(),this._cache={data:[],labels:[],all:[]}}determineDataLimits(){const t=this.options,i=this._adapter,n=t.time.unit||"day";let{min:s,max:o,minDefined:r,maxDefined:a}=this.getUserBounds();function l(c){!r&&!isNaN(c.min)&&(s=Math.min(s,c.min)),!a&&!isNaN(c.max)&&(o=Math.max(o,c.max))}(!r||!a)&&(l(this._getLabelBounds()),(t.bounds!=="ticks"||t.ticks.source!=="labels")&&l(this.getMinMax(!1))),s=V(s)&&!isNaN(s)?s:+i.startOf(Date.now(),n),o=V(o)&&!isNaN(o)?o:+i.endOf(Date.now(),n)+1,this.min=Math.min(s,o-1),this.max=Math.max(s+1,o)}_getLabelBounds(){const t=this.getLabelTimestamps();let i=Number.POSITIVE_INFINITY,n=Number.NEGATIVE_INFINITY;return t.length&&(i=t[0],n=t[t.length-1]),{min:i,max:n}}buildTicks(){const t=this.options,i=t.time,n=t.ticks,s=n.source==="labels"?this.getLabelTimestamps():this._generate();t.bounds==="ticks"&&s.length&&(this.min=this._userMin||s[0],this.max=this._userMax||s[s.length-1]);const o=this.min,r=this.max,a=Ll(s,o,r);return this._unit=i.unit||(n.autoSkip?Oo(i.minUnit,this.min,this.max,this._getLabelCapacity(o)):Zu(this,a.length,i.minUnit,this.min,this.max)),this._majorUnit=!n.major.enabled||this._unit==="year"?void 0:Ju(this._unit),this.initOffsets(s),t.reverse&&a.reverse(),ko(this,a,this._majorUnit)}afterAutoSkip(){this.options.offsetAfterAutoskip&&this.initOffsets(this.ticks.map(t=>+t.value))}initOffsets(t=[]){let i=0,n=0,s,o;this.options.offset&&t.length&&(s=this.getDecimalForValue(t[0]),t.length===1?i=1-s:i=(this.getDecimalForValue(t[1])-s)/2,o=this.getDecimalForValue(t[t.length-1]),t.length===1?n=o:n=(o-this.getDecimalForValue(t[t.length-2]))/2);const r=t.length<3?.5:.25;i=K(i,0,r),n=K(n,0,r),this._offsets={start:i,end:n,factor:1/(i+1+n)}}_generate(){const t=this._adapter,i=this.min,n=this.max,s=this.options,o=s.time,r=o.unit||Oo(o.minUnit,i,n,this._getLabelCapacity(i)),a=C(s.ticks.stepSize,1),l=r==="week"?o.isoWeekday:!1,c=re(l)||l===!0,h={};let d=i,u,f;if(c&&(d=+t.startOf(d,"isoWeek",l)),d=+t.startOf(d,c?"day":r),t.diff(n,i,r)>1e5*a)throw new Error(i+" and "+n+" are too far apart with stepSize of "+a+" "+r);const g=s.ticks.source==="data"&&this.getDataTimestamps();for(u=d,f=0;u<n;u=+t.add(u,a,r),f++)Ao(h,u,g);return(u===n||s.bounds==="ticks"||f===1)&&Ao(h,u,g),Object.keys(h).sort(wo).map(p=>+p)}getLabelForValue(t){const i=this._adapter,n=this.options.time;return n.tooltipFormat?i.format(t,n.tooltipFormat):i.format(t,n.displayFormats.datetime)}format(t,i){const s=this.options.time.displayFormats,o=this._unit,r=i||s[o];return this._adapter.format(t,r)}_tickFormatFunction(t,i,n,s){const o=this.options,r=o.ticks.callback;if(r)return I(r,[t,i,n],this);const a=o.time.displayFormats,l=this._unit,c=this._majorUnit,h=l&&a[l],d=c&&a[c],u=n[i],f=c&&d&&u&&u.major;return this._adapter.format(t,s||(f?d:h))}generateTickLabels(t){let i,n,s;for(i=0,n=t.length;i<n;++i)s=t[i],s.label=this._tickFormatFunction(s.value,i,t)}getDecimalForValue(t){return t===null?NaN:(t-this.min)/(this.max-this.min)}getPixelForValue(t){const i=this._offsets,n=this.getDecimalForValue(t);return this.getPixelForDecimal((i.start+n)*i.factor)}getValueForPixel(t){const i=this._offsets,n=this.getDecimalForPixel(t)/i.factor-i.end;return this.min+n*(this.max-this.min)}_getLabelSize(t){const i=this.options.ticks,n=this.ctx.measureText(t).width,s=ht(this.isHorizontal()?i.maxRotation:i.minRotation),o=Math.cos(s),r=Math.sin(s),a=this._resolveTickFontOptions(0).size;return{w:n*o+a*r,h:n*r+a*o}}_getLabelCapacity(t){const i=this.options.time,n=i.displayFormats,s=n[i.unit]||n.millisecond,o=this._tickFormatFunction(t,0,ko(this,[t],this._majorUnit),s),r=this._getLabelSize(o),a=Math.floor(this.isHorizontal()?this.width/r.w:this.height/r.h)-1;return a>0?a:1}getDataTimestamps(){let t=this._cache.data||[],i,n;if(t.length)return t;const s=this.getMatchingVisibleMetas();if(this._normalized&&s.length)return this._cache.data=s[0].controller.getAllParsedValues(this);for(i=0,n=s.length;i<n;++i)t=t.concat(s[i].controller.getAllParsedValues(this));return this._cache.data=this.normalize(t)}getLabelTimestamps(){const t=this._cache.labels||[];let i,n;if(t.length)return t;const s=this.getLabels();for(i=0,n=s.length;i<n;++i)t.push(Po(this,s[i]));return this._cache.labels=this._normalized?t:this.normalize(t)}normalize(t){return Cr(t.sort(wo))}}M(je,"id","time"),M(je,"defaults",{bounds:"data",adapters:{},time:{parser:!1,unit:!1,round:!1,isoWeekday:!1,minUnit:"millisecond",displayFormats:{}},ticks:{source:"auto",callback:!1,major:{enabled:!1}}});function oi(e,t,i){let n=0,s=e.length-1,o,r,a,l;i?(t>=e[n].pos&&t<=e[s].pos&&({lo:n,hi:s}=Pt(e,"pos",t)),{pos:o,time:a}=e[n],{pos:r,time:l}=e[s]):(t>=e[n].time&&t<=e[s].time&&({lo:n,hi:s}=Pt(e,"time",t)),{time:o,pos:a}=e[n],{time:r,pos:l}=e[s]);const c=r-o;return c?a+(l-a)*(t-o)/c:a}class wn extends je{constructor(t){super(t),this._table=[],this._minPos=void 0,this._tableRange=void 0}initOffsets(){const t=this._getTimestampsForTable(),i=this._table=this.buildLookupTable(t);this._minPos=oi(i,this.min),this._tableRange=oi(i,this.max)-this._minPos,super.initOffsets(t)}buildLookupTable(t){const{min:i,max:n}=this,s=[],o=[];let r,a,l,c,h;for(r=0,a=t.length;r<a;++r)c=t[r],c>=i&&c<=n&&s.push(c);if(s.length<2)return[{time:i,pos:0},{time:n,pos:1}];for(r=0,a=s.length;r<a;++r)h=s[r+1],l=s[r-1],c=s[r],Math.round((h+l)/2)!==c&&o.push({time:c,pos:r/(a-1)});return o}_generate(){const t=this.min,i=this.max;let n=super.getDataTimestamps();return(!n.includes(t)||!n.length)&&n.splice(0,0,t),(!n.includes(i)||n.length===1)&&n.push(i),n.sort((s,o)=>s-o)}_getTimestampsForTable(){let t=this._cache.all||[];if(t.length)return t;const i=this.getDataTimestamps(),n=this.getLabelTimestamps();return i.length&&n.length?t=this.normalize(i.concat(n)):t=i.length?i:n,t=this._cache.all=t,t}getDecimalForValue(t){return(oi(this._table,t)-this._minPos)/this._tableRange}getValueForPixel(t){const i=this._offsets,n=this.getDecimalForPixel(t)/i.factor-i.end;return oi(this._table,n*this._tableRange+this._minPos,!0)}}M(wn,"id","timeseries"),M(wn,"defaults",je.defaults);var tf=Object.freeze({__proto__:null,CategoryScale:xn,LinearScale:vn,LogarithmicScale:Mn,RadialLinearScale:Ae,TimeScale:je,TimeSeriesScale:wn});const ef=[rh,Id,Du,tf];/*!
  * CoreUI v4.1.0 (https://coreui.io)
  * Copyright 2025 [object Object]
  * Licensed under MIT (https://github.com/coreui/coreui-chartjs/blob/main/LICENSE)
  */const ke={TOOLTIP:"chartjs-tooltip",TOOLTIP_BODY:"chartjs-tooltip-body",TOOLTIP_BODY_ITEM:"chartjs-tooltip-body-item",TOOLTIP_HEADER:"chartjs-tooltip-header",TOOLTIP_HEADER_ITEM:"chartjs-tooltip-header-item"},nf=e=>{let t=e.canvas.parentNode.querySelector("div");if(!t){t=document.createElement("div"),t.classList.add(ke.TOOLTIP);const i=document.createElement("table");i.style.margin="0px",t.append(i),e.canvas.parentNode.append(t)}return t},sf=e=>{const{chart:t,tooltip:i}=e,n=nf(t);if(i.opacity===0){n.style.opacity=0;return}if(i.body){const r=i.title||[],a=i.body.map(d=>d.lines),l=document.createElement("thead");l.classList.add(ke.TOOLTIP_HEADER);for(const d of r){const u=document.createElement("tr");u.style.borderWidth=0,u.classList.add(ke.TOOLTIP_HEADER_ITEM);const f=document.createElement("th");f.style.borderWidth=0;const g=document.createTextNode(d);f.append(g),u.append(f),l.append(u)}const c=document.createElement("tbody");c.classList.add(ke.TOOLTIP_BODY);for(const[d,u]of a.entries()){const f=i.labelColors[d],g=document.createElement("span");g.style.background=f.backgroundColor,g.style.borderColor=f.borderColor,g.style.borderWidth="2px",g.style.marginRight="10px",g.style.height="10px",g.style.width="10px",g.style.display="inline-block";const p=document.createElement("tr");p.classList.add(ke.TOOLTIP_BODY_ITEM);const m=document.createElement("td");m.style.borderWidth=0;const b=document.createTextNode(u);m.append(g),m.append(b),p.append(m),c.append(p)}const h=n.querySelector("table");for(;h.firstChild;)h.firstChild.remove();h.append(l),h.append(c)}const{offsetLeft:s,offsetTop:o}=t.canvas;n.style.opacity=1,n.style.left=`${s+i.caretX}px`,n.style.top=`${o+i.caretY}px`,n.style.font=i.options.bodyFont.string,n.style.padding=`${i.padding}px ${i.padding}px`};var Z=function(){return Z=Object.assign||function(t){for(var i,n=1,s=arguments.length;n<s;n++){i=arguments[n];for(var o in i)Object.prototype.hasOwnProperty.call(i,o)&&(t[o]=i[o])}return t},Z.apply(this,arguments)};function Co(e,t){var i={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(i[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var s=0,n=Object.getOwnPropertySymbols(e);s<n.length;s++)t.indexOf(n[s])<0&&Object.prototype.propertyIsEnumerable.call(e,n[s])&&(i[n[s]]=e[n[s]]);return i}var ri=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function Ve(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var ba={exports:{}},sn,To;function of(){if(To)return sn;To=1;var e="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED";return sn=e,sn}var on,Do;function rf(){if(Do)return on;Do=1;var e=of();function t(){}function i(){}return i.resetWarningCache=t,on=function(){function n(r,a,l,c,h,d){if(d!==e){var u=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw u.name="Invariant Violation",u}}n.isRequired=n;function s(){return n}var o={array:n,bigint:n,bool:n,func:n,number:n,object:n,string:n,symbol:n,any:n,arrayOf:s,element:n,elementType:n,instanceOf:s,node:n,objectOf:s,oneOf:s,oneOfType:s,shape:s,exact:s,checkPropTypes:i,resetWarningCache:t};return o.PropTypes=o,o},on}ba.exports=rf()();var af=ba.exports,it=Ve(af),_a={exports:{}};/*!
	Copyright (c) 2018 Jed Watson.
	Licensed under the MIT License (MIT), see
	http://jedwatson.github.io/classnames
*/(function(e){(function(){var t={}.hasOwnProperty;function i(){for(var o="",r=0;r<arguments.length;r++){var a=arguments[r];a&&(o=s(o,n(a)))}return o}function n(o){if(typeof o=="string"||typeof o=="number")return o;if(typeof o!="object")return"";if(Array.isArray(o))return i.apply(null,o);if(o.toString!==Object.prototype.toString&&!o.toString.toString().includes("[native code]"))return o.toString();var r="";for(var a in o)t.call(o,a)&&o[a]&&(r=s(r,a));return r}function s(o,r){return r?o?o+" "+r:o+r:o}e.exports?(i.default=i,e.exports=i):window.classNames=i})()})(_a);var lf=_a.exports,cf=Ve(lf),hf=typeof ri=="object"&&ri&&ri.Object===Object&&ri,ya=hf,df=ya,uf=typeof self=="object"&&self&&self.Object===Object&&self,ff=df||uf||Function("return this")(),_t=ff,gf=_t,pf=gf.Symbol,Ri=pf,Lo=Ri,xa=Object.prototype,mf=xa.hasOwnProperty,bf=xa.toString,ve=Lo?Lo.toStringTag:void 0;function _f(e){var t=mf.call(e,ve),i=e[ve];try{e[ve]=void 0;var n=!0}catch{}var s=bf.call(e);return n&&(t?e[ve]=i:delete e[ve]),s}var yf=_f,xf=Object.prototype,vf=xf.toString;function Mf(e){return vf.call(e)}var Sf=Mf,Eo=Ri,wf=yf,Pf=Sf,Of="[object Null]",Af="[object Undefined]",$o=Eo?Eo.toStringTag:void 0;function kf(e){return e==null?e===void 0?Af:Of:$o&&$o in Object(e)?wf(e):Pf(e)}var ce=kf;function Cf(e){var t=typeof e;return e!=null&&(t=="object"||t=="function")}var At=Cf,Tf=ce,Df=At,Lf="[object AsyncFunction]",Ef="[object Function]",$f="[object GeneratorFunction]",Rf="[object Proxy]";function If(e){if(!Df(e))return!1;var t=Tf(e);return t==Ef||t==$f||t==Lf||t==Rf}var Gn=If,Ff=_t,zf=Ff["__core-js_shared__"],Bf=zf,rn=Bf,Ro=function(){var e=/[^.]+$/.exec(rn&&rn.keys&&rn.keys.IE_PROTO||"");return e?"Symbol(src)_1."+e:""}();function Nf(e){return!!Ro&&Ro in e}var jf=Nf,Hf=Function.prototype,Wf=Hf.toString;function Vf(e){if(e!=null){try{return Wf.call(e)}catch{}try{return e+""}catch{}}return""}var va=Vf,Yf=Gn,Uf=jf,Gf=At,Xf=va,Kf=/[\\^$.*+?()[\]{}|]/g,qf=/^\[object .+?Constructor\]$/,Zf=Function.prototype,Jf=Object.prototype,Qf=Zf.toString,tg=Jf.hasOwnProperty,eg=RegExp("^"+Qf.call(tg).replace(Kf,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");function ig(e){if(!Gf(e)||Uf(e))return!1;var t=Yf(e)?eg:qf;return t.test(Xf(e))}var ng=ig;function sg(e,t){return e==null?void 0:e[t]}var og=sg,rg=ng,ag=og;function lg(e,t){var i=ag(e,t);return rg(i)?i:void 0}var ee=lg,cg=ee,hg=function(){try{var e=cg(Object,"defineProperty");return e({},"",{}),e}catch{}}(),Ma=hg,Io=Ma;function dg(e,t,i){t=="__proto__"&&Io?Io(e,t,{configurable:!0,enumerable:!0,value:i,writable:!0}):e[t]=i}var Xn=dg;function ug(e,t){return e===t||e!==e&&t!==t}var Ye=ug,fg=Xn,gg=Ye,pg=Object.prototype,mg=pg.hasOwnProperty;function bg(e,t,i){var n=e[t];(!(mg.call(e,t)&&gg(n,i))||i===void 0&&!(t in e))&&fg(e,t,i)}var Sa=bg,_g=Sa,yg=Xn;function xg(e,t,i,n){var s=!i;i||(i={});for(var o=-1,r=t.length;++o<r;){var a=t[o],l=n?n(i[a],e[a],a,i,e):void 0;l===void 0&&(l=e[a]),s?yg(i,a,l):_g(i,a,l)}return i}var wa=xg;function vg(e){return e}var Kn=vg;function Mg(e,t,i){switch(i.length){case 0:return e.call(t);case 1:return e.call(t,i[0]);case 2:return e.call(t,i[0],i[1]);case 3:return e.call(t,i[0],i[1],i[2])}return e.apply(t,i)}var Sg=Mg,wg=Sg,Fo=Math.max;function Pg(e,t,i){return t=Fo(t===void 0?e.length-1:t,0),function(){for(var n=arguments,s=-1,o=Fo(n.length-t,0),r=Array(o);++s<o;)r[s]=n[t+s];s=-1;for(var a=Array(t+1);++s<t;)a[s]=n[s];return a[t]=i(r),wg(e,this,a)}}var Og=Pg;function Ag(e){return function(){return e}}var kg=Ag,Cg=kg,zo=Ma,Tg=Kn,Dg=zo?function(e,t){return zo(e,"toString",{configurable:!0,enumerable:!1,value:Cg(t),writable:!0})}:Tg,Lg=Dg,Eg=800,$g=16,Rg=Date.now;function Ig(e){var t=0,i=0;return function(){var n=Rg(),s=$g-(n-i);if(i=n,s>0){if(++t>=Eg)return arguments[0]}else t=0;return e.apply(void 0,arguments)}}var Fg=Ig,zg=Lg,Bg=Fg,Ng=Bg(zg),jg=Ng,Hg=Kn,Wg=Og,Vg=jg;function Yg(e,t){return Vg(Wg(e,t,Hg),e+"")}var Ug=Yg,Gg=9007199254740991;function Xg(e){return typeof e=="number"&&e>-1&&e%1==0&&e<=Gg}var qn=Xg,Kg=Gn,qg=qn;function Zg(e){return e!=null&&qg(e.length)&&!Kg(e)}var he=Zg,Jg=9007199254740991,Qg=/^(?:0|[1-9]\d*)$/;function tp(e,t){var i=typeof e;return t=t??Jg,!!t&&(i=="number"||i!="symbol"&&Qg.test(e))&&e>-1&&e%1==0&&e<t}var Zn=tp,ep=Ye,ip=he,np=Zn,sp=At;function op(e,t,i){if(!sp(i))return!1;var n=typeof t;return(n=="number"?ip(i)&&np(t,i.length):n=="string"&&t in i)?ep(i[t],e):!1}var rp=op,ap=Ug,lp=rp;function cp(e){return ap(function(t,i){var n=-1,s=i.length,o=s>1?i[s-1]:void 0,r=s>2?i[2]:void 0;for(o=e.length>3&&typeof o=="function"?(s--,o):void 0,r&&lp(i[0],i[1],r)&&(o=s<3?void 0:o,s=1),t=Object(t);++n<s;){var a=i[n];a&&e(t,a,n,o)}return t})}var Pa=cp,hp=Object.prototype;function dp(e){var t=e&&e.constructor,i=typeof t=="function"&&t.prototype||hp;return e===i}var Ii=dp;function up(e,t){for(var i=-1,n=Array(e);++i<e;)n[i]=t(i);return n}var fp=up;function gp(e){return e!=null&&typeof e=="object"}var ie=gp,pp=ce,mp=ie,bp="[object Arguments]";function _p(e){return mp(e)&&pp(e)==bp}var yp=_p,Bo=yp,xp=ie,Oa=Object.prototype,vp=Oa.hasOwnProperty,Mp=Oa.propertyIsEnumerable,Sp=Bo(function(){return arguments}())?Bo:function(e){return xp(e)&&vp.call(e,"callee")&&!Mp.call(e,"callee")},Jn=Sp,wp=Array.isArray,kt=wp,Pi={exports:{}};function Pp(){return!1}var Op=Pp;Pi.exports;(function(e,t){var i=_t,n=Op,s=t&&!t.nodeType&&t,o=s&&!0&&e&&!e.nodeType&&e,r=o&&o.exports===s,a=r?i.Buffer:void 0,l=a?a.isBuffer:void 0,c=l||n;e.exports=c})(Pi,Pi.exports);var Qn=Pi.exports,Ap=ce,kp=qn,Cp=ie,Tp="[object Arguments]",Dp="[object Array]",Lp="[object Boolean]",Ep="[object Date]",$p="[object Error]",Rp="[object Function]",Ip="[object Map]",Fp="[object Number]",zp="[object Object]",Bp="[object RegExp]",Np="[object Set]",jp="[object String]",Hp="[object WeakMap]",Wp="[object ArrayBuffer]",Vp="[object DataView]",Yp="[object Float32Array]",Up="[object Float64Array]",Gp="[object Int8Array]",Xp="[object Int16Array]",Kp="[object Int32Array]",qp="[object Uint8Array]",Zp="[object Uint8ClampedArray]",Jp="[object Uint16Array]",Qp="[object Uint32Array]",F={};F[Yp]=F[Up]=F[Gp]=F[Xp]=F[Kp]=F[qp]=F[Zp]=F[Jp]=F[Qp]=!0;F[Tp]=F[Dp]=F[Wp]=F[Lp]=F[Vp]=F[Ep]=F[$p]=F[Rp]=F[Ip]=F[Fp]=F[zp]=F[Bp]=F[Np]=F[jp]=F[Hp]=!1;function tm(e){return Cp(e)&&kp(e.length)&&!!F[Ap(e)]}var em=tm;function im(e){return function(t){return e(t)}}var nm=im,Oi={exports:{}};Oi.exports;(function(e,t){var i=ya,n=t&&!t.nodeType&&t,s=n&&!0&&e&&!e.nodeType&&e,o=s&&s.exports===n,r=o&&i.process,a=function(){try{var l=s&&s.require&&s.require("util").types;return l||r&&r.binding&&r.binding("util")}catch{}}();e.exports=a})(Oi,Oi.exports);var sm=Oi.exports,om=em,rm=nm,No=sm,jo=No&&No.isTypedArray,am=jo?rm(jo):om,ts=am,lm=fp,cm=Jn,hm=kt,dm=Qn,um=Zn,fm=ts,gm=Object.prototype,pm=gm.hasOwnProperty;function mm(e,t){var i=hm(e),n=!i&&cm(e),s=!i&&!n&&dm(e),o=!i&&!n&&!s&&fm(e),r=i||n||s||o,a=r?lm(e.length,String):[],l=a.length;for(var c in e)(t||pm.call(e,c))&&!(r&&(c=="length"||s&&(c=="offset"||c=="parent")||o&&(c=="buffer"||c=="byteLength"||c=="byteOffset")||um(c,l)))&&a.push(c);return a}var Aa=mm;function bm(e,t){return function(i){return e(t(i))}}var ka=bm,_m=ka,ym=_m(Object.keys,Object),xm=ym,vm=Ii,Mm=xm,Sm=Object.prototype,wm=Sm.hasOwnProperty;function Pm(e){if(!vm(e))return Mm(e);var t=[];for(var i in Object(e))wm.call(e,i)&&i!="constructor"&&t.push(i);return t}var Om=Pm,Am=Aa,km=Om,Cm=he;function Tm(e){return Cm(e)?Am(e):km(e)}var Fi=Tm,Dm=Sa,Lm=wa,Em=Pa,$m=he,Rm=Ii,Im=Fi,Fm=Object.prototype,zm=Fm.hasOwnProperty,Bm=Em(function(e,t){if(Rm(t)||$m(t)){Lm(t,Im(t),e);return}for(var i in t)zm.call(t,i)&&Dm(e,i,t[i])}),Nm=Bm,Ho=Ve(Nm);function jm(){this.__data__=[],this.size=0}var Hm=jm,Wm=Ye;function Vm(e,t){for(var i=e.length;i--;)if(Wm(e[i][0],t))return i;return-1}var zi=Vm,Ym=zi,Um=Array.prototype,Gm=Um.splice;function Xm(e){var t=this.__data__,i=Ym(t,e);if(i<0)return!1;var n=t.length-1;return i==n?t.pop():Gm.call(t,i,1),--this.size,!0}var Km=Xm,qm=zi;function Zm(e){var t=this.__data__,i=qm(t,e);return i<0?void 0:t[i][1]}var Jm=Zm,Qm=zi;function tb(e){return Qm(this.__data__,e)>-1}var eb=tb,ib=zi;function nb(e,t){var i=this.__data__,n=ib(i,e);return n<0?(++this.size,i.push([e,t])):i[n][1]=t,this}var sb=nb,ob=Hm,rb=Km,ab=Jm,lb=eb,cb=sb;function de(e){var t=-1,i=e==null?0:e.length;for(this.clear();++t<i;){var n=e[t];this.set(n[0],n[1])}}de.prototype.clear=ob;de.prototype.delete=rb;de.prototype.get=ab;de.prototype.has=lb;de.prototype.set=cb;var Bi=de,hb=Bi;function db(){this.__data__=new hb,this.size=0}var ub=db;function fb(e){var t=this.__data__,i=t.delete(e);return this.size=t.size,i}var gb=fb;function pb(e){return this.__data__.get(e)}var mb=pb;function bb(e){return this.__data__.has(e)}var _b=bb,yb=ee,xb=_t,vb=yb(xb,"Map"),es=vb,Mb=ee,Sb=Mb(Object,"create"),Ni=Sb,Wo=Ni;function wb(){this.__data__=Wo?Wo(null):{},this.size=0}var Pb=wb;function Ob(e){var t=this.has(e)&&delete this.__data__[e];return this.size-=t?1:0,t}var Ab=Ob,kb=Ni,Cb="__lodash_hash_undefined__",Tb=Object.prototype,Db=Tb.hasOwnProperty;function Lb(e){var t=this.__data__;if(kb){var i=t[e];return i===Cb?void 0:i}return Db.call(t,e)?t[e]:void 0}var Eb=Lb,$b=Ni,Rb=Object.prototype,Ib=Rb.hasOwnProperty;function Fb(e){var t=this.__data__;return $b?t[e]!==void 0:Ib.call(t,e)}var zb=Fb,Bb=Ni,Nb="__lodash_hash_undefined__";function jb(e,t){var i=this.__data__;return this.size+=this.has(e)?0:1,i[e]=Bb&&t===void 0?Nb:t,this}var Hb=jb,Wb=Pb,Vb=Ab,Yb=Eb,Ub=zb,Gb=Hb;function ue(e){var t=-1,i=e==null?0:e.length;for(this.clear();++t<i;){var n=e[t];this.set(n[0],n[1])}}ue.prototype.clear=Wb;ue.prototype.delete=Vb;ue.prototype.get=Yb;ue.prototype.has=Ub;ue.prototype.set=Gb;var Xb=ue,Vo=Xb,Kb=Bi,qb=es;function Zb(){this.size=0,this.__data__={hash:new Vo,map:new(qb||Kb),string:new Vo}}var Jb=Zb;function Qb(e){var t=typeof e;return t=="string"||t=="number"||t=="symbol"||t=="boolean"?e!=="__proto__":e===null}var t_=Qb,e_=t_;function i_(e,t){var i=e.__data__;return e_(t)?i[typeof t=="string"?"string":"hash"]:i.map}var ji=i_,n_=ji;function s_(e){var t=n_(this,e).delete(e);return this.size-=t?1:0,t}var o_=s_,r_=ji;function a_(e){return r_(this,e).get(e)}var l_=a_,c_=ji;function h_(e){return c_(this,e).has(e)}var d_=h_,u_=ji;function f_(e,t){var i=u_(this,e),n=i.size;return i.set(e,t),this.size+=i.size==n?0:1,this}var g_=f_,p_=Jb,m_=o_,b_=l_,__=d_,y_=g_;function fe(e){var t=-1,i=e==null?0:e.length;for(this.clear();++t<i;){var n=e[t];this.set(n[0],n[1])}}fe.prototype.clear=p_;fe.prototype.delete=m_;fe.prototype.get=b_;fe.prototype.has=__;fe.prototype.set=y_;var is=fe,x_=Bi,v_=es,M_=is,S_=200;function w_(e,t){var i=this.__data__;if(i instanceof x_){var n=i.__data__;if(!v_||n.length<S_-1)return n.push([e,t]),this.size=++i.size,this;i=this.__data__=new M_(n)}return i.set(e,t),this.size=i.size,this}var P_=w_,O_=Bi,A_=ub,k_=gb,C_=mb,T_=_b,D_=P_;function ge(e){var t=this.__data__=new O_(e);this.size=t.size}ge.prototype.clear=A_;ge.prototype.delete=k_;ge.prototype.get=C_;ge.prototype.has=T_;ge.prototype.set=D_;var ns=ge,L_="__lodash_hash_undefined__";function E_(e){return this.__data__.set(e,L_),this}var $_=E_;function R_(e){return this.__data__.has(e)}var I_=R_,F_=is,z_=$_,B_=I_;function Ai(e){var t=-1,i=e==null?0:e.length;for(this.__data__=new F_;++t<i;)this.add(e[t])}Ai.prototype.add=Ai.prototype.push=z_;Ai.prototype.has=B_;var N_=Ai;function j_(e,t){for(var i=-1,n=e==null?0:e.length;++i<n;)if(t(e[i],i,e))return!0;return!1}var H_=j_;function W_(e,t){return e.has(t)}var V_=W_,Y_=N_,U_=H_,G_=V_,X_=1,K_=2;function q_(e,t,i,n,s,o){var r=i&X_,a=e.length,l=t.length;if(a!=l&&!(r&&l>a))return!1;var c=o.get(e),h=o.get(t);if(c&&h)return c==t&&h==e;var d=-1,u=!0,f=i&K_?new Y_:void 0;for(o.set(e,t),o.set(t,e);++d<a;){var g=e[d],p=t[d];if(n)var m=r?n(p,g,d,t,e,o):n(g,p,d,e,t,o);if(m!==void 0){if(m)continue;u=!1;break}if(f){if(!U_(t,function(b,_){if(!G_(f,_)&&(g===b||s(g,b,i,n,o)))return f.push(_)})){u=!1;break}}else if(!(g===p||s(g,p,i,n,o))){u=!1;break}}return o.delete(e),o.delete(t),u}var Ca=q_,Z_=_t,J_=Z_.Uint8Array,Ta=J_;function Q_(e){var t=-1,i=Array(e.size);return e.forEach(function(n,s){i[++t]=[s,n]}),i}var ty=Q_;function ey(e){var t=-1,i=Array(e.size);return e.forEach(function(n){i[++t]=n}),i}var iy=ey,Yo=Ri,Uo=Ta,ny=Ye,sy=Ca,oy=ty,ry=iy,ay=1,ly=2,cy="[object Boolean]",hy="[object Date]",dy="[object Error]",uy="[object Map]",fy="[object Number]",gy="[object RegExp]",py="[object Set]",my="[object String]",by="[object Symbol]",_y="[object ArrayBuffer]",yy="[object DataView]",Go=Yo?Yo.prototype:void 0,an=Go?Go.valueOf:void 0;function xy(e,t,i,n,s,o,r){switch(i){case yy:if(e.byteLength!=t.byteLength||e.byteOffset!=t.byteOffset)return!1;e=e.buffer,t=t.buffer;case _y:return!(e.byteLength!=t.byteLength||!o(new Uo(e),new Uo(t)));case cy:case hy:case fy:return ny(+e,+t);case dy:return e.name==t.name&&e.message==t.message;case gy:case my:return e==t+"";case uy:var a=oy;case py:var l=n&ay;if(a||(a=ry),e.size!=t.size&&!l)return!1;var c=r.get(e);if(c)return c==t;n|=ly,r.set(e,t);var h=sy(a(e),a(t),n,s,o,r);return r.delete(e),h;case by:if(an)return an.call(e)==an.call(t)}return!1}var vy=xy;function My(e,t){for(var i=-1,n=t.length,s=e.length;++i<n;)e[s+i]=t[i];return e}var Sy=My,wy=Sy,Py=kt;function Oy(e,t,i){var n=t(e);return Py(e)?n:wy(n,i(e))}var Ay=Oy;function ky(e,t){for(var i=-1,n=e==null?0:e.length,s=0,o=[];++i<n;){var r=e[i];t(r,i,e)&&(o[s++]=r)}return o}var Cy=ky;function Ty(){return[]}var Dy=Ty,Ly=Cy,Ey=Dy,$y=Object.prototype,Ry=$y.propertyIsEnumerable,Xo=Object.getOwnPropertySymbols,Iy=Xo?function(e){return e==null?[]:(e=Object(e),Ly(Xo(e),function(t){return Ry.call(e,t)}))}:Ey,Fy=Iy,zy=Ay,By=Fy,Ny=Fi;function jy(e){return zy(e,Ny,By)}var Hy=jy,Ko=Hy,Wy=1,Vy=Object.prototype,Yy=Vy.hasOwnProperty;function Uy(e,t,i,n,s,o){var r=i&Wy,a=Ko(e),l=a.length,c=Ko(t),h=c.length;if(l!=h&&!r)return!1;for(var d=l;d--;){var u=a[d];if(!(r?u in t:Yy.call(t,u)))return!1}var f=o.get(e),g=o.get(t);if(f&&g)return f==t&&g==e;var p=!0;o.set(e,t),o.set(t,e);for(var m=r;++d<l;){u=a[d];var b=e[u],_=t[u];if(n)var v=r?n(_,b,u,t,e,o):n(b,_,u,e,t,o);if(!(v===void 0?b===_||s(b,_,i,n,o):v)){p=!1;break}m||(m=u=="constructor")}if(p&&!m){var x=e.constructor,y=t.constructor;x!=y&&"constructor"in e&&"constructor"in t&&!(typeof x=="function"&&x instanceof x&&typeof y=="function"&&y instanceof y)&&(p=!1)}return o.delete(e),o.delete(t),p}var Gy=Uy,Xy=ee,Ky=_t,qy=Xy(Ky,"DataView"),Zy=qy,Jy=ee,Qy=_t,tx=Jy(Qy,"Promise"),ex=tx,ix=ee,nx=_t,sx=ix(nx,"Set"),ox=sx,rx=ee,ax=_t,lx=rx(ax,"WeakMap"),cx=lx,Pn=Zy,On=es,An=ex,kn=ox,Cn=cx,Da=ce,pe=va,qo="[object Map]",hx="[object Object]",Zo="[object Promise]",Jo="[object Set]",Qo="[object WeakMap]",tr="[object DataView]",dx=pe(Pn),ux=pe(On),fx=pe(An),gx=pe(kn),px=pe(Cn),Xt=Da;(Pn&&Xt(new Pn(new ArrayBuffer(1)))!=tr||On&&Xt(new On)!=qo||An&&Xt(An.resolve())!=Zo||kn&&Xt(new kn)!=Jo||Cn&&Xt(new Cn)!=Qo)&&(Xt=function(e){var t=Da(e),i=t==hx?e.constructor:void 0,n=i?pe(i):"";if(n)switch(n){case dx:return tr;case ux:return qo;case fx:return Zo;case gx:return Jo;case px:return Qo}return t});var mx=Xt,ln=ns,bx=Ca,_x=vy,yx=Gy,er=mx,ir=kt,nr=Qn,xx=ts,vx=1,sr="[object Arguments]",or="[object Array]",ai="[object Object]",Mx=Object.prototype,rr=Mx.hasOwnProperty;function Sx(e,t,i,n,s,o){var r=ir(e),a=ir(t),l=r?or:er(e),c=a?or:er(t);l=l==sr?ai:l,c=c==sr?ai:c;var h=l==ai,d=c==ai,u=l==c;if(u&&nr(e)){if(!nr(t))return!1;r=!0,h=!1}if(u&&!h)return o||(o=new ln),r||xx(e)?bx(e,t,i,n,s,o):_x(e,t,l,i,n,s,o);if(!(i&vx)){var f=h&&rr.call(e,"__wrapped__"),g=d&&rr.call(t,"__wrapped__");if(f||g){var p=f?e.value():e,m=g?t.value():t;return o||(o=new ln),s(p,m,i,n,o)}}return u?(o||(o=new ln),yx(e,t,i,n,s,o)):!1}var wx=Sx,Px=wx,ar=ie;function La(e,t,i,n,s){return e===t?!0:e==null||t==null||!ar(e)&&!ar(t)?e!==e&&t!==t:Px(e,t,i,n,La,s)}var Ea=La,Ox=ns,Ax=Ea,kx=1,Cx=2;function Tx(e,t,i,n){var s=i.length,o=s,r=!n;if(e==null)return!o;for(e=Object(e);s--;){var a=i[s];if(r&&a[2]?a[1]!==e[a[0]]:!(a[0]in e))return!1}for(;++s<o;){a=i[s];var l=a[0],c=e[l],h=a[1];if(r&&a[2]){if(c===void 0&&!(l in e))return!1}else{var d=new Ox;if(n)var u=n(c,h,l,e,t,d);if(!(u===void 0?Ax(h,c,kx|Cx,n,d):u))return!1}}return!0}var Dx=Tx,Lx=At;function Ex(e){return e===e&&!Lx(e)}var $a=Ex,$x=$a,Rx=Fi;function Ix(e){for(var t=Rx(e),i=t.length;i--;){var n=t[i],s=e[n];t[i]=[n,s,$x(s)]}return t}var Fx=Ix;function zx(e,t){return function(i){return i==null?!1:i[e]===t&&(t!==void 0||e in Object(i))}}var Ra=zx,Bx=Dx,Nx=Fx,jx=Ra;function Hx(e){var t=Nx(e);return t.length==1&&t[0][2]?jx(t[0][0],t[0][1]):function(i){return i===e||Bx(i,e,t)}}var Wx=Hx,Vx=ce,Yx=ie,Ux="[object Symbol]";function Gx(e){return typeof e=="symbol"||Yx(e)&&Vx(e)==Ux}var Hi=Gx,Xx=kt,Kx=Hi,qx=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,Zx=/^\w*$/;function Jx(e,t){if(Xx(e))return!1;var i=typeof e;return i=="number"||i=="symbol"||i=="boolean"||e==null||Kx(e)?!0:Zx.test(e)||!qx.test(e)||t!=null&&e in Object(t)}var ss=Jx,Ia=is,Qx="Expected a function";function os(e,t){if(typeof e!="function"||t!=null&&typeof t!="function")throw new TypeError(Qx);var i=function(){var n=arguments,s=t?t.apply(this,n):n[0],o=i.cache;if(o.has(s))return o.get(s);var r=e.apply(this,n);return i.cache=o.set(s,r)||o,r};return i.cache=new(os.Cache||Ia),i}os.Cache=Ia;var t0=os,e0=t0,i0=500;function n0(e){var t=e0(e,function(n){return i.size===i0&&i.clear(),n}),i=t.cache;return t}var s0=n0,o0=s0,r0=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,a0=/\\(\\)?/g,l0=o0(function(e){var t=[];return e.charCodeAt(0)===46&&t.push(""),e.replace(r0,function(i,n,s,o){t.push(s?o.replace(a0,"$1"):n||i)}),t}),c0=l0;function h0(e,t){for(var i=-1,n=e==null?0:e.length,s=Array(n);++i<n;)s[i]=t(e[i],i,e);return s}var d0=h0,lr=Ri,u0=d0,f0=kt,g0=Hi,cr=lr?lr.prototype:void 0,hr=cr?cr.toString:void 0;function Fa(e){if(typeof e=="string")return e;if(f0(e))return u0(e,Fa)+"";if(g0(e))return hr?hr.call(e):"";var t=e+"";return t=="0"&&1/e==-1/0?"-0":t}var p0=Fa,m0=p0;function b0(e){return e==null?"":m0(e)}var _0=b0,y0=kt,x0=ss,v0=c0,M0=_0;function S0(e,t){return y0(e)?e:x0(e,t)?[e]:v0(M0(e))}var za=S0,w0=Hi;function P0(e){if(typeof e=="string"||w0(e))return e;var t=e+"";return t=="0"&&1/e==-1/0?"-0":t}var Wi=P0,O0=za,A0=Wi;function k0(e,t){t=O0(t,e);for(var i=0,n=t.length;e!=null&&i<n;)e=e[A0(t[i++])];return i&&i==n?e:void 0}var Ba=k0,C0=Ba;function T0(e,t,i){var n=e==null?void 0:C0(e,t);return n===void 0?i:n}var D0=T0;function L0(e,t){return e!=null&&t in Object(e)}var E0=L0,$0=za,R0=Jn,I0=kt,F0=Zn,z0=qn,B0=Wi;function N0(e,t,i){t=$0(t,e);for(var n=-1,s=t.length,o=!1;++n<s;){var r=B0(t[n]);if(!(o=e!=null&&i(e,r)))break;e=e[r]}return o||++n!=s?o:(s=e==null?0:e.length,!!s&&z0(s)&&F0(r,s)&&(I0(e)||R0(e)))}var j0=N0,H0=E0,W0=j0;function V0(e,t){return e!=null&&W0(e,t,H0)}var Y0=V0,U0=Ea,G0=D0,X0=Y0,K0=ss,q0=$a,Z0=Ra,J0=Wi,Q0=1,tv=2;function ev(e,t){return K0(e)&&q0(t)?Z0(J0(e),t):function(i){var n=G0(i,e);return n===void 0&&n===t?X0(i,e):U0(t,n,Q0|tv)}}var iv=ev;function nv(e){return function(t){return t==null?void 0:t[e]}}var sv=nv,ov=Ba;function rv(e){return function(t){return ov(t,e)}}var av=rv,lv=sv,cv=av,hv=ss,dv=Wi;function uv(e){return hv(e)?lv(dv(e)):cv(e)}var fv=uv,gv=Wx,pv=iv,mv=Kn,bv=kt,_v=fv;function yv(e){return typeof e=="function"?e:e==null?mv:typeof e=="object"?bv(e)?pv(e[0],e[1]):gv(e):_v(e)}var Na=yv,xv=Na,vv=he,Mv=Fi;function Sv(e){return function(t,i,n){var s=Object(t);if(!vv(t)){var o=xv(i);t=Mv(t),i=function(a){return o(s[a],a,s)}}var r=e(t,i,n);return r>-1?s[o?t[r]:r]:void 0}}var wv=Sv;function Pv(e,t,i,n){for(var s=e.length,o=i+(n?1:-1);n?o--:++o<s;)if(t(e[o],o,e))return o;return-1}var Ov=Pv,Av=/\s/;function kv(e){for(var t=e.length;t--&&Av.test(e.charAt(t)););return t}var Cv=kv,Tv=Cv,Dv=/^\s+/;function Lv(e){return e&&e.slice(0,Tv(e)+1).replace(Dv,"")}var Ev=Lv,$v=Ev,dr=At,Rv=Hi,ur=NaN,Iv=/^[-+]0x[0-9a-f]+$/i,Fv=/^0b[01]+$/i,zv=/^0o[0-7]+$/i,Bv=parseInt;function Nv(e){if(typeof e=="number")return e;if(Rv(e))return ur;if(dr(e)){var t=typeof e.valueOf=="function"?e.valueOf():e;e=dr(t)?t+"":t}if(typeof e!="string")return e===0?e:+e;e=$v(e);var i=Fv.test(e);return i||zv.test(e)?Bv(e.slice(2),i?2:8):Iv.test(e)?ur:+e}var jv=Nv,Hv=jv,Wv=1/0,Vv=17976931348623157e292;function Yv(e){if(!e)return e===0?e:0;if(e=Hv(e),e===Wv||e===-1/0){var t=e<0?-1:1;return t*Vv}return e===e?e:0}var Uv=Yv,Gv=Uv;function Xv(e){var t=Gv(e),i=t%1;return t===t?i?t-i:t:0}var Kv=Xv,qv=Ov,Zv=Na,Jv=Kv,Qv=Math.max;function t1(e,t,i){var n=e==null?0:e.length;if(!n)return-1;var s=i==null?0:Jv(i);return s<0&&(s=Qv(n+s,0)),qv(e,Zv(t),s)}var e1=t1,i1=wv,n1=e1,s1=i1(n1),o1=s1,r1=Ve(o1),a1=Xn,l1=Ye;function c1(e,t,i){(i!==void 0&&!l1(e[t],i)||i===void 0&&!(t in e))&&a1(e,t,i)}var ja=c1;function h1(e){return function(t,i,n){for(var s=-1,o=Object(t),r=n(t),a=r.length;a--;){var l=r[e?a:++s];if(i(o[l],l,o)===!1)break}return t}}var d1=h1,u1=d1,f1=u1(),g1=f1,ki={exports:{}};ki.exports;(function(e,t){var i=_t,n=t&&!t.nodeType&&t,s=n&&!0&&e&&!e.nodeType&&e,o=s&&s.exports===n,r=o?i.Buffer:void 0,a=r?r.allocUnsafe:void 0;function l(c,h){if(h)return c.slice();var d=c.length,u=a?a(d):new c.constructor(d);return c.copy(u),u}e.exports=l})(ki,ki.exports);var p1=ki.exports,fr=Ta;function m1(e){var t=new e.constructor(e.byteLength);return new fr(t).set(new fr(e)),t}var b1=m1,_1=b1;function y1(e,t){var i=t?_1(e.buffer):e.buffer;return new e.constructor(i,e.byteOffset,e.length)}var x1=y1;function v1(e,t){var i=-1,n=e.length;for(t||(t=Array(n));++i<n;)t[i]=e[i];return t}var M1=v1,S1=At,gr=Object.create,w1=function(){function e(){}return function(t){if(!S1(t))return{};if(gr)return gr(t);e.prototype=t;var i=new e;return e.prototype=void 0,i}}(),P1=w1,O1=ka,A1=O1(Object.getPrototypeOf,Object),Ha=A1,k1=P1,C1=Ha,T1=Ii;function D1(e){return typeof e.constructor=="function"&&!T1(e)?k1(C1(e)):{}}var L1=D1,E1=he,$1=ie;function R1(e){return $1(e)&&E1(e)}var I1=R1,F1=ce,z1=Ha,B1=ie,N1="[object Object]",j1=Function.prototype,H1=Object.prototype,Wa=j1.toString,W1=H1.hasOwnProperty,V1=Wa.call(Object);function Y1(e){if(!B1(e)||F1(e)!=N1)return!1;var t=z1(e);if(t===null)return!0;var i=W1.call(t,"constructor")&&t.constructor;return typeof i=="function"&&i instanceof i&&Wa.call(i)==V1}var U1=Y1;function G1(e,t){if(!(t==="constructor"&&typeof e[t]=="function")&&t!="__proto__")return e[t]}var Va=G1;function X1(e){var t=[];if(e!=null)for(var i in Object(e))t.push(i);return t}var K1=X1,q1=At,Z1=Ii,J1=K1,Q1=Object.prototype,tM=Q1.hasOwnProperty;function eM(e){if(!q1(e))return J1(e);var t=Z1(e),i=[];for(var n in e)n=="constructor"&&(t||!tM.call(e,n))||i.push(n);return i}var iM=eM,nM=Aa,sM=iM,oM=he;function rM(e){return oM(e)?nM(e,!0):sM(e)}var Ya=rM,aM=wa,lM=Ya;function cM(e){return aM(e,lM(e))}var hM=cM,pr=ja,dM=p1,uM=x1,fM=M1,gM=L1,mr=Jn,br=kt,pM=I1,mM=Qn,bM=Gn,_M=At,yM=U1,xM=ts,_r=Va,vM=hM;function MM(e,t,i,n,s,o,r){var a=_r(e,i),l=_r(t,i),c=r.get(l);if(c){pr(e,i,c);return}var h=o?o(a,l,i+"",e,t,r):void 0,d=h===void 0;if(d){var u=br(l),f=!u&&mM(l),g=!u&&!f&&xM(l);h=l,u||f||g?br(a)?h=a:pM(a)?h=fM(a):f?(d=!1,h=dM(l,!0)):g?(d=!1,h=uM(l,!0)):h=[]:yM(l)||mr(l)?(h=a,mr(a)?h=vM(a):(!_M(a)||bM(a))&&(h=gM(l))):d=!1}d&&(r.set(l,h),s(h,l,n,o,r),r.delete(l)),pr(e,i,h)}var SM=MM,wM=ns,PM=ja,OM=g1,AM=SM,kM=At,CM=Ya,TM=Va;function Ua(e,t,i,n,s){e!==t&&OM(t,function(o,r){if(s||(s=new wM),kM(o))AM(e,t,r,i,Ua,n,s);else{var a=n?n(TM(e,r),o,r+"",e,t,s):void 0;a===void 0&&(a=o),PM(e,r,a)}},CM)}var DM=Ua,LM=DM,EM=Pa,$M=EM(function(e,t,i){LM(e,t,i)}),RM=$M,yr=Ve(RM),xr=function(e,t){typeof e=="function"?e(t):e&&(e.current=t)},yt=nt.forwardRef(function(e,t){var i=e.className,n=e.customTooltips,s=n===void 0?!0:n,o=e.data,r=e.id,a=e.fallbackContent,l=e.getDatasetAtEvent,c=e.getElementAtEvent,h=e.getElementsAtEvent,d=e.height,u=d===void 0?150:d,f=e.options,g=e.plugins,p=g===void 0?[]:g,m=e.redraw,b=m===void 0?!1:m,_=e.type,v=_===void 0?"bar":_,x=e.width,y=x===void 0?300:x,S=e.wrapper,w=S===void 0?!0:S,P=Co(e,["className","customTooltips","data","id","fallbackContent","getDatasetAtEvent","getElementAtEvent","getElementsAtEvent","height","options","plugins","redraw","type","width","wrapper"]);pt.register.apply(pt,ef);var O=nt.useRef(null),A=nt.useRef(),k=nt.useMemo(function(){return typeof o=="function"?O.current?o(O.current):{datasets:[]}:yr({},o)},[O.current,JSON.stringify(o)]),T=nt.useMemo(function(){return s?yr({},f,{plugins:{tooltip:{enabled:!1,mode:"index",position:"nearest",external:sf}}}):f},[O.current,JSON.stringify(f)]),U=function(){O.current&&(A.current=new pt(O.current,{type:v,data:k,options:T,plugins:p}),xr(t,A.current))},tt=function(W){A.current&&(l&&l(A.current.getElementsAtEventForMode(W,"dataset",{intersect:!0},!1),W),c&&c(A.current.getElementsAtEventForMode(W,"nearest",{intersect:!0},!1),W),h&&h(A.current.getElementsAtEventForMode(W,"index",{intersect:!0},!1),W))},E=function(){if(A.current){if(f&&(A.current.options=Z({},T)),!A.current.config.data){A.current.config.data=k,A.current.update();return}var W=k.datasets,G=W===void 0?[]:W,Ct=Co(k,["datasets"]),Nt=A.current.config.data.datasets,Tt=Nt===void 0?[]:Nt;Ho(A.current.config.data,Ct),A.current.config.data.datasets=G.map(function(rt){var et=r1(Tt,function(Dt){return Dt.label===rt.label&&Dt.type===rt.type});return!et||!rt.data?rt:(et.data?et.data.length=rt.data.length:et.data=[],Ho(et.data,rt.data),Z(Z(Z({},et),rt),{data:et.data}))}),A.current.update()}},R=function(){xr(t,null),A.current&&(A.current.destroy(),A.current=void 0)};nt.useEffect(function(){return U(),function(){return R()}},[]),nt.useEffect(function(){A.current&&(b?(R(),setTimeout(function(){U()},0)):E())},[JSON.stringify(o),k]);var N=function(W){return bt.createElement("canvas",Z({},!w&&i&&{className:i},{"data-testid":"canvas",height:u,id:r,onClick:function(G){tt(G)},ref:W,role:"img",width:y},P),a)};return w?bt.createElement("div",Z({className:cf("chart-wrapper",i)},P),N(O)):N(O)});yt.propTypes={className:it.string,customTooltips:it.bool,data:it.any.isRequired,fallbackContent:it.node,getDatasetAtEvent:it.func,getElementAtEvent:it.func,getElementsAtEvent:it.func,height:it.number,id:it.string,options:it.object,plugins:it.array,redraw:it.bool,type:it.oneOf(["bar","line","scatter","bubble","pie","doughnut","polarArea","radar"]).isRequired,width:it.number,wrapper:it.bool};yt.displayName="CChart";var IM=nt.forwardRef(function(e,t){return bt.createElement(yt,Z({},e,{type:"bar",ref:t}))});IM.displayName="CChartBar";var FM=nt.forwardRef(function(e,t){return bt.createElement(yt,Z({},e,{type:"bubble",ref:t}))});FM.displayName="CChartBubble";var zM=nt.forwardRef(function(e,t){return bt.createElement(yt,Z({},e,{type:"doughnut",ref:t}))});zM.displayName="CChartDoughnut";var BM=nt.forwardRef(function(e,t){return bt.createElement(yt,Z({},e,{type:"line",ref:t}))});BM.displayName="CChartLine";var NM=nt.forwardRef(function(e,t){return bt.createElement(yt,Z({},e,{type:"pie",ref:t}))});NM.displayName="CChartPie";var jM=nt.forwardRef(function(e,t){return bt.createElement(yt,Z({},e,{type:"polarArea",ref:t}))});jM.displayName="CChartPolarArea";var HM=nt.forwardRef(function(e,t){return bt.createElement(yt,Z({},e,{type:"radar",ref:t}))});HM.displayName="CChartRadar";var WM=nt.forwardRef(function(e,t){return bt.createElement(yt,Z({},e,{type:"scatter",ref:t}))});WM.displayName="CChartScatter";export{IM as C,BM as a,zM as b,NM as c,jM as d,HM as e,yt as f};
