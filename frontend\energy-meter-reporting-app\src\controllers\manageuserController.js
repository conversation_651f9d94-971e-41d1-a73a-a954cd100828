const userModel = require('../models/userModel'); // Assuming a User model exists
const bcrypt = require('bcrypt'); // Add bcrypt for password hashing

// Fetch all users
const getUsers = async (req, res) => {
    try {
        const users = await userModel.findAll({
            attributes: { exclude: ['password'] } // Exclude the password field
        });
        res.json(users);
    } catch (error) {
        res.status(500).json({ error: 'Error fetching users' });
    }
};

// Add a new user

const addUser = async (req, res) => {
    try {
        const { username, password, role } = req.body;
        const hashedPassword = await bcrypt.hash(password, 10); // Hash the password
        const newUser = new userModel({ username, password: hashedPassword, role }); // Use hashedPassword
        await newUser.save();
        res.status(201).json({ message: 'User added successfully' });
    } catch (error) {
        res.status(500).json({ error: 'Error adding user' });
    }
};

// Update an existing user
const updateUser = async (req, res) => {
    try {
        const { id } = req.params;
        const updates = req.body;

        // Check if password is being updated
        if (updates.password) {
            updates.password = await bcrypt.hash(updates.password, 10); // Hash the new password
        }

        // Update the user and get the count of affected rows
        const [updatedRowsCount] = await userModel.update(updates, { where: { id } });

        if (updatedRowsCount === 0) {
            return res.status(404).json({ error: 'User not found' });
        }

        // Fetch the updated user manually
        const updatedUser = await userModel.findByPk(id, {
            attributes: { exclude: ['password'] } // Exclude the password field
        });

        res.status(200).json({ message: 'User updated successfully', user: updatedUser });
    } catch (error) {
        res.status(500).json({ error: 'Error updating user' });
    }
};

// Delete a user
const deleteUser = async (req, res) => {
    try {
        const deletedRowsCount = await userModel.destroy({
            where: { id: req.params.id }
        });

        if (deletedRowsCount === 0) {
            return res.status(404).json({ message: 'User not found' });
        }

        res.json({ message: 'User deleted successfully' });
    } catch (error) {
        res.status(500).json({ error: 'Error deleting user' });
    }
};

module.exports = { getUsers, addUser, updateUser, deleteUser };
