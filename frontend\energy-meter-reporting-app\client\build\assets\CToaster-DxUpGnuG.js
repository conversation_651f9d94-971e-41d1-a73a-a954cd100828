import{a as r,_ as E,R as o,b as u,c as S,P as t,d as z,y as V}from"./index-BDJ8oeCE.js";import{u as D,T as G,I as O,r as H}from"./DefaultLayout-BolUaDEE.js";var B=r.createContext({}),M=r.forwardRef(function(e,i){var l=e.children,s=e.animation,n=s===void 0?!0:s,a=e.autohide,x=a===void 0?!0:a,h=e.className,d=e.color,m=e.delay,p=m===void 0?5e3:m,c=e.index,v=e.innerKey,y=e.visible,b=y===void 0?!1:y,C=e.onClose,T=e.onShow,L=E(e,["children","animation","autohide","className","color","delay","index","innerKey","visible","onClose","onShow"]),k=r.useRef(),j=D(i,k),K=r.useState(!1),w=K[0],R=K[1],N=r.useRef();r.useEffect(function(){R(b)},[b]);var q={visible:w,setVisible:R};r.useEffect(function(){return function(){return clearTimeout(N.current)}},[]),r.useEffect(function(){P()},[w]);var P=function(){x&&(clearTimeout(N.current),N.current=window.setTimeout(function(){R(!1)},p))};return o.createElement(G,{in:w,nodeRef:k,onEnter:function(){return T&&T(c??null)},onExited:function(){return C&&C(c??null)},timeout:250,unmountOnExit:!0},function(g){var f;return o.createElement(B.Provider,{value:q},o.createElement("div",u({className:S("toast",(f={fade:n},f["bg-".concat(d)]=d,f["border-0"]=d,f["show showing"]=g==="entering"||g==="exiting",f.show=g==="entered",f),h),"aria-live":"assertive","aria-atomic":"true",role:"alert",onMouseEnter:function(){return clearTimeout(N.current)},onMouseLeave:function(){return P()}},L,{key:v,ref:j}),l))})});M.propTypes={animation:t.bool,autohide:t.bool,children:t.node,className:t.string,color:z,delay:t.number,index:t.number,innerKey:t.oneOfType([t.number,t.string]),onClose:t.func,onShow:t.func,visible:t.bool};M.displayName="CToast";var A=r.forwardRef(function(e,i){var l=e.children,s=e.className,n=E(e,["children","className"]);return o.createElement("div",u({className:S("toast-body",s)},n,{ref:i}),l)});A.propTypes={children:t.node,className:t.string};A.displayName="CToastBody";var F=r.forwardRef(function(e,i){var l=e.children,s=e.as,n=E(e,["children","as"]),a=r.useContext(B).setVisible;return s?o.createElement(s,u({onClick:function(){return a(!1)}},n,{ref:i}),l):o.createElement(O,u({onClick:function(){return a(!1)}},n,{ref:i}))});F.propTypes=u(u({},O.propTypes),{as:t.elementType});F.displayName="CToastClose";var I=r.forwardRef(function(e,i){var l=e.children,s=e.className,n=e.placement,a=e.push,x=E(e,["children","className","placement","push"]),h=r.useState([]),d=h[0],m=h[1],p=r.useRef(0);r.useEffect(function(){p.current++,a&&c(a)},[a]);var c=function(v){m(function(y){return V(V([],y,!0),[o.cloneElement(v,{index:p.current,innerKey:p.current,onClose:function(b){return m(function(C){return C.filter(function(T){return T.props.index!==b})})}})],!1)})};return o.createElement(H,{portal:typeof n=="string"},d.length>0||l?o.createElement("div",u({className:S("toaster toast-container",{"position-fixed":n,"top-0":n&&n.includes("top"),"top-50 translate-middle-y":n&&n.includes("middle"),"bottom-0":n&&n.includes("bottom"),"start-0":n&&n.includes("start"),"start-50 translate-middle-x":n&&n.includes("center"),"end-0":n&&n.includes("end")},s)},x,{ref:i}),l,d.map(function(v){return o.cloneElement(v,{visible:!0})})):null)});I.propTypes={children:t.node,className:t.string,placement:t.oneOfType([t.string,t.oneOf(["top-start","top-center","top-end","middle-start","middle-center","middle-end","bottom-start","bottom-center","bottom-end"])]),push:t.any};I.displayName="CToaster";export{F as C,M as a,A as b,I as c};
