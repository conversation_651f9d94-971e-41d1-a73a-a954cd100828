import{a as r,q as l,v as d,j as e}from"./index-BDJ8oeCE.js";import{a as D,b as H,C as z,c as V}from"./CToaster-DxUpGnuG.js";import{C as q,a as O}from"./CCardBody-iimbKiZ7.js";import{C as P}from"./CCardHeader-CFnfD6gM.js";import{a as u}from"./index.esm-DSzlmaRN.js";import{C as G,a as J,b as E,c as N,d as K,e as M}from"./CTable-BG2MPlsJ.js";import{C as Q,a as W,b as X,c as Y}from"./CModalHeader-DX4AicsN.js";import{C as Z}from"./CForm-C4rJo8l4.js";import{C as T}from"./CFormSelect-B3z3ot4z.js";import"./DefaultLayout-BolUaDEE.js";import"./cil-user-Ddrdy7PS.js";import"./CFormControlWrapper-CyzWU6Dg.js";import"./CFormControlValidation-_wBnnnml.js";import"./CFormLabel-CzXD3nfE.js";const he=()=>{const[m,I]=r.useState([]),[g,L]=r.useState([]),[h,w]=r.useState([]),[S,p]=r.useState(!1),[i,x]=r.useState({location:"",tagname:"",feederName:""}),[f,b]=r.useState(null),[U,k]=r.useState(null);r.useEffect(()=>{R(),$()},[]),r.useEffect(()=>{g.length>0&&h.length>0&&C()},[g,h]);const R=()=>{l.get(`${d.baseURL}/locations`).then(a=>L(a.data.data)).catch(()=>o("Error fetching locations","danger"))},$=()=>{l.get(`${d.baseURL}/energy-meters`).then(a=>w(a.data.data)).catch(()=>o("Error fetching energy meters","danger"))},C=()=>{l.get(`${d.baseURL}/mappings`).then(a=>{const t=a.data.data.map(n=>{const c=h.find(j=>j.id===n.energyMeterId),v=g.find(j=>j.id===n.locationId);return{id:n.id,locationId:n.locationId,location:v?v.name:"Unknown Location",tagname:c?c.tagName:"Unknown TagName",feederName:c?c.feederName:"Unknown FeederName"}});I(t)}).catch(()=>o("Error fetching mappings","danger"))},y=(a={location:"",tagname:"",feederName:""})=>{x(a),b(a.id?m.findIndex(s=>s.id===a.id):null),p(!0)},B=()=>{const a=h.find(n=>n.tagName===i.tagname),s=g.find(n=>n.name===i.location);if(!a||!s){o("Invalid energy meter or location selected","danger");return}const t={energyMeterId:a.id,locationId:s.id};if(f!==null){const n=m[f];l.put(`${d.baseURL}/mappings/${n.id}`,t).then(c=>{C(),p(!1),x({location:"",tagname:"",feederName:""}),b(null),o(c.data.message,"success")}).catch(()=>o("Error updating mapping","danger"))}else l.post(`${d.baseURL}/mappings/map`,t).then(n=>{C(),p(!1),x({location:"",tagname:"",feederName:""}),o(n.data.message,"success")}).catch(()=>o("Error saving mapping","danger"))},F=a=>{const s=m[a];l.delete(`${d.baseURL}/mappings/${s.id}`).then(t=>{C(),o(t.data.message,"success")}).catch(()=>o("Error deleting mapping","danger"))},o=(a,s)=>{k(e.jsx(D,{autohide:5e3,visible:!0,color:s,className:"text-white align-items-center",children:e.jsxs("div",{className:"d-flex",children:[e.jsx(H,{children:a}),e.jsx(z,{className:"me-2 m-auto",white:!0})]})}))},A=()=>{const a={};return m.forEach(s=>{a[s.locationId]||(a[s.locationId]={locationName:s.location,mappings:[]}),a[s.locationId].mappings.push(s)}),Object.values(a)};return e.jsxs(e.Fragment,{children:[e.jsx(V,{push:U,placement:"top-center",className:"mt-3"}),e.jsxs(q,{children:[e.jsxs(P,{children:[e.jsx("strong",{children:"Program Mappings"}),e.jsx(u,{color:"primary",className:"float-end",onClick:()=>y({location:"",tagname:"",feederName:""}),children:"Add New"})]}),e.jsx(O,{children:A().map((a,s)=>e.jsxs("div",{className:"mb-4",children:[e.jsx("h5",{children:a.locationName}),e.jsxs(G,{hover:!0,responsive:!0,children:[e.jsx(J,{children:e.jsxs(E,{children:[e.jsx(N,{children:"Tag Name"}),e.jsx(N,{children:"Feeder Name"}),e.jsx(N,{children:"Actions"})]})}),e.jsx(K,{children:a.mappings.map(t=>e.jsxs(E,{children:[e.jsx(M,{children:t.tagname}),e.jsx(M,{children:t.feederName}),e.jsxs(M,{children:[e.jsx(u,{color:"warning",size:"sm",className:"me-2",onClick:()=>y(t),children:"Edit"}),e.jsx(u,{color:"danger",size:"sm",onClick:()=>F(m.findIndex(n=>n.id===t.id)),children:"Delete"})]})]},t.id))})]})]},s))})]}),e.jsxs(Q,{visible:S,onClose:()=>p(!1),children:[e.jsx(W,{children:f!==null?"Edit Mapping":"Add Mapping"}),e.jsx(X,{children:e.jsxs(Z,{children:[e.jsxs(T,{value:i.location,onChange:a=>x({...i,location:a.target.value}),className:"mb-3",children:[e.jsx("option",{value:"",children:"Select Location"}),g.map(a=>e.jsx("option",{value:a.name,children:a.name},a.id))]}),e.jsxs(T,{value:i.tagname,onChange:a=>x({...i,tagname:a.target.value}),className:"mb-3",children:[e.jsx("option",{value:"",children:"Select Energy Meter"}),h.map(a=>e.jsx("option",{value:a.tagName,children:a.tagName},a.id))]})]})}),e.jsxs(Y,{children:[e.jsx(u,{color:"secondary",onClick:()=>p(!1),children:"Cancel"}),e.jsx(u,{color:"primary",onClick:B,children:f!==null?"Update":"Add"})]})]})]})};export{he as default};
