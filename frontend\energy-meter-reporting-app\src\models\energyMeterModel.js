const { Sequelize, DataTypes } = require('sequelize');
const { sequelize } = require('../utils/db');

// Define the Energy Meter model
const EnergyMeter = sequelize.define('EnergyMeter', {
    id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true
    },
    tagName: {
        type: DataTypes.STRING,
        allowNull: false
    },
    feederName: {
        type: DataTypes.STRING,
        allowNull: false
    },
    createdAt: {
        type: DataTypes.DATE,
        defaultValue: sequelize.literal('GETDATE()')  // ✅ Uses MSSQL's GETDATE()
    }
}, {
    timestamps: false,
});

// Export the Energy Meter model
module.exports = EnergyMeter;