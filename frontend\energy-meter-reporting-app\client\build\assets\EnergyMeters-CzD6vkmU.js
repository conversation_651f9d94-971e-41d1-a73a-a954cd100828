import{a as d,q as i,v as c,j as e}from"./index-BDJ8oeCE.js";import{a as M,b as T,C as v,c as w}from"./CToaster-DxUpGnuG.js";import{C as F,a as R}from"./CCardBody-iimbKiZ7.js";import{C as $}from"./CCardHeader-CFnfD6gM.js";import{a as n}from"./index.esm-DSzlmaRN.js";import{C as k,a as A,b as C,c as m,d as B,e as h}from"./CTable-BG2MPlsJ.js";import{C as U,a as D,b as H,c as L}from"./CModalHeader-DX4AicsN.js";import{C as S}from"./CForm-C4rJo8l4.js";import{C as j}from"./CFormInput-LKfVdWds.js";import"./DefaultLayout-BolUaDEE.js";import"./cil-user-Ddrdy7PS.js";import"./CFormControlWrapper-CyzWU6Dg.js";import"./CFormControlValidation-_wBnnnml.js";import"./CFormLabel-CzXD3nfE.js";const _=()=>{const[p,f]=d.useState([]),[u,t]=d.useState(!1),[s,g]=d.useState({id:null,tagName:"",feederName:""}),[N,y]=d.useState(null);d.useEffect(()=>{o()},[]);const o=()=>{i.get(`${c.baseURL}/energy-meters`).then(a=>f(a.data.data)).catch(a=>r("Error fetching energy meters","danger"))},b=()=>{s.id?i.put(`${c.baseURL}/energy-meters/${s.id}`,{tagName:s.tagName,feederName:s.feederName}).then(a=>{o(),t(!1),r(a.data.message,"success")}).catch(a=>r("Error updating energy meter","danger")):i.post(`${c.baseURL}/energy-meters`,{tagName:s.tagName,feederName:s.feederName}).then(a=>{o(),t(!1),r(a.data.message,"success")}).catch(a=>r("Error adding energy meter","danger"))},E=a=>{i.delete(`${c.baseURL}/energy-meters/${a}`).then(l=>{o(),r(l.data.message,"success")}).catch(l=>r("Error deleting energy meter","danger"))},x=(a={id:null,tagName:"",feederName:""})=>{g(a),t(!0)},r=(a,l)=>{y(e.jsx(M,{autohide:5e3,visible:!0,color:l,className:"text-white align-items-center",children:e.jsxs("div",{className:"d-flex",children:[e.jsx(T,{children:a}),e.jsx(v,{className:"me-2 m-auto",white:!0})]})}))};return e.jsxs(e.Fragment,{children:[e.jsx(w,{push:N,placement:"top-center",className:"mt-3"}),e.jsxs(F,{children:[e.jsxs($,{children:[e.jsx("strong",{children:"Energy Meters"}),e.jsx(n,{color:"primary",className:"float-end",onClick:()=>x(),children:"Add New"})]}),e.jsx(R,{children:e.jsxs(k,{hover:!0,responsive:!0,children:[e.jsx(A,{children:e.jsxs(C,{children:[e.jsx(m,{children:"ID"}),e.jsx(m,{children:"Tag Name"}),e.jsx(m,{children:"Feeder Name"}),e.jsx(m,{children:"Actions"})]})}),e.jsx(B,{children:p.map(a=>e.jsxs(C,{children:[e.jsx(h,{children:a.id}),e.jsx(h,{children:a.tagName}),e.jsx(h,{children:a.feederName}),e.jsxs(h,{children:[e.jsx(n,{color:"warning",size:"sm",className:"me-2",onClick:()=>x(a),children:"Edit"}),e.jsx(n,{color:"danger",size:"sm",onClick:()=>E(a.id),children:"Delete"})]})]},a.id))})]})}),e.jsxs(U,{visible:u,onClose:()=>t(!1),children:[e.jsx(D,{children:s.id?"Edit Energy Meter":"Add Energy Meter"}),e.jsx(H,{children:e.jsxs(S,{children:[e.jsx(j,{type:"text",placeholder:"Tag Name",value:s.tagName,onChange:a=>g({...s,tagName:a.target.value}),className:"mb-3"}),e.jsx(j,{type:"text",placeholder:"Feeder Name",value:s.feederName,onChange:a=>g({...s,feederName:a.target.value})})]})}),e.jsxs(L,{children:[e.jsx(n,{color:"secondary",onClick:()=>t(!1),children:"Cancel"}),e.jsx(n,{color:"primary",onClick:b,children:s.id?"Update":"Add"})]})]})]})]})};export{_ as default};
