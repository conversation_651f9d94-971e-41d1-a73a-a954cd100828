import{j as e}from"./index-BDJ8oeCE.js";import{a as u,b as o}from"./DefaultLayout-BolUaDEE.js";import{a as t}from"./index.esm-DSzlmaRN.js";import{C as i,a as s}from"./CRow-C1o2zw34.js";import{C as c,a as n}from"./CCardBody-iimbKiZ7.js";import{C as d}from"./CCardHeader-CFnfD6gM.js";import{C as l}from"./CFormInput-LKfVdWds.js";import{C as m}from"./CForm-C4rJo8l4.js";import{C as r}from"./CFormLabel-CzXD3nfE.js";import{C as h}from"./CFormSelect-B3z3ot4z.js";import{C as a}from"./CFormCheck-CL4oiK6y.js";import{C as x,a as j}from"./CInputGroupText-BGHrT9V9.js";import"./cil-user-Ddrdy7PS.js";import"./CFormControlWrapper-CyzWU6Dg.js";import"./CFormControlValidation-_wBnnnml.js";const R=()=>e.jsxs(i,{children:[e.jsxs(s,{xs:12,children:[e.jsx(u,{href:"forms/layout/"}),e.jsxs(c,{className:"mb-4",children:[e.jsxs(d,{children:[e.jsx("strong",{children:"Layout"})," ",e.jsx("small",{children:"Form grid"})]}),e.jsxs(n,{children:[e.jsx("p",{className:"text-body-secondary small",children:"More complex forms can be built using our grid classes. Use these for form layouts that require multiple columns, varied widths, and additional alignment options."}),e.jsx(o,{href:"forms/layout#form-grid",children:e.jsxs(i,{children:[e.jsx(s,{xs:!0,children:e.jsx(l,{placeholder:"First name","aria-label":"First name"})}),e.jsx(s,{xs:!0,children:e.jsx(l,{placeholder:"Last name","aria-label":"Last name"})})]})})]})]})]}),e.jsx(s,{xs:12,children:e.jsxs(c,{className:"mb-4",children:[e.jsxs(d,{children:[e.jsx("strong",{children:"Layout"})," ",e.jsx("small",{children:"Gutters"})]}),e.jsxs(n,{children:[e.jsxs("p",{className:"text-body-secondary small",children:["By adding ",e.jsx("a",{href:"https://coreui.io/docs/layout/gutters/",children:"gutter modifier classes"}),", you can have control over the gutter width in as well the inline as block direction."]}),e.jsx(o,{href:"forms/layout#gutters",children:e.jsxs(i,{className:"g-3",children:[e.jsx(s,{xs:!0,children:e.jsx(l,{placeholder:"First name","aria-label":"First name"})}),e.jsx(s,{xs:!0,children:e.jsx(l,{placeholder:"Last name","aria-label":"Last name"})})]})}),e.jsx("p",{className:"text-body-secondary small",children:"More complex layouts can also be created with the grid system."}),e.jsx(o,{href:"forms/layout#gutters",children:e.jsxs(m,{className:"row g-3",children:[e.jsxs(s,{md:6,children:[e.jsx(r,{htmlFor:"inputEmail4",children:"Email"}),e.jsx(l,{type:"email",id:"inputEmail4"})]}),e.jsxs(s,{md:6,children:[e.jsx(r,{htmlFor:"inputPassword4",children:"Password"}),e.jsx(l,{type:"password",id:"inputPassword4"})]}),e.jsxs(s,{xs:12,children:[e.jsx(r,{htmlFor:"inputAddress",children:"Address"}),e.jsx(l,{id:"inputAddress",placeholder:"1234 Main St"})]}),e.jsxs(s,{xs:12,children:[e.jsx(r,{htmlFor:"inputAddress2",children:"Address 2"}),e.jsx(l,{id:"inputAddress2",placeholder:"Apartment, studio, or floor"})]}),e.jsxs(s,{md:6,children:[e.jsx(r,{htmlFor:"inputCity",children:"City"}),e.jsx(l,{id:"inputCity"})]}),e.jsxs(s,{md:4,children:[e.jsx(r,{htmlFor:"inputState",children:"State"}),e.jsxs(h,{id:"inputState",children:[e.jsx("option",{children:"Choose..."}),e.jsx("option",{children:"..."})]})]}),e.jsxs(s,{md:2,children:[e.jsx(r,{htmlFor:"inputZip",children:"Zip"}),e.jsx(l,{id:"inputZip"})]}),e.jsx(s,{xs:12,children:e.jsx(a,{type:"checkbox",id:"gridCheck",label:"Check me out"})}),e.jsx(s,{xs:12,children:e.jsx(t,{color:"primary",type:"submit",children:"Sign in"})})]})})]})]})}),e.jsx(s,{xs:12,children:e.jsxs(c,{className:"mb-4",children:[e.jsxs(d,{children:[e.jsx("strong",{children:"Layout"})," ",e.jsx("small",{children:"Horizontal form"})]}),e.jsxs(n,{children:[e.jsxs("p",{className:"text-body-secondary small",children:["Create horizontal forms with the grid by adding the ",e.jsx("code",{children:".row"})," class to form groups and using the ",e.jsx("code",{children:".col-*-*"})," classes to specify the width of your labels and controls. Be sure to add ",e.jsx("code",{children:".col-form-label"})," to your"," ",e.jsx("code",{children:"<CFormLabel>"}),"s as well so they're vertically centered with their associated form controls."]}),e.jsxs("p",{className:"text-body-secondary small",children:["At times, you maybe need to use margin or padding utilities to create that perfect alignment you need. For example, we've removed the ",e.jsx("code",{children:"padding-top"})," on our stacked radio inputs label to better align the text baseline."]}),e.jsx(o,{href:"forms/layout#horizontal-form",children:e.jsxs(m,{children:[e.jsxs(i,{className:"mb-3",children:[e.jsx(r,{htmlFor:"inputEmail3",className:"col-sm-2 col-form-label",children:"Email"}),e.jsx(s,{sm:10,children:e.jsx(l,{type:"email",id:"inputEmail3"})})]}),e.jsxs(i,{className:"mb-3",children:[e.jsx(r,{htmlFor:"inputPassword3",className:"col-sm-2 col-form-label",children:"Password"}),e.jsx(s,{sm:10,children:e.jsx(l,{type:"password",id:"inputPassword3"})})]}),e.jsxs("fieldset",{className:"row mb-3",children:[e.jsx("legend",{className:"col-form-label col-sm-2 pt-0",children:"Radios"}),e.jsxs(s,{sm:10,children:[e.jsx(a,{type:"radio",name:"gridRadios",id:"gridRadios1",value:"option1",label:"First radio",defaultChecked:!0}),e.jsx(a,{type:"radio",name:"gridRadios",id:"gridRadios2",value:"option2",label:"Second radio"}),e.jsx(a,{type:"radio",name:"gridRadios",id:"gridRadios3",value:"option3",label:"Third disabled radio",disabled:!0})]})]}),e.jsx(i,{className:"mb-3",children:e.jsx("div",{className:"col-sm-10 offset-sm-2",children:e.jsx(a,{type:"checkbox",id:"gridCheck1",label:"Example checkbox"})})}),e.jsx(t,{color:"primary",type:"submit",children:"Sign in"})]})})]})]})}),e.jsx(s,{xs:12,children:e.jsxs(c,{className:"mb-4",children:[e.jsxs(d,{children:[e.jsx("strong",{children:"Layout"})," ",e.jsx("small",{children:"Horizontal form label sizing"})]}),e.jsxs(n,{children:[e.jsxs("p",{className:"text-body-secondary small",children:["Be sure to use ",e.jsx("code",{children:".col-form-label-sm"})," or ",e.jsx("code",{children:".col-form-label-lg"})," to your ",e.jsx("code",{children:"<CFormLabel>"}),"s or ",e.jsx("code",{children:"<legend>"}),"s to correctly follow the size of ",e.jsx("code",{children:".form-control-lg"})," and ",e.jsx("code",{children:".form-control-sm"}),"."]}),e.jsxs(o,{href:"forms/layout#horizontal-form-label-sizing",children:[e.jsxs(i,{className:"mb-3",children:[e.jsx(r,{htmlFor:"colFormLabelSm",className:"col-sm-2 col-form-label col-form-label-sm",children:"Email"}),e.jsx(s,{sm:10,children:e.jsx(l,{type:"email",className:"form-control form-control-sm",id:"colFormLabelSm",placeholder:"col-form-label-sm"})})]}),e.jsxs(i,{className:"mb-3",children:[e.jsx(r,{htmlFor:"colFormLabel",className:"col-sm-2 col-form-label",children:"Email"}),e.jsx(s,{sm:10,children:e.jsx(l,{type:"email",id:"colFormLabel",placeholder:"col-form-label"})})]}),e.jsxs(i,{children:[e.jsx(r,{htmlFor:"colFormLabelLg",className:"col-sm-2 col-form-label col-form-label-lg",children:"Email"}),e.jsx(s,{sm:10,children:e.jsx(l,{type:"email",className:"form-control form-control-lg",id:"colFormLabelLg",placeholder:"col-form-label-lg"})})]})]})]})]})}),e.jsx(s,{xs:12,children:e.jsxs(c,{className:"mb-4",children:[e.jsxs(d,{children:[e.jsx("strong",{children:"Layout"})," ",e.jsx("small",{children:"Column sizing"})]}),e.jsxs(n,{children:[e.jsxs("p",{className:"text-body-secondary small",children:["As shown in the previous examples, our grid system allows you to place any number of"," ",e.jsx("code",{children:"<CCol>"}),"s within a ",e.jsx("code",{children:"<CRow>"}),". They'll split the available width equally between them. You may also pick a subset of your columns to take up more or less space, while the remaining ",e.jsx("code",{children:"<CCol>"}),"s equally split the rest, with specific column classes like"," ",e.jsx("code",{children:'<CCol sm="7">'}),"."]}),e.jsx(o,{href:"forms/layout#column-sizing",children:e.jsxs(i,{className:"g-3",children:[e.jsx(s,{sm:7,children:e.jsx(l,{placeholder:"City","aria-label":"City"})}),e.jsx(s,{sm:!0,children:e.jsx(l,{placeholder:"State","aria-label":"State"})}),e.jsx(s,{sm:!0,children:e.jsx(l,{placeholder:"Zip","aria-label":"Zip"})})]})})]})]})}),e.jsx(s,{xs:12,children:e.jsxs(c,{className:"mb-4",children:[e.jsxs(d,{children:[e.jsx("strong",{children:"Layout"})," ",e.jsx("small",{children:"Auto-sizing"})]}),e.jsxs(n,{children:[e.jsxs("p",{className:"text-body-secondary small",children:["The example below uses a flexbox utility to vertically center the contents and changes"," ",e.jsx("code",{children:"<CCol>"})," to ",e.jsx("code",{children:'<CCol xs="auto">'})," so that your columns only take up as much space as needed. Put another way, the column sizes itself based on the contents."]}),e.jsx(o,{href:"forms/layout#auto-sizing",children:e.jsxs(m,{className:"row gy-2 gx-3 align-items-center",children:[e.jsxs(s,{xs:"auto",children:[e.jsx(r,{className:"visually-hidden",htmlFor:"autoSizingInput",children:"Name"}),e.jsx(l,{id:"autoSizingInput",placeholder:"Jane Doe"})]}),e.jsxs(s,{xs:"auto",children:[e.jsx(r,{className:"visually-hidden",htmlFor:"autoSizingInputGroup",children:"Username"}),e.jsxs(x,{children:[e.jsx(j,{children:"@"}),e.jsx(l,{id:"autoSizingInputGroup",placeholder:"Username"})]})]}),e.jsxs(s,{xs:"auto",children:[e.jsx(r,{className:"visually-hidden",htmlFor:"autoSizingSelect",children:"Preference"}),e.jsxs(h,{id:"autoSizingSelect",children:[e.jsx("option",{children:"Choose..."}),e.jsx("option",{value:"1",children:"One"}),e.jsx("option",{value:"2",children:"Two"}),e.jsx("option",{value:"3",children:"Three"})]})]}),e.jsx(s,{xs:"auto",children:e.jsx(a,{type:"checkbox",id:"autoSizingCheck",label:"Remember me"})}),e.jsx(s,{xs:"auto",children:e.jsx(t,{color:"primary",type:"submit",children:"Submit"})})]})}),e.jsx("p",{className:"text-body-secondary small",children:"You can then remix that once again with size-specific column classes."}),e.jsx(o,{href:"forms/layout#auto-sizing",children:e.jsxs(m,{className:"row gx-3 gy-2 align-items-center",children:[e.jsxs(s,{sm:3,children:[e.jsx(r,{className:"visually-hidden",htmlFor:"specificSizeInputName",children:"Name"}),e.jsx(l,{id:"specificSizeInputName",placeholder:"Jane Doe"})]}),e.jsxs(s,{sm:3,children:[e.jsx(r,{className:"visually-hidden",htmlFor:"specificSizeInputGroupUsername",children:"Username"}),e.jsxs(x,{children:[e.jsx(j,{children:"@"}),e.jsx(l,{id:"specificSizeInputGroupUsername",placeholder:"Username"})]})]}),e.jsxs(s,{sm:3,children:[e.jsx(r,{className:"visually-hidden",htmlFor:"specificSizeSelect",children:"Preference"}),e.jsxs(h,{id:"specificSizeSelect",children:[e.jsx("option",{children:"Choose..."}),e.jsx("option",{value:"1",children:"One"}),e.jsx("option",{value:"2",children:"Two"}),e.jsx("option",{value:"3",children:"Three"})]})]}),e.jsx(s,{xs:"auto",children:e.jsx(a,{type:"checkbox",id:"autoSizingCheck2",label:"Remember me"})}),e.jsx(s,{xs:"auto",children:e.jsx(t,{color:"primary",type:"submit",children:"Submit"})})]})})]})]})}),e.jsx(s,{xs:12,children:e.jsxs(c,{className:"mb-4",children:[e.jsxs(d,{children:[e.jsx("strong",{children:"Layout"})," ",e.jsx("small",{children:"Inline forms"})]}),e.jsxs(n,{children:[e.jsxs("p",{className:"text-body-secondary small",children:["Use the ",e.jsx("code",{children:'<CCol xs="auto">'})," class to create horizontal layouts. By adding"," ",e.jsx("a",{href:"https://coreui.io/docs/layout/gutters/",children:"gutter modifier classes"}),", we will have gutters in horizontal and vertical directions. The"," ",e.jsx("code",{children:".align-items-center"})," aligns the form elements to the middle, making the"," ",e.jsx("code",{children:"<CFormCheck>"})," align properly."]}),e.jsx(o,{href:"forms/layout#inline-forms",children:e.jsxs(m,{className:"row row-cols-lg-auto g-3 align-items-center",children:[e.jsxs(s,{xs:12,children:[e.jsx(r,{className:"visually-hidden",htmlFor:"inlineFormInputGroupUsername",children:"Username"}),e.jsxs(x,{children:[e.jsx(j,{children:"@"}),e.jsx(l,{id:"inlineFormInputGroupUsername",placeholder:"Username"})]})]}),e.jsxs(s,{xs:12,children:[e.jsx(r,{className:"visually-hidden",htmlFor:"inlineFormSelectPref",children:"Preference"}),e.jsxs(h,{id:"inlineFormSelectPref",children:[e.jsx("option",{children:"Choose..."}),e.jsx("option",{value:"1",children:"One"}),e.jsx("option",{value:"2",children:"Two"}),e.jsx("option",{value:"3",children:"Three"})]})]}),e.jsx(s,{xs:12,children:e.jsx(a,{type:"checkbox",id:"inlineFormCheck",label:"Remember me"})}),e.jsx(s,{xs:12,children:e.jsx(t,{color:"primary",type:"submit",children:"Submit"})})]})})]})]})})]});export{R as default};
