import{a as o,q as l,v as d,j as e}from"./index-BDJ8oeCE.js";import{a as E,b as N,C as y,c as w}from"./CToaster-DxUpGnuG.js";import{C as D,a as M}from"./CCardBody-iimbKiZ7.js";import{C as R}from"./CCardHeader-CFnfD6gM.js";import{a as i}from"./index.esm-DSzlmaRN.js";import{C as $,a as k,b as C,c as m,d as A,e as h}from"./CTable-BG2MPlsJ.js";import{C as B,a as U,b as F,c as H}from"./CModalHeader-DX4AicsN.js";import{C as S}from"./CForm-C4rJo8l4.js";import{C as j}from"./CFormInput-LKfVdWds.js";import"./DefaultLayout-BolUaDEE.js";import"./cil-user-Ddrdy7PS.js";import"./CFormControlWrapper-CyzWU6Dg.js";import"./CFormControlValidation-_wBnnnml.js";import"./CFormLabel-CzXD3nfE.js";const _=()=>{const[u,f]=o.useState([]),[g,t]=o.useState(!1),[a,p]=o.useState({id:null,name:"",description:""}),[b,L]=o.useState(null);o.useEffect(()=>{n()},[]);const n=()=>{l.get(`${d.baseURL}/locations`).then(s=>f(s.data.data)).catch(s=>r("Error fetching locations","danger"))},T=()=>{a.id?l.put(`${d.baseURL}/locations/${a.id}`,{name:a.name,description:a.description}).then(s=>{n(),t(!1),r(s.data.message,"success")}).catch(s=>r("Error updating location","danger")):l.post(`${d.baseURL}/locations`,{name:a.name,description:a.description}).then(s=>{n(),t(!1),r(s.data.message,"success")}).catch(s=>r("Error adding location","danger"))},v=s=>{l.delete(`${d.baseURL}/locations/${s}`).then(c=>{n(),r(c.data.message,"success")}).catch(c=>r("Error deleting location","danger"))},x=(s={id:null,name:"",description:""})=>{p(s),t(!0)},r=(s,c)=>{L(e.jsx(E,{autohide:5e3,visible:!0,color:c,className:"text-white align-items-center",children:e.jsxs("div",{className:"d-flex",children:[e.jsx(N,{children:s}),e.jsx(y,{className:"me-2 m-auto",white:!0})]})}))};return e.jsxs(e.Fragment,{children:[e.jsx(w,{push:b,placement:"top-center",className:"mt-3"}),e.jsxs(D,{children:[e.jsxs(R,{children:[e.jsx("strong",{children:"Locations"}),e.jsx(i,{color:"primary",className:"float-end",onClick:()=>x(),children:"Add New"})]}),e.jsx(M,{children:e.jsxs($,{hover:!0,responsive:!0,children:[e.jsx(k,{children:e.jsxs(C,{children:[e.jsx(m,{children:"ID"}),e.jsx(m,{children:"Name"}),e.jsx(m,{children:"Description"}),e.jsx(m,{children:"Actions"})]})}),e.jsx(A,{children:u.map(s=>e.jsxs(C,{children:[e.jsx(h,{children:s.id}),e.jsx(h,{children:s.name}),e.jsx(h,{children:s.description}),e.jsxs(h,{children:[e.jsx(i,{color:"warning",size:"sm",className:"me-2",onClick:()=>x(s),children:"Edit"}),e.jsx(i,{color:"danger",size:"sm",onClick:()=>v(s.id),children:"Delete"})]})]},s.id))})]})}),e.jsxs(B,{visible:g,onClose:()=>t(!1),children:[e.jsx(U,{children:a.id?"Edit Location":"Add Location"}),e.jsx(F,{children:e.jsxs(S,{children:[e.jsx(j,{type:"text",placeholder:"Name",value:a.name,onChange:s=>p({...a,name:s.target.value}),className:"mb-3"}),e.jsx(j,{type:"text",placeholder:"Description",value:a.description,onChange:s=>p({...a,description:s.target.value})})]})}),e.jsxs(H,{children:[e.jsx(i,{color:"secondary",onClick:()=>t(!1),children:"Cancel"}),e.jsx(i,{color:"primary",onClick:T,children:a.id?"Update":"Add"})]})]})]})]})};export{_ as default};
