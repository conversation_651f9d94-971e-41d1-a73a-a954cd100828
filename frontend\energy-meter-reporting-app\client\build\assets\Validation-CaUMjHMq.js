import{j as e,a as y}from"./index-BDJ8oeCE.js";import{a as C,b as c}from"./DefaultLayout-BolUaDEE.js";import{a as x}from"./index.esm-DSzlmaRN.js";import{C as g,a as i}from"./CRow-C1o2zw34.js";import{C as h,a as u}from"./CCardBody-iimbKiZ7.js";import{C as m}from"./CCardHeader-CFnfD6gM.js";import{C as j}from"./CForm-C4rJo8l4.js";import{C as t}from"./CFormLabel-CzXD3nfE.js";import{C as a}from"./CFormInput-LKfVdWds.js";import{a as r}from"./CFormControlValidation-_wBnnnml.js";import{C as v,a as f}from"./CInputGroupText-BGHrT9V9.js";import{C as p}from"./CFormSelect-B3z3ot4z.js";import{C as l}from"./CFormCheck-CL4oiK6y.js";import{C as k}from"./CFormTextarea-Cg-QPt4v.js";import"./cil-user-Ddrdy7PS.js";import"./CFormControlWrapper-CyzWU6Dg.js";const F=()=>{const[d,o]=y.useState(!1),n=s=>{s.currentTarget.checkValidity()===!1&&(s.preventDefault(),s.stopPropagation()),o(!0)};return e.jsxs(j,{className:"row g-3 needs-validation",noValidate:!0,validated:d,onSubmit:n,children:[e.jsxs(i,{md:4,children:[e.jsx(t,{htmlFor:"validationCustom01",children:"Email"}),e.jsx(a,{type:"text",id:"validationCustom01",defaultValue:"Mark",required:!0}),e.jsx(r,{valid:!0,children:"Looks good!"})]}),e.jsxs(i,{md:4,children:[e.jsx(t,{htmlFor:"validationCustom02",children:"Email"}),e.jsx(a,{type:"text",id:"validationCustom02",defaultValue:"Otto",required:!0}),e.jsx(r,{valid:!0,children:"Looks good!"})]}),e.jsxs(i,{md:4,children:[e.jsx(t,{htmlFor:"validationCustomUsername",children:"Username"}),e.jsxs(v,{className:"has-validation",children:[e.jsx(f,{id:"inputGroupPrepend",children:"@"}),e.jsx(a,{type:"text",id:"validationCustomUsername",defaultValue:"","aria-describedby":"inputGroupPrepend",required:!0}),e.jsx(r,{invalid:!0,children:"Please choose a username."})]})]}),e.jsxs(i,{md:6,children:[e.jsx(t,{htmlFor:"validationCustom03",children:"City"}),e.jsx(a,{type:"text",id:"validationCustom03",required:!0}),e.jsx(r,{invalid:!0,children:"Please provide a valid city."})]}),e.jsxs(i,{md:3,children:[e.jsx(t,{htmlFor:"validationCustom04",children:"City"}),e.jsxs(p,{id:"validationCustom04",children:[e.jsx("option",{disabled:!0,children:"Choose..."}),e.jsx("option",{children:"..."})]}),e.jsx(r,{invalid:!0,children:"Please provide a valid city."})]}),e.jsxs(i,{md:3,children:[e.jsx(t,{htmlFor:"validationCustom05",children:"City"}),e.jsx(a,{type:"text",id:"validationCustom05",required:!0}),e.jsx(r,{invalid:!0,children:"Please provide a valid zip."})]}),e.jsxs(i,{xs:12,children:[e.jsx(l,{type:"checkbox",id:"invalidCheck",label:"Agree to terms and conditions",required:!0}),e.jsx(r,{invalid:!0,children:"You must agree before submitting."})]}),e.jsx(i,{xs:12,children:e.jsx(x,{color:"primary",type:"submit",children:"Submit form"})})]})},S=()=>{const[d,o]=y.useState(!1),n=s=>{s.currentTarget.checkValidity()===!1&&(s.preventDefault(),s.stopPropagation()),o(!0)};return e.jsxs(j,{className:"row g-3 needs-validation",validated:d,onSubmit:n,children:[e.jsxs(i,{md:4,children:[e.jsx(t,{htmlFor:"validationDefault01",children:"Email"}),e.jsx(a,{type:"text",id:"validationDefault01",defaultValue:"Mark",required:!0}),e.jsx(r,{valid:!0,children:"Looks good!"})]}),e.jsxs(i,{md:4,children:[e.jsx(t,{htmlFor:"validationDefault02",children:"Email"}),e.jsx(a,{type:"text",id:"validationDefault02",defaultValue:"Otto",required:!0}),e.jsx(r,{valid:!0,children:"Looks good!"})]}),e.jsxs(i,{md:4,children:[e.jsx(t,{htmlFor:"validationDefaultUsername",children:"Username"}),e.jsxs(v,{className:"has-validation",children:[e.jsx(f,{id:"inputGroupPrepend02",children:"@"}),e.jsx(a,{type:"text",id:"validationDefaultUsername",defaultValue:"","aria-describedby":"inputGroupPrepend02",required:!0}),e.jsx(r,{invalid:!0,children:"Please choose a username."})]})]}),e.jsxs(i,{md:6,children:[e.jsx(t,{htmlFor:"validationDefault03",children:"City"}),e.jsx(a,{type:"text",id:"validationDefault03",required:!0}),e.jsx(r,{invalid:!0,children:"Please provide a valid city."})]}),e.jsxs(i,{md:3,children:[e.jsx(t,{htmlFor:"validationDefault04",children:"City"}),e.jsxs(p,{id:"validationDefault04",children:[e.jsx("option",{disabled:!0,children:"Choose..."}),e.jsx("option",{children:"..."})]}),e.jsx(r,{invalid:!0,children:"Please provide a valid city."})]}),e.jsxs(i,{md:3,children:[e.jsx(t,{htmlFor:"validationDefault05",children:"City"}),e.jsx(a,{type:"text",id:"validationDefault05",required:!0}),e.jsx(r,{invalid:!0,children:"Please provide a valid zip."})]}),e.jsxs(i,{xs:12,children:[e.jsx(l,{type:"checkbox",id:"invalidCheck",label:"Agree to terms and conditions",required:!0}),e.jsx(r,{invalid:!0,children:"You must agree before submitting."})]}),e.jsx(i,{xs:12,children:e.jsx(x,{color:"primary",type:"submit",children:"Submit form"})})]})},N=()=>{const[d,o]=y.useState(!1),n=s=>{s.currentTarget.checkValidity()===!1&&(s.preventDefault(),s.stopPropagation()),o(!0)};return e.jsxs(j,{className:"row g-3 needs-validation",noValidate:!0,validated:d,onSubmit:n,children:[e.jsxs(i,{md:4,className:"position-relative",children:[e.jsx(t,{htmlFor:"validationTooltip01",children:"Email"}),e.jsx(a,{type:"text",id:"validationTooltip01",defaultValue:"Mark",required:!0}),e.jsx(r,{tooltip:!0,valid:!0,children:"Looks good!"})]}),e.jsxs(i,{md:4,className:"position-relative",children:[e.jsx(t,{htmlFor:"validationTooltip02",children:"Email"}),e.jsx(a,{type:"text",id:"validationTooltip02",defaultValue:"Otto",required:!0}),e.jsx(r,{tooltip:!0,valid:!0,children:"Looks good!"})]}),e.jsxs(i,{md:4,className:"position-relative",children:[e.jsx(t,{htmlFor:"validationTooltipUsername",children:"Username"}),e.jsxs(v,{className:"has-validation",children:[e.jsx(f,{id:"inputGroupPrepend",children:"@"}),e.jsx(a,{type:"text",id:"validationTooltipUsername",defaultValue:"","aria-describedby":"inputGroupPrepend",required:!0}),e.jsx(r,{tooltip:!0,invalid:!0,children:"Please choose a username."})]})]}),e.jsxs(i,{md:6,className:"position-relative",children:[e.jsx(t,{htmlFor:"validationTooltip03",children:"City"}),e.jsx(a,{type:"text",id:"validationTooltip03",required:!0}),e.jsx(r,{tooltip:!0,invalid:!0,children:"Please provide a valid city."})]}),e.jsxs(i,{md:3,className:"position-relative",children:[e.jsx(t,{htmlFor:"validationTooltip04",children:"City"}),e.jsxs(p,{id:"validationTooltip04",required:!0,children:[e.jsx("option",{disabled:!0,defaultValue:"",children:"Choose..."}),e.jsx("option",{children:"..."})]}),e.jsx(r,{tooltip:!0,invalid:!0,children:"Please provide a valid city."})]}),e.jsxs(i,{md:3,className:"position-relative",children:[e.jsx(t,{htmlFor:"validationTooltip05",children:"City"}),e.jsx(a,{type:"text",id:"validationTooltip05",required:!0}),e.jsx(r,{tooltip:!0,invalid:!0,children:"Please provide a valid zip."})]}),e.jsx(i,{xs:12,className:"position-relative",children:e.jsx(x,{color:"primary",type:"submit",children:"Submit form"})})]})},J=()=>e.jsxs(g,{children:[e.jsxs(i,{xs:12,children:[e.jsx(C,{href:"forms/validation/"}),e.jsxs(h,{className:"mb-4",children:[e.jsxs(m,{children:[e.jsx("strong",{children:"Validation"})," ",e.jsx("small",{children:"Custom styles"})]}),e.jsxs(u,{children:[e.jsxs("p",{className:"text-body-secondary small",children:["For custom CoreUI form validation messages, you'll need to add the"," ",e.jsx("code",{children:"noValidate"})," boolean property to your ",e.jsx("code",{children:"<CForm>"}),". This disables the browser default feedback tooltips, but still provides access to the form validation APIs in JavaScript. Try to submit the form below; our JavaScript will intercept the submit button and relay feedback to you. When attempting to submit, you'll see the ",e.jsx("code",{children:":invalid"})," and ",e.jsx("code",{children:":valid"})," styles applied to your form controls."]}),e.jsxs("p",{className:"text-body-secondary small",children:["Custom feedback styles apply custom colors, borders, focus styles, and background icons to better communicate feedback."," "]}),e.jsx(c,{href:"forms/validation",children:F()})]})]})]}),e.jsx(i,{xs:12,children:e.jsxs(h,{className:"mb-4",children:[e.jsxs(m,{children:[e.jsx("strong",{children:"Validation"})," ",e.jsx("small",{children:"Browser defaults"})]}),e.jsxs(u,{children:[e.jsx("p",{className:"text-body-secondary small",children:"Not interested in custom validation feedback messages or writing JavaScript to change form behaviors? All good, you can use the browser defaults. Try submitting the form below. Depending on your browser and OS, you'll see a slightly different style of feedback."}),e.jsx("p",{className:"text-body-secondary small",children:"While these feedback styles cannot be styled with CSS, you can still customize the feedback text through JavaScript."}),e.jsx(c,{href:"forms/validation#browser-defaults",children:S()})]})]})}),e.jsx(i,{xs:12,children:e.jsxs(h,{className:"mb-4",children:[e.jsxs(m,{children:[e.jsx("strong",{children:"Validation"})," ",e.jsx("small",{children:"Server side"})]}),e.jsxs(u,{children:[e.jsxs("p",{className:"text-body-secondary small",children:["We recommend using client-side validation, but in case you require server-side validation, you can indicate invalid and valid form fields with ",e.jsx("code",{children:"invalid"})," ","and ",e.jsx("code",{children:"valid"})," boolean properties."]}),e.jsxs("p",{className:"text-body-secondary small",children:["For invalid fields, ensure that the invalid feedback/error message is associated with the relevant form field using ",e.jsx("code",{children:"aria-describedby"})," (noting that this attribute allows more than one ",e.jsx("code",{children:"id"})," to be referenced, in case the field already points to additional form text)."]}),e.jsx(c,{href:"forms/validation#server-side",children:e.jsxs(j,{className:"row g-3 needs-validation",children:[e.jsxs(i,{md:4,children:[e.jsx(t,{htmlFor:"validationServer01",children:"Email"}),e.jsx(a,{type:"text",id:"validationServer01",defaultValue:"Mark",valid:!0,required:!0}),e.jsx(r,{valid:!0,children:"Looks good!"})]}),e.jsxs(i,{md:4,children:[e.jsx(t,{htmlFor:"validationServer02",children:"Email"}),e.jsx(a,{type:"text",id:"validationServer02",defaultValue:"Otto",valid:!0,required:!0}),e.jsx(r,{valid:!0,children:"Looks good!"})]}),e.jsxs(i,{md:4,children:[e.jsx(t,{htmlFor:"validationServerUsername",children:"Username"}),e.jsxs(v,{className:"has-validation",children:[e.jsx(f,{id:"inputGroupPrepend03",children:"@"}),e.jsx(a,{type:"text",id:"validationServerUsername",defaultValue:"","aria-describedby":"inputGroupPrepend03",invalid:!0,required:!0}),e.jsx(r,{invalid:!0,children:"Please choose a username."})]})]}),e.jsxs(i,{md:6,children:[e.jsx(t,{htmlFor:"validationServer03",children:"City"}),e.jsx(a,{type:"text",id:"validationServer03",invalid:!0,required:!0}),e.jsx(r,{invalid:!0,children:"Please provide a valid city."})]}),e.jsxs(i,{md:3,children:[e.jsx(t,{htmlFor:"validationServer04",children:"City"}),e.jsxs(p,{id:"validationServer04",invalid:!0,children:[e.jsx("option",{disabled:!0,children:"Choose..."}),e.jsx("option",{children:"..."})]}),e.jsx(r,{invalid:!0,children:"Please provide a valid city."})]}),e.jsxs(i,{md:3,children:[e.jsx(t,{htmlFor:"validationServer05",children:"City"}),e.jsx(a,{type:"text",id:"validationServer05",invalid:!0,required:!0}),e.jsx(r,{invalid:!0,children:"Please provide a valid zip."})]}),e.jsxs(i,{xs:12,children:[e.jsx(l,{type:"checkbox",id:"invalidCheck",label:"Agree to terms and conditions",invalid:!0,required:!0}),e.jsx(r,{invalid:!0,children:"You must agree before submitting."})]}),e.jsx(i,{xs:12,children:e.jsx(x,{color:"primary",type:"submit",children:"Submit form"})})]})})]})]})}),e.jsx(i,{xs:12,children:e.jsxs(h,{className:"mb-4",children:[e.jsxs(m,{children:[e.jsx("strong",{children:"Validation"})," ",e.jsx("small",{children:"Supported elements"})]}),e.jsxs(u,{children:[e.jsx("p",{className:"text-body-secondary small",children:"Validation styles are available for the following form controls and components:"}),e.jsxs("ul",{children:[e.jsxs("li",{children:[e.jsx("code",{children:"<CFormInput>"}),"s"]}),e.jsxs("li",{children:[e.jsx("code",{children:"<CFormSelect>"}),"s"]}),e.jsxs("li",{children:[e.jsx("code",{children:"<CFormCheck>"}),"s"]})]}),e.jsx(c,{href:"forms/validation#supported-elements",children:e.jsxs(j,{validated:!0,children:[e.jsxs("div",{className:"mb-3",children:[e.jsx(t,{htmlFor:"validationTextarea",className:"form-label",children:"Textarea"}),e.jsx(k,{id:"validationTextarea",placeholder:"Required example textarea",invalid:!0,required:!0}),e.jsx(r,{invalid:!0,children:"Please enter a message in the textarea."})]}),e.jsx(l,{className:"mb-3",id:"validationFormCheck1",label:"Check this checkbox",required:!0}),e.jsx(r,{invalid:!0,children:"Example invalid feedback text"}),e.jsx(l,{type:"radio",name:"radio-stacked",id:"validationFormCheck2",label:"Check this checkbox",required:!0}),e.jsx(l,{className:"mb-3",type:"radio",name:"radio-stacked",id:"validationFormCheck3",label:"Or toggle this other radio",required:!0}),e.jsx(r,{invalid:!0,children:"More example invalid feedback text"}),e.jsxs("div",{className:"mb-3",children:[e.jsxs(p,{required:!0,"aria-label":"select example",children:[e.jsx("option",{children:"Open this select menu"}),e.jsx("option",{value:"1",children:"One"}),e.jsx("option",{value:"2",children:"Two"}),e.jsx("option",{value:"3",children:"Three"})]}),e.jsx(r,{invalid:!0,children:"Example invalid select feedback"})]}),e.jsxs("div",{className:"mb-3",children:[e.jsx(a,{type:"file",id:"validationTextarea","aria-label":"file example",required:!0}),e.jsx(r,{invalid:!0,children:"Example invalid form file feedback"})]}),e.jsx("div",{className:"mb-3",children:e.jsx(x,{type:"submit",color:"primary",disabled:!0,children:"Submit form"})})]})})]})]})}),e.jsx(i,{xs:12,children:e.jsxs(h,{className:"mb-4",children:[e.jsxs(m,{children:[e.jsx("strong",{children:"Validation"})," ",e.jsx("small",{children:"Tooltips"})]}),e.jsxs(u,{children:[e.jsxs("p",{className:"text-body-secondary small",children:["If your form layout allows it, you can swap the text for the tooltip to display validation feedback in a styled tooltip. Be sure to have a parent with"," ",e.jsx("code",{children:"position: relative"})," on it for tooltip positioning. In the example below, our column classes have this already, but your project may require an alternative setup."]}),e.jsx(c,{href:"forms/validation#tooltips",children:N()})]})]})})]});export{J as default};
