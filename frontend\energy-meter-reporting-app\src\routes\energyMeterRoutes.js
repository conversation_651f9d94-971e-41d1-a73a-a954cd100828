const express = require('express');
const router = express.Router();
const energyMeterController = require('../controllers/energyMeterController');
const { authenticate, authorizeRole } = require('../utils/middleware');

// Define routes

router.post('/emdata',  energyMeterController.getEnergyMeterKWHDataFromDB);
router.post('/', authenticate, authorize<PERSON>ole('SuperAdmin'),energyMeterController.addEnergyMeter);
router.get('/', energyMeterController.getAllEnergyMeters);
router.get('/:id', energyMeterController.getEnergyMeterById);
router.put('/:id', authenticate, authorizeRole('SuperAdmin'),energyMeterController.updateEnergyMeter);
router.delete('/:id',authenticate, authorizeRole('SuperAdmin'), energyMeterController.deleteEnergyMeter);

module.exports = router;