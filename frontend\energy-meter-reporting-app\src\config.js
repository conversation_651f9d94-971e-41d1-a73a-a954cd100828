module.exports = {
    db: {
        server: process.env.DB_HOST || 'localhost',
        user: process.env.DB_USER || 'ewolfsys',
        password: process.env.DB_PASSWORD || 'user@568arp$',
        database: process.env.DB_NAME || 'REPORTS_DB',
        port: process.env.DB_PORT || 1433,
    },
    server: {
        port: process.env.PORT || 3000,
    },
    jwt: {
        secret: process.env.JWT_SECRET || '464561214548421',
        expiresIn: process.env.JWT_EXPIRES_IN || '1h',
    },
    roles: {
        admin: 'Admin',
        viewer: 'Viewer',
    },
};