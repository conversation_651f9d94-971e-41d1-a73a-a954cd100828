const path = require('path');
const { generateIncomerReport, generateShiftReport, generateOutgoingReport, generateAll<PERSON><PERSON>erReport, getIncomerReportData, getShiftReportData, getOutgoingReportData, getAllFeederReportData } = require('../services/reportgenerator');
const { getPlantNameByLocationId } = require('../controllers/locationController');
const { getEnergyMeterIdsByLocationId } = require('../controllers/mappingController');
const { getEnergyMeterandFeederNamesByIds } = require('../controllers/energyMeterController');

// Generate report based on energy meter data
exports.generateReport = async (req, res) => {
    let {
        reportId, // 1 - Incomer, 2 - Shift, 3 - Outgoing, 4 - All Feeder
        reportType, // 1 - Daily, 2 - Monthly, 3 - Yearly
        startTime,
        emIDs,
        emNames,
        plantName,
        locationId,
        userId, // Added userId to uniquely identify the user
        // New variable passed from the API
    } = req.body;

    // If plantname not passed or null then get plantname from locationId,
    //using method from locationController to get from Id
    //if both emIDs and emNames not passed or null get Ids from MappingController by locationId and then emNames from energymeterController.

    if (!plantName) {
        try {
            plantName = await getPlantNameByLocationId(locationId);
        } catch (error) {
            return res.status(400).json({ error: 'Failed to retrieve plant name', details: error.message });
        }
    }

    if ((!emIDs || emIDs.length === 0) || (!emNames || emNames.length === 0)) {
        try {
            emIDs = await getEnergyMeterIdsByLocationId(locationId);
            //console.log(emIDs);
            let tagFeederResult = await getEnergyMeterandFeederNamesByIds(emIDs);
            emIDs = [];
            emNames = [],
                tagFeederResult.forEach(emeter => {
                    emIDs.push(emeter.tagName);
                    emNames.push(emeter.feederName);

                });
            emIDs = emIDs.join(',').replace(/\s+/g, '');
            emNames = emNames.join(',').replace(/\s+/g, '');

        } catch (error) {
            return res.status(400).json({ error: 'Failed to retrieve energy meter data', details: error.message });
        }
    }
    // console.log(emIDs,emNames,plantName);
    reportId = parseInt(reportId, 10);
    reportType = parseInt(reportType, 10);

    //console.log(req.body);

    try {
        const timestamp = Date.now(); // Generate a unique timestamp

        const validateReportType = (reportId, reportType) => {
            const allowedTypes = {
                1: [1, 2, 3], // Incomer: Daily, Monthly, Yearly
                2: [1],       // Shift: Daily
                3: [2, 3],    // Outgoing: Monthly, Yearly
                4: [2]        // All Feeder: Monthly
            };

            if (!allowedTypes[reportId] || !allowedTypes[reportId].includes(reportType)) {
                throw new Error(`Invalid report type ${reportType} for report ID ${reportId}`);
            }
        };

        try {
            validateReportType(reportId, reportType);
        } catch (error) {
            return res.status(400).json({ error: error.message });
        }

        const getReportTypeName = (reportType) => {
            switch (reportType) {
                case 1: return 'Daily';
                case 2: return 'Monthly';
                case 3: return 'Yearly';
                default: throw new Error('Invalid report type');
            }
        };

        const getReportName = (reportId) => {
            switch (reportId) {
                case 1: return 'Incomer';
                case 2: return 'Shift';
                case 3: return 'Outgoing';
                case 4: return 'All_Feeder';
                default: throw new Error('Invalid report type');
            }
        };

        let reportTypeName;
        try {
            reportTypeName = getReportTypeName(reportType);
        } catch (error) {
            return res.status(400).json({ error: error.message });
        }

        let reportName;
        try {
            reportName = getReportName(reportId);
        } catch (error) {
            return res.status(400).json({ error: error.message });
        }

        const fileName = `${reportName}_${reportTypeName}_${timestamp}.xlsx`; // Updated filename

        const filePath = path.join(__dirname, '../../Generated', fileName);

        let templatePath;
        let parameterArray;
        const protectSheet = true;
        const arrayUnlockCells = [];

        switch (reportId) {
            case 1: // Incomer Report
                templatePath = path.join(__dirname, '../../', 'Report_Templates', 'Incomer_Template.xlsx');
                parameterArray = ['RYPHASE_VOL', 'YBPHASE_VOL', 'BRPHASE_VOL', 'RPHASE_CUR', 'YPHASE_CUR', 'BPHASE_CUR', 'CD_KWH', 'AVGPF'];
               // console.log(startTime, emIDs, emNames, parameterArray, plantName, reportType, protectSheet, arrayUnlockCells, filePath, templatePath);
                await generateIncomerReport(startTime, emIDs, emNames, parameterArray, plantName, reportType, protectSheet, arrayUnlockCells, filePath, templatePath);
                break;
            case 2: // Shift Report
                parameterArray = ['CD_KWH', 'AVGPF'];
                templatePath = path.join(__dirname, '../../', 'Report_Templates', 'Shift_Report_Template.xlsx');
                await generateShiftReport(startTime, emIDs, emNames, parameterArray, plantName, reportType, protectSheet, arrayUnlockCells, filePath, templatePath);
                break;
            case 3: // Outgoing Report
                parameterArray = ['CD_KWH'];
                templatePath = path.join(__dirname, '../../', 'Report_Templates', 'Outgoing_Template.xlsx');
                await generateOutgoingReport(startTime, emIDs, emNames, parameterArray, plantName, reportType, protectSheet, arrayUnlockCells, filePath, templatePath);
                break;
            case 4: // All Feeder Report
                parameterArray = ['CD_KWH', 'AVGPF'];
                templatePath = path.join(__dirname, '../../', 'Report_Templates', 'All_Feeder_Template.xlsx');
                await generateAllFeederReport(startTime, emIDs, emNames, parameterArray, plantName, reportType, protectSheet, arrayUnlockCells, filePath, templatePath);
                break;
            default:
                return res.status(400).json({ error: 'Invalid report type' });
        }

        // Send the file for download
        res.download(filePath, fileName, (err) => {
            if (err) {
                console.error('Error sending file:', err);
                res.status(500).json({ error: 'Failed to download report' });
            }
        });
    } catch (error) {
        console.error('Error generating report:', error);
        res.status(500).json({ error: 'Failed to generate report', details: error.message });
    }
};

// Fetch report data and send it as JSON response
exports.getReportData = async (req, res) => {
    let {
        reportId, // 1 - Incomer, 2 - Shift, 3 - Outgoing, 4 - All Feeder
        reportType, // 1 - Daily, 2 - Monthly, 3 - Yearly
        startTime,
        emIDs,
        emNames,
        plantName,
        locationId,
    } = req.body;

    // If plantName is not provided, fetch it using locationId
    if (!plantName) {
        try {
            plantName = await getPlantNameByLocationId(locationId);
        } catch (error) {
            return res.status(400).json({ error: 'Failed to retrieve plant name', details: error.message });
        }
    }

    // If emIDs or emNames are not provided, fetch them using locationId
    if ((!emIDs || emIDs.length === 0) || (!emNames || emNames.length === 0)) {
        try {
            emIDs = await getEnergyMeterIdsByLocationId(locationId);
            const tagFeederResult = await getEnergyMeterandFeederNamesByIds(emIDs);
            emIDs = [];
            emNames = [];
            tagFeederResult.forEach(emeter => {
                emIDs.push(emeter.tagName);
                emNames.push(emeter.feederName);
            });
            emIDs = emIDs.join(',').replace(/\s+/g, '');
            emNames = emNames.join(',').replace(/\s+/g, '');
        } catch (error) {
            return res.status(400).json({ error: 'Failed to retrieve energy meter data', details: error.message });
        }
    }

    reportId = parseInt(reportId, 10);
    reportType = parseInt(reportType, 10);

    try {
        const validateReportType = (reportId, reportType) => {
            const allowedTypes = {
                1: [1, 2, 3], // Incomer: Daily, Monthly, Yearly
                2: [1],       // Shift: Daily
                3: [2, 3],    // Outgoing: Monthly, Yearly
                4: [2]        // All Feeder: Monthly
            };

            if (!allowedTypes[reportId] || !allowedTypes[reportId].includes(reportType)) {
                throw new Error(`Invalid report type ${reportType} for report ID ${reportId}`);
            }
        };

        validateReportType(reportId, reportType);

        const getReportName = (reportId) => {
            switch (reportId) {
                case 1: return 'Incomer';
                case 2: return 'Shift';
                case 3: return 'Outgoing';
                case 4: return 'All Feeder';
                default: throw new Error('Invalid report type');
            }
        };

        const reportName = getReportName(reportId);

        let reportData;
        switch (reportId) {
            case 1: // Incomer Report
                reportData = await getIncomerReportData(startTime, emIDs, emNames, ['RYPHASE_VOL', 'YBPHASE_VOL', 'BRPHASE_VOL', 'RPHASE_CUR', 'YPHASE_CUR', 'BPHASE_CUR', 'CD_KWH', 'AVGPF'], plantName, reportType);
                break;
            case 2: // Shift Report
                reportData = await getShiftReportData(startTime, emIDs, emNames, ['CD_KWH', 'AVGPF'], plantName, reportType);
                break;
            case 3: // Outgoing Report
                reportData = await getOutgoingReportData(startTime, emIDs, emNames, ['CD_KWH'], plantName, reportType);
                break;
            case 4: // All Feeder Report
                reportData = await getAllFeederReportData(startTime, emIDs, emNames, ['CD_KWH', 'AVGPF'], plantName, reportType);
                break;
            default:
                return res.status(400).json({ error: 'Invalid report type' });
        }

        // Send the report data as JSON response
        res.status(200).json({
            success: true,
            message: 'Report data fetched successfully',
            data: reportData
        });
    } catch (error) {
        console.error('Error fetching report data:', error);
        res.status(500).json({ error: 'Failed to fetch report data', details: error.message });
    }
};

