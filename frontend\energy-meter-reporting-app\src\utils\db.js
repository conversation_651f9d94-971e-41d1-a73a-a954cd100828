const sql = require('mssql');
const { Sequelize } = require('sequelize');
const config = require('../config');

const poolPromise = new sql.ConnectionPool({
    ...config.db,
    options: {
        encrypt: true, // Use encryption
        trustServerCertificate: true // Trust self-signed certificate
    },
})
    .connect()
    .then(pool => {
        console.log('Connected to MSSQL');
        return pool;
    })
    .catch(err => console.log('Database connection failed: ', err));

const sequelize = new Sequelize(config.db.database, config.db.user, config.db.password, {
    host: config.db.server,
    dialect: 'mssql'
});

const executeQuery = async (query) => {
    try {
        const pool = await poolPromise;
        const result = await pool.request().query(query);
        return result.recordset;
    } catch (err) {
        console.error('SQL error: ', err);
        throw err;
    }
};

module.exports = {
    executeQuery,
    sequelize
};