const express = require('express');
const bodyParser = require('body-parser');
const session = require('express-session');
const db = require('./utils/db');
const authRoutes = require('./routes/authRoutes');
const energyMeterRoutes = require('./routes/energyMeterRoutes');
const locationRoutes = require('./routes/locationRoutes');
const mappingRoutes = require('./routes/mappingRoutes');
const reportRoutes = require('./routes/reportRoutes');
const manageuserRoutes =require('./routes/manageuserRoutes');
const middleware = require('./utils/middleware');
const cookieParser = require('cookie-parser')
const cors = require('cors');
const app = express();


const corsOptions = {
    origin: 'http://localhost:3001',
    optionsSuccessStatus: 200, // Some legacy browsers (IE11, various SmartTVs) choke on 204,
    credentials: true,
};

const PORT = process.env.PORT || 3000;


// Use the cors middleware with options
app.use(cors(corsOptions));

// Your other middleware and routes
app.use(express.json());

// Middleware
app.use(cookieParser());
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: true }));
app.use(session({
    secret: 'recfr4645w4f64564fww54',
    resave: false,
    saveUninitialized: true,
}));

// Database connection
// db.connect(); // Remove this line

// Ensure routes are functions

if (typeof authRoutes !== 'function') throw new TypeError('authRoutes is not a function');
if (typeof energyMeterRoutes !== 'function') throw new TypeError('energyMeterRoutes is not a function');
if (typeof locationRoutes !== 'function') throw new TypeError('locationRoutes is not a function');
if (typeof mappingRoutes !== 'function') throw new TypeError('mappingRoutes is not a function');
if (typeof reportRoutes !== 'function') throw new TypeError('reportRoutes is not a function');

// Serve static files
const path = require('path');
app.use(express.static(path.join(__dirname, '../client/build')));


// Routes
app.use('/api/auth', authRoutes);
app.use('/api/energy-meters', energyMeterRoutes);
app.use('/api/locations', locationRoutes);
app.use('/api/mappings', mappingRoutes);
app.use('/api/reports', reportRoutes);
app.use('/api/manageusers', manageuserRoutes);

app.get('*', (req, res) => {
    res.sendFile(path.join(__dirname, '../client/build', 'index.html'));
});

// Error handling middleware
app.use(middleware.errorHandler);

// Start the server
app.listen(PORT, () => {
    console.log(`Server is running on http://localhost:${PORT}`);
});