import{a as v,_ as L,R as G,b as q,c as W,P as y,j as e}from"./index-BDJ8oeCE.js";import{a as B,b as a,d as T,e as u,f as b}from"./DefaultLayout-BolUaDEE.js";import{C as I,a as h}from"./index.esm-DSzlmaRN.js";import{R as c}from"./react-DOh6XfOk.js";import{C as p,a as l}from"./CRow-C1o2zw34.js";import{C as s,a as r}from"./CCardBody-iimbKiZ7.js";import{C as n}from"./CCardHeader-CFnfD6gM.js";import{C as o,a as i,b as t}from"./CCardTitle-Cc00wz7L.js";import{C as f,a as j}from"./CListGroupItem-sk_b22Qj.js";import{C as x}from"./CCardFooter-DKzDUSkb.js";import{C as S}from"./CCardGroup-s55qtq2U.js";import"./cil-user-Ddrdy7PS.js";var g=v.forwardRef(function(d,m){var w=d.children,C=d.className,N=L(d,["children","className"]);return G.createElement(I,q({className:W("card-link",C)},N,{ref:m}),w)});g.propTypes={children:y.node,className:y.string};g.displayName="CCardLink";var k=v.forwardRef(function(d,m){var w=d.children,C=d.as,N=C===void 0?"h6":C,U=d.className,H=L(d,["children","as","className"]);return G.createElement(N,q({className:W("card-subtitle",U)},H,{ref:m}),w)});k.propTypes={as:y.elementType,children:y.node,className:y.string};k.displayName="CCardSubtitle";const Q=()=>e.jsxs(p,{children:[e.jsxs(l,{xs:12,children:[e.jsx(B,{href:"components/card/"}),e.jsxs(s,{className:"mb-4",children:[e.jsxs(n,{children:[e.jsx("strong",{children:"Card"})," ",e.jsx("small",{children:"Example"})]}),e.jsxs(r,{children:[e.jsxs("p",{className:"text-body-secondary small",children:["Cards are built with as little markup and styles as possible but still manage to deliver a bunch of control and customization. Built with flexbox, they offer easy alignment and mix well with other CoreUI components. Cards have no top, left, and right margins by default, so use"," ",e.jsx("a",{href:"https://coreui.io/docs/utilities/spacing",children:"spacing utilities"})," as needed. They have no fixed width to start, so they'll fill the full width of its parent."]}),e.jsx("p",{className:"text-body-secondary small",children:"Below is an example of a basic card with mixed content and a fixed width. Cards have no fixed width to start, so they'll naturally fill the full width of its parent element."}),e.jsx(a,{href:"components/card",children:e.jsxs(s,{style:{width:"18rem"},children:[e.jsx(o,{orientation:"top",src:c}),e.jsxs(r,{children:[e.jsx(i,{children:"Card title"}),e.jsx(t,{children:"Some quick example text to build on the card title and make up the bulk of the card's content."}),e.jsx(h,{color:"primary",href:"#",children:"Go somewhere"})]})]})})]})]})]}),e.jsx(l,{xs:12,children:e.jsxs(s,{className:"mb-4",children:[e.jsxs(n,{children:[e.jsx("strong",{children:"Card"})," ",e.jsx("small",{children:"Body"})]}),e.jsxs(r,{children:[e.jsxs("p",{className:"text-body-secondary small",children:["The main block of a card is the ",e.jsx("code",{children:"<CCardBody>"}),". Use it whenever you need a padded section within a card."]}),e.jsx(a,{href:"components/card/#body",children:e.jsx(s,{children:e.jsx(r,{children:"This is some text within a card body."})})})]})]})}),e.jsx(l,{xs:12,children:e.jsxs(s,{className:"mb-4",children:[e.jsxs(n,{children:[e.jsx("strong",{children:"Card"})," ",e.jsx("small",{children:"Titles, text, and links"})]}),e.jsxs(r,{children:[e.jsxs("p",{className:"text-body-secondary small",children:["Card titles are managed by ",e.jsx("code",{children:"<CCardTitle>"})," component. Identically, links are attached and collected next to each other by ",e.jsx("code",{children:"<CCardLink>"})," ","component."]}),e.jsxs("p",{className:"text-body-secondary small",children:["Subtitles are managed by ",e.jsx("code",{children:"<CCardSubtitle>"})," component. If the"," ",e.jsx("code",{children:"<CCardTitle>"})," also, the ",e.jsx("code",{children:"<CCardSubtitle>"})," items are stored in a ",e.jsx("code",{children:"<CCardBody>"})," item, the card title, and subtitle are arranged rightly."]}),e.jsx(a,{href:"components/card/#titles-text-and-links",children:e.jsx(s,{style:{width:"18rem"},children:e.jsxs(r,{children:[e.jsx(i,{children:"Card title"}),e.jsx(k,{className:"mb-2 text-body-secondary",children:"Card subtitle"}),e.jsx(t,{children:"Some quick example text to build on the card title and make up the bulk of the card's content."}),e.jsx(g,{href:"#",children:"Card link"}),e.jsx(g,{href:"#",children:"Another link"})]})})})]})]})}),e.jsx(l,{xs:12,children:e.jsxs(s,{className:"mb-4",children:[e.jsxs(n,{children:[e.jsx("strong",{children:"Card"})," ",e.jsx("small",{children:"Images"})]}),e.jsxs(r,{children:[e.jsxs("p",{className:"text-body-secondary small",children:[e.jsx("code",{children:".card-img-top"})," places a picture to the top of the card. With"," ",e.jsx("code",{children:".card-text"}),", text can be added to the card. Text within"," ",e.jsx("code",{children:".card-text"})," can additionally be styled with the regular HTML tags."]}),e.jsx(a,{href:"components/card/#images",children:e.jsxs(s,{style:{width:"18rem"},children:[e.jsx(o,{orientation:"top",src:c}),e.jsx(r,{children:e.jsx(t,{children:"Some quick example text to build on the card title and make up the bulk of the card's content."})})]})})]})]})}),e.jsx(l,{xs:12,children:e.jsxs(s,{className:"mb-4",children:[e.jsxs(n,{children:[e.jsx("strong",{children:"Card"})," ",e.jsx("small",{children:"List groups"})]}),e.jsxs(r,{children:[e.jsx("p",{className:"text-body-secondary small",children:"Create lists of content in a card with a flush list group."}),e.jsx(a,{href:"components/card/#list-groups",children:e.jsxs(p,{children:[e.jsx(l,{lg:4,children:e.jsx(s,{children:e.jsxs(f,{flush:!0,children:[e.jsx(j,{children:"Cras justo odio"}),e.jsx(j,{children:"Dapibus ac facilisis in"}),e.jsx(j,{children:"Vestibulum at eros"})]})})}),e.jsx(l,{lg:4,children:e.jsxs(s,{children:[e.jsx(n,{children:"Header"}),e.jsxs(f,{flush:!0,children:[e.jsx(j,{children:"Cras justo odio"}),e.jsx(j,{children:"Dapibus ac facilisis in"}),e.jsx(j,{children:"Vestibulum at eros"})]})]})}),e.jsx(l,{lg:4,children:e.jsxs(s,{children:[e.jsxs(f,{flush:!0,children:[e.jsx(j,{children:"Cras justo odio"}),e.jsx(j,{children:"Dapibus ac facilisis in"}),e.jsx(j,{children:"Vestibulum at eros"})]}),e.jsx(x,{children:"Footer"})]})})]})})]})]})}),e.jsx(l,{xs:12,children:e.jsxs(s,{className:"mb-4",children:[e.jsxs(n,{children:[e.jsx("strong",{children:"Card"})," ",e.jsx("small",{children:"Kitchen sink"})]}),e.jsxs(r,{children:[e.jsx("p",{className:"text-body-secondary small",children:"Combine and match many content types to build the card you need, or throw everything in there. Shown below are image styles, blocks, text styles, and a list group—all wrapped in a fixed-width card."}),e.jsx(a,{href:"components/card/#kitchen-sink",children:e.jsxs(s,{style:{width:"18rem"},children:[e.jsx(o,{orientation:"top",src:c}),e.jsxs(r,{children:[e.jsx(i,{children:"Card title"}),e.jsx(t,{children:"Some quick example text to build on the card title and make up the bulk of the card's content."})]}),e.jsxs(f,{flush:!0,children:[e.jsx(j,{children:"Cras justo odio"}),e.jsx(j,{children:"Dapibus ac facilisis in"}),e.jsx(j,{children:"Vestibulum at eros"})]}),e.jsxs(r,{children:[e.jsx(g,{href:"#",children:"Card link"}),e.jsx(g,{href:"#",children:"Another link"})]})]})})]})]})}),e.jsx(l,{xs:12,children:e.jsxs(s,{className:"mb-4",children:[e.jsxs(n,{children:[e.jsx("strong",{children:"Card"})," ",e.jsx("small",{children:"Header and footer"})]}),e.jsxs(r,{children:[e.jsx("p",{className:"text-body-secondary small",children:"Add an optional header and/or footer within a card."}),e.jsx(a,{href:"components/card/#header-and-footer",children:e.jsxs(s,{children:[e.jsx(n,{children:"Header"}),e.jsxs(r,{children:[e.jsx(i,{children:"Special title treatment"}),e.jsx(t,{children:"With supporting text below as a natural lead-in to additional content."}),e.jsx(h,{color:"primary",href:"#",children:"Go somewhere"})]})]})}),e.jsxs("p",{className:"text-body-secondary small",children:["Card headers can be styled by adding ex. ",e.jsx("code",{children:'as="h5"'}),"."]}),e.jsx(a,{href:"components/card/#header-and-footer",children:e.jsxs(s,{children:[e.jsx(n,{as:"h5",children:"Header"}),e.jsxs(r,{children:[e.jsx(i,{children:"Special title treatment"}),e.jsx(t,{children:"With supporting text below as a natural lead-in to additional content."}),e.jsx(h,{color:"primary",href:"#",children:"Go somewhere"})]})]})}),e.jsx(a,{href:"components/card/#header-and-footer",children:e.jsxs(s,{children:[e.jsx(n,{children:"Quote"}),e.jsx(r,{children:e.jsxs("blockquote",{className:"blockquote mb-0",children:[e.jsx("p",{children:"Lorem ipsum dolor sit amet, consectetur adipiscing elit. Integer posuere erat a ante."}),e.jsxs("footer",{className:"blockquote-footer",children:["Someone famous in ",e.jsx("cite",{title:"Source Title",children:"Source Title"})]})]})})]})}),e.jsx(a,{href:"components/card/#header-and-footer",children:e.jsxs(s,{className:"text-center",children:[e.jsx(n,{children:"Header"}),e.jsxs(r,{children:[e.jsx(i,{children:"Special title treatment"}),e.jsx(t,{children:"With supporting text below as a natural lead-in to additional content."}),e.jsx(h,{color:"primary",href:"#",children:"Go somewhere"})]}),e.jsx(x,{className:"text-body-secondary",children:"2 days ago"})]})})]})]})}),e.jsx(l,{xs:12,children:e.jsxs(s,{className:"mb-4",children:[e.jsxs(n,{children:[e.jsx("strong",{children:"Card"})," ",e.jsx("small",{children:"Body"})]}),e.jsxs(r,{children:[e.jsxs("p",{className:"text-body-secondary small",children:["Cards assume no specific ",e.jsx("code",{children:"width"})," to start, so they'll be 100% wide unless otherwise stated. You can adjust this as required with custom CSS, grid classes, grid Sass mixins, or services."]}),e.jsx("h3",{children:"Using grid markup"}),e.jsx("p",{className:"text-body-secondary small",children:"Using the grid, wrap cards in columns and rows as needed."}),e.jsx(a,{href:"components/card/#sizing",children:e.jsxs(p,{children:[e.jsx(l,{sm:6,children:e.jsx(s,{children:e.jsxs(r,{children:[e.jsx(i,{children:"Special title treatment"}),e.jsx(t,{children:"With supporting text below as a natural lead-in to additional content."}),e.jsx(h,{color:"primary",href:"#",children:"Go somewhere"})]})})}),e.jsx(l,{sm:6,children:e.jsx(s,{children:e.jsxs(r,{children:[e.jsx(i,{children:"Special title treatment"}),e.jsx(t,{children:"With supporting text below as a natural lead-in to additional content."}),e.jsx(h,{color:"primary",href:"#",children:"Go somewhere"})]})})})]})}),e.jsx("h3",{children:"Using utilities"}),e.jsxs("p",{className:"text-body-secondary small",children:["Use some of"," ",e.jsx("a",{href:"https://coreui.io/docs/utilities/sizing/",children:"available sizing utilities"})," to rapidly set a card's width."]}),e.jsxs(a,{href:"components/card/#sizing",children:[e.jsx(s,{className:"w-75",children:e.jsxs(r,{children:[e.jsx(i,{children:"Card title"}),e.jsx(t,{children:"With supporting text below as a natural lead-in to additional content."}),e.jsx(h,{color:"primary",href:"#",children:"Go somewhere"})]})}),e.jsx(s,{className:"w-50",children:e.jsxs(r,{children:[e.jsx(i,{children:"Card title"}),e.jsx(t,{children:"With supporting text below as a natural lead-in to additional content."}),e.jsx(h,{color:"primary",href:"#",children:"Go somewhere"})]})})]}),e.jsx("strong",{children:"Using custom CSS"}),e.jsx("p",{className:"text-body-secondary small",children:"Use custom CSS in your stylesheets or as inline styles to set a width."}),e.jsx(a,{href:"components/card/#sizing",children:e.jsx(s,{style:{width:"18rem"},children:e.jsxs(r,{children:[e.jsx(i,{children:"Special title treatment"}),e.jsx(t,{children:"With supporting text below as a natural lead-in to additional content."}),e.jsx(h,{color:"primary",href:"#",children:"Go somewhere"})]})})})]})]})}),e.jsx(l,{xs:12,children:e.jsxs(s,{className:"mb-4",children:[e.jsxs(n,{children:[e.jsx("strong",{children:"Card"})," ",e.jsx("small",{children:"Text alignment"})]}),e.jsxs(r,{children:[e.jsxs("p",{className:"text-body-secondary small",children:["You can instantly change the text arrangement of any card—in its whole or specific parts—with"," ",e.jsx("a",{href:"https://coreui.io/docs/utilities/text/#text-alignment",children:"text align classes"}),"."]}),e.jsxs(a,{href:"components/card/#text-alignment",children:[e.jsx(s,{style:{width:"18rem"},children:e.jsxs(r,{children:[e.jsx(i,{children:"Special title treatment"}),e.jsx(t,{children:"With supporting text below as a natural lead-in to additional content."}),e.jsx(h,{color:"primary",href:"#",children:"Go somewhere"})]})}),e.jsx(s,{className:"text-center",style:{width:"18rem"},children:e.jsxs(r,{children:[e.jsx(i,{children:"Special title treatment"}),e.jsx(t,{children:"With supporting text below as a natural lead-in to additional content."}),e.jsx(h,{color:"primary",href:"#",children:"Go somewhere"})]})}),e.jsx(s,{className:"text-end",style:{width:"18rem"},children:e.jsxs(r,{children:[e.jsx(i,{children:"Special title treatment"}),e.jsx(t,{children:"With supporting text below as a natural lead-in to additional content."}),e.jsx(h,{color:"primary",href:"#",children:"Go somewhere"})]})})]})]})]})}),e.jsx(l,{xs:12,children:e.jsxs(s,{className:"mb-4",children:[e.jsxs(n,{children:[e.jsx("strong",{children:"Card"})," ",e.jsx("small",{children:"Navigation"})]}),e.jsxs(r,{children:[e.jsxs("p",{className:"text-body-secondary small",children:["Add some navigation to a ",e.jsx("code",{children:"<CCardHeader>"})," with our"," ",e.jsx("code",{children:"<CNav>"})," component."]}),e.jsx(a,{href:"components/card/##navigation",children:e.jsxs(s,{className:"text-center",children:[e.jsx(n,{children:e.jsxs(T,{variant:"tabs",className:"card-header-tabs",children:[e.jsx(u,{children:e.jsx(b,{href:"#",active:!0,children:"Active"})}),e.jsx(u,{children:e.jsx(b,{href:"#",children:"Link"})}),e.jsx(u,{children:e.jsx(b,{href:"#",disabled:!0,children:"Disabled"})})]})}),e.jsxs(r,{children:[e.jsx(i,{children:"Special title treatment"}),e.jsx(t,{children:"With supporting text below as a natural lead-in to additional content."}),e.jsx(h,{color:"primary",href:"#",children:"Go somewhere"})]})]})}),e.jsx(a,{href:"components/card/##navigation",children:e.jsxs(s,{className:"text-center",children:[e.jsx(n,{children:e.jsxs(T,{variant:"pills",className:"card-header-pills",children:[e.jsx(u,{children:e.jsx(b,{href:"#",active:!0,children:"Active"})}),e.jsx(u,{children:e.jsx(b,{href:"#",children:"Link"})}),e.jsx(u,{children:e.jsx(b,{href:"#",disabled:!0,children:"Disabled"})})]})}),e.jsxs(r,{children:[e.jsx(i,{children:"Special title treatment"}),e.jsx(t,{children:"With supporting text below as a natural lead-in to additional content."}),e.jsx(h,{color:"primary",href:"#",children:"Go somewhere"})]})]})})]})]})}),e.jsx(l,{xs:12,children:e.jsxs(s,{className:"mb-4",children:[e.jsxs(n,{children:[e.jsx("strong",{children:"Card"})," ",e.jsx("small",{children:"Image caps"})]}),e.jsxs(r,{children:[e.jsx("p",{className:"text-body-secondary small",children:'Similar to headers and footers, cards can include top and bottom "image caps"—images at the top or bottom of a card.'}),e.jsx(a,{href:"components/card/#image-caps",children:e.jsxs(p,{children:[e.jsx(l,{lg:6,children:e.jsxs(s,{className:"mb-3",children:[e.jsx(o,{orientation:"top",src:c}),e.jsxs(r,{children:[e.jsx(i,{children:"Card title"}),e.jsx(t,{children:"This is a wider card with supporting text below as a natural lead-in to additional content. This content is a little bit longer."}),e.jsx(t,{children:e.jsx("small",{className:"text-body-secondary",children:"Last updated 3 mins ago"})})]})]})}),e.jsx(l,{lg:6,children:e.jsxs(s,{className:"mb-3",children:[e.jsxs(r,{children:[e.jsx(i,{children:"Card title"}),e.jsx(t,{children:"This is a wider card with supporting text below as a natural lead-in to additional content. This content is a little bit longer."}),e.jsx(t,{children:e.jsx("small",{className:"text-body-secondary",children:"Last updated 3 mins ago"})})]}),e.jsx(o,{orientation:"bottom",src:c})]})})]})})]})]})}),e.jsx(l,{xs:12,children:e.jsxs(s,{className:"mb-4",children:[e.jsxs(n,{children:[e.jsx("strong",{children:"Card"})," ",e.jsx("small",{children:"Card styles"})]}),e.jsxs(r,{children:[e.jsx("p",{className:"text-body-secondary small",children:"Cards include various options for customizing their backgrounds, borders, and color."}),e.jsx("h3",{children:"Background and color"}),e.jsxs("p",{className:"text-body-secondary small",children:["Use ",e.jsx("code",{children:"color"})," property to change the appearance of a card."]}),e.jsx(a,{href:"components/card/#background-and-color",children:e.jsx(p,{children:[{color:"primary",textColor:"white"},{color:"secondary",textColor:"white"},{color:"success",textColor:"white"},{color:"danger",textColor:"white"},{color:"warning"},{color:"info",textColor:"white"},{color:"light"},{color:"dark",textColor:"white"}].map((d,m)=>e.jsx(l,{lg:4,children:e.jsxs(s,{color:d.color,textColor:d.textColor,className:"mb-3",children:[e.jsx(n,{children:"Header"}),e.jsxs(r,{children:[e.jsxs(i,{children:[d.color," card title"]}),e.jsx(t,{children:"Some quick example text to build on the card title and make up the bulk of the card's content."})]})]})},m))})}),e.jsx("h3",{children:"Border"}),e.jsxs("p",{className:"text-body-secondary small",children:["Use ",e.jsx("a",{href:"https://coreui.io/docs/utilities/borders/",children:"border utilities"})," to change just the ",e.jsx("code",{children:"border-color"})," of a card. Note that you can set"," ",e.jsx("code",{children:"textColor"})," property on the ",e.jsx("code",{children:"<CCard>"})," or a subset of the card's contents as shown below."]}),e.jsx(a,{href:"components/card/#border",children:e.jsx(p,{children:[{color:"primary",textColor:"primary"},{color:"secondary",textColor:"secondary"},{color:"success",textColor:"success"},{color:"danger",textColor:"danger"},{color:"warning",textColor:"warning"},{color:"info",textColor:"info"},{color:"light"},{color:"dark"}].map((d,m)=>e.jsx(l,{lg:4,children:e.jsxs(s,{textColor:d.textColor,className:`mb-3 border-${d.color}`,children:[e.jsx(n,{children:"Header"}),e.jsxs(r,{children:[e.jsxs(i,{children:[d.color," card title"]}),e.jsx(t,{children:"Some quick example text to build on the card title and make up the bulk of the card's content."})]})]})},m))})}),e.jsx("h3",{children:"Top border"}),e.jsxs("p",{className:"text-body-secondary small",children:["Use ",e.jsx("a",{href:"https://coreui.io/docs/utilities/borders/",children:"border utilities"})," to change just the ",e.jsx("code",{children:"border-color"})," of a card. Note that you can set"," ",e.jsx("code",{children:"textColor"})," property on the ",e.jsx("code",{children:"<CCard>"})," or a subset of the card's contents as shown below."]}),e.jsx(a,{href:"components/card/#top-border",children:e.jsx(p,{children:[{color:"primary",textColor:"primary"},{color:"secondary",textColor:"secondary"},{color:"success",textColor:"success"},{color:"danger",textColor:"danger"},{color:"warning",textColor:"warning"},{color:"info",textColor:"info"},{color:"light"},{color:"dark"}].map((d,m)=>e.jsx(l,{lg:4,children:e.jsxs(s,{textColor:d.textColor,className:`mb-3 border-top-${d.color} border-top-3`,children:[e.jsx(n,{children:"Header"}),e.jsxs(r,{children:[e.jsxs(i,{children:[d.color," card title"]}),e.jsx(t,{children:"Some quick example text to build on the card title and make up the bulk of the card's content."})]})]})},m))})})]})]})}),e.jsx(l,{xs:12,children:e.jsxs(s,{className:"mb-4",children:[e.jsxs(n,{children:[e.jsx("strong",{children:"Card"})," ",e.jsx("small",{children:"Card groups"})]}),e.jsxs(r,{children:[e.jsxs("p",{className:"text-body-secondary small",children:["Use card groups to render cards as a single, attached element with equal width and height columns. Card groups start off stacked and use ",e.jsx("code",{children:"display: flex;"})," to become attached with uniform dimensions starting at the ",e.jsx("code",{children:"sm"})," breakpoint."]}),e.jsx(a,{href:"components/card/#card-groups",children:e.jsxs(S,{children:[e.jsxs(s,{children:[e.jsx(o,{orientation:"top",src:c}),e.jsxs(r,{children:[e.jsx(i,{children:"Card title"}),e.jsx(t,{children:"This is a wider card with supporting text below as a natural lead-in to additional content. This content is a little bit longer."}),e.jsx(t,{children:e.jsx("small",{className:"text-body-secondary",children:"Last updated 3 mins ago"})})]})]}),e.jsxs(s,{children:[e.jsx(o,{orientation:"top",src:c}),e.jsxs(r,{children:[e.jsx(i,{children:"Card title"}),e.jsx(t,{children:"This card has supporting text below as a natural lead-in to additional content."}),e.jsx(t,{children:e.jsx("small",{className:"text-body-secondary",children:"Last updated 3 mins ago"})})]})]}),e.jsxs(s,{children:[e.jsx(o,{orientation:"top",src:c}),e.jsxs(r,{children:[e.jsx(i,{children:"Card title"}),e.jsx(t,{children:"This is a wider card with supporting text below as a natural lead-in to additional content. This card has even longer content than the first to show that equal height action."}),e.jsx(t,{children:e.jsx("small",{className:"text-body-secondary",children:"Last updated 3 mins ago"})})]})]})]})}),e.jsx("p",{className:"text-body-secondary small",children:"When using card groups with footers, their content will automatically line up."}),e.jsx(a,{href:"components/card/#card-groups",children:e.jsxs(S,{children:[e.jsxs(s,{children:[e.jsx(o,{orientation:"top",src:c}),e.jsxs(r,{children:[e.jsx(i,{children:"Card title"}),e.jsx(t,{children:"This is a wider card with supporting text below as a natural lead-in to additional content. This content is a little bit longer."})]}),e.jsx(x,{children:e.jsx("small",{className:"text-body-secondary",children:"Last updated 3 mins ago"})})]}),e.jsxs(s,{children:[e.jsx(o,{orientation:"top",src:c}),e.jsxs(r,{children:[e.jsx(i,{children:"Card title"}),e.jsx(t,{children:"This card has supporting text below as a natural lead-in to additional content."})]}),e.jsx(x,{children:e.jsx("small",{className:"text-body-secondary",children:"Last updated 3 mins ago"})})]}),e.jsxs(s,{children:[e.jsx(o,{orientation:"top",src:c}),e.jsxs(r,{children:[e.jsx(i,{children:"Card title"}),e.jsx(t,{children:"This is a wider card with supporting text below as a natural lead-in to additional content. This card has even longer content than the first to show that equal height action."})]}),e.jsx(x,{children:e.jsx("small",{className:"text-body-secondary",children:"Last updated 3 mins ago"})})]})]})})]})]})}),e.jsx(l,{xs:12,children:e.jsxs(s,{className:"mb-4",children:[e.jsxs(n,{children:[e.jsx("strong",{children:"Card"})," ",e.jsx("small",{children:"Grid cards"})]}),e.jsxs(r,{children:[e.jsxs("p",{className:"text-body-secondary small",children:["Use the ",e.jsx("code",{children:"CRow"})," component and set"," ",e.jsx("code",{children:"{xs|sm|md|lg|xl|xxl}={{ cols: * }}"})," property to control how many grid columns (wrapped around your cards) you show per row. For example, here's ",e.jsx("code",{children:"xs={{cols: 1}}"})," laying out the cards on one column, and ",e.jsx("code",{children:"md={{cols: 1}}"})," splitting four cards to equal width across multiple rows, from the medium breakpoint up."]}),e.jsx(a,{href:"components/card/#grid-cards",children:e.jsxs(p,{xs:{cols:1,gutter:4},md:{cols:2},children:[e.jsx(l,{xs:!0,children:e.jsxs(s,{children:[e.jsx(o,{orientation:"top",src:c}),e.jsxs(r,{children:[e.jsx(i,{children:"Card title"}),e.jsx(t,{children:"This is a wider card with supporting text below as a natural lead-in to additional content. This content is a little bit longer."})]}),e.jsx(x,{children:e.jsx("small",{className:"text-body-secondary",children:"Last updated 3 mins ago"})})]})}),e.jsx(l,{xs:!0,children:e.jsxs(s,{children:[e.jsx(o,{orientation:"top",src:c}),e.jsxs(r,{children:[e.jsx(i,{children:"Card title"}),e.jsx(t,{children:"This is a wider card with supporting text below as a natural lead-in to additional content. This content is a little bit longer."})]}),e.jsx(x,{children:e.jsx("small",{className:"text-body-secondary",children:"Last updated 3 mins ago"})})]})}),e.jsx(l,{xs:!0,children:e.jsxs(s,{children:[e.jsx(o,{orientation:"top",src:c}),e.jsxs(r,{children:[e.jsx(i,{children:"Card title"}),e.jsx(t,{children:"This is a wider card with supporting text below as a natural lead-in to additional content. This content is a little bit longer."})]}),e.jsx(x,{children:e.jsx("small",{className:"text-body-secondary",children:"Last updated 3 mins ago"})})]})}),e.jsx(l,{xs:!0,children:e.jsxs(s,{children:[e.jsx(o,{orientation:"top",src:c}),e.jsxs(r,{children:[e.jsx(i,{children:"Card title"}),e.jsx(t,{children:"This is a wider card with supporting text below as a natural lead-in to additional content. This content is a little bit longer."})]}),e.jsx(x,{children:e.jsx("small",{className:"text-body-secondary",children:"Last updated 3 mins ago"})})]})})]})}),e.jsxs("p",{className:"text-body-secondary small",children:["Change it to ",e.jsx("code",{children:"md={{ cols: 3}}"})," and you'll see the fourth card wrap."]}),e.jsx(a,{href:"components/card/#grid-cards",children:e.jsxs(p,{xs:{cols:1,gutter:4},md:{cols:3},children:[e.jsx(l,{xs:!0,children:e.jsxs(s,{children:[e.jsx(o,{orientation:"top",src:c}),e.jsxs(r,{children:[e.jsx(i,{children:"Card title"}),e.jsx(t,{children:"This is a wider card with supporting text below as a natural lead-in to additional content. This content is a little bit longer."})]}),e.jsx(x,{children:e.jsx("small",{className:"text-body-secondary",children:"Last updated 3 mins ago"})})]})}),e.jsx(l,{xs:!0,children:e.jsxs(s,{children:[e.jsx(o,{orientation:"top",src:c}),e.jsxs(r,{children:[e.jsx(i,{children:"Card title"}),e.jsx(t,{children:"This is a wider card with supporting text below as a natural lead-in to additional content. This content is a little bit longer."})]}),e.jsx(x,{children:e.jsx("small",{className:"text-body-secondary",children:"Last updated 3 mins ago"})})]})}),e.jsx(l,{xs:!0,children:e.jsxs(s,{children:[e.jsx(o,{orientation:"top",src:c}),e.jsxs(r,{children:[e.jsx(i,{children:"Card title"}),e.jsx(t,{children:"This is a wider card with supporting text below as a natural lead-in to additional content. This content is a little bit longer."})]}),e.jsx(x,{children:e.jsx("small",{className:"text-body-secondary",children:"Last updated 3 mins ago"})})]})}),e.jsx(l,{xs:!0,children:e.jsxs(s,{children:[e.jsx(o,{orientation:"top",src:c}),e.jsxs(r,{children:[e.jsx(i,{children:"Card title"}),e.jsx(t,{children:"This is a wider card with supporting text below as a natural lead-in to additional content. This content is a little bit longer."})]}),e.jsx(x,{children:e.jsx("small",{className:"text-body-secondary",children:"Last updated 3 mins ago"})})]})})]})})]})]})})]});export{Q as default};
