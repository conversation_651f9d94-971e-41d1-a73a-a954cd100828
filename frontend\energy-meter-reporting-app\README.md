# Energy Meter Reporting Application

## Overview
The Energy Meter Reporting Application is a web-based application designed to manage and report energy meter data. It allows users to add energy meters, manage locations, and generate reports based on the collected data.

## Features
- User authentication with role-based access control (Admin and Viewer roles).
- Admins can add and map energy meters and locations.
- Viewers can generate reports based on energy meter data.
- Responsive and user-friendly interface built with HTML, CSS, and JavaScript.


## Installation
1. Clone the repository:
   ```
   git clone <repository-url>
   cd energy-meter-reporting-app
   ```

2. Install dependencies:
   ```
   npm install
   ```

3. Set up the database:
   - Ensure that your MSSQL server is running and accessible.
   - Create the necessary tables as per the provided schema.

4. Configure environment variables:
   - Create a `.env` file in the root directory and add your database connection details and other sensitive information.

## Usage
1. Start the application:
   ```
   npm start
   ```

2. Access the application in your web browser at `http://localhost:3000`.

3. Use the login page to authenticate as an Admin or Viewer.

## Contributing
Contributions are welcome! Please submit a pull request or open an issue for any enhancements or bug fixes.

## License
This project is licensed under the MIT License. See the LICENSE file for details.