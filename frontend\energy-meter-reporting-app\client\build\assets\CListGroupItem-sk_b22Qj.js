import{a as p,_ as h,R as f,b as t,c as b,P as r,d as g}from"./index-BDJ8oeCE.js";import{C}from"./index.esm-DSzlmaRN.js";var v=p.forwardRef(function(e,c){var a,d=e.children,o=e.as,i=o===void 0?"ul":o,s=e.className,u=e.flush,l=e.layout,n=h(e,["children","as","className","flush","layout"]);return f.createElement(i,t({className:b("list-group",(a={"list-group-flush":u},a["list-group-".concat(l)]=l,a),s)},n,{ref:c}),d)});v.propTypes={as:r.elementType,children:r.node,className:r.string,flush:r.bool,layout:r.oneOf(["horizontal","horizontal-sm","horizontal-md","horizontal-lg","horizontal-xl","horizontal-xxl"])};v.displayName="CListGroup";var N=p.forwardRef(function(e,c){var a,d=e.children,o=e.active,i=e.as,s=i===void 0?"li":i,u=e.className,l=e.disabled,n=e.color,m=h(e,["children","active","as","className","disabled","color"]),y=s==="a"||s==="button"?C:s;return m=t(t(t(t({},(s==="a"||s==="button")&&{active:o,disabled:l,as:s,ref:c}),o&&{"aria-current":!0}),l&&{"aria-disabled":!0}),m),f.createElement(y,t({className:b("list-group-item",(a={},a["list-group-item-".concat(n)]=n,a["list-group-item-action"]=s==="a"||s==="button",a.active=o,a.disabled=l,a),u)},m),d)});N.propTypes={active:r.bool,as:r.elementType,children:r.node,className:r.string,color:g,disabled:r.bool};N.displayName="CListGroupItem";export{v as C,N as a};
