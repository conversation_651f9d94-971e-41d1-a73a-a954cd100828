import{j as a}from"./index-BDJ8oeCE.js";import{b as s,c as e,a as r}from"./index.esm-DSzlmaRN.js";import{C as o,a as t}from"./CRow-C1o2zw34.js";import{C as i,a as n}from"./CInputGroupText-BGHrT9V9.js";import{c as l}from"./cil-magnifying-glass-COgjSVTM.js";import{C as c}from"./CFormInput-LKfVdWds.js";import"./CFormControlWrapper-CyzWU6Dg.js";import"./CFormControlValidation-_wBnnnml.js";import"./CFormLabel-CzXD3nfE.js";const y=()=>a.jsx("div",{className:"bg-body-tertiary min-vh-100 d-flex flex-row align-items-center",children:a.jsx(s,{children:a.jsx(o,{className:"justify-content-center",children:a.jsxs(t,{md:6,children:[a.jsxs("span",{className:"clearfix",children:[a.jsx("h1",{className:"float-start display-3 me-4",children:"500"}),a.jsx("h4",{className:"pt-3",children:"Houston, we have a problem!"}),a.jsx("p",{className:"text-body-secondary float-start",children:"The page you are looking for is temporarily unavailable."})]}),a.jsxs(i,{className:"input-prepend",children:[a.jsx(n,{children:a.jsx(e,{icon:l})}),a.jsx(c,{type:"text",placeholder:"What are you looking for?"}),a.jsx(r,{color:"info",children:"Search"})]})]})})})});export{y as default};
