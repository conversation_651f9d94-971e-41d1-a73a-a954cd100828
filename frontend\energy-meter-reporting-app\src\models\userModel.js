const { DataTypes } = require('sequelize');
const { sequelize } = require('../utils/db');

const User = sequelize.define('User', {
    username: {
        type: DataTypes.STRING,
        allowNull: false,
        unique: true
    },
    password: {
        type: DataTypes.STRING,
        allowNull: false
    },
    role: {
        type: DataTypes.ENUM('Admin', 'Viewer'),
        defaultValue: 'Viewer'
    },
    createdAt: {
        type: DataTypes.DATE,
        defaultValue: sequelize.literal('GETDATE()')  // ✅ Uses MSSQL's GETDATE()
    }
}, {
    timestamps: false
});

module.exports = User;