import{a as p,_ as m,R as u,b as v,c as d,P as s}from"./index-BDJ8oeCE.js";var t=p.forwardRef(function(e,c){var r,o=e.children,i=e.className,a=e.size,l=e.vertical,n=m(e,["children","className","size","vertical"]);return u.createElement("div",v({className:d(l?"btn-group-vertical":"btn-group",(r={},r["btn-group-".concat(a)]=a,r),i)},n,{ref:c}),o)});t.propTypes={children:s.node,className:s.string,size:s.oneOf(["sm","lg"]),vertical:s.bool};t.displayName="CButtonGroup";export{t as C};
