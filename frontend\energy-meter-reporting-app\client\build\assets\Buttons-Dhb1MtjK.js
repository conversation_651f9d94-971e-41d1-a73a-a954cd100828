import{j as s}from"./index-BDJ8oeCE.js";import{a as e,c as x}from"./index.esm-DSzlmaRN.js";import{a as u,b as o}from"./DefaultLayout-BolUaDEE.js";import{C as h,a as r}from"./CRow-C1o2zw34.js";import{C as l,a}from"./CCardBody-iimbKiZ7.js";import{C as c}from"./CCardHeader-CFnfD6gM.js";import{c as m}from"./cil-bell-BJbb-OTi.js";import"./cil-user-Ddrdy7PS.js";const k=()=>s.jsxs(h,{children:[s.jsxs(r,{xs:12,children:[s.jsx(u,{href:"components/buttons/"}),s.jsxs(l,{className:"mb-4",children:[s.jsx(c,{children:s.jsx("strong",{children:"React Button"})}),s.jsxs(a,{children:[s.jsx("p",{className:"text-body-secondary small",children:"CoreUI includes a bunch of predefined buttons components, each serving its own semantic purpose. Buttons show what action will happen when the user clicks or touches it. CoreUI buttons are used to initialize operations, both in the background or foreground of an experience."}),s.jsx(o,{href:"components/buttons",children:["normal","active","disabled"].map((n,i)=>s.jsxs(h,{className:"align-items-center mb-3",children:[s.jsx(r,{xs:12,xl:2,className:"mb-3 mb-xl-0",children:n.charAt(0).toUpperCase()+n.slice(1)}),s.jsxs(r,{xs:!0,children:[["primary","secondary","success","danger","warning","info","light","dark"].map((t,d)=>s.jsx(e,{color:t,active:n==="active",disabled:n==="disabled",children:t.charAt(0).toUpperCase()+t.slice(1)},d)),s.jsx(e,{color:"link",children:"Link"})]})]},i))})]})]})]}),s.jsx(r,{xs:12,children:s.jsxs(l,{className:"mb-4",children:[s.jsxs(c,{children:[s.jsx("strong",{children:"React Button"})," ",s.jsx("small",{children:"with icons"})]}),s.jsxs(a,{children:[s.jsxs("p",{className:"text-body-secondary small",children:["You can combine button with our ",s.jsx("a",{href:"https://coreui.io/icons/",children:"CoreUI Icons"}),"."]}),s.jsx(o,{href:"components/buttons",children:["normal","active","disabled"].map((n,i)=>s.jsxs(h,{className:"align-items-center mb-3",children:[s.jsx(r,{xs:12,xl:2,className:"mb-3 mb-xl-0",children:n.charAt(0).toUpperCase()+n.slice(1)}),s.jsxs(r,{xs:!0,children:[["primary","secondary","success","danger","warning","info","light","dark"].map((t,d)=>s.jsxs(e,{color:t,active:n==="active",disabled:n==="disabled",children:[s.jsx(x,{icon:m,className:"me-2"}),t.charAt(0).toUpperCase()+t.slice(1)]},d)),s.jsxs(e,{color:"link",children:[s.jsx(x,{icon:m,className:"me-2"}),"Link"]})]})]},i))})]})]})}),s.jsx(r,{xs:12,children:s.jsxs(l,{className:"mb-4",children:[s.jsxs(c,{children:[s.jsx("strong",{children:"React Button"})," ",s.jsx("small",{children:"Button components"})]}),s.jsxs(a,{children:[s.jsxs("p",{className:"text-body-secondary small",children:["The ",s.jsx("code",{children:"<CButton>"})," component are designed for"," ",s.jsx("code",{children:"<button>"})," , ",s.jsx("code",{children:"<a>"})," or ",s.jsx("code",{children:"<input>"})," ","elements (though some browsers may apply a slightly different rendering)."]}),s.jsxs("p",{className:"text-body-secondary small",children:["If you're using ",s.jsx("code",{children:"<CButton>"})," component as ",s.jsx("code",{children:"<a>"})," ","elements that are used to trigger functionality ex. collapsing content, these links should be given a ",s.jsx("code",{children:'role="button"'})," to adequately communicate their meaning to assistive technologies such as screen readers."]}),s.jsxs(o,{href:"components/buttons#button-components",children:[s.jsx(e,{as:"a",color:"primary",href:"#",role:"button",children:"Link"}),s.jsx(e,{type:"submit",color:"primary",children:"Button"}),s.jsx(e,{as:"input",type:"button",color:"primary",value:"Input"}),s.jsx(e,{as:"input",type:"submit",color:"primary",value:"Submit"}),s.jsx(e,{as:"input",type:"reset",color:"primary",value:"Reset"})]})]})]})}),s.jsx(r,{xs:12,children:s.jsxs(l,{className:"mb-4",children:[s.jsxs(c,{children:[s.jsx("strong",{children:"React Button"})," ",s.jsx("small",{children:"outline"})]}),s.jsxs(a,{children:[s.jsxs("p",{className:"text-body-secondary small",children:["If you need a button, but without the strong background colors. Set"," ",s.jsx("code",{children:'variant="outline"'})," prop to remove all background colors."]}),s.jsx(o,{href:"components/buttons#outline-buttons",children:["normal","active","disabled"].map((n,i)=>s.jsxs(h,{className:"align-items-center mb-3",children:[s.jsx(r,{xs:12,xl:2,className:"mb-3 mb-xl-0",children:n.charAt(0).toUpperCase()+n.slice(1)}),s.jsx(r,{xs:!0,children:["primary","secondary","success","danger","warning","info","light","dark"].map((t,d)=>s.jsx(e,{color:t,variant:"outline",active:n==="active",disabled:n==="disabled",children:t.charAt(0).toUpperCase()+t.slice(1)},d))})]},i))})]})]})}),s.jsx(r,{xs:12,children:s.jsxs(l,{className:"mb-4",children:[s.jsxs(c,{children:[s.jsx("strong",{children:"React Button"})," ",s.jsx("small",{children:"ghost"})]}),s.jsxs(a,{children:[s.jsxs("p",{className:"text-body-secondary small",children:["If you need a ghost variant of button, set ",s.jsx("code",{children:'variant="ghost"'})," prop to remove all background colors."]}),s.jsx(o,{href:"components/buttons#ghost-buttons",children:["normal","active","disabled"].map((n,i)=>s.jsxs(h,{className:"align-items-center mb-3",children:[s.jsx(r,{xs:12,xl:2,className:"mb-3 mb-xl-0",children:n.charAt(0).toUpperCase()+n.slice(1)}),s.jsx(r,{xs:!0,children:["primary","secondary","success","danger","warning","info","light","dark"].map((t,d)=>s.jsx(e,{color:t,variant:"ghost",active:n==="active",disabled:n==="disabled",children:t.charAt(0).toUpperCase()+t.slice(1)},d))})]},i))})]})]})}),s.jsx(r,{xs:12,children:s.jsxs(l,{className:"mb-4",children:[s.jsxs(c,{children:[s.jsx("strong",{children:"React Button"})," ",s.jsx("small",{children:"Sizes"})]}),s.jsxs(a,{children:[s.jsxs("p",{className:"text-body-secondary small",children:["Larger or smaller buttons? Add ",s.jsx("code",{children:'size="lg"'})," or"," ",s.jsx("code",{children:'size="sm"'})," for additional sizes."]}),s.jsxs(o,{href:"components/buttons#sizes",children:[s.jsx(e,{color:"primary",size:"lg",children:"Large button"}),s.jsx(e,{color:"secondary",size:"lg",children:"Large button"})]}),s.jsxs(o,{href:"components/buttons#sizes",children:[s.jsx(e,{color:"primary",size:"sm",children:"Small button"}),s.jsx(e,{color:"secondary",size:"sm",children:"Small button"})]})]})]})}),s.jsx(r,{xs:12,children:s.jsxs(l,{className:"mb-4",children:[s.jsxs(c,{children:[s.jsx("strong",{children:"React Button"})," ",s.jsx("small",{children:"Pill"})]}),s.jsx(a,{children:s.jsx(o,{href:"components/buttons#pill-buttons",children:["primary","secondary","success","danger","warning","info","light","dark"].map((n,i)=>s.jsx(e,{color:n,shape:"rounded-pill",children:n.charAt(0).toUpperCase()+n.slice(1)},i))})})]})}),s.jsx(r,{xs:12,children:s.jsxs(l,{className:"mb-4",children:[s.jsxs(c,{children:[s.jsx("strong",{children:"React Button"})," ",s.jsx("small",{children:"Square"})]}),s.jsx(a,{children:s.jsx(o,{href:"components/buttons#square",children:["primary","secondary","success","danger","warning","info","light","dark"].map((n,i)=>s.jsx(e,{color:n,shape:"rounded-0",children:n.charAt(0).toUpperCase()+n.slice(1)},i))})})]})}),s.jsx(r,{xs:12,children:s.jsxs(l,{className:"mb-4",children:[s.jsxs(c,{children:[s.jsx("strong",{children:"React Button"})," ",s.jsx("small",{children:"Disabled state"})]}),s.jsxs(a,{children:[s.jsxs("p",{className:"text-body-secondary small",children:["Add the ",s.jsx("code",{children:"disabled"})," boolean prop to any ",s.jsx("code",{children:"<CButton>"})," ","component to make buttons look inactive. Disabled button has"," ",s.jsx("code",{children:"pointer-events: none"})," applied to, disabling hover and active states from triggering."]}),s.jsxs(o,{href:"components/buttons#disabled-state",children:[s.jsx(e,{color:"primary",size:"lg",disabled:!0,children:"Primary button"}),s.jsx(e,{color:"secondary",size:"lg",disabled:!0,children:"Button"})]}),s.jsxs("p",{className:"text-body-secondary small",children:["Disabled buttons using the ",s.jsx("code",{children:"<a>"})," component act a little different:"]}),s.jsxs("p",{className:"text-body-secondary small",children:[s.jsx("code",{children:"<a>"}),"s don'tsupport the ",s.jsx("code",{children:"disabled"})," attribute, so CoreUI has to add ",s.jsx("code",{children:".disabled"})," className to make buttons look inactive. CoreUI also has to add to the disabled button component"," ",s.jsx("code",{children:'aria-disabled="true"'})," attribute to show the state of the component to assistive technologies."]}),s.jsxs(o,{href:"components/buttons#disabled-state",children:[s.jsx(e,{as:"a",href:"#",color:"primary",size:"lg",disabled:!0,children:"Primary link"}),s.jsx(e,{as:"a",href:"#",color:"secondary",size:"lg",disabled:!0,children:"Link"})]})]})]})}),s.jsx(r,{xs:12,children:s.jsxs(l,{className:"mb-4",children:[s.jsxs(c,{children:[s.jsx("strong",{children:"React Button"})," ",s.jsx("small",{children:"Block buttons"})]}),s.jsxs(a,{children:[s.jsx("p",{className:"text-body-secondary small",children:"Create buttons that span the full width of a parent—by using utilities."}),s.jsx(o,{href:"components/buttons#block-buttons",children:s.jsxs("div",{className:"d-grid gap-2",children:[s.jsx(e,{color:"primary",children:"Button"}),s.jsx(e,{color:"primary",children:"Button"})]})}),s.jsxs("p",{className:"text-body-secondary small",children:["Here we create a responsive variation, starting with vertically stacked buttons until the ",s.jsx("code",{children:"md"})," breakpoint, where ",s.jsx("code",{children:".d-md-block"})," replaces the"," ",s.jsx("code",{children:".d-grid"})," class, thus nullifying the ",s.jsx("code",{children:"gap-2"})," utility. Resize your browser to see them change."]}),s.jsx(o,{href:"components/buttons#block-buttons",children:s.jsxs("div",{className:"d-grid gap-2 d-md-block",children:[s.jsx(e,{color:"primary",children:"Button"}),s.jsx(e,{color:"primary",children:"Button"})]})}),s.jsxs("p",{className:"text-body-secondary small",children:['You can adjust the width of your block buttons with grid column width classes. For example, for a half-width "block button", use ',s.jsx("code",{children:".col-6"}),". Center it horizontally with ",s.jsx("code",{children:".mx-auto"}),", too."]}),s.jsx(o,{href:"components/buttons#block-buttons",children:s.jsxs("div",{className:"d-grid gap-2 col-6 mx-auto",children:[s.jsx(e,{color:"primary",children:"Button"}),s.jsx(e,{color:"primary",children:"Button"})]})}),s.jsx("p",{className:"text-body-secondary small",children:"Additional utilities can be used to adjust the alignment of buttons when horizontal. Here we've taken our previous responsive example and added some flex utilities and a margin utility on the button to right align the buttons when they're no longer stacked."}),s.jsx(o,{href:"components/buttons#block-buttons",children:s.jsxs("div",{className:"d-grid gap-2 d-md-flex justify-content-md-end",children:[s.jsx(e,{color:"primary",className:"me-md-2",children:"Button"}),s.jsx(e,{color:"primary",children:"Button"})]})})]})]})})]});export{k as default};
