const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./Dashboard-CSWFqJqc.js","./index-BDJ8oeCE.js","./index-0R2xuYuJ.css","./Colors-ChMP3dxt.js","./index.esm-DSzlmaRN.js","./CCardBody-iimbKiZ7.js","./CCardHeader-CFnfD6gM.js","./CRow-C1o2zw34.js","./cil-user-Ddrdy7PS.js","./Typography-1JhztNZn.js","./Accordion-dcjJFzKa.js","./CCollapse-vuYQ3vPL.js","./Breadcrumbs-Cu2_herO.js","./Cards-CniHNcCG.js","./react-DOh6XfOk.js","./CCardTitle-Cc00wz7L.js","./CListGroupItem-sk_b22Qj.js","./CCardFooter-DKzDUSkb.js","./CCardGroup-s55qtq2U.js","./Carousels-DUdGWsYK.js","./Collapses-Dl171kQS.js","./ListGroups-DSRI2x7_.js","./CFormCheck-CL4oiK6y.js","./CFormControlValidation-_wBnnnml.js","./CFormLabel-CzXD3nfE.js","./Navs-D1wvwi7g.js","./Paginations-DZHvFOvv.js","./Placeholders-DeaUn_Ru.js","./Popovers-DacycEyH.js","./CPopover-DsLOhH2M.js","./getRTLPlacement-BTVXPs3l.js","./getTransitionDurationFromElement-Cpu4p4hx.js","./Progress-D8_LEqs4.js","./CProgress-CgQiCfdj.js","./Spinners-I70k7UMS.js","./Tabs-BdcyedAU.js","./Tables-CJSPl4YE.js","./CTable-BG2MPlsJ.js","./Tooltips-BFBUv_j7.js","./CTooltip-CKesHgCe.js","./Buttons-Dhb1MtjK.js","./cil-bell-BJbb-OTi.js","./ButtonGroups-Chr2KOpP.js","./CButtonGroup-DjNOigk5.js","./CInputGroupText-BGHrT9V9.js","./CFormInput-LKfVdWds.js","./CFormControlWrapper-CyzWU6Dg.js","./CDropdownDivider-DVoRzmnU.js","./Dropdowns-DPpf5yDu.js","./ChecksRadios-D7DsE6L6.js","./FloatingLabels-CujwugcH.js","./CFormTextarea-Cg-QPt4v.js","./CFormSelect-B3z3ot4z.js","./FormControl-BIzpcQSk.js","./CForm-C4rJo8l4.js","./InputGroup-qHiF8Jc0.js","./Layout-BCq8b_mV.js","./Range-DLmeyKqg.js","./Select-D4guXORx.js","./Validation-CaUMjHMq.js","./Charts-DDOjm_TY.js","./index.esm-CMTGkdDj.js","./CoreUIIcons-F_gPw1i5.js","./cil-user-follow--bbLhwpj.js","./cil-lock-locked-DmxpJbVL.js","./cil-magnifying-glass-COgjSVTM.js","./Brands-CCROc7v2.js","./cib-twitter-CkoJaElQ.js","./Flags-C0QiyoVo.js","./Alerts-X1-6Wce8.js","./Badges-CHGirAJD.js","./Modals-Bk1C2UqN.js","./CModalHeader-DX4AicsN.js","./Toasts-CNu6ahi0.js","./CToaster-DxUpGnuG.js","./Widgets-DSNH8gOR.js","./EnergyMeters-CzD6vkmU.js","./Locations-CDW5y2tq.js","./Mappings-CjCKNWV9.js","./Reports-BPZEoZmh.js","./Reports-CMY8WBAt.css","./Users-Av0TM49y.js","./Login--rsbMHaO.js"])))=>i.map(i=>d[i]);
import{r as zn,g as jt,a as g,R as p,_ as z,b as _,c as M,P as u,t as rn,s as nn,d as St,p as Vn,e as In,f as O,u as Wn,j as f,h as Bn,i as yr,N as Hn,C as qn,k as Fn,l as an,m as Tt,n as on,o as Ue}from"./index-BDJ8oeCE.js";import{C as tt,a as $n,b as Rt,c as H}from"./index.esm-DSzlmaRN.js";import{c as Zn}from"./cil-user-Ddrdy7PS.js";var Dt=zn();const Ge=jt(Dt);function Pe(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return g.useMemo(function(){return e.every(function(r){return r==null})?null:function(r){e.forEach(function(n){Xn(n,r)})}},e)}function Xn(e,t){if(e!=null)if(Un(e))e(t);else try{e.current=t}catch{throw new Error('Cannot assign value "'.concat(t,'" to ref "').concat(e,'"'))}}function Un(e){return!!(e&&{}.toString.call(e)=="[object Function]")}var U="top",re="bottom",ne="right",G="left",Pt="auto",$e=[U,re,ne,G],ke="start",qe="end",Gn="clippingParents",sn="viewport",ze="popper",Yn="reference",Cr=$e.reduce(function(e,t){return e.concat([t+"-"+ke,t+"-"+qe])},[]),ln=[].concat($e,[Pt]).reduce(function(e,t){return e.concat([t,t+"-"+ke,t+"-"+qe])},[]),Kn="beforeRead",Qn="read",Jn="afterRead",ei="beforeMain",ti="main",ri="afterMain",ni="beforeWrite",ii="write",ai="afterWrite",oi=[Kn,Qn,Jn,ei,ti,ri,ni,ii,ai];function ce(e){return e?(e.nodeName||"").toLowerCase():null}function J(e){if(e==null)return window;if(e.toString()!=="[object Window]"){var t=e.ownerDocument;return t&&t.defaultView||window}return e}function Ne(e){var t=J(e).Element;return e instanceof t||e instanceof Element}function te(e){var t=J(e).HTMLElement;return e instanceof t||e instanceof HTMLElement}function Mt(e){if(typeof ShadowRoot>"u")return!1;var t=J(e).ShadowRoot;return e instanceof t||e instanceof ShadowRoot}function si(e){var t=e.state;Object.keys(t.elements).forEach(function(r){var n=t.styles[r]||{},i=t.attributes[r]||{},a=t.elements[r];!te(a)||!ce(a)||(Object.assign(a.style,n),Object.keys(i).forEach(function(o){var s=i[o];s===!1?a.removeAttribute(o):a.setAttribute(o,s===!0?"":s)}))})}function li(e){var t=e.state,r={popper:{position:t.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(t.elements.popper.style,r.popper),t.styles=r,t.elements.arrow&&Object.assign(t.elements.arrow.style,r.arrow),function(){Object.keys(t.elements).forEach(function(n){var i=t.elements[n],a=t.attributes[n]||{},o=Object.keys(t.styles.hasOwnProperty(n)?t.styles[n]:r[n]),s=o.reduce(function(l,c){return l[c]="",l},{});!te(i)||!ce(i)||(Object.assign(i.style,s),Object.keys(a).forEach(function(l){i.removeAttribute(l)}))})}}const ci={name:"applyStyles",enabled:!0,phase:"write",fn:si,effect:li,requires:["computeStyles"]};function le(e){return e.split("-")[0]}var we=Math.max,Je=Math.min,Le=Math.round;function At(){var e=navigator.userAgentData;return e!=null&&e.brands&&Array.isArray(e.brands)?e.brands.map(function(t){return t.brand+"/"+t.version}).join(" "):navigator.userAgent}function cn(){return!/^((?!chrome|android).)*safari/i.test(At())}function je(e,t,r){t===void 0&&(t=!1),r===void 0&&(r=!1);var n=e.getBoundingClientRect(),i=1,a=1;t&&te(e)&&(i=e.offsetWidth>0&&Le(n.width)/e.offsetWidth||1,a=e.offsetHeight>0&&Le(n.height)/e.offsetHeight||1);var o=Ne(e)?J(e):window,s=o.visualViewport,l=!cn()&&r,c=(n.left+(l&&s?s.offsetLeft:0))/i,d=(n.top+(l&&s?s.offsetTop:0))/a,m=n.width/i,v=n.height/a;return{width:m,height:v,top:d,right:c+m,bottom:d+v,left:c,x:c,y:d}}function zt(e){var t=je(e),r=e.offsetWidth,n=e.offsetHeight;return Math.abs(t.width-r)<=1&&(r=t.width),Math.abs(t.height-n)<=1&&(n=t.height),{x:e.offsetLeft,y:e.offsetTop,width:r,height:n}}function un(e,t){var r=t.getRootNode&&t.getRootNode();if(e.contains(t))return!0;if(r&&Mt(r)){var n=t;do{if(n&&e.isSameNode(n))return!0;n=n.parentNode||n.host}while(n)}return!1}function pe(e){return J(e).getComputedStyle(e)}function ui(e){return["table","td","th"].indexOf(ce(e))>=0}function be(e){return((Ne(e)?e.ownerDocument:e.document)||window.document).documentElement}function rt(e){return ce(e)==="html"?e:e.assignedSlot||e.parentNode||(Mt(e)?e.host:null)||be(e)}function xr(e){return!te(e)||pe(e).position==="fixed"?null:e.offsetParent}function di(e){var t=/firefox/i.test(At()),r=/Trident/i.test(At());if(r&&te(e)){var n=pe(e);if(n.position==="fixed")return null}var i=rt(e);for(Mt(i)&&(i=i.host);te(i)&&["html","body"].indexOf(ce(i))<0;){var a=pe(i);if(a.transform!=="none"||a.perspective!=="none"||a.contain==="paint"||["transform","perspective"].indexOf(a.willChange)!==-1||t&&a.willChange==="filter"||t&&a.filter&&a.filter!=="none")return i;i=i.parentNode}return null}function Ze(e){for(var t=J(e),r=xr(e);r&&ui(r)&&pe(r).position==="static";)r=xr(r);return r&&(ce(r)==="html"||ce(r)==="body"&&pe(r).position==="static")?t:r||di(e)||t}function Vt(e){return["top","bottom"].indexOf(e)>=0?"x":"y"}function We(e,t,r){return we(e,Je(t,r))}function pi(e,t,r){var n=We(e,t,r);return n>r?r:n}function dn(){return{top:0,right:0,bottom:0,left:0}}function pn(e){return Object.assign({},dn(),e)}function fn(e,t){return t.reduce(function(r,n){return r[n]=e,r},{})}var fi=function(t,r){return t=typeof t=="function"?t(Object.assign({},r.rects,{placement:r.placement})):t,pn(typeof t!="number"?t:fn(t,$e))};function mi(e){var t,r=e.state,n=e.name,i=e.options,a=r.elements.arrow,o=r.modifiersData.popperOffsets,s=le(r.placement),l=Vt(s),c=[G,ne].indexOf(s)>=0,d=c?"height":"width";if(!(!a||!o)){var m=fi(i.padding,r),v=zt(a),h=l==="y"?U:G,x=l==="y"?re:ne,b=r.rects.reference[d]+r.rects.reference[l]-o[l]-r.rects.popper[d],y=o[l]-r.rects.reference[l],w=Ze(a),S=w?l==="y"?w.clientHeight||0:w.clientWidth||0:0,C=b/2-y/2,E=m[h],R=S-v[d]-m[x],N=S/2-v[d]/2+C,k=We(E,N,R),L=l;r.modifiersData[n]=(t={},t[L]=k,t.centerOffset=k-N,t)}}function hi(e){var t=e.state,r=e.options,n=r.element,i=n===void 0?"[data-popper-arrow]":n;i!=null&&(typeof i=="string"&&(i=t.elements.popper.querySelector(i),!i)||un(t.elements.popper,i)&&(t.elements.arrow=i))}const vi={name:"arrow",enabled:!0,phase:"main",fn:mi,effect:hi,requires:["popperOffsets"],requiresIfExists:["preventOverflow"]};function De(e){return e.split("-")[1]}var gi={top:"auto",right:"auto",bottom:"auto",left:"auto"};function bi(e,t){var r=e.x,n=e.y,i=t.devicePixelRatio||1;return{x:Le(r*i)/i||0,y:Le(n*i)/i||0}}function Er(e){var t,r=e.popper,n=e.popperRect,i=e.placement,a=e.variation,o=e.offsets,s=e.position,l=e.gpuAcceleration,c=e.adaptive,d=e.roundOffsets,m=e.isFixed,v=o.x,h=v===void 0?0:v,x=o.y,b=x===void 0?0:x,y=typeof d=="function"?d({x:h,y:b}):{x:h,y:b};h=y.x,b=y.y;var w=o.hasOwnProperty("x"),S=o.hasOwnProperty("y"),C=G,E=U,R=window;if(c){var N=Ze(r),k="clientHeight",L="clientWidth";if(N===J(r)&&(N=be(r),pe(N).position!=="static"&&s==="absolute"&&(k="scrollHeight",L="scrollWidth")),N=N,i===U||(i===G||i===ne)&&a===qe){E=re;var A=m&&N===R&&R.visualViewport?R.visualViewport.height:N[k];b-=A-n.height,b*=l?1:-1}if(i===G||(i===U||i===re)&&a===qe){C=ne;var P=m&&N===R&&R.visualViewport?R.visualViewport.width:N[L];h-=P-n.width,h*=l?1:-1}}var V=Object.assign({position:s},c&&gi),I=d===!0?bi({x:h,y:b},J(r)):{x:h,y:b};if(h=I.x,b=I.y,l){var T;return Object.assign({},V,(T={},T[E]=S?"0":"",T[C]=w?"0":"",T.transform=(R.devicePixelRatio||1)<=1?"translate("+h+"px, "+b+"px)":"translate3d("+h+"px, "+b+"px, 0)",T))}return Object.assign({},V,(t={},t[E]=S?b+"px":"",t[C]=w?h+"px":"",t.transform="",t))}function yi(e){var t=e.state,r=e.options,n=r.gpuAcceleration,i=n===void 0?!0:n,a=r.adaptive,o=a===void 0?!0:a,s=r.roundOffsets,l=s===void 0?!0:s,c={placement:le(t.placement),variation:De(t.placement),popper:t.elements.popper,popperRect:t.rects.popper,gpuAcceleration:i,isFixed:t.options.strategy==="fixed"};t.modifiersData.popperOffsets!=null&&(t.styles.popper=Object.assign({},t.styles.popper,Er(Object.assign({},c,{offsets:t.modifiersData.popperOffsets,position:t.options.strategy,adaptive:o,roundOffsets:l})))),t.modifiersData.arrow!=null&&(t.styles.arrow=Object.assign({},t.styles.arrow,Er(Object.assign({},c,{offsets:t.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:l})))),t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-placement":t.placement})}const Ci={name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:yi,data:{}};var Ye={passive:!0};function xi(e){var t=e.state,r=e.instance,n=e.options,i=n.scroll,a=i===void 0?!0:i,o=n.resize,s=o===void 0?!0:o,l=J(t.elements.popper),c=[].concat(t.scrollParents.reference,t.scrollParents.popper);return a&&c.forEach(function(d){d.addEventListener("scroll",r.update,Ye)}),s&&l.addEventListener("resize",r.update,Ye),function(){a&&c.forEach(function(d){d.removeEventListener("scroll",r.update,Ye)}),s&&l.removeEventListener("resize",r.update,Ye)}}const Ei={name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:xi,data:{}};var wi={left:"right",right:"left",bottom:"top",top:"bottom"};function Qe(e){return e.replace(/left|right|bottom|top/g,function(t){return wi[t]})}var Ni={start:"end",end:"start"};function wr(e){return e.replace(/start|end/g,function(t){return Ni[t]})}function It(e){var t=J(e),r=t.pageXOffset,n=t.pageYOffset;return{scrollLeft:r,scrollTop:n}}function Wt(e){return je(be(e)).left+It(e).scrollLeft}function Oi(e,t){var r=J(e),n=be(e),i=r.visualViewport,a=n.clientWidth,o=n.clientHeight,s=0,l=0;if(i){a=i.width,o=i.height;var c=cn();(c||!c&&t==="fixed")&&(s=i.offsetLeft,l=i.offsetTop)}return{width:a,height:o,x:s+Wt(e),y:l}}function Si(e){var t,r=be(e),n=It(e),i=(t=e.ownerDocument)==null?void 0:t.body,a=we(r.scrollWidth,r.clientWidth,i?i.scrollWidth:0,i?i.clientWidth:0),o=we(r.scrollHeight,r.clientHeight,i?i.scrollHeight:0,i?i.clientHeight:0),s=-n.scrollLeft+Wt(e),l=-n.scrollTop;return pe(i||r).direction==="rtl"&&(s+=we(r.clientWidth,i?i.clientWidth:0)-a),{width:a,height:o,x:s,y:l}}function Bt(e){var t=pe(e),r=t.overflow,n=t.overflowX,i=t.overflowY;return/auto|scroll|overlay|hidden/.test(r+i+n)}function mn(e){return["html","body","#document"].indexOf(ce(e))>=0?e.ownerDocument.body:te(e)&&Bt(e)?e:mn(rt(e))}function Be(e,t){var r;t===void 0&&(t=[]);var n=mn(e),i=n===((r=e.ownerDocument)==null?void 0:r.body),a=J(n),o=i?[a].concat(a.visualViewport||[],Bt(n)?n:[]):n,s=t.concat(o);return i?s:s.concat(Be(rt(o)))}function _t(e){return Object.assign({},e,{left:e.x,top:e.y,right:e.x+e.width,bottom:e.y+e.height})}function Ti(e,t){var r=je(e,!1,t==="fixed");return r.top=r.top+e.clientTop,r.left=r.left+e.clientLeft,r.bottom=r.top+e.clientHeight,r.right=r.left+e.clientWidth,r.width=e.clientWidth,r.height=e.clientHeight,r.x=r.left,r.y=r.top,r}function Nr(e,t,r){return t===sn?_t(Oi(e,r)):Ne(t)?Ti(t,r):_t(Si(be(e)))}function Ri(e){var t=Be(rt(e)),r=["absolute","fixed"].indexOf(pe(e).position)>=0,n=r&&te(e)?Ze(e):e;return Ne(n)?t.filter(function(i){return Ne(i)&&un(i,n)&&ce(i)!=="body"}):[]}function Ai(e,t,r,n){var i=t==="clippingParents"?Ri(e):[].concat(t),a=[].concat(i,[r]),o=a[0],s=a.reduce(function(l,c){var d=Nr(e,c,n);return l.top=we(d.top,l.top),l.right=Je(d.right,l.right),l.bottom=Je(d.bottom,l.bottom),l.left=we(d.left,l.left),l},Nr(e,o,n));return s.width=s.right-s.left,s.height=s.bottom-s.top,s.x=s.left,s.y=s.top,s}function hn(e){var t=e.reference,r=e.element,n=e.placement,i=n?le(n):null,a=n?De(n):null,o=t.x+t.width/2-r.width/2,s=t.y+t.height/2-r.height/2,l;switch(i){case U:l={x:o,y:t.y-r.height};break;case re:l={x:o,y:t.y+t.height};break;case ne:l={x:t.x+t.width,y:s};break;case G:l={x:t.x-r.width,y:s};break;default:l={x:t.x,y:t.y}}var c=i?Vt(i):null;if(c!=null){var d=c==="y"?"height":"width";switch(a){case ke:l[c]=l[c]-(t[d]/2-r[d]/2);break;case qe:l[c]=l[c]+(t[d]/2-r[d]/2);break}}return l}function Fe(e,t){t===void 0&&(t={});var r=t,n=r.placement,i=n===void 0?e.placement:n,a=r.strategy,o=a===void 0?e.strategy:a,s=r.boundary,l=s===void 0?Gn:s,c=r.rootBoundary,d=c===void 0?sn:c,m=r.elementContext,v=m===void 0?ze:m,h=r.altBoundary,x=h===void 0?!1:h,b=r.padding,y=b===void 0?0:b,w=pn(typeof y!="number"?y:fn(y,$e)),S=v===ze?Yn:ze,C=e.rects.popper,E=e.elements[x?S:v],R=Ai(Ne(E)?E:E.contextElement||be(e.elements.popper),l,d,o),N=je(e.elements.reference),k=hn({reference:N,element:C,placement:i}),L=_t(Object.assign({},C,k)),A=v===ze?L:N,P={top:R.top-A.top+w.top,bottom:A.bottom-R.bottom+w.bottom,left:R.left-A.left+w.left,right:A.right-R.right+w.right},V=e.modifiersData.offset;if(v===ze&&V){var I=V[i];Object.keys(P).forEach(function(T){var j=[ne,re].indexOf(T)>=0?1:-1,D=[U,re].indexOf(T)>=0?"y":"x";P[T]+=I[D]*j})}return P}function _i(e,t){t===void 0&&(t={});var r=t,n=r.placement,i=r.boundary,a=r.rootBoundary,o=r.padding,s=r.flipVariations,l=r.allowedAutoPlacements,c=l===void 0?ln:l,d=De(n),m=d?s?Cr:Cr.filter(function(x){return De(x)===d}):$e,v=m.filter(function(x){return c.indexOf(x)>=0});v.length===0&&(v=m);var h=v.reduce(function(x,b){return x[b]=Fe(e,{placement:b,boundary:i,rootBoundary:a,padding:o})[le(b)],x},{});return Object.keys(h).sort(function(x,b){return h[x]-h[b]})}function ki(e){if(le(e)===Pt)return[];var t=Qe(e);return[wr(e),t,wr(t)]}function Li(e){var t=e.state,r=e.options,n=e.name;if(!t.modifiersData[n]._skip){for(var i=r.mainAxis,a=i===void 0?!0:i,o=r.altAxis,s=o===void 0?!0:o,l=r.fallbackPlacements,c=r.padding,d=r.boundary,m=r.rootBoundary,v=r.altBoundary,h=r.flipVariations,x=h===void 0?!0:h,b=r.allowedAutoPlacements,y=t.options.placement,w=le(y),S=w===y,C=l||(S||!x?[Qe(y)]:ki(y)),E=[y].concat(C).reduce(function(F,Z){return F.concat(le(Z)===Pt?_i(t,{placement:Z,boundary:d,rootBoundary:m,padding:c,flipVariations:x,allowedAutoPlacements:b}):Z)},[]),R=t.rects.reference,N=t.rects.popper,k=new Map,L=!0,A=E[0],P=0;P<E.length;P++){var V=E[P],I=le(V),T=De(V)===ke,j=[U,re].indexOf(I)>=0,D=j?"width":"height",W=Fe(t,{placement:V,boundary:d,rootBoundary:m,altBoundary:v,padding:c}),q=j?T?ne:G:T?re:U;R[D]>N[D]&&(q=Qe(q));var Y=Qe(q),$=[];if(a&&$.push(W[I]<=0),s&&$.push(W[q]<=0,W[Y]<=0),$.every(function(F){return F})){A=V,L=!1;break}k.set(V,$)}if(L)for(var ae=x?3:1,fe=function(Z){var ie=E.find(function(de){var se=k.get(de);if(se)return se.slice(0,Z).every(function(Oe){return Oe})});if(ie)return A=ie,"break"},oe=ae;oe>0;oe--){var B=fe(oe);if(B==="break")break}t.placement!==A&&(t.modifiersData[n]._skip=!0,t.placement=A,t.reset=!0)}}const ji={name:"flip",enabled:!0,phase:"main",fn:Li,requiresIfExists:["offset"],data:{_skip:!1}};function Or(e,t,r){return r===void 0&&(r={x:0,y:0}),{top:e.top-t.height-r.y,right:e.right-t.width+r.x,bottom:e.bottom-t.height+r.y,left:e.left-t.width-r.x}}function Sr(e){return[U,ne,re,G].some(function(t){return e[t]>=0})}function Di(e){var t=e.state,r=e.name,n=t.rects.reference,i=t.rects.popper,a=t.modifiersData.preventOverflow,o=Fe(t,{elementContext:"reference"}),s=Fe(t,{altBoundary:!0}),l=Or(o,n),c=Or(s,i,a),d=Sr(l),m=Sr(c);t.modifiersData[r]={referenceClippingOffsets:l,popperEscapeOffsets:c,isReferenceHidden:d,hasPopperEscaped:m},t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-reference-hidden":d,"data-popper-escaped":m})}const Pi={name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:Di};function Mi(e,t,r){var n=le(e),i=[G,U].indexOf(n)>=0?-1:1,a=typeof r=="function"?r(Object.assign({},t,{placement:e})):r,o=a[0],s=a[1];return o=o||0,s=(s||0)*i,[G,ne].indexOf(n)>=0?{x:s,y:o}:{x:o,y:s}}function zi(e){var t=e.state,r=e.options,n=e.name,i=r.offset,a=i===void 0?[0,0]:i,o=ln.reduce(function(d,m){return d[m]=Mi(m,t.rects,a),d},{}),s=o[t.placement],l=s.x,c=s.y;t.modifiersData.popperOffsets!=null&&(t.modifiersData.popperOffsets.x+=l,t.modifiersData.popperOffsets.y+=c),t.modifiersData[n]=o}const Vi={name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:zi};function Ii(e){var t=e.state,r=e.name;t.modifiersData[r]=hn({reference:t.rects.reference,element:t.rects.popper,placement:t.placement})}const Wi={name:"popperOffsets",enabled:!0,phase:"read",fn:Ii,data:{}};function Bi(e){return e==="x"?"y":"x"}function Hi(e){var t=e.state,r=e.options,n=e.name,i=r.mainAxis,a=i===void 0?!0:i,o=r.altAxis,s=o===void 0?!1:o,l=r.boundary,c=r.rootBoundary,d=r.altBoundary,m=r.padding,v=r.tether,h=v===void 0?!0:v,x=r.tetherOffset,b=x===void 0?0:x,y=Fe(t,{boundary:l,rootBoundary:c,padding:m,altBoundary:d}),w=le(t.placement),S=De(t.placement),C=!S,E=Vt(w),R=Bi(E),N=t.modifiersData.popperOffsets,k=t.rects.reference,L=t.rects.popper,A=typeof b=="function"?b(Object.assign({},t.rects,{placement:t.placement})):b,P=typeof A=="number"?{mainAxis:A,altAxis:A}:Object.assign({mainAxis:0,altAxis:0},A),V=t.modifiersData.offset?t.modifiersData.offset[t.placement]:null,I={x:0,y:0};if(N){if(a){var T,j=E==="y"?U:G,D=E==="y"?re:ne,W=E==="y"?"height":"width",q=N[E],Y=q+y[j],$=q-y[D],ae=h?-L[W]/2:0,fe=S===ke?k[W]:L[W],oe=S===ke?-L[W]:-k[W],B=t.elements.arrow,F=h&&B?zt(B):{width:0,height:0},Z=t.modifiersData["arrow#persistent"]?t.modifiersData["arrow#persistent"].padding:dn(),ie=Z[j],de=Z[D],se=We(0,k[W],F[W]),Oe=C?k[W]/2-ae-se-ie-P.mainAxis:fe-se-ie-P.mainAxis,K=C?-k[W]/2+ae+se+de+P.mainAxis:oe+se+de+P.mainAxis,Me=t.elements.arrow&&Ze(t.elements.arrow),ot=Me?E==="y"?Me.clientTop||0:Me.clientLeft||0:0,ur=(T=V==null?void 0:V[E])!=null?T:0,jn=q+Oe-ur-ot,Dn=q+K-ur,dr=We(h?Je(Y,jn):Y,q,h?we($,Dn):$);N[E]=dr,I[E]=dr-q}if(s){var pr,Pn=E==="x"?U:G,Mn=E==="x"?re:ne,ye=N[R],Xe=R==="y"?"height":"width",fr=ye+y[Pn],mr=ye-y[Mn],st=[U,G].indexOf(w)!==-1,hr=(pr=V==null?void 0:V[R])!=null?pr:0,vr=st?fr:ye-k[Xe]-L[Xe]-hr+P.altAxis,gr=st?ye+k[Xe]+L[Xe]-hr-P.altAxis:mr,br=h&&st?pi(vr,ye,gr):We(h?vr:fr,ye,h?gr:mr);N[R]=br,I[R]=br-ye}t.modifiersData[n]=I}}const qi={name:"preventOverflow",enabled:!0,phase:"main",fn:Hi,requiresIfExists:["offset"]};function Fi(e){return{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}}function $i(e){return e===J(e)||!te(e)?It(e):Fi(e)}function Zi(e){var t=e.getBoundingClientRect(),r=Le(t.width)/e.offsetWidth||1,n=Le(t.height)/e.offsetHeight||1;return r!==1||n!==1}function Xi(e,t,r){r===void 0&&(r=!1);var n=te(t),i=te(t)&&Zi(t),a=be(t),o=je(e,i,r),s={scrollLeft:0,scrollTop:0},l={x:0,y:0};return(n||!n&&!r)&&((ce(t)!=="body"||Bt(a))&&(s=$i(t)),te(t)?(l=je(t,!0),l.x+=t.clientLeft,l.y+=t.clientTop):a&&(l.x=Wt(a))),{x:o.left+s.scrollLeft-l.x,y:o.top+s.scrollTop-l.y,width:o.width,height:o.height}}function Ui(e){var t=new Map,r=new Set,n=[];e.forEach(function(a){t.set(a.name,a)});function i(a){r.add(a.name);var o=[].concat(a.requires||[],a.requiresIfExists||[]);o.forEach(function(s){if(!r.has(s)){var l=t.get(s);l&&i(l)}}),n.push(a)}return e.forEach(function(a){r.has(a.name)||i(a)}),n}function Gi(e){var t=Ui(e);return oi.reduce(function(r,n){return r.concat(t.filter(function(i){return i.phase===n}))},[])}function Yi(e){var t;return function(){return t||(t=new Promise(function(r){Promise.resolve().then(function(){t=void 0,r(e())})})),t}}function Ki(e){var t=e.reduce(function(r,n){var i=r[n.name];return r[n.name]=i?Object.assign({},i,n,{options:Object.assign({},i.options,n.options),data:Object.assign({},i.data,n.data)}):n,r},{});return Object.keys(t).map(function(r){return t[r]})}var Tr={placement:"bottom",modifiers:[],strategy:"absolute"};function Rr(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return!t.some(function(n){return!(n&&typeof n.getBoundingClientRect=="function")})}function Qi(e){e===void 0&&(e={});var t=e,r=t.defaultModifiers,n=r===void 0?[]:r,i=t.defaultOptions,a=i===void 0?Tr:i;return function(s,l,c){c===void 0&&(c=a);var d={placement:"bottom",orderedModifiers:[],options:Object.assign({},Tr,a),modifiersData:{},elements:{reference:s,popper:l},attributes:{},styles:{}},m=[],v=!1,h={state:d,setOptions:function(w){var S=typeof w=="function"?w(d.options):w;b(),d.options=Object.assign({},a,d.options,S),d.scrollParents={reference:Ne(s)?Be(s):s.contextElement?Be(s.contextElement):[],popper:Be(l)};var C=Gi(Ki([].concat(n,d.options.modifiers)));return d.orderedModifiers=C.filter(function(E){return E.enabled}),x(),h.update()},forceUpdate:function(){if(!v){var w=d.elements,S=w.reference,C=w.popper;if(Rr(S,C)){d.rects={reference:Xi(S,Ze(C),d.options.strategy==="fixed"),popper:zt(C)},d.reset=!1,d.placement=d.options.placement,d.orderedModifiers.forEach(function(P){return d.modifiersData[P.name]=Object.assign({},P.data)});for(var E=0;E<d.orderedModifiers.length;E++){if(d.reset===!0){d.reset=!1,E=-1;continue}var R=d.orderedModifiers[E],N=R.fn,k=R.options,L=k===void 0?{}:k,A=R.name;typeof N=="function"&&(d=N({state:d,options:L,name:A,instance:h})||d)}}}},update:Yi(function(){return new Promise(function(y){h.forceUpdate(),y(d)})}),destroy:function(){b(),v=!0}};if(!Rr(s,l))return h;h.setOptions(c).then(function(y){!v&&c.onFirstUpdate&&c.onFirstUpdate(y)});function x(){d.orderedModifiers.forEach(function(y){var w=y.name,S=y.options,C=S===void 0?{}:S,E=y.effect;if(typeof E=="function"){var R=E({state:d,name:w,instance:h,options:C}),N=function(){};m.push(R||N)}})}function b(){m.forEach(function(y){return y()}),m=[]}return h}}var Ji=[Ei,Wi,Ci,ci,Vi,ji,qi,vi,Pi],ea=Qi({defaultModifiers:Ji});function ta(e,t){if(e==null)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(t.includes(n))continue;r[n]=e[n]}return r}function kt(e,t){return kt=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(r,n){return r.__proto__=n,r},kt(e,t)}function ra(e,t){e.prototype=Object.create(t.prototype),e.prototype.constructor=e,kt(e,t)}var Ar={disabled:!1},vn=p.createContext(null),na=function(t){return t.scrollTop},Ie="unmounted",xe="exited",Ee="entering",Te="entered",Lt="exiting",ue=function(e){ra(t,e);function t(n,i){var a;a=e.call(this,n,i)||this;var o=i,s=o&&!o.isMounting?n.enter:n.appear,l;return a.appearStatus=null,n.in?s?(l=xe,a.appearStatus=Ee):l=Te:n.unmountOnExit||n.mountOnEnter?l=Ie:l=xe,a.state={status:l},a.nextCallback=null,a}t.getDerivedStateFromProps=function(i,a){var o=i.in;return o&&a.status===Ie?{status:xe}:null};var r=t.prototype;return r.componentDidMount=function(){this.updateStatus(!0,this.appearStatus)},r.componentDidUpdate=function(i){var a=null;if(i!==this.props){var o=this.state.status;this.props.in?o!==Ee&&o!==Te&&(a=Ee):(o===Ee||o===Te)&&(a=Lt)}this.updateStatus(!1,a)},r.componentWillUnmount=function(){this.cancelNextCallback()},r.getTimeouts=function(){var i=this.props.timeout,a,o,s;return a=o=s=i,i!=null&&typeof i!="number"&&(a=i.exit,o=i.enter,s=i.appear!==void 0?i.appear:o),{exit:a,enter:o,appear:s}},r.updateStatus=function(i,a){if(i===void 0&&(i=!1),a!==null)if(this.cancelNextCallback(),a===Ee){if(this.props.unmountOnExit||this.props.mountOnEnter){var o=this.props.nodeRef?this.props.nodeRef.current:Ge.findDOMNode(this);o&&na(o)}this.performEnter(i)}else this.performExit();else this.props.unmountOnExit&&this.state.status===xe&&this.setState({status:Ie})},r.performEnter=function(i){var a=this,o=this.props.enter,s=this.context?this.context.isMounting:i,l=this.props.nodeRef?[s]:[Ge.findDOMNode(this),s],c=l[0],d=l[1],m=this.getTimeouts(),v=s?m.appear:m.enter;if(!i&&!o||Ar.disabled){this.safeSetState({status:Te},function(){a.props.onEntered(c)});return}this.props.onEnter(c,d),this.safeSetState({status:Ee},function(){a.props.onEntering(c,d),a.onTransitionEnd(v,function(){a.safeSetState({status:Te},function(){a.props.onEntered(c,d)})})})},r.performExit=function(){var i=this,a=this.props.exit,o=this.getTimeouts(),s=this.props.nodeRef?void 0:Ge.findDOMNode(this);if(!a||Ar.disabled){this.safeSetState({status:xe},function(){i.props.onExited(s)});return}this.props.onExit(s),this.safeSetState({status:Lt},function(){i.props.onExiting(s),i.onTransitionEnd(o.exit,function(){i.safeSetState({status:xe},function(){i.props.onExited(s)})})})},r.cancelNextCallback=function(){this.nextCallback!==null&&(this.nextCallback.cancel(),this.nextCallback=null)},r.safeSetState=function(i,a){a=this.setNextCallback(a),this.setState(i,a)},r.setNextCallback=function(i){var a=this,o=!0;return this.nextCallback=function(s){o&&(o=!1,a.nextCallback=null,i(s))},this.nextCallback.cancel=function(){o=!1},this.nextCallback},r.onTransitionEnd=function(i,a){this.setNextCallback(a);var o=this.props.nodeRef?this.props.nodeRef.current:Ge.findDOMNode(this),s=i==null&&!this.props.addEndListener;if(!o||s){setTimeout(this.nextCallback,0);return}if(this.props.addEndListener){var l=this.props.nodeRef?[this.nextCallback]:[o,this.nextCallback],c=l[0],d=l[1];this.props.addEndListener(c,d)}i!=null&&setTimeout(this.nextCallback,i)},r.render=function(){var i=this.state.status;if(i===Ie)return null;var a=this.props,o=a.children;a.in,a.mountOnEnter,a.unmountOnExit,a.appear,a.enter,a.exit,a.timeout,a.addEndListener,a.onEnter,a.onEntering,a.onEntered,a.onExit,a.onExiting,a.onExited,a.nodeRef;var s=ta(a,["children","in","mountOnEnter","unmountOnExit","appear","enter","exit","timeout","addEndListener","onEnter","onEntering","onEntered","onExit","onExiting","onExited","nodeRef"]);return p.createElement(vn.Provider,{value:null},typeof o=="function"?o(i,s):p.cloneElement(p.Children.only(o),s))},t}(p.Component);ue.contextType=vn;ue.propTypes={};function Se(){}ue.defaultProps={in:!1,mountOnEnter:!1,unmountOnExit:!1,appear:!1,enter:!0,exit:!0,onEnter:Se,onEntering:Se,onEntered:Se,onExit:Se,onExiting:Se,onExited:Se};ue.UNMOUNTED=Ie;ue.EXITED=xe;ue.ENTERING=Ee;ue.ENTERED=Te;ue.EXITING=Lt;var Ht=g.forwardRef(function(e,t){var r=e.className,n=e.dark,i=e.disabled,a=e.white,o=z(e,["className","dark","disabled","white"]);return p.createElement("button",_({type:"button",className:M("btn","btn-close",{"btn-close-white":a},i,r),"aria-label":"Close",disabled:i},n&&{"data-coreui-theme":"dark"},o,{ref:t}))});Ht.propTypes={className:u.string,dark:u.bool,disabled:u.bool,white:u.bool};Ht.displayName="CCloseButton";var qt=g.forwardRef(function(e,t){var r,n=e.children,i=e.className,a=e.color,o=e.shape,s=e.size,l=e.src,c=e.status,d=e.textColor,m=z(e,["children","className","color","shape","size","src","status","textColor"]),v=c&&M("avatar-status","bg-".concat(c));return p.createElement("div",_({className:M("avatar",(r={},r["bg-".concat(a)]=a,r["avatar-".concat(s)]=s,r["text-".concat(d)]=d,r),o,i)},m,{ref:t}),l?p.createElement("img",{src:l,className:"avatar-img"}):n,c&&p.createElement("span",{className:v}))});qt.propTypes={children:u.node,className:u.string,color:St,shape:nn,size:u.string,src:u.string,status:u.string,textColor:rn};qt.displayName="CAvatar";var Ft=g.forwardRef(function(e,t){var r=e.className,n=r===void 0?"modal-backdrop":r,i=e.visible,a=z(e,["className","visible"]),o=g.useRef(null),s=Pe(t,o);return p.createElement(ue,{in:i,mountOnEnter:!0,nodeRef:o,timeout:150,unmountOnExit:!0},function(l){return p.createElement("div",_({className:M(n,"fade",{show:l==="entered"})},a,{ref:s}))})});Ft.propTypes={className:u.string,visible:u.bool};Ft.displayName="CBackdrop";var $t=g.forwardRef(function(e,t){var r,n=e.children,i=e.as,a=i===void 0?"span":i,o=e.className,s=e.color,l=e.position,c=e.shape,d=e.size,m=e.textBgColor,v=e.textColor,h=z(e,["children","as","className","color","position","shape","size","textBgColor","textColor"]);return p.createElement(a,_({className:M("badge",(r={},r["bg-".concat(s)]=s,r["position-absolute translate-middle"]=l,r["top-0"]=l==null?void 0:l.includes("top"),r["top-100"]=l==null?void 0:l.includes("bottom"),r["start-100"]=l==null?void 0:l.includes("end"),r["start-0"]=l==null?void 0:l.includes("start"),r["badge-".concat(d)]=d,r["text-".concat(v)]=v,r["text-bg-".concat(m)]=m,r),c,o)},h,{ref:t}),n)});$t.propTypes={as:u.elementType,children:u.node,className:u.string,color:St,position:u.oneOf(["top-start","top-end","bottom-end","bottom-start"]),shape:nn,size:u.oneOf(["sm"]),textBgColor:St,textColor:rn};$t.displayName="CBadge";var Zt=g.forwardRef(function(e,t){var r=e.children,n=e.className,i=z(e,["children","className"]);return p.createElement("nav",{"aria-label":"breadcrumb"},p.createElement("ol",_({className:M("breadcrumb",n)},i,{ref:t}),r))});Zt.propTypes={children:u.node,className:u.string};Zt.displayName="CBreadcrumb";var et=g.forwardRef(function(e,t){var r=e.children,n=e.active,i=e.as,a=e.className,o=e.href,s=z(e,["children","active","as","className","href"]);return p.createElement("li",_({className:M("breadcrumb-item",{active:n},a)},n&&{"aria-current":"page"},s,{ref:t}),o?p.createElement(tt,{as:i,href:o},r):r)});et.propTypes={active:u.bool,children:u.node,className:u.string,href:u.string};et.displayName="CBreadcrumbItem";var Ke=function(e){var t=e.getBoundingClientRect();return Math.floor(t.top)>=0&&Math.floor(t.left)>=0&&Math.floor(t.bottom)<=(window.innerHeight||document.documentElement.clientHeight)&&Math.floor(t.right)<=(window.innerWidth||document.documentElement.clientWidth)},ia=function(e){return e?typeof e=="function"?e():e:document.body},Xt=function(e){var t=e.children,r=e.container,n=e.portal,i=g.useState(null),a=i[0],o=i[1];return g.useEffect(function(){n&&o(ia(r)||document.body)},[r,n]),typeof window<"u"&&n&&a?Dt.createPortal(t,a):p.createElement(p.Fragment,null,t)};Xt.propTypes={children:u.node,container:u.any,portal:u.bool.isRequired};Xt.displayName="CConditionalPortal";var aa=function(){var e=g.useRef(),t=g.useRef(),r=function(a,o,s){e.current=ea(a,o,s),t.current=o},n=function(){var a=e.current;a&&t.current&&a.destroy(),e.current=void 0},i=function(a){var o=e.current;o&&a&&o.setOptions(a),o&&o.update()};return{popper:e.current,initPopper:r,destroyPopper:n,updatePopper:i}},oa=function(e,t,r,n){var i=e.length,a=e.indexOf(t);return a===-1?!r&&n?e[i-1]:e[0]:(a+=r?1:-1,a=(a+i)%i,e[Math.max(0,Math.min(a,i-1))])},sa=function(e){return typeof document<"u"&&document.documentElement.dir==="rtl"?!0:e?e.closest('[dir="rtl"]')!==null:!1},la=function(e){var t=[];if(typeof e=="object")for(var r in e)t.push("dropdown-menu".concat(r==="xs"?"":"-".concat(r),"-").concat(e[r]));return typeof e=="string"&&t.push("dropdown-menu-".concat(e)),t},ca=function(e,t,r,n){var i=e;return t==="dropup"&&(i=n?"top-end":"top-start"),t==="dropup-center"&&(i="top"),t==="dropend"&&(i=n?"left-start":"right-start"),t==="dropstart"&&(i=n?"right-start":"left-start"),r==="end"&&(i=n?"bottom-start":"bottom-end"),i},Ut=g.createContext({}),nt=g.forwardRef(function(e,t){var r,n=e.children,i=e.alignment,a=e.as,o=a===void 0?"div":a,s=e.autoClose,l=s===void 0?!0:s,c=e.className,d=e.container,m=e.dark,v=e.direction,h=e.offset,x=h===void 0?[0,2]:h,b=e.onHide,y=e.onShow,w=e.placement,S=w===void 0?"bottom-start":w,C=e.popper,E=C===void 0?!0:C,R=e.popperConfig,N=e.portal,k=N===void 0?!1:N,L=e.variant,A=L===void 0?"btn-group":L,P=e.visible,V=P===void 0?!1:P,I=z(e,["children","alignment","as","autoClose","className","container","dark","direction","offset","onHide","onShow","placement","popper","popperConfig","portal","variant","visible"]),T=g.useRef(null),j=g.useRef(null),D=g.useRef(null),W=Pe(t,T),q=g.useState(V),Y=q[0],$=q[1],ae=aa(),fe=ae.initPopper,oe=ae.destroyPopper,B=A==="nav-item"?"li":o;typeof i=="object"&&(E=!1);var F={alignment:i,container:d,dark:m,dropdownToggleRef:j,dropdownMenuRef:D,popper:E,portal:k,variant:A,visible:Y,setVisible:$},Z={modifiers:[{name:"offset",options:{offset:x}}],placement:ca(S,v,i,sa(D.current))},ie=_(_({},Z),typeof R=="function"?R(Z):R);g.useEffect(function(){$(V)},[V]),g.useEffect(function(){return Y&&j.current&&D.current&&(j.current.focus(),E&&fe(j.current,D.current,ie),window.addEventListener("mouseup",Oe),window.addEventListener("keyup",se),j.current.addEventListener("keydown",de),D.current.addEventListener("keydown",de),y&&y()),function(){E&&oe(),window.removeEventListener("mouseup",Oe),window.removeEventListener("keyup",se),j.current&&j.current.removeEventListener("keydown",de),D.current&&D.current.removeEventListener("keydown",de),b&&b()}},[Y]);var de=function(K){if(Y&&D.current&&(K.key==="ArrowDown"||K.key==="ArrowUp")){K.preventDefault();var Me=K.target,ot=Array.from(D.current.querySelectorAll(".dropdown-item:not(.disabled):not(:disabled)"));oa(ot,Me,K.key==="ArrowDown",!0).focus()}},se=function(K){l!==!1&&K.key==="Escape"&&$(!1)},Oe=function(K){if(!(!j.current||!D.current)&&!j.current.contains(K.target)&&(l===!0||l==="inside"&&D.current.contains(K.target)||l==="outside"&&!D.current.contains(K.target))){setTimeout(function(){return $(!1)},1);return}};return p.createElement(Ut.Provider,{value:F},A==="input-group"?p.createElement(p.Fragment,null,n):p.createElement(B,_({className:M(A==="nav-item"?"nav-item dropdown":A,(r={"dropdown-center":v==="center","dropup dropup-center":v==="dropup-center"},r["".concat(v)]=v&&v!=="center"&&v!=="dropup-center",r),c)},I,{ref:W}),n))}),Ce=u.oneOf(["start","end"]);nt.propTypes={alignment:u.oneOfType([Ce,u.shape({xs:Ce.isRequired}),u.shape({sm:Ce.isRequired}),u.shape({md:Ce.isRequired}),u.shape({lg:Ce.isRequired}),u.shape({xl:Ce.isRequired}),u.shape({xxl:Ce.isRequired})]),as:u.elementType,autoClose:u.oneOfType([u.bool,u.oneOf(["inside","outside"])]),children:u.node,className:u.string,dark:u.bool,direction:u.oneOf(["center","dropup","dropup-center","dropend","dropstart"]),offset:u.any,onHide:u.func,onShow:u.func,placement:Vn,popper:u.bool,popperConfig:u.oneOfType([u.func,u.object]),portal:u.bool,variant:u.oneOf(["btn-group","dropdown","input-group","nav-item"]),visible:u.bool};nt.displayName="CDropdown";var Gt=g.forwardRef(function(e,t){var r=e.children,n=e.as,i=n===void 0?"h6":n,a=e.className,o=z(e,["children","as","className"]);return p.createElement(i,_({className:M("dropdown-header",a)},o,{ref:t}),r)});Gt.propTypes={as:u.elementType,children:u.node,className:u.string};Gt.displayName="CDropdownHeader";var _e=g.forwardRef(function(e,t){var r=e.children,n=e.as,i=n===void 0?"a":n,a=e.className,o=z(e,["children","as","className"]);return p.createElement(tt,_({className:M("dropdown-item",a),as:i},o,{ref:t}),r)});_e.propTypes={as:u.elementType,children:u.node,className:u.string};_e.displayName="CDropdownItem";var it=g.forwardRef(function(e,t){var r=e.children,n=e.as,i=n===void 0?"ul":n,a=e.className,o=z(e,["children","as","className"]),s=g.useContext(Ut),l=s.alignment,c=s.container,d=s.dark,m=s.dropdownMenuRef,v=s.popper,h=s.portal,x=s.visible,b=Pe(t,m);return p.createElement(Xt,{container:c,portal:h??!1},p.createElement(i,_({className:M("dropdown-menu",{show:x},l&&la(l),a),ref:b,role:"menu"},!v&&{"data-coreui-popper":"static"},d&&{"data-coreui-theme":"dark"},o),i==="ul"?p.Children.map(r,function(y,w){if(p.isValidElement(y))return p.createElement("li",{key:w},p.cloneElement(y))}):r))});it.propTypes={as:u.elementType,children:u.node,className:u.string};it.displayName="CDropdownMenu";var at=function(e){var t=e.children,r=e.caret,n=r===void 0?!0:r,i=e.custom,a=e.className,o=e.navLink,s=o===void 0?!0:o,l=e.split,c=e.trigger,d=c===void 0?"click":c,m=z(e,["children","caret","custom","className","navLink","split","trigger"]),v=g.useContext(Ut),h=v.dropdownToggleRef,x=v.variant,b=v.visible,y=v.setVisible,w=_(_({},(d==="click"||d.includes("click"))&&{onClick:function(E){E.preventDefault(),y(!b)}}),(d==="focus"||d.includes("focus"))&&{onFocus:function(){return y(!0)},onBlur:function(){return y(!1)}}),S=_({className:M({"nav-link":x==="nav-item"&&s,"dropdown-toggle":n,"dropdown-toggle-split":l,show:b},a),"aria-expanded":b},!m.disabled&&_({},w)),C=function(){return i&&p.isValidElement(t)?p.createElement(p.Fragment,null,p.cloneElement(t,_(_({"aria-expanded":b},!m.disabled&&_({},w)),{ref:h}))):x==="nav-item"&&s?p.createElement("a",_({href:"#"},S,{role:"button"},m,{ref:h}),t):p.createElement($n,_({},S,{tabIndex:0},m,{ref:h}),t,l&&p.createElement("span",{className:"visually-hidden"},"Toggle Dropdown"))};return p.createElement(C,null)};at.propTypes={caret:u.bool,children:u.node,className:u.string,custom:u.bool,split:u.bool,trigger:In};at.displayName="CDropdownToggle";var Yt=g.forwardRef(function(e,t){var r,n=e.children,i=e.className,a=e.position,o=z(e,["children","className","position"]);return p.createElement("div",_({className:M("footer",(r={},r["footer-".concat(a)]=a,r),i)},o,{ref:t}),n)});Yt.propTypes={children:u.node,className:u.string,position:u.oneOf(["fixed","sticky"])};Yt.displayName="CFooter";var Kt=g.forwardRef(function(e,t){var r,n=e.children,i=e.className,a=e.container,o=e.position,s=z(e,["children","className","container","position"]);return p.createElement("div",_({className:M("header",(r={},r["header-".concat(o)]=o,r),i)},s,{ref:t}),a?p.createElement("div",{className:typeof a=="string"?"container-".concat(a):"container"},n):p.createElement(p.Fragment,null,n))});Kt.propTypes={children:u.node,className:u.string,container:u.oneOfType([u.bool,u.oneOf(["sm","md","lg","xl","xxl","fluid"])]),position:u.oneOf(["fixed","sticky"])};Kt.displayName="CHeader";var He=g.forwardRef(function(e,t){var r=e.children,n=e.as,i=n===void 0?"ul":n,a=e.className,o=z(e,["children","as","className"]);return p.createElement(i,_({className:M("header-nav",a),role:"navigation"},o,{ref:t}),r)});He.propTypes={as:u.elementType,children:u.node,className:u.string};He.displayName="CHeaderNav";var Qt=g.forwardRef(function(e,t){var r=e.children,n=e.className,i=z(e,["children","className"]);return p.createElement("button",_({type:"button",className:M("header-toggler",n)},i,{ref:t}),r??p.createElement("span",{className:"header-toggler-icon"}))});Qt.propTypes={children:u.node,className:u.string};Qt.displayName="CHeaderToggler";var Jt=g.forwardRef(function(e,t){var r,n=e.children,i=e.as,a=i===void 0?"ul":i,o=e.className,s=e.layout,l=e.variant,c=z(e,["children","as","className","layout","variant"]);return p.createElement(a,_({className:M("nav",(r={},r["nav-".concat(s)]=s,r["nav-".concat(l)]=l,r),o),role:"navigation"},c,{ref:t}),n)});Jt.propTypes={as:u.elementType,children:u.node,className:u.string,layout:u.oneOf(["fill","justified"]),variant:u.oneOf(["pills","tabs","underline","underline-border"])};Jt.displayName="CNav";var gn=g.createContext({}),bn=function(e,t,r){return p.Children.map(e,function(n,i){if(!p.isValidElement(n)||n.type.displayName!=="CNavGroup"&&n.type.displayName!=="CNavLink"&&n.type.displayName!=="CNavItem")return n;var a=t?r?"".concat(t,".").concat(i):"".concat(t):"".concat(i);return n.props&&n.props.children?p.cloneElement(n,{idx:a,children:bn(n.props.children,a,n.type.displayName!=="CNavItem")}):p.cloneElement(n,{idx:a})})},er=g.forwardRef(function(e,t){var r=e.children,n=e.as,i=n===void 0?"ul":n,a=e.className,o=z(e,["children","as","className"]),s=g.useState(""),l=s[0],c=s[1],d={visibleGroup:l,setVisibleGroup:c};return p.createElement(gn.Provider,{value:d},p.createElement(i,_({className:M("sidebar-nav",a),ref:t},o),bn(r)))});er.propTypes={as:u.elementType,children:u.node,className:u.string};er.displayName="CSidebarNav";var ge=g.forwardRef(function(e,t){var r=e.children,n=e.className,i=e.idx,a=z(e,["children","className","idx"]),o=g.useRef(null),s=Pe(t,o),l=g.useContext(gn).setVisibleGroup;return g.useEffect(function(){var c;a.active=(c=o.current)===null||c===void 0?void 0:c.classList.contains("active"),i&&a.active&&l(i)},[a.active,n]),p.createElement(tt,_({className:M("nav-link",n)},a,{ref:s}),r)});ge.propTypes={active:u.bool,as:u.elementType,children:u.node,className:u.string,disabled:u.bool,idx:u.string};ge.displayName="CNavLink";var ee=g.forwardRef(function(e,t){var r=e.children,n=e.as,i=n===void 0?"li":n,a=e.className,o=z(e,["children","as","className"]);return p.createElement(i,{className:M("nav-item",a),ref:t},o.href||o.to?p.createElement(ge,_({className:a},o),r):r)});ee.propTypes={as:u.elementType,children:u.node,className:u.string};ee.displayName="CNavItem";var tr=g.forwardRef(function(e,t){var r=e.children,n=e.as,i=n===void 0?"li":n,a=e.className,o=z(e,["children","as","className"]);return p.createElement(i,_({className:M("nav-title",a)},o,{ref:t}),r)});tr.propTypes={as:u.elementType,children:u.node,className:u.string};tr.displayName="CNavTitle";var lt=function(e){return!!getComputedStyle(e).getPropertyValue("--cui-is-mobile")},rr=g.forwardRef(function(e,t){var r,n=e.children,i=e.as,a=i===void 0?"div":i,o=e.className,s=e.colorScheme,l=e.narrow,c=e.onHide,d=e.onShow,m=e.onVisibleChange,v=e.overlaid,h=e.placement,x=e.position,b=e.size,y=e.unfoldable,w=e.visible,S=z(e,["children","as","className","colorScheme","narrow","onHide","onShow","onVisibleChange","overlaid","placement","position","size","unfoldable","visible"]),C=g.useRef(null),E=Pe(t,C),R=g.useState(),N=R[0],k=R[1],L=g.useState(!1),A=L[0],P=L[1],V=g.useState(!1),I=V[0],T=V[1],j=g.useState(w!==void 0?w:!v),D=j[0],W=j[1];g.useEffect(function(){C.current&&P(lt(C.current)),w!==void 0&&q(w)},[w]),g.useEffect(function(){N!==void 0&&m&&m(N),!N&&c&&c(),N&&d&&d()},[N]),g.useEffect(function(){A&&T(!1)},[A]),g.useEffect(function(){var B,F;return C.current&&P(lt(C.current)),C.current&&k(Ke(C.current)),window.addEventListener("resize",$),window.addEventListener("mouseup",fe),window.addEventListener("keyup",ae),(B=C.current)===null||B===void 0||B.addEventListener("mouseup",oe),(F=C.current)===null||F===void 0||F.addEventListener("transitionend",function(){C.current&&k(Ke(C.current))}),function(){var Z,ie;window.removeEventListener("resize",$),window.removeEventListener("mouseup",fe),window.removeEventListener("keyup",ae),(Z=C.current)===null||Z===void 0||Z.removeEventListener("mouseup",oe),(ie=C.current)===null||ie===void 0||ie.removeEventListener("transitionend",function(){C.current&&k(Ke(C.current))})}});var q=function(B){if(A){T(B);return}W(B)},Y=function(){q(!1)},$=function(){C.current&&P(lt(C.current)),C.current&&k(Ke(C.current))},ae=function(B){A&&C.current&&!C.current.contains(B.target)&&Y()},fe=function(B){A&&C.current&&!C.current.contains(B.target)&&Y()},oe=function(B){var F=B.target;F&&F.classList.contains("nav-link")&&!F.classList.contains("nav-group-toggle")&&A&&Y()};return p.createElement(p.Fragment,null,p.createElement(a,_({className:M("sidebar",(r={},r["sidebar-".concat(s)]=s,r["sidebar-narrow"]=l,r["sidebar-overlaid"]=v,r["sidebar-".concat(h)]=h,r["sidebar-".concat(x)]=x,r["sidebar-".concat(b)]=b,r["sidebar-narrow-unfoldable"]=y,r.show=A&&I||v&&D,r.hide=D===!1&&!A&&!v,r),o)},S,{ref:E}),n),typeof window<"u"&&A&&Dt.createPortal(p.createElement(Ft,{className:"sidebar-backdrop",visible:A&&I}),document.body))});rr.propTypes={as:u.elementType,children:u.node,className:u.string,colorScheme:u.oneOf(["dark","light"]),narrow:u.bool,onHide:u.func,onShow:u.func,onVisibleChange:u.func,overlaid:u.bool,placement:u.oneOf(["start","end"]),position:u.oneOf(["fixed","sticky"]),size:u.oneOf(["sm","lg","xl"]),unfoldable:u.bool,visible:u.bool};rr.displayName="CSidebar";var nr=g.forwardRef(function(e,t){var r=e.children,n=e.as,i=n===void 0?"a":n,a=e.className,o=z(e,["children","as","className"]);return p.createElement(i,_({className:M("sidebar-brand",a),ref:t},o),r)});nr.propTypes={as:u.elementType,children:u.node,className:u.string};nr.displayName="CSidebarBrand";var ir=g.forwardRef(function(e,t){var r=e.children,n=e.className,i=z(e,["children","className"]);return p.createElement("div",_({className:M("sidebar-footer",n),ref:t},i),r)});ir.propTypes={children:u.node,className:u.string};ir.displayName="CSidebarFooter";var ar=g.forwardRef(function(e,t){var r=e.children,n=e.className,i=z(e,["children","className"]);return p.createElement("div",_({className:M("sidebar-header",n),ref:t},i),r)});ar.propTypes={children:u.node,className:u.string};ar.displayName="CSidebarHeader";var or=g.forwardRef(function(e,t){var r=e.children,n=e.className,i=z(e,["children","className"]);return p.createElement("div",_({className:M("tab-content",n)},i,{ref:t}),r)});or.propTypes={children:u.node,className:u.string};or.displayName="CTabContent";var sr=g.forwardRef(function(e,t){var r=e.children,n=e.className,i=e.onHide,a=e.onShow,o=e.transition,s=o===void 0?!0:o,l=e.visible,c=z(e,["children","className","onHide","onShow","transition","visible"]),d=g.useRef(),m=Pe(t,d);return p.createElement(ue,{in:l,nodeRef:d,onEnter:a,onExit:i,timeout:150},function(v){return p.createElement("div",_({className:M("tab-pane",{active:l,fade:s,show:v==="entered"},n)},c,{ref:m}),r)})});sr.propTypes={children:u.node,className:u.string,onHide:u.func,onShow:u.func,transition:u.bool,visible:u.bool};sr.displayName="CTabPane";const ua=p.lazy(()=>O(()=>import("./Dashboard-CSWFqJqc.js"),__vite__mapDeps([0,1,2]),import.meta.url)),_r=p.lazy(()=>O(()=>import("./Colors-ChMP3dxt.js"),__vite__mapDeps([3,1,2,4,5,6,7,8]),import.meta.url)),da=p.lazy(()=>O(()=>import("./Typography-1JhztNZn.js"),__vite__mapDeps([9,1,2,4,5,6,8]),import.meta.url)),pa=p.lazy(()=>O(()=>import("./Accordion-dcjJFzKa.js"),__vite__mapDeps([10,1,2,4,7,5,6,11,8]),import.meta.url)),fa=p.lazy(()=>O(()=>import("./Breadcrumbs-Cu2_herO.js"),__vite__mapDeps([12,1,2,4,7,5,6,8]),import.meta.url)),kr=p.lazy(()=>O(()=>import("./Cards-CniHNcCG.js"),__vite__mapDeps([13,1,2,4,14,7,5,6,15,16,17,18,8]),import.meta.url)),ma=p.lazy(()=>O(()=>import("./Carousels-DUdGWsYK.js"),__vite__mapDeps([19,1,2,4,14,7,5,6,8]),import.meta.url)),ha=p.lazy(()=>O(()=>import("./Collapses-Dl171kQS.js"),__vite__mapDeps([20,1,2,4,7,5,6,11,8]),import.meta.url)),va=p.lazy(()=>O(()=>import("./ListGroups-DSRI2x7_.js"),__vite__mapDeps([21,1,2,4,7,5,6,16,22,23,24,8]),import.meta.url)),ga=p.lazy(()=>O(()=>import("./Navs-D1wvwi7g.js"),__vite__mapDeps([25,1,2,4,7,5,6,8]),import.meta.url)),ba=p.lazy(()=>O(()=>import("./Paginations-DZHvFOvv.js"),__vite__mapDeps([26,1,2,4,7,5,6,8]),import.meta.url)),ya=p.lazy(()=>O(()=>import("./Placeholders-DeaUn_Ru.js"),__vite__mapDeps([27,1,2,4,14,7,5,6,15,8]),import.meta.url)),Ca=p.lazy(()=>O(()=>import("./Popovers-DacycEyH.js"),__vite__mapDeps([28,1,2,4,7,5,6,29,30,31,8]),import.meta.url)),xa=p.lazy(()=>O(()=>import("./Progress-D8_LEqs4.js"),__vite__mapDeps([32,1,2,4,7,5,6,33,8]),import.meta.url)),Ea=p.lazy(()=>O(()=>import("./Spinners-I70k7UMS.js"),__vite__mapDeps([34,1,2,4,7,5,6,8]),import.meta.url)),wa=p.lazy(()=>O(()=>import("./Tabs-BdcyedAU.js"),__vite__mapDeps([35,1,2,4,7,5,6,31,8]),import.meta.url)),Na=p.lazy(()=>O(()=>import("./Tables-CJSPl4YE.js"),__vite__mapDeps([36,1,2,4,7,5,6,37,8]),import.meta.url)),Oa=p.lazy(()=>O(()=>import("./Tooltips-BFBUv_j7.js"),__vite__mapDeps([38,1,2,4,7,5,6,39,30,31,8]),import.meta.url)),Lr=p.lazy(()=>O(()=>import("./Buttons-Dhb1MtjK.js"),__vite__mapDeps([40,1,2,4,7,5,6,41,8]),import.meta.url)),Sa=p.lazy(()=>O(()=>import("./ButtonGroups-Chr2KOpP.js"),__vite__mapDeps([42,1,2,4,7,5,6,43,22,23,24,44,45,46,47,8]),import.meta.url)),Ta=p.lazy(()=>O(()=>import("./Dropdowns-DPpf5yDu.js"),__vite__mapDeps([48,1,2,4,7,5,6,47,43,8]),import.meta.url)),Ra=p.lazy(()=>O(()=>import("./ChecksRadios-D7DsE6L6.js"),__vite__mapDeps([49,1,2,4,7,5,6,22,23,24,8]),import.meta.url)),Aa=p.lazy(()=>O(()=>import("./FloatingLabels-CujwugcH.js"),__vite__mapDeps([50,1,2,4,7,5,6,46,23,24,45,51,52,8]),import.meta.url)),jr=p.lazy(()=>O(()=>import("./FormControl-BIzpcQSk.js"),__vite__mapDeps([53,1,2,4,7,5,6,54,24,45,46,23,51,8]),import.meta.url)),_a=p.lazy(()=>O(()=>import("./InputGroup-qHiF8Jc0.js"),__vite__mapDeps([55,1,2,4,7,5,6,44,45,46,23,24,51,22,47,52,8]),import.meta.url)),ka=p.lazy(()=>O(()=>import("./Layout-BCq8b_mV.js"),__vite__mapDeps([56,1,2,4,7,5,6,45,46,23,24,54,52,22,44,8]),import.meta.url)),La=p.lazy(()=>O(()=>import("./Range-DLmeyKqg.js"),__vite__mapDeps([57,1,2,4,7,5,6,24,8]),import.meta.url)),ja=p.lazy(()=>O(()=>import("./Select-D4guXORx.js"),__vite__mapDeps([58,1,2,4,7,5,6,52,46,23,24,8]),import.meta.url)),Da=p.lazy(()=>O(()=>import("./Validation-CaUMjHMq.js"),__vite__mapDeps([59,1,2,4,7,5,6,54,24,45,46,23,44,52,22,51,8]),import.meta.url)),Pa=p.lazy(()=>O(()=>import("./Charts-DDOjm_TY.js"),__vite__mapDeps([60,1,2,61,4,7,5,6,8]),import.meta.url)),Dr=p.lazy(()=>O(()=>import("./CoreUIIcons-F_gPw1i5.js"),__vite__mapDeps([62,1,2,63,41,64,65,8,66,4,67,7,5,6]),import.meta.url)),Ma=p.lazy(()=>O(()=>import("./Flags-C0QiyoVo.js"),__vite__mapDeps([68,1,2,66,4,67,7,5,6,8]),import.meta.url)),za=p.lazy(()=>O(()=>import("./Brands-CCROc7v2.js").then(e=>e.B),__vite__mapDeps([66,1,2,4,67,7,5,6]),import.meta.url)),Pr=p.lazy(()=>O(()=>import("./Alerts-X1-6Wce8.js"),__vite__mapDeps([69,1,2,4,7,5,6,8]),import.meta.url)),Va=p.lazy(()=>O(()=>import("./Badges-CHGirAJD.js"),__vite__mapDeps([70,1,2,4,7,5,6,8]),import.meta.url)),Ia=p.lazy(()=>O(()=>import("./Modals-Bk1C2UqN.js"),__vite__mapDeps([71,1,2,4,7,5,6,72,29,30,31,39,8]),import.meta.url)),Wa=p.lazy(()=>O(()=>import("./Toasts-CNu6ahi0.js"),__vite__mapDeps([73,1,2,4,7,5,6,74,8]),import.meta.url)),Ba=p.lazy(()=>O(()=>import("./Widgets-DSNH8gOR.js"),__vite__mapDeps([75,1,2,4,61,7,5,6,67,63,33,17,8,41,18]),import.meta.url)),Ha=p.lazy(()=>O(()=>import("./EnergyMeters-CzD6vkmU.js"),__vite__mapDeps([76,1,2,74,5,6,4,37,72,54,45,46,23,24,8]),import.meta.url)),qa=p.lazy(()=>O(()=>import("./Locations-CDW5y2tq.js"),__vite__mapDeps([77,1,2,74,5,6,4,37,72,54,45,46,23,24,8]),import.meta.url)),Fa=p.lazy(()=>O(()=>import("./Mappings-CjCKNWV9.js"),__vite__mapDeps([78,1,2,74,5,6,4,37,72,54,52,46,23,24,8]),import.meta.url)),$a=p.lazy(()=>O(()=>import("./Reports-BPZEoZmh.js"),__vite__mapDeps([79,1,2,74,5,6,54,52,46,23,24,4,8,80]),import.meta.url)),Za=p.lazy(()=>O(()=>import("./Users-Av0TM49y.js"),__vite__mapDeps([81,1,2,74,5,6,4,37,72,54,45,46,23,24,52,8]),import.meta.url)),Xa=p.lazy(()=>O(()=>import("./Login--rsbMHaO.js"),__vite__mapDeps([82,1,2,4,7,18,5,54,44,8,45,46,23,24,64]),import.meta.url)),yn=[{path:"/",exact:!0,name:"Home"},{path:"/dashboard",name:"Dashboard",element:ua},{path:"/theme",name:"Theme",element:_r,exact:!0},{path:"/theme/colors",name:"Colors",element:_r},{path:"/theme/typography",name:"Typography",element:da},{path:"/base",name:"Base",element:kr,exact:!0},{path:"/base/accordion",name:"Accordion",element:pa},{path:"/base/breadcrumbs",name:"Breadcrumbs",element:fa},{path:"/base/cards",name:"Cards",element:kr},{path:"/base/carousels",name:"Carousel",element:ma},{path:"/base/collapses",name:"Collapse",element:ha},{path:"/base/list-groups",name:"List Groups",element:va},{path:"/base/navs",name:"Navs",element:ga},{path:"/base/paginations",name:"Paginations",element:ba},{path:"/base/placeholders",name:"Placeholders",element:ya},{path:"/base/popovers",name:"Popovers",element:Ca},{path:"/base/progress",name:"Progress",element:xa},{path:"/base/spinners",name:"Spinners",element:Ea},{path:"/base/tabs",name:"Tabs",element:wa},{path:"/base/tables",name:"Tables",element:Na},{path:"/base/tooltips",name:"Tooltips",element:Oa},{path:"/buttons",name:"Buttons",element:Lr,exact:!0},{path:"/buttons/buttons",name:"Buttons",element:Lr},{path:"/buttons/dropdowns",name:"Dropdowns",element:Ta},{path:"/buttons/button-groups",name:"Button Groups",element:Sa},{path:"/charts",name:"Charts",element:Pa},{path:"/forms",name:"Forms",element:jr,exact:!0},{path:"/forms/form-control",name:"Form Control",element:jr},{path:"/forms/select",name:"Select",element:ja},{path:"/forms/checks-radios",name:"Checks & Radios",element:Ra},{path:"/forms/range",name:"Range",element:La},{path:"/forms/input-group",name:"Input Group",element:_a},{path:"/forms/floating-labels",name:"Floating Labels",element:Aa},{path:"/forms/layout",name:"Layout",element:ka},{path:"/forms/validation",name:"Validation",element:Da},{path:"/icons",exact:!0,name:"Icons",element:Dr},{path:"/icons/coreui-icons",name:"CoreUI Icons",element:Dr},{path:"/icons/flags",name:"Flags",element:Ma},{path:"/icons/brands",name:"Brands",element:za},{path:"/notifications",name:"Notifications",element:Pr,exact:!0},{path:"/notifications/alerts",name:"Alerts",element:Pr},{path:"/notifications/badges",name:"Badges",element:Va},{path:"/notifications/modals",name:"Modals",element:Ia},{path:"/notifications/toasts",name:"Toasts",element:Wa},{path:"/widgets",name:"Widgets",element:Ba},{path:"/energy-meters",name:"Energy Meters",element:Ha},{path:"/locations",name:"Locations",element:qa},{path:"/mappings",name:"Mappings",element:Fa},{path:"/reports",name:"Reports",element:$a},{path:"/users",name:"Users",element:Za},{path:"/login",name:"Login",element:Xa}],Ua=()=>{const e=Wn().pathname,t=(i,a)=>{const o=a.find(s=>s.path===i);return o?o.name:!1},n=(i=>{const a=[];return i.split("/").reduce((o,s,l,c)=>{const d=`${o}/${s}`,m=t(d,yn);return m&&a.push({pathname:d,name:m,active:l+1===c.length}),d}),a})(e);return f.jsxs(Zt,{className:"my-0",children:[f.jsx(et,{href:"/",children:"Home"}),n.map((i,a)=>g.createElement(et,{...i.active?{active:!0}:{href:i.pathname},key:a},i.name))]})},Ga=p.memo(Ua),Ya=()=>f.jsx(Rt,{className:"px-4",lg:!0,children:f.jsx(g.Suspense,{fallback:f.jsx(qn,{color:"primary"}),children:f.jsxs(Bn,{children:[yn.map((e,t)=>e.element&&f.jsx(yr,{path:e.path,exact:e.exact,name:e.name,element:f.jsx(e.element,{})},t)),f.jsx(yr,{path:"/",element:f.jsx(Hn,{to:"dashboard",replace:!0})})]})})}),Ka=p.memo(Ya),Qa=()=>f.jsx(Yt,{className:"px-4",children:f.jsxs("div",{children:[f.jsx("span",{className:"ms-1",children:"Banas Dairy"}),f.jsx("span",{className:"ms-1",children:"© 2025 ARP Automation"})]})}),Ja=p.memo(Qa);var eo=["512 512","<polygon fill='var(--ci-primary-color, currentColor)' points='77.155 272.034 351.75 272.034 351.75 272.033 351.75 240.034 351.75 240.033 77.155 240.033 152.208 164.98 152.208 164.98 152.208 164.979 129.58 142.353 15.899 256.033 15.9 256.034 15.899 256.034 129.58 369.715 152.208 347.088 152.208 347.087 152.208 347.087 77.155 272.034' class='ci-primary'/><polygon fill='var(--ci-primary-color, currentColor)' points='160 16 160 48 464 48 464 464 160 464 160 496 496 496 496 16 160 16' class='ci-primary'/>"],to=["512 512","<polygon fill='var(--ci-primary-color, currentColor)' points='388.632 393.82 495.823 255.94 388.684 118.178 363.424 137.822 455.288 255.944 363.368 374.18 388.632 393.82' class='ci-primary'/><polygon fill='var(--ci-primary-color, currentColor)' points='148.579 374.181 56.712 255.999 148.629 137.823 123.371 118.177 16.177 255.993 123.314 393.819 148.579 374.181' class='ci-primary'/><polygon fill='var(--ci-primary-color, currentColor)' points='330.529 16 297.559 16 178.441 496 211.412 496 330.529 16' class='ci-primary'/>"],Mr=["512 512","<path fill='var(--ci-primary-color, currentColor)' d='M256,16C123.452,16,16,123.452,16,256S123.452,496,256,496,496,388.548,496,256,388.548,16,256,16ZM234,462.849a208.346,208.346,0,0,1-169.667-125.9c-.364-.859-.706-1.724-1.057-2.587L234,429.939Zm0-69.582L50.889,290.76A209.848,209.848,0,0,1,48,256q0-9.912.922-19.67L234,339.939Zm0-90L54.819,202.96a206.385,206.385,0,0,1,9.514-27.913Q67.1,168.5,70.3,162.191L234,253.934Zm0-86.015L86.914,134.819a209.42,209.42,0,0,1,22.008-25.9q3.72-3.72,7.6-7.228L234,166.027Zm0-87.708L144.352,80.451A206.951,206.951,0,0,1,234,49.151ZM464,256A207.775,207.775,0,0,1,266,463.761V48.239A207.791,207.791,0,0,1,464,256Z' class='ci-primary'/>"],ro=["512 512","<path fill='var(--ci-primary-color, currentColor)' d='M334.627,16H48V496H472V153.373ZM440,166.627V168H320V48h1.373ZM80,464V48H288V200H440V464Z' class='ci-primary'/>"],no=["512 512","<rect width='264' height='32' x='208' y='80' fill='var(--ci-primary-color, currentColor)' class='ci-primary'/><path fill='var(--ci-primary-color, currentColor)' d='M40,96a64,64,0,1,0,64-64A64.072,64.072,0,0,0,40,96Zm64-32A32,32,0,1,1,72,96,32.036,32.036,0,0,1,104,64Z' class='ci-primary'/><rect width='264' height='32' x='208' y='240' fill='var(--ci-primary-color, currentColor)' class='ci-primary'/><path fill='var(--ci-primary-color, currentColor)' d='M104,320a64,64,0,1,0-64-64A64.072,64.072,0,0,0,104,320Zm0-96a32,32,0,1,1-32,32A32.036,32.036,0,0,1,104,224Z' class='ci-primary'/><rect width='264' height='32' x='208' y='400' fill='var(--ci-primary-color, currentColor)' class='ci-primary'/><path fill='var(--ci-primary-color, currentColor)' d='M104,480a64,64,0,1,0-64-64A64.072,64.072,0,0,0,104,480Zm0-96a32,32,0,1,1-32,32A32.036,32.036,0,0,1,104,384Z' class='ci-primary'/>"],io=["512 512","<path fill='var(--ci-primary-color, currentColor)' d='M478.465,89.022,329.6,47.382,180.3,89.438,41.459,50.052h0A20,20,0,0,0,16,69.293v340.6a24.093,24.093,0,0,0,17.449,23.089l146.817,41.65,149.365-42.074,140.983,39.436A20,20,0,0,0,496,452.728V112.135A24.08,24.08,0,0,0,478.465,89.022ZM163,436.466,48,403.842V85.17l115,32.624Zm150.615-32.647L195,437.231V118.542L313.615,85.13ZM464,436.91,345.615,403.8V85.089L464,118.2Z' class='ci-primary'/>"],ao=["512 512","<path fill='var(--ci-primary-color, currentColor)' d='M444.4,235.236,132.275,49.449A24,24,0,0,0,96,70.072V434.214a24.017,24.017,0,0,0,35.907,20.839L444.03,276.7a24,24,0,0,0,.367-41.461ZM128,420.429V84.144L416.244,255.718Z' class='ci-primary'/>"],oo=["512 512","<rect width='352' height='32' x='80' y='96' fill='var(--ci-primary-color, currentColor)' class='ci-primary'/><rect width='352' height='32' x='80' y='240' fill='var(--ci-primary-color, currentColor)' class='ci-primary'/><rect width='352' height='32' x='80' y='384' fill='var(--ci-primary-color, currentColor)' class='ci-primary'/>"],zr=["512 512","<path fill='var(--ci-primary-color, currentColor)' d='M268.279,496c-67.574,0-130.978-26.191-178.534-73.745S16,311.293,16,243.718A252.252,252.252,0,0,1,154.183,18.676a24.44,24.44,0,0,1,34.46,28.958,220.12,220.12,0,0,0,54.8,220.923A218.746,218.746,0,0,0,399.085,333.2h0a220.2,220.2,0,0,0,65.277-9.846,24.439,24.439,0,0,1,28.959,34.461A252.256,252.256,0,0,1,268.279,496ZM153.31,55.781A219.3,219.3,0,0,0,48,243.718C48,365.181,146.816,464,268.279,464a219.3,219.3,0,0,0,187.938-105.31,252.912,252.912,0,0,1-57.13,6.513h0a250.539,250.539,0,0,1-178.268-74.016,252.147,252.147,0,0,1-67.509-235.4Z' class='ci-primary'/>"],so=["512 512","<path fill='var(--ci-primary-color, currentColor)' d='M245.151,168a88,88,0,1,0,88,88A88.1,88.1,0,0,0,245.151,168Zm0,144a56,56,0,1,1,56-56A56.063,56.063,0,0,1,245.151,312Z' class='ci-primary'/><path fill='var(--ci-primary-color, currentColor)' d='M464.7,322.319l-31.77-26.153a193.081,193.081,0,0,0,0-80.332l31.77-26.153a19.941,19.941,0,0,0,4.606-25.439l-32.612-56.483a19.936,19.936,0,0,0-24.337-8.73l-38.561,14.447a192.038,192.038,0,0,0-69.54-40.192L297.49,32.713A19.936,19.936,0,0,0,277.762,16H212.54a19.937,19.937,0,0,0-19.728,16.712L186.05,73.284a192.03,192.03,0,0,0-69.54,40.192L77.945,99.027a19.937,19.937,0,0,0-24.334,8.731L21,164.245a19.94,19.94,0,0,0,4.61,25.438l31.767,26.151a193.081,193.081,0,0,0,0,80.332l-31.77,26.153A19.942,19.942,0,0,0,21,347.758l32.612,56.483a19.937,19.937,0,0,0,24.337,8.73l38.562-14.447a192.03,192.03,0,0,0,69.54,40.192l6.762,40.571A19.937,19.937,0,0,0,212.54,496h65.222a19.936,19.936,0,0,0,19.728-16.712l6.763-40.572a192.038,192.038,0,0,0,69.54-40.192l38.564,14.449a19.938,19.938,0,0,0,24.334-8.731L469.3,347.755A19.939,19.939,0,0,0,464.7,322.319Zm-50.636,57.12-48.109-18.024-7.285,7.334a159.955,159.955,0,0,1-72.625,41.973l-10,2.636L267.6,464h-44.89l-8.442-50.642-10-2.636a159.955,159.955,0,0,1-72.625-41.973l-7.285-7.334L76.241,379.439,53.8,340.562l39.629-32.624-2.7-9.973a160.9,160.9,0,0,1,0-83.93l2.7-9.972L53.8,171.439l22.446-38.878,48.109,18.024,7.285-7.334a159.955,159.955,0,0,1,72.625-41.973l10-2.636L222.706,48H267.6l8.442,50.642,10,2.636a159.955,159.955,0,0,1,72.625,41.973l7.285,7.334,48.109-18.024,22.447,38.877-39.629,32.625,2.7,9.972a160.9,160.9,0,0,1,0,83.93l-2.7,9.973,39.629,32.623Z' class='ci-primary'/>"],lo=["512 512","<path fill='var(--ci-primary-color, currentColor)' d='M425.706,142.294A240,240,0,0,0,16,312v88H160V368H48V312c0-114.691,93.309-208,208-208s208,93.309,208,208v56H352v32H496V312A238.432,238.432,0,0,0,425.706,142.294Z' class='ci-primary'/><rect width='32' height='32' x='80' y='264' fill='var(--ci-primary-color, currentColor)' class='ci-primary'/><rect width='32' height='32' x='240' y='128' fill='var(--ci-primary-color, currentColor)' class='ci-primary'/><rect width='32' height='32' x='136' y='168' fill='var(--ci-primary-color, currentColor)' class='ci-primary'/><rect width='32' height='32' x='400' y='264' fill='var(--ci-primary-color, currentColor)' class='ci-primary'/><path fill='var(--ci-primary-color, currentColor)' d='M297.222,335.1l69.2-144.173-28.85-13.848L268.389,321.214A64.141,64.141,0,1,0,297.222,335.1ZM256,416a32,32,0,1,1,32-32A32.036,32.036,0,0,1,256,416Z' class='ci-primary'/>"],Vr=["512 512","<path fill='var(--ci-primary-color, currentColor)' d='M256,104c-83.813,0-152,68.187-152,152s68.187,152,152,152,152-68.187,152-152S339.813,104,256,104Zm0,272A120,120,0,1,1,376,256,120.136,120.136,0,0,1,256,376Z' class='ci-primary'/><rect width='32' height='48' x='240' y='16' fill='var(--ci-primary-color, currentColor)' class='ci-primary'/><rect width='32' height='48' x='240' y='448' fill='var(--ci-primary-color, currentColor)' class='ci-primary'/><rect width='48' height='32' x='448' y='240' fill='var(--ci-primary-color, currentColor)' class='ci-primary'/><rect width='48' height='32' x='16' y='240' fill='var(--ci-primary-color, currentColor)' class='ci-primary'/><rect width='32' height='45.255' x='400' y='393.373' fill='var(--ci-primary-color, currentColor)' class='ci-primary' transform='rotate(-45 416 416)'/><rect width='32.001' height='45.255' x='80' y='73.373' fill='var(--ci-primary-color, currentColor)' class='ci-primary' transform='rotate(-45 96 96)'/><rect width='45.255' height='32' x='73.373' y='400' fill='var(--ci-primary-color, currentColor)' class='ci-primary' transform='rotate(-45.001 96.002 416.003)'/><rect width='45.255' height='32.001' x='393.373' y='80' fill='var(--ci-primary-color, currentColor)' class='ci-primary' transform='rotate(-45 416 96)'/>"];const co=""+new URL("8-CjJZ1yLz.jpg",import.meta.url).href,uo=()=>f.jsxs(nt,{variant:"nav-item",children:[f.jsx(at,{placement:"bottom-end",className:"py-0 pe-0",caret:!1,children:f.jsx(qt,{src:co,size:"md"})}),f.jsxs(it,{className:"pt-0",placement:"bottom-end",children:[f.jsxs(Gt,{className:"bg-body-secondary fw-semibold mb-2",children:["User - ",localStorage.getItem("username")]}),f.jsxs(_e,{href:"#",onClick:()=>{localStorage.removeItem("token"),window.location.href="/"},children:[f.jsx(H,{icon:eo,className:"me-2"}),"Logout"]})]})]}),po=()=>{const e=g.useRef(),{colorMode:t,setColorMode:r}=Fn("coreui-free-react-admin-template-theme"),n=an(),i=Tt(a=>a.sidebarShow);return g.useEffect(()=>{document.addEventListener("scroll",()=>{e.current&&e.current.classList.toggle("shadow-sm",document.documentElement.scrollTop>0)})},[]),f.jsxs(Kt,{position:"sticky",className:"mb-4 p-0",ref:e,children:[f.jsxs(Rt,{className:"border-bottom px-4",fluid:!0,children:[f.jsx(Qt,{onClick:()=>n({type:"set",sidebarShow:!i}),style:{marginInlineStart:"-14px"},children:f.jsx(H,{icon:oo,size:"lg"})}),f.jsxs(He,{className:"d-none d-md-flex",children:[f.jsx(ee,{children:f.jsx(ge,{to:"/dashboard",as:on,children:"Dashboard"})}),f.jsx(ee,{children:f.jsx(ge,{href:"#/reports",children:"Reports"})})]}),f.jsx(He,{className:"ms-auto"}),f.jsxs(He,{children:[f.jsx("li",{className:"nav-item py-1",children:f.jsx("div",{className:"vr h-100 mx-2 text-body text-opacity-75"})}),f.jsxs(nt,{variant:"nav-item",placement:"bottom-end",children:[f.jsx(at,{caret:!1,children:t==="dark"?f.jsx(H,{icon:zr,size:"lg"}):t==="auto"?f.jsx(H,{icon:Mr,size:"lg"}):f.jsx(H,{icon:Vr,size:"lg"})}),f.jsxs(it,{children:[f.jsxs(_e,{active:t==="light",className:"d-flex align-items-center",as:"button",type:"button",onClick:()=>r("light"),children:[f.jsx(H,{className:"me-2",icon:Vr,size:"lg"})," Light"]}),f.jsxs(_e,{active:t==="dark",className:"d-flex align-items-center",as:"button",type:"button",onClick:()=>r("dark"),children:[f.jsx(H,{className:"me-2",icon:zr,size:"lg"})," Dark"]}),f.jsxs(_e,{active:t==="auto",className:"d-flex align-items-center",as:"button",type:"button",onClick:()=>r("auto"),children:[f.jsx(H,{className:"me-2",icon:Mr,size:"lg"})," Auto"]})]})]}),f.jsx("li",{className:"nav-item py-1",children:f.jsx("div",{className:"vr h-100 mx-2 text-body text-opacity-75"})}),f.jsx(uo,{})]})]}),f.jsx(Rt,{className:"px-4",fluid:!0,children:f.jsx(Ga,{})})]})};var ct,Ir;function lr(){if(Ir)return ct;Ir=1;function e(t){var r=typeof t;return t!=null&&(r=="object"||r=="function")}return ct=e,ct}var ut,Wr;function fo(){if(Wr)return ut;Wr=1;var e=typeof Ue=="object"&&Ue&&Ue.Object===Object&&Ue;return ut=e,ut}var dt,Br;function Cn(){if(Br)return dt;Br=1;var e=fo(),t=typeof self=="object"&&self&&self.Object===Object&&self,r=e||t||Function("return this")();return dt=r,dt}var pt,Hr;function mo(){if(Hr)return pt;Hr=1;var e=Cn(),t=function(){return e.Date.now()};return pt=t,pt}var ft,qr;function ho(){if(qr)return ft;qr=1;var e=/\s/;function t(r){for(var n=r.length;n--&&e.test(r.charAt(n)););return n}return ft=t,ft}var mt,Fr;function vo(){if(Fr)return mt;Fr=1;var e=ho(),t=/^\s+/;function r(n){return n&&n.slice(0,e(n)+1).replace(t,"")}return mt=r,mt}var ht,$r;function xn(){if($r)return ht;$r=1;var e=Cn(),t=e.Symbol;return ht=t,ht}var vt,Zr;function go(){if(Zr)return vt;Zr=1;var e=xn(),t=Object.prototype,r=t.hasOwnProperty,n=t.toString,i=e?e.toStringTag:void 0;function a(o){var s=r.call(o,i),l=o[i];try{o[i]=void 0;var c=!0}catch{}var d=n.call(o);return c&&(s?o[i]=l:delete o[i]),d}return vt=a,vt}var gt,Xr;function bo(){if(Xr)return gt;Xr=1;var e=Object.prototype,t=e.toString;function r(n){return t.call(n)}return gt=r,gt}var bt,Ur;function yo(){if(Ur)return bt;Ur=1;var e=xn(),t=go(),r=bo(),n="[object Null]",i="[object Undefined]",a=e?e.toStringTag:void 0;function o(s){return s==null?s===void 0?i:n:a&&a in Object(s)?t(s):r(s)}return bt=o,bt}var yt,Gr;function Co(){if(Gr)return yt;Gr=1;function e(t){return t!=null&&typeof t=="object"}return yt=e,yt}var Ct,Yr;function xo(){if(Yr)return Ct;Yr=1;var e=yo(),t=Co(),r="[object Symbol]";function n(i){return typeof i=="symbol"||t(i)&&e(i)==r}return Ct=n,Ct}var xt,Kr;function Eo(){if(Kr)return xt;Kr=1;var e=vo(),t=lr(),r=xo(),n=NaN,i=/^[-+]0x[0-9a-f]+$/i,a=/^0b[01]+$/i,o=/^0o[0-7]+$/i,s=parseInt;function l(c){if(typeof c=="number")return c;if(r(c))return n;if(t(c)){var d=typeof c.valueOf=="function"?c.valueOf():c;c=t(d)?d+"":d}if(typeof c!="string")return c===0?c:+c;c=e(c);var m=a.test(c);return m||o.test(c)?s(c.slice(2),m?2:8):i.test(c)?n:+c}return xt=l,xt}var Et,Qr;function En(){if(Qr)return Et;Qr=1;var e=lr(),t=mo(),r=Eo(),n="Expected a function",i=Math.max,a=Math.min;function o(s,l,c){var d,m,v,h,x,b,y=0,w=!1,S=!1,C=!0;if(typeof s!="function")throw new TypeError(n);l=r(l)||0,e(c)&&(w=!!c.leading,S="maxWait"in c,v=S?i(r(c.maxWait)||0,l):v,C="trailing"in c?!!c.trailing:C);function E(T){var j=d,D=m;return d=m=void 0,y=T,h=s.apply(D,j),h}function R(T){return y=T,x=setTimeout(L,l),w?E(T):h}function N(T){var j=T-b,D=T-y,W=l-j;return S?a(W,v-D):W}function k(T){var j=T-b,D=T-y;return b===void 0||j>=l||j<0||S&&D>=v}function L(){var T=t();if(k(T))return A(T);x=setTimeout(L,N(T))}function A(T){return x=void 0,C&&d?E(T):(d=m=void 0,h)}function P(){x!==void 0&&clearTimeout(x),y=0,d=b=m=x=void 0}function V(){return x===void 0?h:A(t())}function I(){var T=t(),j=k(T);if(d=arguments,m=this,b=T,j){if(x===void 0)return R(b);if(S)return clearTimeout(x),x=setTimeout(L,l),E(b)}return x===void 0&&(x=setTimeout(L,l)),h}return I.cancel=P,I.flush=V,I}return Et=o,Et}var wo=En();const wt=jt(wo);var Nt,Jr;function No(){if(Jr)return Nt;Jr=1;var e=En(),t=lr(),r="Expected a function";function n(i,a,o){var s=!0,l=!0;if(typeof i!="function")throw new TypeError(r);return t(o)&&(s="leading"in o?!!o.leading:s,l="trailing"in o?!!o.trailing:l),e(i,a,{leading:s,maxWait:a,trailing:l})}return Nt=n,Nt}var Oo=No();const So=jt(Oo);var Ae=function(){return Ae=Object.assign||function(t){for(var r,n=1,i=arguments.length;n<i;n++){r=arguments[n];for(var a in r)Object.prototype.hasOwnProperty.call(r,a)&&(t[a]=r[a])}return t},Ae.apply(this,arguments)};function wn(e){return!e||!e.ownerDocument||!e.ownerDocument.defaultView?window:e.ownerDocument.defaultView}function Nn(e){return!e||!e.ownerDocument?document:e.ownerDocument}var On=function(e){var t={},r=Array.prototype.reduce.call(e,function(n,i){var a=i.name.match(/data-simplebar-(.+)/);if(a){var o=a[1].replace(/\W+(.)/g,function(s,l){return l.toUpperCase()});switch(i.value){case"true":n[o]=!0;break;case"false":n[o]=!1;break;case void 0:n[o]=!0;break;default:n[o]=i.value}}return n},t);return r};function Sn(e,t){var r;e&&(r=e.classList).add.apply(r,t.split(" "))}function Tn(e,t){e&&t.split(" ").forEach(function(r){e.classList.remove(r)})}function Rn(e){return".".concat(e.split(" ").join("."))}var cr=!!(typeof window<"u"&&window.document&&window.document.createElement),To=Object.freeze({__proto__:null,addClasses:Sn,canUseDOM:cr,classNamesToQuery:Rn,getElementDocument:Nn,getElementWindow:wn,getOptions:On,removeClasses:Tn}),Re=null,en=null;cr&&window.addEventListener("resize",function(){en!==window.devicePixelRatio&&(en=window.devicePixelRatio,Re=null)});function tn(){if(Re===null){if(typeof document>"u")return Re=0,Re;var e=document.body,t=document.createElement("div");t.classList.add("simplebar-hide-scrollbar"),e.appendChild(t);var r=t.getBoundingClientRect().right;e.removeChild(t),Re=r}return Re}var me=wn,Ot=Nn,Ro=On,he=Sn,ve=Tn,X=Rn,Ve=function(){function e(t,r){r===void 0&&(r={});var n=this;if(this.removePreventClickId=null,this.minScrollbarWidth=20,this.stopScrollDelay=175,this.isScrolling=!1,this.isMouseEntering=!1,this.isDragging=!1,this.scrollXTicking=!1,this.scrollYTicking=!1,this.wrapperEl=null,this.contentWrapperEl=null,this.contentEl=null,this.offsetEl=null,this.maskEl=null,this.placeholderEl=null,this.heightAutoObserverWrapperEl=null,this.heightAutoObserverEl=null,this.rtlHelpers=null,this.scrollbarWidth=0,this.resizeObserver=null,this.mutationObserver=null,this.elStyles=null,this.isRtl=null,this.mouseX=0,this.mouseY=0,this.onMouseMove=function(){},this.onWindowResize=function(){},this.onStopScrolling=function(){},this.onMouseEntered=function(){},this.onScroll=function(){var i=me(n.el);n.scrollXTicking||(i.requestAnimationFrame(n.scrollX),n.scrollXTicking=!0),n.scrollYTicking||(i.requestAnimationFrame(n.scrollY),n.scrollYTicking=!0),n.isScrolling||(n.isScrolling=!0,he(n.el,n.classNames.scrolling)),n.showScrollbar("x"),n.showScrollbar("y"),n.onStopScrolling()},this.scrollX=function(){n.axis.x.isOverflowing&&n.positionScrollbar("x"),n.scrollXTicking=!1},this.scrollY=function(){n.axis.y.isOverflowing&&n.positionScrollbar("y"),n.scrollYTicking=!1},this._onStopScrolling=function(){ve(n.el,n.classNames.scrolling),n.options.autoHide&&(n.hideScrollbar("x"),n.hideScrollbar("y")),n.isScrolling=!1},this.onMouseEnter=function(){n.isMouseEntering||(he(n.el,n.classNames.mouseEntered),n.showScrollbar("x"),n.showScrollbar("y"),n.isMouseEntering=!0),n.onMouseEntered()},this._onMouseEntered=function(){ve(n.el,n.classNames.mouseEntered),n.options.autoHide&&(n.hideScrollbar("x"),n.hideScrollbar("y")),n.isMouseEntering=!1},this._onMouseMove=function(i){n.mouseX=i.clientX,n.mouseY=i.clientY,(n.axis.x.isOverflowing||n.axis.x.forceVisible)&&n.onMouseMoveForAxis("x"),(n.axis.y.isOverflowing||n.axis.y.forceVisible)&&n.onMouseMoveForAxis("y")},this.onMouseLeave=function(){n.onMouseMove.cancel(),(n.axis.x.isOverflowing||n.axis.x.forceVisible)&&n.onMouseLeaveForAxis("x"),(n.axis.y.isOverflowing||n.axis.y.forceVisible)&&n.onMouseLeaveForAxis("y"),n.mouseX=-1,n.mouseY=-1},this._onWindowResize=function(){n.scrollbarWidth=n.getScrollbarWidth(),n.hideNativeScrollbar()},this.onPointerEvent=function(i){if(!(!n.axis.x.track.el||!n.axis.y.track.el||!n.axis.x.scrollbar.el||!n.axis.y.scrollbar.el)){var a,o;n.axis.x.track.rect=n.axis.x.track.el.getBoundingClientRect(),n.axis.y.track.rect=n.axis.y.track.el.getBoundingClientRect(),(n.axis.x.isOverflowing||n.axis.x.forceVisible)&&(a=n.isWithinBounds(n.axis.x.track.rect)),(n.axis.y.isOverflowing||n.axis.y.forceVisible)&&(o=n.isWithinBounds(n.axis.y.track.rect)),(a||o)&&(i.stopPropagation(),i.type==="pointerdown"&&i.pointerType!=="touch"&&(a&&(n.axis.x.scrollbar.rect=n.axis.x.scrollbar.el.getBoundingClientRect(),n.isWithinBounds(n.axis.x.scrollbar.rect)?n.onDragStart(i,"x"):n.onTrackClick(i,"x")),o&&(n.axis.y.scrollbar.rect=n.axis.y.scrollbar.el.getBoundingClientRect(),n.isWithinBounds(n.axis.y.scrollbar.rect)?n.onDragStart(i,"y"):n.onTrackClick(i,"y"))))}},this.drag=function(i){var a,o,s,l,c,d,m,v,h,x,b;if(!(!n.draggedAxis||!n.contentWrapperEl)){var y,w=n.axis[n.draggedAxis].track,S=(o=(a=w.rect)===null||a===void 0?void 0:a[n.axis[n.draggedAxis].sizeAttr])!==null&&o!==void 0?o:0,C=n.axis[n.draggedAxis].scrollbar,E=(l=(s=n.contentWrapperEl)===null||s===void 0?void 0:s[n.axis[n.draggedAxis].scrollSizeAttr])!==null&&l!==void 0?l:0,R=parseInt((d=(c=n.elStyles)===null||c===void 0?void 0:c[n.axis[n.draggedAxis].sizeAttr])!==null&&d!==void 0?d:"0px",10);i.preventDefault(),i.stopPropagation(),n.draggedAxis==="y"?y=i.pageY:y=i.pageX;var N=y-((v=(m=w.rect)===null||m===void 0?void 0:m[n.axis[n.draggedAxis].offsetAttr])!==null&&v!==void 0?v:0)-n.axis[n.draggedAxis].dragOffset;N=n.draggedAxis==="x"&&n.isRtl?((x=(h=w.rect)===null||h===void 0?void 0:h[n.axis[n.draggedAxis].sizeAttr])!==null&&x!==void 0?x:0)-C.size-N:N;var k=N/(S-C.size),L=k*(E-R);n.draggedAxis==="x"&&n.isRtl&&(L=!((b=e.getRtlHelpers())===null||b===void 0)&&b.isScrollingToNegative?-L:L),n.contentWrapperEl[n.axis[n.draggedAxis].scrollOffsetAttr]=L}},this.onEndDrag=function(i){n.isDragging=!1;var a=Ot(n.el),o=me(n.el);i.preventDefault(),i.stopPropagation(),ve(n.el,n.classNames.dragging),n.onStopScrolling(),a.removeEventListener("mousemove",n.drag,!0),a.removeEventListener("mouseup",n.onEndDrag,!0),n.removePreventClickId=o.setTimeout(function(){a.removeEventListener("click",n.preventClick,!0),a.removeEventListener("dblclick",n.preventClick,!0),n.removePreventClickId=null})},this.preventClick=function(i){i.preventDefault(),i.stopPropagation()},this.el=t,this.options=Ae(Ae({},e.defaultOptions),r),this.classNames=Ae(Ae({},e.defaultOptions.classNames),r.classNames),this.axis={x:{scrollOffsetAttr:"scrollLeft",sizeAttr:"width",scrollSizeAttr:"scrollWidth",offsetSizeAttr:"offsetWidth",offsetAttr:"left",overflowAttr:"overflowX",dragOffset:0,isOverflowing:!0,forceVisible:!1,track:{size:null,el:null,rect:null,isVisible:!1},scrollbar:{size:null,el:null,rect:null,isVisible:!1}},y:{scrollOffsetAttr:"scrollTop",sizeAttr:"height",scrollSizeAttr:"scrollHeight",offsetSizeAttr:"offsetHeight",offsetAttr:"top",overflowAttr:"overflowY",dragOffset:0,isOverflowing:!0,forceVisible:!1,track:{size:null,el:null,rect:null,isVisible:!1},scrollbar:{size:null,el:null,rect:null,isVisible:!1}}},typeof this.el!="object"||!this.el.nodeName)throw new Error("Argument passed to SimpleBar must be an HTML element instead of ".concat(this.el));this.onMouseMove=So(this._onMouseMove,64),this.onWindowResize=wt(this._onWindowResize,64,{leading:!0}),this.onStopScrolling=wt(this._onStopScrolling,this.stopScrollDelay),this.onMouseEntered=wt(this._onMouseEntered,this.stopScrollDelay),this.init()}return e.getRtlHelpers=function(){if(e.rtlHelpers)return e.rtlHelpers;var t=document.createElement("div");t.innerHTML='<div class="simplebar-dummy-scrollbar-size"><div></div></div>';var r=t.firstElementChild,n=r==null?void 0:r.firstElementChild;if(!n)return null;document.body.appendChild(r),r.scrollLeft=0;var i=e.getOffset(r),a=e.getOffset(n);r.scrollLeft=-999;var o=e.getOffset(n);return document.body.removeChild(r),e.rtlHelpers={isScrollOriginAtZero:i.left!==a.left,isScrollingToNegative:a.left!==o.left},e.rtlHelpers},e.prototype.getScrollbarWidth=function(){try{return this.contentWrapperEl&&getComputedStyle(this.contentWrapperEl,"::-webkit-scrollbar").display==="none"||"scrollbarWidth"in document.documentElement.style||"-ms-overflow-style"in document.documentElement.style?0:tn()}catch{return tn()}},e.getOffset=function(t){var r=t.getBoundingClientRect(),n=Ot(t),i=me(t);return{top:r.top+(i.pageYOffset||n.documentElement.scrollTop),left:r.left+(i.pageXOffset||n.documentElement.scrollLeft)}},e.prototype.init=function(){cr&&(this.initDOM(),this.rtlHelpers=e.getRtlHelpers(),this.scrollbarWidth=this.getScrollbarWidth(),this.recalculate(),this.initListeners())},e.prototype.initDOM=function(){var t,r;this.wrapperEl=this.el.querySelector(X(this.classNames.wrapper)),this.contentWrapperEl=this.options.scrollableNode||this.el.querySelector(X(this.classNames.contentWrapper)),this.contentEl=this.options.contentNode||this.el.querySelector(X(this.classNames.contentEl)),this.offsetEl=this.el.querySelector(X(this.classNames.offset)),this.maskEl=this.el.querySelector(X(this.classNames.mask)),this.placeholderEl=this.findChild(this.wrapperEl,X(this.classNames.placeholder)),this.heightAutoObserverWrapperEl=this.el.querySelector(X(this.classNames.heightAutoObserverWrapperEl)),this.heightAutoObserverEl=this.el.querySelector(X(this.classNames.heightAutoObserverEl)),this.axis.x.track.el=this.findChild(this.el,"".concat(X(this.classNames.track)).concat(X(this.classNames.horizontal))),this.axis.y.track.el=this.findChild(this.el,"".concat(X(this.classNames.track)).concat(X(this.classNames.vertical))),this.axis.x.scrollbar.el=((t=this.axis.x.track.el)===null||t===void 0?void 0:t.querySelector(X(this.classNames.scrollbar)))||null,this.axis.y.scrollbar.el=((r=this.axis.y.track.el)===null||r===void 0?void 0:r.querySelector(X(this.classNames.scrollbar)))||null,this.options.autoHide||(he(this.axis.x.scrollbar.el,this.classNames.visible),he(this.axis.y.scrollbar.el,this.classNames.visible))},e.prototype.initListeners=function(){var t=this,r,n=me(this.el);if(this.el.addEventListener("mouseenter",this.onMouseEnter),this.el.addEventListener("pointerdown",this.onPointerEvent,!0),this.el.addEventListener("mousemove",this.onMouseMove),this.el.addEventListener("mouseleave",this.onMouseLeave),(r=this.contentWrapperEl)===null||r===void 0||r.addEventListener("scroll",this.onScroll),n.addEventListener("resize",this.onWindowResize),!!this.contentEl){if(window.ResizeObserver){var i=!1,a=n.ResizeObserver||ResizeObserver;this.resizeObserver=new a(function(){i&&n.requestAnimationFrame(function(){t.recalculate()})}),this.resizeObserver.observe(this.el),this.resizeObserver.observe(this.contentEl),n.requestAnimationFrame(function(){i=!0})}this.mutationObserver=new n.MutationObserver(function(){n.requestAnimationFrame(function(){t.recalculate()})}),this.mutationObserver.observe(this.contentEl,{childList:!0,subtree:!0,characterData:!0})}},e.prototype.recalculate=function(){if(!(!this.heightAutoObserverEl||!this.contentEl||!this.contentWrapperEl||!this.wrapperEl||!this.placeholderEl)){var t=me(this.el);this.elStyles=t.getComputedStyle(this.el),this.isRtl=this.elStyles.direction==="rtl";var r=this.contentEl.offsetWidth,n=this.heightAutoObserverEl.offsetHeight<=1,i=this.heightAutoObserverEl.offsetWidth<=1||r>0,a=this.contentWrapperEl.offsetWidth,o=this.elStyles.overflowX,s=this.elStyles.overflowY;this.contentEl.style.padding="".concat(this.elStyles.paddingTop," ").concat(this.elStyles.paddingRight," ").concat(this.elStyles.paddingBottom," ").concat(this.elStyles.paddingLeft),this.wrapperEl.style.margin="-".concat(this.elStyles.paddingTop," -").concat(this.elStyles.paddingRight," -").concat(this.elStyles.paddingBottom," -").concat(this.elStyles.paddingLeft);var l=this.contentEl.scrollHeight,c=this.contentEl.scrollWidth;this.contentWrapperEl.style.height=n?"auto":"100%",this.placeholderEl.style.width=i?"".concat(r||c,"px"):"auto",this.placeholderEl.style.height="".concat(l,"px");var d=this.contentWrapperEl.offsetHeight;this.axis.x.isOverflowing=r!==0&&c>r,this.axis.y.isOverflowing=l>d,this.axis.x.isOverflowing=o==="hidden"?!1:this.axis.x.isOverflowing,this.axis.y.isOverflowing=s==="hidden"?!1:this.axis.y.isOverflowing,this.axis.x.forceVisible=this.options.forceVisible==="x"||this.options.forceVisible===!0,this.axis.y.forceVisible=this.options.forceVisible==="y"||this.options.forceVisible===!0,this.hideNativeScrollbar();var m=this.axis.x.isOverflowing?this.scrollbarWidth:0,v=this.axis.y.isOverflowing?this.scrollbarWidth:0;this.axis.x.isOverflowing=this.axis.x.isOverflowing&&c>a-v,this.axis.y.isOverflowing=this.axis.y.isOverflowing&&l>d-m,this.axis.x.scrollbar.size=this.getScrollbarSize("x"),this.axis.y.scrollbar.size=this.getScrollbarSize("y"),this.axis.x.scrollbar.el&&(this.axis.x.scrollbar.el.style.width="".concat(this.axis.x.scrollbar.size,"px")),this.axis.y.scrollbar.el&&(this.axis.y.scrollbar.el.style.height="".concat(this.axis.y.scrollbar.size,"px")),this.positionScrollbar("x"),this.positionScrollbar("y"),this.toggleTrackVisibility("x"),this.toggleTrackVisibility("y")}},e.prototype.getScrollbarSize=function(t){var r,n;if(t===void 0&&(t="y"),!this.axis[t].isOverflowing||!this.contentEl)return 0;var i=this.contentEl[this.axis[t].scrollSizeAttr],a=(n=(r=this.axis[t].track.el)===null||r===void 0?void 0:r[this.axis[t].offsetSizeAttr])!==null&&n!==void 0?n:0,o=a/i,s;return s=Math.max(~~(o*a),this.options.scrollbarMinSize),this.options.scrollbarMaxSize&&(s=Math.min(s,this.options.scrollbarMaxSize)),s},e.prototype.positionScrollbar=function(t){var r,n,i;t===void 0&&(t="y");var a=this.axis[t].scrollbar;if(!(!this.axis[t].isOverflowing||!this.contentWrapperEl||!a.el||!this.elStyles)){var o=this.contentWrapperEl[this.axis[t].scrollSizeAttr],s=((r=this.axis[t].track.el)===null||r===void 0?void 0:r[this.axis[t].offsetSizeAttr])||0,l=parseInt(this.elStyles[this.axis[t].sizeAttr],10),c=this.contentWrapperEl[this.axis[t].scrollOffsetAttr];c=t==="x"&&this.isRtl&&(!((n=e.getRtlHelpers())===null||n===void 0)&&n.isScrollOriginAtZero)?-c:c,t==="x"&&this.isRtl&&(c=!((i=e.getRtlHelpers())===null||i===void 0)&&i.isScrollingToNegative?c:-c);var d=c/(o-l),m=~~((s-a.size)*d);m=t==="x"&&this.isRtl?-m+(s-a.size):m,a.el.style.transform=t==="x"?"translate3d(".concat(m,"px, 0, 0)"):"translate3d(0, ".concat(m,"px, 0)")}},e.prototype.toggleTrackVisibility=function(t){t===void 0&&(t="y");var r=this.axis[t].track.el,n=this.axis[t].scrollbar.el;!r||!n||!this.contentWrapperEl||(this.axis[t].isOverflowing||this.axis[t].forceVisible?(r.style.visibility="visible",this.contentWrapperEl.style[this.axis[t].overflowAttr]="scroll",this.el.classList.add("".concat(this.classNames.scrollable,"-").concat(t))):(r.style.visibility="hidden",this.contentWrapperEl.style[this.axis[t].overflowAttr]="hidden",this.el.classList.remove("".concat(this.classNames.scrollable,"-").concat(t))),this.axis[t].isOverflowing?n.style.display="block":n.style.display="none")},e.prototype.showScrollbar=function(t){t===void 0&&(t="y"),this.axis[t].isOverflowing&&!this.axis[t].scrollbar.isVisible&&(he(this.axis[t].scrollbar.el,this.classNames.visible),this.axis[t].scrollbar.isVisible=!0)},e.prototype.hideScrollbar=function(t){t===void 0&&(t="y"),!this.isDragging&&this.axis[t].isOverflowing&&this.axis[t].scrollbar.isVisible&&(ve(this.axis[t].scrollbar.el,this.classNames.visible),this.axis[t].scrollbar.isVisible=!1)},e.prototype.hideNativeScrollbar=function(){this.offsetEl&&(this.offsetEl.style[this.isRtl?"left":"right"]=this.axis.y.isOverflowing||this.axis.y.forceVisible?"-".concat(this.scrollbarWidth,"px"):"0px",this.offsetEl.style.bottom=this.axis.x.isOverflowing||this.axis.x.forceVisible?"-".concat(this.scrollbarWidth,"px"):"0px")},e.prototype.onMouseMoveForAxis=function(t){t===void 0&&(t="y");var r=this.axis[t];!r.track.el||!r.scrollbar.el||(r.track.rect=r.track.el.getBoundingClientRect(),r.scrollbar.rect=r.scrollbar.el.getBoundingClientRect(),this.isWithinBounds(r.track.rect)?(this.showScrollbar(t),he(r.track.el,this.classNames.hover),this.isWithinBounds(r.scrollbar.rect)?he(r.scrollbar.el,this.classNames.hover):ve(r.scrollbar.el,this.classNames.hover)):(ve(r.track.el,this.classNames.hover),this.options.autoHide&&this.hideScrollbar(t)))},e.prototype.onMouseLeaveForAxis=function(t){t===void 0&&(t="y"),ve(this.axis[t].track.el,this.classNames.hover),ve(this.axis[t].scrollbar.el,this.classNames.hover),this.options.autoHide&&this.hideScrollbar(t)},e.prototype.onDragStart=function(t,r){var n;r===void 0&&(r="y"),this.isDragging=!0;var i=Ot(this.el),a=me(this.el),o=this.axis[r].scrollbar,s=r==="y"?t.pageY:t.pageX;this.axis[r].dragOffset=s-(((n=o.rect)===null||n===void 0?void 0:n[this.axis[r].offsetAttr])||0),this.draggedAxis=r,he(this.el,this.classNames.dragging),i.addEventListener("mousemove",this.drag,!0),i.addEventListener("mouseup",this.onEndDrag,!0),this.removePreventClickId===null?(i.addEventListener("click",this.preventClick,!0),i.addEventListener("dblclick",this.preventClick,!0)):(a.clearTimeout(this.removePreventClickId),this.removePreventClickId=null)},e.prototype.onTrackClick=function(t,r){var n=this,i,a,o,s;r===void 0&&(r="y");var l=this.axis[r];if(!(!this.options.clickOnTrack||!l.scrollbar.el||!this.contentWrapperEl)){t.preventDefault();var c=me(this.el);this.axis[r].scrollbar.rect=l.scrollbar.el.getBoundingClientRect();var d=this.axis[r].scrollbar,m=(a=(i=d.rect)===null||i===void 0?void 0:i[this.axis[r].offsetAttr])!==null&&a!==void 0?a:0,v=parseInt((s=(o=this.elStyles)===null||o===void 0?void 0:o[this.axis[r].sizeAttr])!==null&&s!==void 0?s:"0px",10),h=this.contentWrapperEl[this.axis[r].scrollOffsetAttr],x=r==="y"?this.mouseY-m:this.mouseX-m,b=x<0?-1:1,y=b===-1?h-v:h+v,w=40,S=function(){n.contentWrapperEl&&(b===-1?h>y&&(h-=w,n.contentWrapperEl[n.axis[r].scrollOffsetAttr]=h,c.requestAnimationFrame(S)):h<y&&(h+=w,n.contentWrapperEl[n.axis[r].scrollOffsetAttr]=h,c.requestAnimationFrame(S)))};S()}},e.prototype.getContentElement=function(){return this.contentEl},e.prototype.getScrollElement=function(){return this.contentWrapperEl},e.prototype.removeListeners=function(){var t=me(this.el);this.el.removeEventListener("mouseenter",this.onMouseEnter),this.el.removeEventListener("pointerdown",this.onPointerEvent,!0),this.el.removeEventListener("mousemove",this.onMouseMove),this.el.removeEventListener("mouseleave",this.onMouseLeave),this.contentWrapperEl&&this.contentWrapperEl.removeEventListener("scroll",this.onScroll),t.removeEventListener("resize",this.onWindowResize),this.mutationObserver&&this.mutationObserver.disconnect(),this.resizeObserver&&this.resizeObserver.disconnect(),this.onMouseMove.cancel(),this.onWindowResize.cancel(),this.onStopScrolling.cancel(),this.onMouseEntered.cancel()},e.prototype.unMount=function(){this.removeListeners()},e.prototype.isWithinBounds=function(t){return this.mouseX>=t.left&&this.mouseX<=t.left+t.width&&this.mouseY>=t.top&&this.mouseY<=t.top+t.height},e.prototype.findChild=function(t,r){var n=t.matches||t.webkitMatchesSelector||t.mozMatchesSelector||t.msMatchesSelector;return Array.prototype.filter.call(t.children,function(i){return n.call(i,r)})[0]},e.rtlHelpers=null,e.defaultOptions={forceVisible:!1,clickOnTrack:!0,scrollbarMinSize:25,scrollbarMaxSize:0,ariaLabel:"scrollable content",tabIndex:0,classNames:{contentEl:"simplebar-content",contentWrapper:"simplebar-content-wrapper",offset:"simplebar-offset",mask:"simplebar-mask",wrapper:"simplebar-wrapper",placeholder:"simplebar-placeholder",scrollbar:"simplebar-scrollbar",track:"simplebar-track",heightAutoObserverWrapperEl:"simplebar-height-auto-observer-wrapper",heightAutoObserverEl:"simplebar-height-auto-observer",visible:"simplebar-visible",horizontal:"simplebar-horizontal",vertical:"simplebar-vertical",hover:"simplebar-hover",dragging:"simplebar-dragging",scrolling:"simplebar-scrolling",scrollable:"simplebar-scrollable",mouseEntered:"simplebar-mouse-entered"},scrollableNode:null,contentNode:null,autoHide:!0},e.getOptions=Ro,e.helpers=To,e}(),Q=function(){return Q=Object.assign||function(t){for(var r,n=1,i=arguments.length;n<i;n++){r=arguments[n];for(var a in r)Object.prototype.hasOwnProperty.call(r,a)&&(t[a]=r[a])}return t},Q.apply(this,arguments)};function Ao(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var i=0,n=Object.getOwnPropertySymbols(e);i<n.length;i++)t.indexOf(n[i])<0&&Object.prototype.propertyIsEnumerable.call(e,n[i])&&(r[n[i]]=e[n[i]]);return r}var An=g.forwardRef(function(e,t){var r=e.children,n=e.scrollableNodeProps,i=n===void 0?{}:n,a=Ao(e,["children","scrollableNodeProps"]),o=g.useRef(),s=g.useRef(),l=g.useRef(),c={},d={};Object.keys(a).forEach(function(h){Object.prototype.hasOwnProperty.call(Ve.defaultOptions,h)?c[h]=a[h]:d[h]=a[h]});var m=Q(Q({},Ve.defaultOptions.classNames),c.classNames),v=Q(Q({},i),{className:"".concat(m.contentWrapper).concat(i.className?" ".concat(i.className):""),tabIndex:c.tabIndex||Ve.defaultOptions.tabIndex,role:"region","aria-label":c.ariaLabel||Ve.defaultOptions.ariaLabel});return g.useEffect(function(){var h;return s.current=v.ref?v.ref.current:s.current,o.current&&(h=new Ve(o.current,Q(Q(Q({},c),s.current&&{scrollableNode:s.current}),l.current&&{contentNode:l.current})),typeof t=="function"?t(h):t&&(t.current=h)),function(){h==null||h.unMount(),h=null,typeof t=="function"&&t(null)}},[]),g.createElement("div",Q({"data-simplebar":"init",ref:o},d),g.createElement("div",{className:m.wrapper},g.createElement("div",{className:m.heightAutoObserverWrapperEl},g.createElement("div",{className:m.heightAutoObserverEl})),g.createElement("div",{className:m.mask},g.createElement("div",{className:m.offset},typeof r=="function"?r({scrollableNodeRef:s,scrollableNodeProps:Q(Q({},v),{ref:s}),contentNodeRef:l,contentNodeProps:{className:m.contentEl,ref:l}}):g.createElement("div",Q({},v),g.createElement("div",{className:m.contentEl},r)))),g.createElement("div",{className:m.placeholder})),g.createElement("div",{className:"".concat(m.track," simplebar-horizontal")},g.createElement("div",{className:m.scrollbar})),g.createElement("div",{className:"".concat(m.track," simplebar-vertical")},g.createElement("div",{className:m.scrollbar})))});An.displayName="SimpleBar";const _n=({items:e})=>{const t=(i,a,o,s=!1)=>f.jsxs(f.Fragment,{children:[a||s&&f.jsx("span",{className:"nav-icon",children:f.jsx("span",{className:"nav-icon-bullet"})}),i&&i,o&&f.jsx($t,{color:o.color,className:"ms-auto",size:"sm",children:o.text})]}),r=(i,a,o=!1)=>{const{component:s,name:l,badge:c,icon:d,...m}=i,v=s;return f.jsx(v,{as:"div",children:m.to||m.href?f.jsx(ge,{...m.to&&{as:on},...m.href&&{target:"_blank",rel:"noopener noreferrer"},...m,children:t(l,d,c,o)}):t(l,d,c,o)},a)},n=(i,a)=>{const{component:o,name:s,icon:l,items:c,to:d,...m}=i,v=o;return f.jsx(v,{compact:!0,as:"div",toggler:t(s,l),...m,children:c==null?void 0:c.map((h,x)=>h.items?n(h,x):r(h,x,!0))},a)};return f.jsx(er,{as:An,children:e&&e.map((i,a)=>i.items?n(i,a):r(i,a))})};_n.propTypes={items:u.arrayOf(u.any).isRequired};const _o=["1024 280",`<path d="M0 0 C12.4575 -0.12375 24.915 -0.2475 37.75 -0.375 C43.61604004 -0.45693604 43.61604004 -0.45693604 49.60058594 -0.54052734 C54.43408203 -0.56762695 54.43408203 -0.56762695 56.70806885 -0.57266235 C58.27181287 -0.58178192 59.83551889 -0.60268671 61.39898682 -0.63345337 C72.98361636 -0.84971332 82.52742202 -0.00720899 91.48602295 8.30728149 C99.80762398 17.09472683 101.42095796 25.2266681 101.3515625 37.05859375 C100.63240896 45.12110397 97.37627374 51.99181349 92 58 C89.859375 59.58203125 89.859375 59.58203125 87.75 60.8125 C86.69039062 61.44091797 86.69039062 61.44091797 85.609375 62.08203125 C85.07828125 62.38496094 84.5471875 62.68789062 84 63 C84.70125 63.32871094 85.4025 63.65742188 86.125 63.99609375 C95.5859256 68.73941107 102.80548916 74.90233946 106.375 85.1328125 C108.46338659 94.713286 107.99882423 104.45060903 103 113 C96.68113176 122.7759998 85.91995007 126.1654349 75.29217529 129.54270935 C72.11926775 130.17570673 69.07995837 130.11910338 65.84472656 130.11352539 C65.12065487 130.11364593 64.39658318 130.11376646 63.65056992 130.11389065 C61.31389003 130.11314609 58.97729056 130.10553869 56.640625 130.09765625 C55.19515013 130.09618542 53.74967473 130.09516558 52.30419922 130.09460449 C46.95279134 130.089368 41.60139488 130.07540676 36.25 130.0625 C18.30625 130.0315625 18.30625 130.0315625 0 130 C0 87.1 0 44.2 0 0 Z " fill="#023C65" transform="translate(3,113)"/>
  <path d="M0 0 C7.7192573 -0.06924251 15.43741674 -0.12886659 23.15673828 -0.16479492 C25.77721834 -0.17978844 28.39767371 -0.20019002 31.01806641 -0.22631836 C34.80637748 -0.2631191 38.59435393 -0.27982142 42.3828125 -0.29296875 C43.53548615 -0.30845261 44.68815979 -0.32393646 45.87576294 -0.33988953 C64.68994698 -0.34369557 81.65388522 5.38384792 95.625 18.125 C109.27958457 32.61887346 112.7481695 51.33143439 112.41015625 70.5703125 C111.80851962 82.59873237 107.30134828 95.4073621 100 105 C99.37738281 105.83660156 98.75476563 106.67320312 98.11328125 107.53515625 C87.82204054 120.14715734 71.83502569 126.68205042 56 129 C51.58806641 129.25360953 47.19143933 129.234181 42.7734375 129.1953125 C40.93019394 129.19106766 40.93019394 129.19106766 39.04971313 129.18673706 C35.15803942 129.17561897 31.26660315 129.15052976 27.375 129.125 C24.72265959 129.11495939 22.07031555 129.10583544 19.41796875 129.09765625 C12.94514932 129.07741604 6.47283778 129.03825052 0 129 C0 112.5 0 96 0 79 C9.57 79 19.14 79 29 79 C29 86.92 29 94.84 29 103 C32.774375 102.896875 36.54875 102.79375 40.4375 102.6875 C41.60853271 102.66260498 42.77956543 102.63770996 43.98608398 102.61206055 C54.52914243 102.24536875 64.74126001 99.80524558 72.1875 91.875 C80.31326801 81.77737896 81.82594381 68.48092867 81 56 C79.68281395 47.94192064 76.06296005 39.69713797 69.9609375 34.14453125 C60.92892501 27.68169546 51.29078974 26.55093519 40.4296875 26.48828125 C39.26744659 26.47033005 38.10520569 26.45237885 36.90774536 26.43388367 C33.23019944 26.38011295 29.55278081 26.34599132 25.875 26.3125 C23.36716991 26.27929765 20.85935655 26.24480533 18.3515625 26.20898438 C12.23436002 26.12200035 6.11771908 26.06164484 0 26 C0 17.42 0 8.84 0 0 Z " fill="#039FE3" transform="translate(555.000,114.000)"/>
  <path d="M0 0 C6.86163369 6.25328266 11.82629234 13.7806819 12.29516602 23.24316406 C12.32268844 24.86766226 12.33570834 26.4924562 12.3359375 28.1171875 C12.35574928 29.41774078 12.35574928 29.41774078 12.3759613 30.74456787 C12.41654023 33.48859993 12.4384498 36.23229998 12.45703125 38.9765625 C12.48086075 41.73500729 12.50898984 44.49314157 12.54881287 47.25140381 C12.57307365 48.96080681 12.58801039 50.67037439 12.59242249 52.37994385 C12.52217862 57.90973108 12.52217862 57.90973108 14.89453125 62.7265625 C16.94453996 63.53185139 16.94453996 63.53185139 18.89453125 63.7265625 C18.92339876 67.87238137 18.94128452 72.01817466 18.95703125 76.1640625 C18.96541016 77.34806641 18.97378906 78.53207031 18.98242188 79.75195312 C18.98564453 80.87666016 18.98886719 82.00136719 18.9921875 83.16015625 C18.99742432 84.20228271 19.00266113 85.24440918 19.00805664 86.31811523 C18.89453125 88.7265625 18.89453125 88.7265625 17.89453125 89.7265625 C14.8409965 90.00573315 11.7718042 89.95841433 8.70703125 89.9765625 C7.85560547 89.9971875 7.00417969 90.0178125 6.12695312 90.0390625 C0.94199232 90.07114269 -2.6173043 89.61230657 -7.10546875 86.7265625 C-10.80695503 83.02507622 -11.93755074 79.69021402 -13.10546875 74.7265625 C-13.76546875 75.4278125 -14.42546875 76.1290625 -15.10546875 76.8515625 C-24.14232178 85.78716228 -34.38064048 90.68957919 -47.16796875 91.1015625 C-56.69917461 90.98309553 -64.13024165 87.98449497 -71.10546875 81.4765625 C-78.34678474 73.44671846 -79.95173605 66.14729757 -79.4921875 55.48046875 C-79.00169153 50.71918965 -77.93822364 47.54096512 -75.10546875 43.7265625 C-74.40421875 42.6953125 -73.70296875 41.6640625 -72.98046875 40.6015625 C-65.94978975 32.73745955 -54.39622936 28.11445783 -44.0546875 27.34375 C-34.44343772 26.94765797 -26.15605938 28.51367454 -17.10546875 31.7265625 C-17.22059028 29.99671008 -17.34802552 28.26767499 -17.48046875 26.5390625 C-17.58488281 25.09466797 -17.58488281 25.09466797 -17.69140625 23.62109375 C-18.11321996 20.67237718 -18.77103373 18.38603512 -20.10546875 15.7265625 C-22.46733014 14.46510482 -22.46733014 14.46510482 -25.10546875 13.7265625 C-25.67652344 13.38882813 -26.24757812 13.05109375 -26.8359375 12.703125 C-34.19692909 9.5357448 -43.88098238 11.25586949 -51.10546875 13.9765625 C-54.6206145 15.4879888 -57.94864802 17.13796265 -61.23046875 19.1015625 C-62.17921875 19.6378125 -63.12796875 20.1740625 -64.10546875 20.7265625 C-67.72627086 19.51962846 -68.23081914 18.14919164 -69.90625 14.85742188 C-70.21949219 14.21611328 -70.53273438 13.57480469 -70.85546875 12.9140625 C-71.18417969 12.28435547 -71.51289062 11.65464844 -71.8515625 11.00585938 C-73.10034602 8.49511952 -74.08356261 6.32220094 -74.1875 3.49609375 C-70.35007736 -2.77954689 -58.29671035 -5.76244931 -51.48046875 -7.7109375 C-34.429966 -11.79510335 -14.49805202 -10.8111669 0 0 Z " fill="#043D67" transform="translate(196.10546875,154.2734375)"/>
  <path d="M0 0 C5.67096463 5.00470376 10.32809048 13.33793987 11.0737915 20.88761902 C11.16766849 23.21029589 11.20359771 25.52704529 11.21875 27.8515625 C11.2333728 28.69923187 11.24799561 29.54690125 11.26306152 30.42025757 C11.3250272 34.02676812 11.36496325 37.63344247 11.40673828 41.24023438 C11.4404934 43.88169122 11.48558337 46.52278908 11.53125 49.1640625 C11.53916565 50.38447739 11.53916565 50.38447739 11.54724121 51.62954712 C11.516136 57.12366625 11.516136 57.12366625 13.875 61.875 C15.855 62.37 15.855 62.37 17.875 62.875 C17.875 71.125 17.875 79.375 17.875 87.875 C14.79311519 89.4159424 11.56627677 89.05945331 8.1875 89.0625 C7.49205078 89.07474609 6.79660156 89.08699219 6.08007812 89.09960938 C-0.07446384 89.11684899 -5.31982641 88.59994365 -9.9375 84.1875 C-12.30270092 80.60591004 -13.21358556 78.01779289 -14.125 73.875 C-14.785 74.57625 -15.445 75.2775 -16.125 76 C-25.43729032 85.28590989 -35.34085267 89.74852954 -48.4375 90.3125 C-57.83398331 90.11514096 -65.25539501 87.03438636 -72.125 80.625 C-79.28828676 72.68168202 -81.06624301 65.33789823 -80.56640625 54.7578125 C-79.60154522 46.27045088 -75.83553739 40.60152136 -69.22265625 35.3359375 C-65.9886338 33.08356432 -62.77980559 31.3450894 -59.125 29.875 C-58.42246094 29.5759375 -57.71992188 29.276875 -56.99609375 28.96875 C-44.15405861 24.27521431 -30.64377008 26.43093582 -18.125 30.875 C-18.24012153 29.14514758 -18.36755677 27.41611249 -18.5 25.6875 C-18.60441406 24.24310547 -18.60441406 24.24310547 -18.7109375 22.76953125 C-19.13238363 19.82338426 -19.7994663 17.53546836 -21.125 14.875 C-23.39101865 13.58457237 -23.39101865 13.58457237 -26.125 12.875 C-27.04410156 12.53597656 -27.96320313 12.19695312 -28.91015625 11.84765625 C-38.29956454 9.00687537 -47.44171033 10.74385916 -56.125 14.875 C-58.54198556 16.20067996 -60.91432463 17.55544062 -63.25 19.01953125 C-65.125 19.875 -65.125 19.875 -68.125 18.875 C-69.51788007 16.613033 -70.71467008 14.43807256 -71.875 12.0625 C-72.20371094 11.43279297 -72.53242188 10.80308594 -72.87109375 10.15429688 C-74.11638952 7.6505693 -75.09711052 5.46377795 -75.23046875 2.6484375 C-73.44543731 -0.21518184 -70.64997748 -1.31055217 -67.6875 -2.6875 C-67.05440918 -2.99123535 -66.42131836 -3.2949707 -65.76904297 -3.60791016 C-44.64774901 -13.50300383 -19.57096443 -13.30825581 0 0 Z " fill="#043D67" transform="translate(402.125,155.125)"/>
  <path d="M0 0 C0.87052002 -0.01772461 1.74104004 -0.03544922 2.63793945 -0.05371094 C13.91007375 -0.04095364 25.78148568 3.76719462 34.1875 11.5 C41.63951149 19.55099114 43.482512 28.44016868 43.62109375 39.0078125 C43.65400683 40.70964105 43.68721887 42.41146384 43.72070312 44.11328125 C43.76824462 46.78398003 43.81275081 49.45465133 43.85180664 52.12548828 C43.89144893 54.71149488 43.9429798 57.29705248 43.99609375 59.8828125 C44.0045079 60.68284698 44.01292206 61.48288147 44.02159119 62.30715942 C44.10739285 66.14520683 44.19760977 68.04641465 46.375 71.3125 C47.86 71.8075 47.86 71.8075 49.375 72.3125 C49.375 80.5625 49.375 88.8125 49.375 97.3125 C45.05518612 98.17646278 41.31751375 98.54119309 36.9375 98.625 C35.02130859 98.68107422 35.02130859 98.68107422 33.06640625 98.73828125 C28.1757968 98.17417921 25.03784767 96.38992065 21.94921875 92.53125 C20.3734619 89.47691953 19.11670654 86.66119488 18.375 83.3125 C17.91351563 83.83199219 17.45203125 84.35148437 16.9765625 84.88671875 C11.97772116 90.23132975 7.09946961 93.48229909 0.375 96.3125 C-0.34945312 96.67730469 -1.07390625 97.04210937 -1.8203125 97.41796875 C-10.19541025 100.08913503 -22.58217631 99.98084676 -30.5 96.125 C-32.72265625 94.72265625 -32.72265625 94.72265625 -34.625 93.3125 C-35.20121094 92.93996094 -35.77742187 92.56742187 -36.37109375 92.18359375 C-42.23012678 87.99022099 -46.1034807 80.22849683 -47.625 73.3125 C-48.83616537 64.38197941 -46.59861075 57.75694093 -41.625 50.3125 C-35.73582302 43.29424334 -26.69674715 38.55243197 -17.625 37.3125 C-6.82285428 36.70614004 4.15848037 36.29288572 14.375 40.3125 C14.38199179 32.52722902 14.38199179 32.52722902 11.0625 25.75 C6.28334091 21.41541384 2.6140309 20.95137188 -3.75 21 C-4.89287476 21.00845947 -4.89287476 21.00845947 -6.05883789 21.01708984 C-15.6893245 21.22388885 -22.52924951 23.86099138 -30.59375 29.10546875 C-32.625 30.3125 -32.625 30.3125 -34.625 30.3125 C-37.265 24.3725 -39.905 18.4325 -42.625 12.3125 C-28.62546463 3.51279206 -16.50270908 0.20286059 0 0 Z " fill="#049FE3" transform="translate(716.625,145.688)"/>
  <path d="M0 0 C5.91737654 4.66350865 9.99749593 10.66459533 11.20579529 18.18878174 C11.82827918 26.53029634 11.91858991 34.8436206 11.88671875 43.203125 C11.88484842 44.66570995 11.88342743 46.12829554 11.88243103 47.59088135 C11.87865421 51.39821166 11.86886659 55.20550244 11.85772705 59.01281738 C11.84558602 63.60356835 11.84108762 68.19433271 11.83527374 72.78509521 C11.82565113 79.72134078 11.80646112 86.65749999 11.7890625 93.59375 C2.2190625 93.59375 -7.3509375 93.59375 -17.2109375 93.59375 C-17.24832031 89.18773437 -17.28570312 84.78171875 -17.32421875 80.2421875 C-17.36234915 76.71978332 -17.40342178 73.19744067 -17.4464798 69.6750946 C-17.49106505 65.96196567 -17.53051557 62.24894761 -17.55737305 58.53564453 C-17.58346239 54.95630267 -17.62390353 51.37737964 -17.67412376 47.79829788 C-17.69075465 46.43567761 -17.70229243 45.07298495 -17.70855141 43.71027756 C-17.71823281 41.79550349 -17.74802288 39.88086355 -17.77856445 37.96630859 C-17.79521904 36.33415909 -17.79521904 36.33415909 -17.81221008 34.66903687 C-18.38381625 30.26037859 -19.90927676 26.86313816 -23.1015625 23.75 C-27.00843368 21.6084558 -30.89817495 21.91176195 -35.2109375 22.59375 C-41.62384449 25.13407407 -47.12777328 30.42742157 -50.2109375 36.59375 C-50.33051224 38.41093084 -50.3881843 40.23232835 -50.41625977 42.05322266 C-50.43620499 43.19425446 -50.45615021 44.33528625 -50.47669983 45.51089478 C-50.50176857 47.36561905 -50.50176857 47.36561905 -50.52734375 49.2578125 C-50.54812485 50.52015717 -50.56890594 51.78250183 -50.59031677 53.08309937 C-50.64471676 56.44007045 -50.69493986 59.79705189 -50.74316406 63.15411377 C-50.79338792 66.57936055 -50.84901654 70.00451921 -50.90429688 73.4296875 C-51.01237582 80.15097431 -51.11140766 86.87230933 -51.2109375 93.59375 C-60.7809375 93.59375 -70.3509375 93.59375 -80.2109375 93.59375 C-80.2109375 61.91375 -80.2109375 30.23375 -80.2109375 -2.40625 C-71.6309375 -2.40625 -63.0509375 -2.40625 -54.2109375 -2.40625 C-53.7159375 4.52375 -53.7159375 4.52375 -53.2109375 11.59375 C-52.5715625 10.7275 -51.9321875 9.86125 -51.2734375 8.96875 C-39.53411392 -4.93480642 -15.68437349 -9.9759266 0 0 Z " fill="#023C66" transform="translate(303.2109375,149.40625)"/>
  <path d="M0 0 C9.9 0 19.8 0 30 0 C31.90352628 4.44156132 33.52234009 8.38274695 34.875 12.9375 C35.21660156 14.071875 35.55820312 15.20625 35.91015625 16.375 C36.44962891 18.169375 36.44962891 18.169375 37 20 C37.76708455 22.48032915 38.53810715 24.95944288 39.3125 27.4375 C39.51512451 28.08710693 39.71774902 28.73671387 39.92651367 29.40600586 C41.20227515 33.4858301 42.49765143 37.55899067 43.8046875 41.62890625 C44.11623779 42.60093994 44.42778809 43.57297363 44.7487793 44.57446289 C45.35999142 46.4793146 45.97332709 48.38348665 46.58911133 50.28686523 C48.25788419 55.49023448 49.73899381 60.6826537 51 66 C53.23397141 62.64904288 54.22049548 59.38473308 55.3125 55.5625 C55.5297876 54.81387695 55.7470752 54.06525391 55.97094727 53.29394531 C57.82269754 46.82116056 59.44934365 40.2994957 61.04516602 33.75976562 C62.85390332 26.41420764 64.90647214 19.14901169 67.03295898 11.88964844 C67.27473877 11.06110352 67.51651855 10.23255859 67.765625 9.37890625 C67.97993164 8.6526001 68.19423828 7.92629395 68.41503906 7.17797852 C69.05061399 4.81154948 69.51945776 2.40271118 70 0 C78.91 0 87.82 0 97 0 C96.29553979 5.6356817 95.72437786 9.41411346 93.890625 14.52734375 C93.46523438 15.73326172 93.03984375 16.93917969 92.6015625 18.18164062 C91.93253906 20.04079102 91.93253906 20.04079102 91.25 21.9375 C88.4997123 29.6325415 85.86019507 37.3006061 83.609375 45.15625 C80.75939127 55.07964443 77.38151057 64.81001442 74 74.5625 C72.7887872 78.06281459 71.58031595 81.56405668 70.375 85.06640625 C69.93954865 86.33068199 69.93954865 86.33068199 69.49530029 87.62049866 C67.21858699 94.27034286 65.1046736 100.96076172 63.06738281 107.6875 C59.79220356 117.85164273 54.62833457 126.2418509 45.11328125 131.63671875 C37.83288857 135.02905125 32.01406184 136.1811132 24.0625 136.1875 C23.26392578 136.19974609 22.46535156 136.21199219 21.64257812 136.22460938 C20.4756543 136.22750977 20.4756543 136.22750977 19.28515625 136.23046875 C18.58269775 136.23457764 17.88023926 136.23868652 17.15649414 136.24291992 C15 136 15 136 10 134 C10 125.75 10 117.5 10 109 C11.98 109.33 13.96 109.66 16 110 C23.35669671 110.74698917 23.35669671 110.74698917 30 108.0625 C32.82490606 103.73686259 35.47524121 98.92868531 34.61694336 93.6394043 C34.04281963 91.18319076 33.34659406 88.86686894 32.51171875 86.48828125 C32.21716797 85.64716797 31.92261719 84.80605469 31.61914062 83.93945312 C31.31169922 83.07255859 31.00425781 82.20566406 30.6875 81.3125 C30.39166016 80.46494141 30.09582031 79.61738281 29.79101562 78.74414062 C28.35261616 74.65883762 26.86287435 70.60719873 25.234375 66.59375 C21.83442487 58.19382483 18.73249117 49.676092 15.5625 41.1875 C14.25206706 37.68070312 12.94090546 34.17417995 11.62890625 30.66796875 C11.31345856 29.8249118 10.99801086 28.98185486 10.67300415 28.11325073 C8.5203486 22.36819897 6.3377527 16.63589255 4.12084961 10.9152832 C2.72519778 7.28525087 1.36554876 3.64146335 0 0 Z " fill="#04A0E3" transform="translate(884.000,148.000)"/>
  <path d="M0 0 C0.62003906 0.57621094 1.24007812 1.15242187 1.87890625 1.74609375 C1.87890625 6.58393435 0.08696205 9.0629718 -2.49609375 12.93359375 C-2.90859375 13.57490234 -3.32109375 14.21621094 -3.74609375 14.87695312 C-6.84282403 19.60695889 -6.84282403 19.60695889 -9.12109375 20.74609375 C-11.85325339 19.52722081 -14.47433251 18.25462803 -17.12109375 16.87109375 C-17.8584375 16.49339844 -18.59578125 16.11570313 -19.35546875 15.7265625 C-20.72449147 15.02456363 -22.09185208 14.31930774 -23.45703125 13.60986328 C-27.2395494 11.64646111 -30.34672192 11.562556 -34.55859375 11.55859375 C-35.58049805 11.54022461 -35.58049805 11.54022461 -36.62304688 11.52148438 C-40.34939184 11.51053527 -41.94100946 11.62603756 -45.12109375 13.74609375 C-45.85333975 17.86013633 -45.85333975 17.86013633 -45.12109375 21.74609375 C-41.20678344 24.92284114 -37.52940788 26.44502877 -32.68359375 27.68359375 C-2.43193616 35.87008883 -2.43193616 35.87008883 5.87890625 47.74609375 C8.76476872 53.5178187 8.78918539 60.13768184 7.2265625 66.33203125 C4.31381263 74.45772831 -0.24169045 80.50132608 -7.93359375 84.55859375 C-9.64973749 85.31790399 -11.37906098 86.04822326 -13.12109375 86.74609375 C-13.85714844 87.06578125 -14.59320313 87.38546875 -15.3515625 87.71484375 C-29.72890675 93.05994936 -47.36492067 90.14570483 -61.0546875 84.0625 C-66.53335974 81.47053765 -71.47595597 78.67593202 -76.12109375 74.74609375 C-74.5925544 70.4742285 -72.40599894 66.75282512 -69.99609375 62.93359375 C-69.62355469 62.33611328 -69.25101562 61.73863281 -68.8671875 61.12304688 C-67.95538895 59.66183124 -67.03873399 58.2036479 -66.12109375 56.74609375 C-61.38437089 58.53353634 -56.94285337 60.82955408 -52.46875 63.1796875 C-46.22226993 66.37843444 -40.5200274 68.0334484 -33.49609375 68.05859375 C-31.86800781 68.08373047 -31.86800781 68.08373047 -30.20703125 68.109375 C-26.73153573 67.70023439 -24.85252044 66.91825209 -22.12109375 64.74609375 C-21.37109375 61.24609375 -21.37109375 61.24609375 -22.12109375 57.74609375 C-24.97188844 55.04882872 -27.91493762 53.9778313 -31.62890625 52.84375 C-33.23378906 52.34391602 -33.23378906 52.34391602 -34.87109375 51.83398438 C-35.98484375 51.49560547 -37.09859375 51.15722656 -38.24609375 50.80859375 C-66.97632531 41.9959518 -66.97632531 41.9959518 -73.12109375 30.74609375 C-73.48554437 27.95101578 -73.65033341 25.64195906 -73.55859375 22.87109375 C-73.54248047 22.16073975 -73.52636719 21.45038574 -73.50976562 20.71850586 C-73.17320341 11.78831992 -70.79875327 6.47097186 -64.7109375 -0.0703125 C-45.63591619 -16.1531736 -19.79805023 -12.47824709 0 0 Z " fill="#053D67" transform="translate(503.12109375,155.25390625)"/>
  <path d="M0 0 C0.98822754 0.0095874 1.97645508 0.0191748 2.99462891 0.02905273 C3.74663574 0.04137939 4.49864258 0.05370605 5.2734375 0.06640625 C5.30230287 4.37889234 5.32019005 8.69135387 5.3359375 13.00390625 C5.34431641 14.23560547 5.35269531 15.46730469 5.36132812 16.73632812 C5.36455078 17.90615234 5.36777344 19.07597656 5.37109375 20.28125 C5.37633057 21.365271 5.38156738 22.44929199 5.38696289 23.56616211 C5.2734375 26.06640625 5.2734375 26.06640625 4.2734375 27.06640625 C2.24732165 27.37844074 0.22036581 27.6859122 -1.81030273 27.96679688 C-9.5273983 29.0552319 -15.5400196 30.01616715 -21.7265625 35.06640625 C-22.27580887 35.49423904 -22.82505524 35.92207184 -23.39094543 36.36286926 C-25.32604948 38.83103279 -25.12577611 40.71264319 -25.15893555 43.82299805 C-25.17872971 45.00399582 -25.19852386 46.18499359 -25.21891785 47.40177917 C-25.22850273 49.31438286 -25.22850273 49.31438286 -25.23828125 51.265625 C-25.25623245 52.57094681 -25.27418365 53.87626862 -25.29267883 55.22114563 C-25.34019303 58.68823221 -25.36999272 62.15525366 -25.39611816 65.62255859 C-25.42591686 69.16191382 -25.47228786 72.70104906 -25.51757812 76.24023438 C-25.60519457 83.18230984 -25.66512396 90.12385177 -25.7265625 97.06640625 C-35.6265625 97.06640625 -45.5265625 97.06640625 -55.7265625 97.06640625 C-55.7265625 65.38640625 -55.7265625 33.70640625 -55.7265625 1.06640625 C-46.8165625 1.06640625 -37.9065625 1.06640625 -28.7265625 1.06640625 C-28.2315625 9.48140625 -28.2315625 9.48140625 -27.7265625 18.06640625 C-26.9221875 16.89078125 -26.1178125 15.71515625 -25.2890625 14.50390625 C-19.01550175 6.15888676 -10.70436035 -0.13479565 0 0 Z " fill="#09A2E3" transform="translate(871.727,145.934)"/>
  <path d="M0 0 C9.57 0 19.14 0 29 0 C29 31.35 29 62.7 29 95 C19.43 95 9.86 95 0 95 C0 63.65 0 32.3 0 0 Z " fill="#04A0E3" transform="translate(776.000,148.000)"/>
  <path d="M0 0 C1.61335914 3.22671828 0.13341232 5.82126671 -0.921875 9.140625 C-6.14644963 22.99710555 -14.31571099 32.11726623 -27.6875 38.375 C-33.83493956 40.67184555 -40.54489953 41.10565953 -47 40 C-49.4140625 37.96875 -49.4140625 37.96875 -51 36 C-46.80711775 26.04470739 -34.95008257 20.14840173 -26 15 C-30.63914492 16.38187296 -34.70916223 18.32365463 -38.875 20.75 C-39.4848877 21.10126953 -40.09477539 21.45253906 -40.72314453 21.81445312 C-45.40550756 24.55401461 -49.80080956 27.55718548 -54 31 C-55.77447243 29.22552757 -55.37925289 26.84942344 -55.5 24.4375 C-55.42493554 18.69848077 -54.36618388 15.63866724 -50.2734375 11.62109375 C-46.21459231 8.72690001 -41.77504166 7.29430685 -37 6 C-36.39269043 5.82509033 -35.78538086 5.65018066 -35.15966797 5.4699707 C-32.69772553 4.9342213 -30.41352123 4.83903427 -27.89453125 4.7890625 C-26.91033203 4.76070312 -25.92613281 4.73234375 -24.91210938 4.703125 C-22.86540569 4.65153827 -20.8185071 4.60712473 -18.77148438 4.5703125 C-8.70278225 4.41106238 -8.70278225 4.41106238 0 0 Z " fill="#74B528" transform="translate(84,183)"/>
  <path d="M0 0 C4.85853072 -0.13830293 9.71015536 -0.25753443 14.5690918 -0.32958984 C16.21906755 -0.35963934 17.8688852 -0.40049264 19.51831055 -0.45263672 C34.98941266 -0.92924572 34.98941266 -0.92924572 39.58203125 2.61328125 C42.32279178 5.74690109 42.83209863 8.38601096 42.625 12.5625 C42.58246094 14.20412109 42.58246094 14.20412109 42.5390625 15.87890625 C41.93759534 19.36131392 41.2122865 21.24640935 39 24 C34.3797272 26.85718357 30.51121431 27.41210986 25.1953125 27.29296875 C24.4713327 27.28872391 23.74735291 27.28447906 23.00143433 27.28010559 C20.70889713 27.26342972 18.41726754 27.22580081 16.125 27.1875 C14.56251278 27.17243871 13.00001165 27.15875284 11.4375 27.14648438 C7.62437647 27.11612511 3.81319355 27.0573892 0 27 C0 18.09 0 9.18 0 0 Z " fill="#212631" transform="translate(32,139)"/>
  <path d="M0 0 C1.03147317 11.63272523 1.03147317 11.63272523 -2.4375 16 C-7.22447915 20.59594946 -12.93387092 23.1457112 -19.5625 23.5 C-25.24873018 23.2211656 -29.75753513 20.96215676 -34 17.25 C-35.54716861 13.76887062 -35.82677063 10.66922174 -35 7 C-32.67283762 2.62698059 -30.01358255 0.40898945 -25.4375 -1.4375 C-16.90734133 -2.92741255 -8.24576426 -2.58690643 0 0 Z " fill="#212631" transform="translate(179,201)"/>
  <path d="M0 0 C0.72574219 -0.00773438 1.45148438 -0.01546875 2.19921875 -0.0234375 C7.34804556 -0.00508161 11.79910152 0.8386579 16.75 2.25 C17.78156227 13.88373002 17.78156227 13.88373002 14.25 18.3125 C7.72765355 24.50268992 0.70884071 24.60238962 -7.98046875 24.74609375 C-11.85106719 24.15879865 -13.46715634 22.94209875 -16.25 20.25 C-18.78482149 16.74953223 -18.82167124 13.46607539 -18.25 9.25 C-13.54011484 1.66524987 -8.73128598 -0.09110907 0 0 Z " fill="#212631" transform="translate(367.25,198.75)"/>
  <path d="M0 0 C1.08764648 -0.01740234 1.08764648 -0.01740234 2.19726562 -0.03515625 C7.35121607 -0.00737215 10.95144349 1.0785845 15.5625 3.375 C16.78062689 13.67832326 16.78062689 13.67832326 13.921875 17.66015625 C11.87454273 19.44588495 9.88146223 20.96161791 7.5625 22.375 C6.96695313 22.75527344 6.37140625 23.13554687 5.7578125 23.52734375 C0.44053047 25.58045799 -5.84726742 24.98871032 -11.4375 24.375 C-14.84227882 22.80670291 -16.74264992 20.76470016 -18.4375 17.375 C-18.97067364 12.92605928 -19.04517228 9.45011249 -16.8125 5.5 C-11.52837688 0.77210036 -6.81186415 -0.10841163 0 0 Z " fill="#212631" transform="translate(715.438,198.625)"/>
  <path d="M0 0 C4.28885139 2.32628512 7.16602742 5.47904434 9 10 C9.73285105 14.68683808 9.44402628 18.14366361 7.25 22.375 C3.91187196 26.26948271 1.16514022 28.51541458 -4.015625 29.30859375 C-8.60797393 29.57353696 -11.33450637 29.24871797 -14.9375 26.25 C-18.68177838 22.74471811 -20.30906614 19.38407954 -20.5625 14.25 C-20.4905536 8.85402007 -18.40695585 6.25978818 -14.7265625 2.49609375 C-10.5184476 -1.15030446 -5.29022618 -0.77968898 0 0 Z " fill="#05A0E3" transform="translate(796.000,111.000)"/>
  <path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C0.33710937 2.03060547 0.33710937 2.03060547 -1.359375 3.08203125 C-2.82297656 3.99209119 -4.28651491 4.90225278 -5.75 5.8125 C-6.47960938 6.26431641 -7.20921875 6.71613281 -7.9609375 7.18164062 C-11.41374251 9.33179647 -14.80661473 11.47050272 -18 14 C-18.66 13.67 -19.32 13.34 -20 13 C-20 12.01 -20 11.02 -20 10 C-17.23116804 8.51706418 -14.4596027 7.03931138 -11.6875 5.5625 C-10.90181641 5.14162109 -10.11613281 4.72074219 -9.30664062 4.28710938 C-8.54931641 3.88427734 -7.79199219 3.48144531 -7.01171875 3.06640625 C-6.31522217 2.69459229 -5.61872559 2.32277832 -4.90112305 1.93969727 C-3 1 -3 1 0 0 Z " fill="#0B4065" transform="translate(59,196)"/>
  <path d="M0 0 C-1.49097658 3.7952131 -3.75700849 5.61042731 -7 8 C-6.44414399 4.1090079 -4.34122117 0 0 0 Z " fill="#12456D" transform="translate(443,153)"/>
  <path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C-0.97 2.65 -3.94 4.3 -7 6 C-7.66 5.34 -8.32 4.68 -9 4 C-7.54618103 3.30300448 -6.08677047 2.61766134 -4.625 1.9375 C-3.40683594 1.36322266 -3.40683594 1.36322266 -2.1640625 0.77734375 C-1.44992188 0.52082031 -0.73578125 0.26429688 0 0 Z " fill="#235F5D" transform="translate(59,196)"/>`],ko=["102 115",`<g style="fill: currentColor">
    <path d="m3 113 37.75 -0.375 11.85 -0.166c4.834 -0.027 4.834 -0.027 7.108 -0.032q2.346 -0.014 4.691 -0.06c11.585 -0.217 21.128 0.626 30.087 8.94 8.322 8.788 9.935 16.92 9.866 28.752 -0.72 8.062 -3.976 14.933 -9.352 20.941 -2.14 1.582 -2.14 1.582 -4.25 2.813l-2.14 1.269 -1.61 0.918 2.125 0.996c9.46 4.743 16.68 10.906 20.25 21.137 2.088 9.58 1.624 19.318 -3.375 27.867 -6.319 9.776 -17.08 13.165 -27.708 16.543 -3.173 0.633 -6.212 0.576 -9.447 0.57h-2.195q-3.506 -0.002 -7.01 -0.015 -2.168 -0.003 -4.337 -0.003c-5.351 -0.006 -10.703 -0.02 -16.054 -0.032L3 243z" fill="#023C65"/>
  </g>`],Lo=localStorage.getItem("userRole"),jo=[{component:ee,name:"Dashboard",to:"/dashboard",icon:f.jsx(H,{icon:lo,customClassName:"nav-icon"})},{component:tr,name:"Management"},{component:ee,name:"Users",to:"/users",icon:f.jsx(H,{icon:Zn,customClassName:"nav-icon"})},...Lo==="SuperAdmin"?[{component:ee,name:"Energy Meters",to:"/energy-meters",icon:f.jsx(H,{icon:so,customClassName:"nav-icon"})},{component:ee,name:"Locations",to:"/locations",icon:f.jsx(H,{icon:io,customClassName:"nav-icon"})},{component:ee,name:"Mappings",to:"/mappings",icon:f.jsx(H,{icon:no,customClassName:"nav-icon"})}]:[],{component:ee,name:"Reports",to:"/reports",icon:f.jsx(H,{icon:ro,customClassName:"nav-icon"})}],Do=()=>{const e=an(),t=Tt(n=>n.sidebarUnfoldable),r=Tt(n=>n.sidebarShow);return f.jsxs(rr,{className:"border-end",colorScheme:"dark",position:"fixed",unfoldable:t,visible:r,onVisibleChange:n=>{e({type:"set",sidebarShow:n})},children:[f.jsxs(ar,{className:"border-bottom",children:[f.jsxs(nr,{to:"/",children:[f.jsx(H,{customClassName:"sidebar-brand-full",width:"100%",height:"100%",icon:_o}),f.jsx(H,{customClassName:"sidebar-brand-narrow",icon:ko,height:32})]}),f.jsx(Ht,{className:"d-lg-none",dark:!0,onClick:()=>e({type:"set",sidebarShow:!1})})]}),f.jsx(_n,{items:jo}),f.jsx(ir,{className:"border-top d-none d-lg-flex"})]})},Po=p.memo(Do),Mo=""+new URL("components-rl0RgVTV.webp",import.meta.url).href,zo=e=>f.jsx("div",{className:"bg-primary bg-opacity-10 border border-2 border-primary rounded mb-4",children:f.jsxs("div",{className:"row d-flex align-items-center p-3 px-xl-4 flex-xl-nowrap",children:[f.jsx("div",{className:"col-xl-auto col-12 d-none d-xl-block p-0",children:f.jsx("img",{className:"img-fluid",src:Mo,width:"160px",height:"160px",alt:"CoreUI PRO hexagon"})}),f.jsxs("div",{className:"col-md col-12 px-lg-4",children:["Our Admin Panel isn’t just a mix of third-party components. It’s"," ",f.jsx("strong",{children:"the only open-source React dashboard built on a professional, enterprise-grade UI Components Library"}),". This component is part of this library, and we present only the basic usage of it here. To explore extended examples, detailed API documentation, and customization options, refer to our docs."]}),f.jsx("div",{className:"col-md-auto col-12 mt-3 mt-lg-0",children:f.jsx("a",{className:"btn btn-primary text-nowrap text-white",href:`https://coreui.io/react/docs/${e.href}`,target:"_blank",rel:"noopener noreferrer",children:"Explore Documentation"})})]})});zo.propTypes={href:u.string};const kn=e=>{const{href:t,name:r,text:n,...i}=e,a=r?`https://coreui.io/react/docs/components/${r}`:t;return f.jsx("div",{className:"float-end",children:f.jsx(tt,{...i,href:a,rel:"noreferrer noopener",target:"_blank",className:"card-header-action",children:f.jsx("small",{className:"text-body-secondary",children:n||"docs"})})})};kn.propTypes={href:u.string,name:u.string,text:u.string};const Ho=p.memo(kn),Ln=e=>{const{children:t,href:r,tabContentClassName:n}=e,i=`https://coreui.io/react/docs/${r}`;return f.jsxs("div",{className:"example",children:[f.jsxs(Jt,{variant:"underline-border",children:[f.jsx(ee,{children:f.jsxs(ge,{href:"#",active:!0,children:[f.jsx(H,{icon:ao,className:"me-2"}),"Preview"]})}),f.jsx(ee,{children:f.jsxs(ge,{href:i,target:"_blank",children:[f.jsx(H,{icon:to,className:"me-2"}),"Code"]})})]}),f.jsx(or,{className:`rounded-bottom ${n||""}`,children:f.jsx(sr,{className:"p-3 preview",visible:!0,children:t})})]})};Ln.propTypes={children:u.node,href:u.string,tabContentClassName:u.string};const qo=p.memo(Ln),Vo=()=>f.jsxs("div",{children:[f.jsx(Po,{}),f.jsxs("div",{className:"wrapper d-flex flex-column min-vh-100",children:[f.jsx(po,{}),f.jsx("div",{className:"body flex-grow-1",children:f.jsx(Ka,{})}),f.jsx(Ja,{})]})]}),Fo=Object.freeze(Object.defineProperty({__proto__:null,default:Vo},Symbol.toStringTag,{value:"Module"}));export{ao as A,oo as B,Zt as C,Ho as D,zr as E,so as F,lo as G,Vr as H,Ht as I,Ft as J,Dt as K,Fo as L,Ge as R,ue as T,ra as _,zo as a,qo as b,et as c,Jt as d,ee as e,ge as f,na as g,ta as h,Ke as i,$t as j,nt as k,at as l,it as m,_e as n,oa as o,or as p,aa as q,Xt as r,sa as s,eo as t,Pe as u,to as v,Mr as w,ro as x,no as y,io as z};
