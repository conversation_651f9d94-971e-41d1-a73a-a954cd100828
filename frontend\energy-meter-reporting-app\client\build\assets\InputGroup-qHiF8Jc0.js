import{j as e}from"./index-BDJ8oeCE.js";import{a as b,b as a,k as h,l as x,m as p,n as r}from"./DefaultLayout-BolUaDEE.js";import{a as l}from"./index.esm-DSzlmaRN.js";import{C as f,a as o}from"./CRow-C1o2zw34.js";import{C as t,a as d}from"./CCardBody-iimbKiZ7.js";import{C as c}from"./CCardHeader-CFnfD6gM.js";import{C as s,a as i}from"./CInputGroupText-BGHrT9V9.js";import{C as n}from"./CFormInput-LKfVdWds.js";import{C as g}from"./CFormLabel-CzXD3nfE.js";import{C as y}from"./CFormTextarea-Cg-QPt4v.js";import{C as m}from"./CFormCheck-CL4oiK6y.js";import{C as u}from"./CDropdownDivider-DVoRzmnU.js";import{C as j}from"./CFormSelect-B3z3ot4z.js";import"./cil-user-Ddrdy7PS.js";import"./CFormControlWrapper-CyzWU6Dg.js";import"./CFormControlValidation-_wBnnnml.js";const E=()=>e.jsxs(f,{children:[e.jsxs(o,{xs:12,children:[e.jsx(b,{href:"forms/input-group/"}),e.jsxs(t,{className:"mb-4",children:[e.jsxs(c,{children:[e.jsx("strong",{children:"React Input group"})," ",e.jsx("small",{children:"Basic example"})]}),e.jsxs(d,{children:[e.jsxs("p",{className:"text-body-secondary small",children:["Place one add-on or button on either side of an input. You may also place one on both sides of an input. Remember to place ",e.jsx("code",{children:"<CFormLabel>"}),"s outside the input group."]}),e.jsxs(a,{href:"forms/input-group",children:[e.jsxs(s,{className:"mb-3",children:[e.jsx(i,{id:"basic-addon1",children:"@"}),e.jsx(n,{placeholder:"Username","aria-label":"Username","aria-describedby":"basic-addon1"})]}),e.jsxs(s,{className:"mb-3",children:[e.jsx(n,{placeholder:"Recipient's username","aria-label":"Recipient's username","aria-describedby":"basic-addon2"}),e.jsx(i,{id:"basic-addon2",children:"@example.com"})]}),e.jsx(g,{htmlFor:"basic-url",children:"Your vanity URL"}),e.jsxs(s,{className:"mb-3",children:[e.jsx(i,{id:"basic-addon3",children:"https://example.com/users/"}),e.jsx(n,{id:"basic-url","aria-describedby":"basic-addon3"})]}),e.jsxs(s,{className:"mb-3",children:[e.jsx(i,{children:"$"}),e.jsx(n,{"aria-label":"Amount (to the nearest dollar)"}),e.jsx(i,{children:".00"})]}),e.jsxs(s,{className:"mb-3",children:[e.jsx(n,{placeholder:"Username","aria-label":"Username"}),e.jsx(i,{children:"@"}),e.jsx(n,{placeholder:"Server","aria-label":"Server"})]}),e.jsxs(s,{children:[e.jsx(i,{children:"With textarea"}),e.jsx(y,{"aria-label":"With textarea"})]})]})]})]})]}),e.jsx(o,{xs:12,children:e.jsxs(t,{className:"mb-4",children:[e.jsxs(c,{children:[e.jsx("strong",{children:"React Input group"})," ",e.jsx("small",{children:"Wrapping"})]}),e.jsxs(d,{children:[e.jsxs("p",{className:"text-body-secondary small",children:["Input groups wrap by default via ",e.jsx("code",{children:"flex-wrap: wrap"})," in order to accommodate custom form field validation within an input group. You may disable this with"," ",e.jsx("code",{children:".flex-nowrap"}),"."]}),e.jsx(a,{href:"forms/input-group#wrapping",children:e.jsxs(s,{className:"flex-nowrap",children:[e.jsx(i,{id:"addon-wrapping",children:"@"}),e.jsx(n,{placeholder:"Username","aria-label":"Username","aria-describedby":"addon-wrapping"})]})})]})]})}),e.jsx(o,{xs:12,children:e.jsxs(t,{className:"mb-4",children:[e.jsxs(c,{children:[e.jsx("strong",{children:"React Input group"})," ",e.jsx("small",{children:"Sizing"})]}),e.jsxs(d,{children:[e.jsxs("p",{className:"text-body-secondary small",children:["Add the relative form sizing classes to the ",e.jsx("code",{children:"<CInputGroup>"})," itself and contents within will automatically resize—no need for repeating the form control size classes on each element."]}),e.jsx("p",{className:"text-body-secondary small",children:e.jsx("strong",{children:"Sizing on the individual input group elements isn'tsupported."})}),e.jsxs(a,{href:"forms/input-group#sizing",children:[e.jsxs(s,{size:"sm",className:"mb-3",children:[e.jsx(i,{id:"inputGroup-sizing-sm",children:"Small"}),e.jsx(n,{"aria-label":"Sizing example input","aria-describedby":"inputGroup-sizing-sm"})]}),e.jsxs(s,{className:"mb-3",children:[e.jsx(i,{id:"inputGroup-sizing-default",children:"Default"}),e.jsx(n,{"aria-label":"Sizing example input","aria-describedby":"inputGroup-sizing-default"})]}),e.jsxs(s,{size:"lg",children:[e.jsx(i,{id:"inputGroup-sizing-lg",children:"Large"}),e.jsx(n,{"aria-label":"Sizing example input","aria-describedby":"inputGroup-sizing-lg"})]})]})]})]})}),e.jsx(o,{xs:12,children:e.jsxs(t,{className:"mb-4",children:[e.jsxs(c,{children:[e.jsx("strong",{children:"React Input group"})," ",e.jsx("small",{children:"Checkboxes and radios"})]}),e.jsxs(d,{children:[e.jsx("p",{className:"text-body-secondary small",children:"Place any checkbox or radio option within an input group's addon instead of text."}),e.jsxs(a,{href:"forms/input-group#checkboxes-and-radios",children:[e.jsxs(s,{className:"mb-3",children:[e.jsx(i,{children:e.jsx(m,{type:"checkbox",value:"","aria-label":"Checkbox for following text input"})}),e.jsx(n,{"aria-label":"Text input with checkbox"})]}),e.jsxs(s,{children:[e.jsx(i,{children:e.jsx(m,{type:"radio",value:"","aria-label":"Radio button for following text input"})}),e.jsx(n,{"aria-label":"Text input with radio button"})]})]})]})]})}),e.jsx(o,{xs:12,children:e.jsxs(t,{className:"mb-4",children:[e.jsxs(c,{children:[e.jsx("strong",{children:"React Input group"})," ",e.jsx("small",{children:"Multiple inputs"})]}),e.jsxs(d,{children:[e.jsxs("p",{className:"text-body-secondary small",children:["While multiple ",e.jsx("code",{children:"<CFormInput>"}),"s are supported visually, validation styles are only available for input groups with a single"," ",e.jsx("code",{children:"<CFormInput>"}),"."]}),e.jsx(a,{href:"forms/input-group#multiple-inputs",children:e.jsxs(s,{children:[e.jsx(i,{children:"First and last name"}),e.jsx(n,{"aria-label":"First name"}),e.jsx(n,{"aria-label":"Last name"})]})})]})]})}),e.jsx(o,{xs:12,children:e.jsxs(t,{className:"mb-4",children:[e.jsxs(c,{children:[e.jsx("strong",{children:"React Input group"})," ",e.jsx("small",{children:"Multiple addons"})]}),e.jsxs(d,{children:[e.jsx("p",{className:"text-body-secondary small",children:"Multiple add-ons are supported and can be mixed with checkbox and radio input versions.."}),e.jsxs(a,{href:"forms/input-group#multiple-addons",children:[e.jsxs(s,{className:"mb-3",children:[e.jsx(i,{children:"$"}),e.jsx(i,{children:"0.00"}),e.jsx(n,{"aria-label":"Dollar amount (with dot and two decimal places)"})]}),e.jsxs(s,{children:[e.jsx(n,{"aria-label":"Dollar amount (with dot and two decimal places)"}),e.jsx(i,{children:"$"}),e.jsx(i,{children:"0.00"})]})]})]})]})}),e.jsx(o,{xs:12,children:e.jsxs(t,{className:"mb-4",children:[e.jsxs(c,{children:[e.jsx("strong",{children:"React Input group"})," ",e.jsx("small",{children:"Button addons"})]}),e.jsxs(d,{children:[e.jsx("p",{className:"text-body-secondary small",children:"Multiple add-ons are supported and can be mixed with checkbox and radio input versions.."}),e.jsxs(a,{href:"forms/input-group#button-addons",children:[e.jsxs(s,{className:"mb-3",children:[e.jsx(l,{type:"button",color:"secondary",variant:"outline",id:"button-addon1",children:"Button"}),e.jsx(n,{placeholder:"","aria-label":"Example text with button addon","aria-describedby":"button-addon1"})]}),e.jsxs(s,{className:"mb-3",children:[e.jsx(n,{placeholder:"Recipient's username","aria-label":"Recipient's username","aria-describedby":"button-addon2"}),e.jsx(l,{type:"button",color:"secondary",variant:"outline",id:"button-addon2",children:"Button"})]}),e.jsxs(s,{className:"mb-3",children:[e.jsx(l,{type:"button",color:"secondary",variant:"outline",children:"Button"}),e.jsx(l,{type:"button",color:"secondary",variant:"outline",children:"Button"}),e.jsx(n,{placeholder:"","aria-label":"Example text with two button addons"})]}),e.jsxs(s,{children:[e.jsx(n,{placeholder:"Recipient's username","aria-label":"Recipient's username with two button addons"}),e.jsx(l,{type:"button",color:"secondary",variant:"outline",children:"Button"}),e.jsx(l,{type:"button",color:"secondary",variant:"outline",children:"Button"})]})]})]})]})}),e.jsx(o,{xs:12,children:e.jsxs(t,{className:"mb-4",children:[e.jsxs(c,{children:[e.jsx("strong",{children:"React Input group"})," ",e.jsx("small",{children:"Buttons with dropdowns"})]}),e.jsx(d,{children:e.jsxs(a,{href:"forms/input-group#buttons-with-dropdowns",children:[e.jsxs(s,{className:"mb-3",children:[e.jsxs(h,{variant:"input-group",children:[e.jsx(x,{color:"secondary",variant:"outline",children:"Dropdown"}),e.jsxs(p,{children:[e.jsx(r,{href:"#",children:"Action"}),e.jsx(r,{href:"#",children:"Another action"}),e.jsx(r,{href:"#",children:"Something else here"}),e.jsx(u,{}),e.jsx(r,{href:"#",children:"Separated link"})]})]}),e.jsx(n,{"aria-label":"Text input with dropdown button"})]}),e.jsxs(s,{className:"mb-3",children:[e.jsx(n,{"aria-label":"Text input with dropdown button"}),e.jsxs(h,{alignment:"end",variant:"input-group",children:[e.jsx(x,{color:"secondary",variant:"outline",children:"Dropdown"}),e.jsxs(p,{children:[e.jsx(r,{href:"#",children:"Action"}),e.jsx(r,{href:"#",children:"Another action"}),e.jsx(r,{href:"#",children:"Something else here"}),e.jsx(u,{}),e.jsx(r,{href:"#",children:"Separated link"})]})]})]}),e.jsxs(s,{children:[e.jsxs(h,{variant:"input-group",children:[e.jsx(x,{color:"secondary",variant:"outline",children:"Dropdown"}),e.jsxs(p,{children:[e.jsx(r,{href:"#",children:"Action"}),e.jsx(r,{href:"#",children:"Another action"}),e.jsx(r,{href:"#",children:"Something else here"}),e.jsx(u,{}),e.jsx(r,{href:"#",children:"Separated link"})]})]}),e.jsx(n,{"aria-label":"Text input with 2 dropdown buttons"}),e.jsxs(h,{alignment:"end",variant:"input-group",children:[e.jsx(x,{color:"secondary",variant:"outline",children:"Dropdown"}),e.jsxs(p,{children:[e.jsx(r,{href:"#",children:"Action"}),e.jsx(r,{href:"#",children:"Another action"}),e.jsx(r,{href:"#",children:"Something else here"}),e.jsx(u,{}),e.jsx(r,{href:"#",children:"Separated link"})]})]})]})]})})]})}),e.jsx(o,{xs:12,children:e.jsxs(t,{className:"mb-4",children:[e.jsxs(c,{children:[e.jsx("strong",{children:"React Input group"})," ",e.jsx("small",{children:"Segmented buttons"})]}),e.jsx(d,{children:e.jsxs(a,{href:"forms/input-group#segmented-buttons",children:[e.jsxs(s,{className:"mb-3",children:[e.jsxs(h,{variant:"input-group",children:[e.jsx(l,{type:"button",color:"secondary",variant:"outline",children:"Action"}),e.jsx(x,{color:"secondary",variant:"outline",split:!0}),e.jsxs(p,{children:[e.jsx(r,{href:"#",children:"Action"}),e.jsx(r,{href:"#",children:"Another action"}),e.jsx(r,{href:"#",children:"Something else here"}),e.jsx(u,{}),e.jsx(r,{href:"#",children:"Separated link"})]})]}),e.jsx(n,{"aria-label":"Text input with segmented dropdown button"})]}),e.jsxs(s,{children:[e.jsx(n,{"aria-label":"Text input with segmented dropdown button"}),e.jsxs(h,{alignment:"end",variant:"input-group",children:[e.jsx(l,{type:"button",color:"secondary",variant:"outline",children:"Action"}),e.jsx(x,{color:"secondary",variant:"outline",split:!0}),e.jsxs(p,{children:[e.jsx(r,{href:"#",children:"Action"}),e.jsx(r,{href:"#",children:"Another action"}),e.jsx(r,{href:"#",children:"Something else here"}),e.jsx(u,{}),e.jsx(r,{href:"#",children:"Separated link"})]})]})]})]})})]})}),e.jsx(o,{xs:12,children:e.jsxs(t,{className:"mb-4",children:[e.jsxs(c,{children:[e.jsx("strong",{children:"React Input group"})," ",e.jsx("small",{children:"Custom select"})]}),e.jsx(d,{children:e.jsxs(a,{href:"forms/input-group#custom-select",children:[e.jsxs(s,{className:"mb-3",children:[e.jsx(i,{as:"label",htmlFor:"inputGroupSelect01",children:"Options"}),e.jsxs(j,{id:"inputGroupSelect01",children:[e.jsx("option",{children:"Choose..."}),e.jsx("option",{value:"1",children:"One"}),e.jsx("option",{value:"2",children:"Two"}),e.jsx("option",{value:"3",children:"Three"})]})]}),e.jsxs(s,{className:"mb-3",children:[e.jsxs(j,{id:"inputGroupSelect02",children:[e.jsx("option",{children:"Choose..."}),e.jsx("option",{value:"1",children:"One"}),e.jsx("option",{value:"2",children:"Two"}),e.jsx("option",{value:"3",children:"Three"})]}),e.jsx(i,{as:"label",htmlFor:"inputGroupSelect02",children:"Options"})]}),e.jsxs(s,{className:"mb-3",children:[e.jsx(l,{type:"button",color:"secondary",variant:"outline",children:"Button"}),e.jsxs(j,{id:"inputGroupSelect03","aria-label":"Example select with button addon",children:[e.jsx("option",{children:"Choose..."}),e.jsx("option",{value:"1",children:"One"}),e.jsx("option",{value:"2",children:"Two"}),e.jsx("option",{value:"3",children:"Three"})]})]}),e.jsxs(s,{children:[e.jsxs(j,{id:"inputGroupSelect04","aria-label":"Example select with button addon",children:[e.jsx("option",{children:"Choose..."}),e.jsx("option",{value:"1",children:"One"}),e.jsx("option",{value:"2",children:"Two"}),e.jsx("option",{value:"3",children:"Three"})]}),e.jsx(l,{type:"button",color:"secondary",variant:"outline",children:"Button"})]})]})})]})}),e.jsx(o,{xs:12,children:e.jsxs(t,{className:"mb-4",children:[e.jsxs(c,{children:[e.jsx("strong",{children:"React Input group"})," ",e.jsx("small",{children:"Custom file input"})]}),e.jsx(d,{children:e.jsxs(a,{href:"forms/input-group#custom-file-input",children:[e.jsxs(s,{className:"mb-3",children:[e.jsx(i,{as:"label",htmlFor:"inputGroupFile01",children:"Upload"}),e.jsx(n,{type:"file",id:"inputGroupFile01"})]}),e.jsxs(s,{className:"mb-3",children:[e.jsx(n,{type:"file",id:"inputGroupFile02"}),e.jsx(i,{as:"label",htmlFor:"inputGroupFile02",children:"Upload"})]}),e.jsxs(s,{className:"mb-3",children:[e.jsx(l,{type:"button",color:"secondary",variant:"outline",id:"inputGroupFileAddon03",children:"Button"}),e.jsx(n,{type:"file",id:"inputGroupFile03","aria-describedby":"inputGroupFileAddon03","aria-label":"Upload"})]}),e.jsxs(s,{children:[e.jsx(n,{type:"file",id:"inputGroupFile04","aria-describedby":"inputGroupFileAddon04","aria-label":"Upload"}),e.jsx(l,{type:"button",color:"secondary",variant:"outline",id:"inputGroupFileAddon04",children:"Button"})]})]})})]})})]});export{E as default};
