
const fs = require('fs');

function getdailyquery(start_time, end_time, emIDArray, parameterArray) {


    const queryString = `${dailypreprocess(start_time, end_time, emIDArray, parameterArray)}     
    SELECT * FROM FinalData
WHERE DateAndTime >= @TimeStart 
AND DateAndTime < @TimeEnd
ORDER BY DateAndTime, TagName;`;
    writequerylog('dailyquery.txt', queryString);
    return queryString;
}

function writequerylog(filename, queryString) {

    fs.writeFile('./logs/' + filename, queryString, err => {
        if (err) {
            console.error(err);
        } else {
            // file written successfully
        }
    });

}
function getmonthlyquery(start_time, end_time, emIDArray, parameterArray) {
    const queryString = `${monthlypreprocess(start_time, end_time, emIDArray, parameterArray)}

SELECT DISTINCT ReadingDate  DateAndTime, TagName, MedianVal AS Val FROM MedianData
UNION ALL
SELECT DISTINCT ReadingDate AS DateAndTime, TagName, MaxVal AS Val FROM MaxKWH
ORDER BY ReadingDate, TagName`;

writequerylog('monthlyquery.txt', queryString);
   
    return queryString;
}

function getyearlyquery(start_time, end_time, emIDArray, parameterArray) {
    const queryString = `${yearlypreprocess(start_time, end_time, emIDArray, parameterArray)}
SELECT 
    FORMAT(DATEFROMPARTS(Year, Month, 1), 'MMMM-yyyy') AS DateAndTime,
    TagName,
    Val
FROM YearlyData
ORDER BY Year, Month, TagName;`;

writequerylog('yearlyquery.txt', queryString);
    return queryString;
}

function dailypreprocess(start_time, end_time, emIDArray, parameterArray) {
    return `DECLARE @TimeStart DATETIME = DATEADD(SECOND, -30, '${start_time}');
    DECLARE @TimeEnd DATETIME = DATEADD(SECOND, +1, '${end_time}');
    DECLARE @EMIDs TABLE (EMID NVARCHAR(10));
    ${emIDArray.map(id => `INSERT INTO @EMIDs (EMID) VALUES ('${id}');`).join(' ')}
    
    DECLARE @Parameters TABLE (ParameterName NVARCHAR(50));
     ${parameterArray.map(param => `INSERT INTO @Parameters (ParameterName) VALUES ('${param}');`).join(' ')}

           
    
    WITH TagIndices AS (
        SELECT TagIndex, TagName FROM dbo.TagTable_01
        WHERE EXISTS (
            SELECT 1 FROM @EMIDs e
            JOIN @Parameters p ON TagName = e.EMID + '_' + p.ParameterName
        )
    ),
    HourlyData AS (
-- Find the closest reading to either hh:00:00 or hh+1:00:00
SELECT 
CASE 
WHEN DATEPART(MINUTE, f.DateAndTime) >= 30 
THEN DATEADD(HOUR, DATEDIFF(HOUR, 0, f.DateAndTime) + 1, 0)  
ELSE DATEADD(HOUR, DATEDIFF(HOUR, 0, f.DateAndTime), 0)  
END AS RoundedDateTime,
f.DateAndTime AS ActualDateTime,
f.TagIndex,
f.Val,
ROW_NUMBER() OVER (
PARTITION BY t.TagName, 
      CASE 
          WHEN DATEPART(MINUTE, f.DateAndTime) >= 30 
          THEN DATEADD(HOUR, DATEDIFF(HOUR, 0, f.DateAndTime) + 1, 0)  
          ELSE DATEADD(HOUR, DATEDIFF(HOUR, 0, f.DateAndTime), 0)  
      END 
ORDER BY ABS(DATEDIFF(SECOND, 
CASE 
 WHEN DATEPART(MINUTE, f.DateAndTime) >= 30 
 THEN DATEADD(HOUR, DATEDIFF(HOUR, 0, f.DateAndTime) + 1, 0)  
 ELSE DATEADD(HOUR, DATEDIFF(HOUR, 0, f.DateAndTime), 0)  
END, f.DateAndTime))
) AS rn
FROM dbo.FloatTable_01 f
JOIN TagIndices t ON f.TagIndex = t.TagIndex
WHERE f.DateAndTime >= @TimeStart 
AND f.DateAndTime < @TimeEnd
),
LastReading AS (
-- Find the closest reading to 23:57 for each day
SELECT 
CAST(DATEADD(DAY, DATEDIFF(DAY, 0, f.DateAndTime), '23:57:00') AS DATETIME) AS RoundedDateTime,
f.DateAndTime AS ActualDateTime,
f.TagIndex,
f.Val,
ROW_NUMBER() OVER (
PARTITION BY t.TagName, CAST(f.DateAndTime AS DATE) 
ORDER BY ABS(DATEDIFF(SECOND, 
CAST(DATEADD(DAY, DATEDIFF(DAY, 0, f.DateAndTime), '23:57:00') AS DATETIME), 
f.DateAndTime))
) AS rn
FROM dbo.FloatTable_01 f
JOIN TagIndices t ON f.TagIndex = t.TagIndex
WHERE f.DateAndTime >= @TimeStart 
AND f.DateAndTime < @TimeEnd
),
FinalData AS (
-- Combine HourlyData and LastReading, ensuring final rounding
SELECT 
CASE 
WHEN DATEPART(MINUTE, h.ActualDateTime) >= 30 
THEN DATEADD(HOUR, DATEDIFF(HOUR, 0, h.ActualDateTime) + 1, 0)  
ELSE DATEADD(HOUR, DATEDIFF(HOUR, 0, h.ActualDateTime), 0)  
END AS DateAndTime, 
t.TagName, 
h.Val
FROM HourlyData h
JOIN TagIndices t ON h.TagIndex = t.TagIndex
WHERE h.rn = 1

UNION ALL

SELECT 
l.RoundedDateTime AS DateAndTime, 
t.TagName, 
l.Val
FROM LastReading l
JOIN TagIndices t ON l.TagIndex = t.TagIndex
WHERE l.rn = 1
)`;
}

function monthlypreprocess(start_time, end_time, emIDArray, parameterArray) {
    return `${dailypreprocess(start_time, end_time, emIDArray, parameterArray)},
    
    ProcessedData AS (
    SELECT CAST(DateAndTime AS DATE) AS ReadingDate, TagName, Val FROM FinalData
    WHERE DateAndTime >= @TimeStart 
    AND DateAndTime < @TimeEnd
    ),

    MedianData AS (
    SELECT 
        ReadingDate, 
        TagName,
        PERCENTILE_CONT(0.5) WITHIN GROUP (ORDER BY Val) OVER (PARTITION BY ReadingDate, TagName) AS MedianVal
    FROM ProcessedData
    WHERE TagName NOT LIKE '%_KWH' -- Exclude KWH for now
    ),
    -- Get Max KWH Value (Last of the Day)
    MaxKWH AS (
    SELECT 
        f.ReadingDate,
        f.TagName,
        MAX(f.Val) AS MaxVal
    FROM ProcessedData f
    WHERE f.TagName LIKE '%_KWH' -- Only select KWH values
    GROUP BY f.ReadingDate, f.TagName
    )

    `;

}

function yearlypreprocess(start_time, end_time, emIDArray, parameterArray) {
    return `${monthlypreprocess(start_time, end_time, emIDArray, parameterArray)},
MonthlyData As (
SELECT DISTINCT ReadingDate , TagName, MedianVal AS Val FROM MedianData
UNION ALL
SELECT DISTINCT ReadingDate , TagName, MaxVal AS Val FROM MaxKWH
),
MonthlyMedian AS (
    -- Compute the median for Voltage & Current per month
    SELECT 
        YEAR(ReadingDate) AS Year,
        MONTH(ReadingDate) AS Month,
        TagName,
        PERCENTILE_CONT(0.5) WITHIN GROUP (ORDER BY Val) 
        OVER (PARTITION BY YEAR(ReadingDate), MONTH(ReadingDate), TagName) AS MedianVal
    FROM MonthlyData
    WHERE TagName NOT LIKE '%_KWH' -- Exclude KWH for now
),
MonthlyTotalKWH AS (
    -- Compute the total KWH consumed per month
    SELECT 
        YEAR(ReadingDate) AS Year,
        MONTH(ReadingDate) AS Month,
        TagName,
        SUM(Val) AS TotalKWH
    FROM MonthlyData
    WHERE TagName LIKE '%_KWH' -- Only KWH values
    GROUP BY YEAR(ReadingDate), MONTH(ReadingDate), TagName
),
YearlyData AS (
    -- Combine median and max KWH values
    SELECT DISTINCT Year, Month, TagName, MedianVal AS Val FROM MonthlyMedian
    UNION ALL
    SELECT DISTINCT Year, Month, TagName, TotalKWH AS Val FROM MonthlyTotalKWH
)
`;
}

function getmonthlyAllFeeders(start_time, end_time, emIDArray, parameterArray) {
    return getmonthlyquery(start_time, end_time, emIDArray, parameterArray);
}

module.exports = {
    getdailyquery,
    getyearlyquery,
    getmonthlyquery,
    getmonthlyAllFeeders
};