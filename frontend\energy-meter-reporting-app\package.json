{"name": "energy-meter-reporting-app", "version": "1.0.0", "description": "A Node.js application for reporting energy meter data with an MSSQL database.", "main": "src/app.js", "scripts": {"start": "node src/app.js", "dev": "nodemon src/app.js"}, "dependencies": {"bcrypt": "^5.1.1", "body-parser": "^1.20.3", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^16.4.7", "exceljs": "^4.4.0", "express": "^4.21.2", "express-session": "^1.18.1", "jsonwebtoken": "^9.0.2", "moment": "^2.30.1", "mssql": "^11.0.1", "sequelize": "^6.37.6"}, "devDependencies": {"nodemon": "^3.1.9"}, "author": "Your Name", "license": "ISC"}