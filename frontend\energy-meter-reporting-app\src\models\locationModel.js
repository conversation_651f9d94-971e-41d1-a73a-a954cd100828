const { Sequelize, DataTypes } = require('sequelize');
const { sequelize } = require('../utils/db');

// Define the Location model
const Location = sequelize.define('Locations', {
    id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true
    },
    name: {
        type: DataTypes.STRING,
        allowNull: false
    },
    description: {
        type: DataTypes.TEXT,
        allowNull: true
    },
    createdAt: {
        type: DataTypes.DATE,
        defaultValue: sequelize.literal('GETDATE()')  // ✅ Uses MSSQL's GETDATE()
    }
}, {
    timestamps: false
});

// Export the Location model
module.exports = Location;