import{j as a}from"./index-BDJ8oeCE.js";import{C as n,a as d,b as i,c as C,d as c,e as h}from"./index.esm-CMTGkdDj.js";import{D as o}from"./DefaultLayout-BolUaDEE.js";import"./index.esm-DSzlmaRN.js";import{C as b,a as s}from"./CRow-C1o2zw34.js";import{C as e,a as l}from"./CCardBody-iimbKiZ7.js";import{C as t}from"./CCardHeader-CFnfD6gM.js";import"./cil-user-Ddrdy7PS.js";const B=()=>{const r=()=>Math.round(Math.random()*100);return a.jsxs(b,{children:[a.jsx(s,{xs:12}),a.jsx(s,{xs:6,children:a.jsxs(e,{className:"mb-4",children:[a.jsxs(t,{children:["Bar Chart ",a.jsx(o,{name:"chart"})]}),a.jsx(l,{children:a.jsx(n,{data:{labels:["January","February","March","April","May","June","July"],datasets:[{label:"GitHub Commits",backgroundColor:"#f87979",data:[40,20,12,39,10,40,39,80,40]}]},labels:"months"})})]})}),a.jsx(s,{xs:6,children:a.jsxs(e,{className:"mb-4",children:[a.jsxs(t,{children:["Line Chart ",a.jsx(o,{name:"chart"})]}),a.jsx(l,{children:a.jsx(d,{data:{labels:["January","February","March","April","May","June","July"],datasets:[{label:"My First dataset",backgroundColor:"rgba(220, 220, 220, 0.2)",borderColor:"rgba(220, 220, 220, 1)",pointBackgroundColor:"rgba(220, 220, 220, 1)",pointBorderColor:"#fff",data:[r(),r(),r(),r(),r(),r(),r()]},{label:"My Second dataset",backgroundColor:"rgba(151, 187, 205, 0.2)",borderColor:"rgba(151, 187, 205, 1)",pointBackgroundColor:"rgba(151, 187, 205, 1)",pointBorderColor:"#fff",data:[r(),r(),r(),r(),r(),r(),r()]}]}})})]})}),a.jsx(s,{xs:6,children:a.jsxs(e,{className:"mb-4",children:[a.jsxs(t,{children:["Doughnut Chart ",a.jsx(o,{name:"chart"})]}),a.jsx(l,{children:a.jsx(i,{data:{labels:["VueJs","EmberJs","ReactJs","AngularJs"],datasets:[{backgroundColor:["#41B883","#E46651","#00D8FF","#DD1B16"],data:[40,20,80,10]}]}})})]})}),a.jsx(s,{xs:6,children:a.jsxs(e,{className:"mb-4",children:[a.jsxs(t,{children:["Pie Chart ",a.jsx(o,{name:"chart"})," "]}),a.jsx(l,{children:a.jsx(C,{data:{labels:["Red","Green","Yellow"],datasets:[{data:[300,50,100],backgroundColor:["#FF6384","#36A2EB","#FFCE56"],hoverBackgroundColor:["#FF6384","#36A2EB","#FFCE56"]}]}})})]})}),a.jsx(s,{xs:6,children:a.jsxs(e,{className:"mb-4",children:[a.jsxs(t,{children:["Polar Area Chart",a.jsx(o,{name:"chart"})]}),a.jsx(l,{children:a.jsx(c,{data:{labels:["Red","Green","Yellow","Grey","Blue"],datasets:[{data:[11,16,7,3,14],backgroundColor:["#FF6384","#4BC0C0","#FFCE56","#E7E9ED","#36A2EB"]}]}})})]})}),a.jsx(s,{xs:6,children:a.jsxs(e,{className:"mb-4",children:[a.jsxs(t,{children:["Radar Chart ",a.jsx(o,{name:"chart"})]}),a.jsx(l,{children:a.jsx(h,{data:{labels:["Eating","Drinking","Sleeping","Designing","Coding","Cycling","Running"],datasets:[{label:"My First dataset",backgroundColor:"rgba(220, 220, 220, 0.2)",borderColor:"rgba(220, 220, 220, 1)",pointBackgroundColor:"rgba(220, 220, 220, 1)",pointBorderColor:"#fff",pointHighlightFill:"#fff",pointHighlightStroke:"rgba(220, 220, 220, 1)",data:[65,59,90,81,56,55,40]},{label:"My Second dataset",backgroundColor:"rgba(151, 187, 205, 0.2)",borderColor:"rgba(151, 187, 205, 1)",pointBackgroundColor:"rgba(151, 187, 205, 1)",pointBorderColor:"#fff",pointHighlightFill:"#fff",pointHighlightStroke:"rgba(151, 187, 205, 1)",data:[28,48,40,19,96,27,100]}]}})})]})})]})};export{B as default};
