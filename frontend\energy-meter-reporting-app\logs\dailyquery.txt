DECLARE @TimeStart DATETIME = DATEADD(SECOND, -30, '2025-05-02');
    DECLARE @TimeEnd DATETIME = DATEADD(SECOND, +1, '2025-05-02T23:59:59.000Z');
    DECLARE @EMIDs TABLE (EMID NVARCHAR(10));
    INSERT INTO @EMIDs (EMID) VALUES ('EM1'); INSERT INTO @EMIDs (EMID) VALUES ('EM13');
    
    DECLARE @Parameters TABLE (ParameterName NVARCHAR(50));
     INSERT INTO @Parameters (ParameterName) VALUES ('CD_KWH'); INSERT INTO @Parameters (ParameterName) VALUES ('AVGPF');

           
    
    WITH TagIndices AS (
        SELECT TagIndex, TagName FROM dbo.TagTable_01
        WHERE EXISTS (
            SELECT 1 FROM @EMIDs e
            JOIN @Parameters p ON TagName = e.EMID + '_' + p.ParameterName
        )
    ),
    HourlyData AS (
-- Find the closest reading to either hh:00:00 or hh+1:00:00
SELECT 
CASE 
WHEN DATEPART(MINUTE, f.DateAndTime) >= 30 
THEN DATEADD(HOUR, DATEDIFF(HOUR, 0, f.DateAndTime) + 1, 0)  
ELSE DATEADD(HOUR, DATEDIFF(HOUR, 0, f.DateAndTime), 0)  
END AS RoundedDateTime,
f.DateAndTime AS ActualDateTime,
f.TagIndex,
f.Val,
ROW_NUMBER() OVER (
PARTITION BY t.TagName, 
      CASE 
          WHEN DATEPART(MINUTE, f.DateAndTime) >= 30 
          THEN DATEADD(HOUR, DATEDIFF(HOUR, 0, f.DateAndTime) + 1, 0)  
          ELSE DATEADD(HOUR, DATEDIFF(HOUR, 0, f.DateAndTime), 0)  
      END 
ORDER BY ABS(DATEDIFF(SECOND, 
CASE 
 WHEN DATEPART(MINUTE, f.DateAndTime) >= 30 
 THEN DATEADD(HOUR, DATEDIFF(HOUR, 0, f.DateAndTime) + 1, 0)  
 ELSE DATEADD(HOUR, DATEDIFF(HOUR, 0, f.DateAndTime), 0)  
END, f.DateAndTime))
) AS rn
FROM dbo.FloatTable_01 f
JOIN TagIndices t ON f.TagIndex = t.TagIndex
WHERE f.DateAndTime >= @TimeStart 
AND f.DateAndTime < @TimeEnd
),
LastReading AS (
-- Find the closest reading to 23:57 for each day
SELECT 
CAST(DATEADD(DAY, DATEDIFF(DAY, 0, f.DateAndTime), '23:57:00') AS DATETIME) AS RoundedDateTime,
f.DateAndTime AS ActualDateTime,
f.TagIndex,
f.Val,
ROW_NUMBER() OVER (
PARTITION BY t.TagName, CAST(f.DateAndTime AS DATE) 
ORDER BY ABS(DATEDIFF(SECOND, 
CAST(DATEADD(DAY, DATEDIFF(DAY, 0, f.DateAndTime), '23:57:00') AS DATETIME), 
f.DateAndTime))
) AS rn
FROM dbo.FloatTable_01 f
JOIN TagIndices t ON f.TagIndex = t.TagIndex
WHERE f.DateAndTime >= @TimeStart 
AND f.DateAndTime < @TimeEnd
),
FinalData AS (
-- Combine HourlyData and LastReading, ensuring final rounding
SELECT 
CASE 
WHEN DATEPART(MINUTE, h.ActualDateTime) >= 30 
THEN DATEADD(HOUR, DATEDIFF(HOUR, 0, h.ActualDateTime) + 1, 0)  
ELSE DATEADD(HOUR, DATEDIFF(HOUR, 0, h.ActualDateTime), 0)  
END AS DateAndTime, 
t.TagName, 
h.Val
FROM HourlyData h
JOIN TagIndices t ON h.TagIndex = t.TagIndex
WHERE h.rn = 1

UNION ALL

SELECT 
l.RoundedDateTime AS DateAndTime, 
t.TagName, 
l.Val
FROM LastReading l
JOIN TagIndices t ON l.TagIndex = t.TagIndex
WHERE l.rn = 1
)     
    SELECT * FROM FinalData
WHERE DateAndTime >= @TimeStart 
AND DateAndTime < @TimeEnd
ORDER BY DateAndTime, TagName;