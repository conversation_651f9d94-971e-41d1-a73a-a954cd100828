// Function to copy formatting and text from a range and apply to another range
function copyFormatAndText(sourceSheet, targetSheet, sourceRange, targetStartCol, times, formatonly = false, dataColumnsLength = 8, shiftcolumns = 0, startCopyfromColumn = 2) {
    const [start, end] = sourceRange.split(':');
    const startCell = sourceSheet.getCell(start);
    const endCell = sourceSheet.getCell(end);

    const startRow = startCell.row;
    const endRow = endCell.row;


    for (let i = 0; i < times; i++) {
        let targetColOffset = targetStartCol + i * dataColumnsLength; // Each EM has 8 columns
        for (let colOffset = 0; colOffset < dataColumnsLength; colOffset++) {
            const sourceCol = sourceSheet.getColumn(startCopyfromColumn + colOffset);
            const targetCol = targetSheet.getColumn(targetStartCol + i * dataColumnsLength + colOffset + shiftcolumns);
            targetCol.width = sourceCol.width;
        }
        for (let row = startRow; row <= endRow; row++) { // Rows B5:I7
            for (let colOffset = 0; colOffset < dataColumnsLength; colOffset++) {
                let sourceCell = sourceSheet.getCell(row, startCopyfromColumn + colOffset); // B5 = col 2, I7 = col 9
                let targetCell = targetSheet.getCell(row, targetColOffset + colOffset + shiftcolumns);
                //   console.log(row, startCopyfromColumn + colOffset, ':',row, targetColOffset + colOffset)
                // Copy formatting
                targetCell.style = { ...sourceCell.style };
                targetCell.numFmt = sourceCell.numFmt; // Copy number formatting
                targetCell.alignment = sourceCell.alignment; // Copy alignment
                targetCell.font = { ...sourceCell.font }; // Copy font styles
                targetCell.border = { ...sourceCell.border }; // Copy border styles
                targetCell.fill = { ...sourceCell.fill }; // Copy background fill

                // Copy value (text or number)
                if (!formatonly) { //&& sourceCell.value !== null
                    targetCell.value = sourceCell.value;
                }
            }
        }
    }
}

// Function to copy merged cell properties
function copyMergedCells(sourceSheet, targetSheet, sourceStartCol, targetStartCol, times, dataColumnsLength = 8, startRowNo = 5, endRowNo = 7, shiftcolumns = 0) {
    // Loop through all merged ranges in the worksheet
    sourceSheet.model.merges.forEach(mergeAddress => {


        const [start, end] = mergeAddress.split(':');
        const startCell = sourceSheet.getCell(start);
        const endCell = sourceSheet.getCell(end);

        const startRow = startCell.row;
        const startCol = startCell.col;
        const endRow = endCell.row;
        const endCol = endCell.col;
        // console.log(mergeAddress, startRow, startCol, endRow, endCol, sourceStartCol, targetStartCol);
        // Check if the merged cell falls within the B5:I7 range
        if (startCol >= sourceStartCol && endCol <= sourceStartCol + endRowNo && startRow >= startRowNo && endRow <= endRowNo) {

            for (let i = 0; i < times; i++) {
                let targetColOffset = targetStartCol + i * (dataColumnsLength + shiftcolumns); // Offset by 8 columns per EM
                let newStartCol = startCol + targetColOffset - sourceStartCol;
                let newEndCol = endCol + targetColOffset - sourceStartCol;

                targetSheet.mergeCells(startRow, newStartCol, endRow, newEndCol);
            }
        }
    });
}


function addFormulaForRow(
    worksheet,
    lastDatarowNo,
    formulatype,
    formulacolIndex = 8,
    dataColumnsLength = 8,
    firstdatarow = 8,
    shiftleftcol = 0,
    lastDataRowPerEM = null,
    emIDArray = null,
    shiftup = 0 // <-- add this parameter
) {
    let colIndex = formulacolIndex;
    let sumformularow = worksheet.getRow(lastDatarowNo + 1);
 
    if (
        lastDataRowPerEM &&
        typeof lastDataRowPerEM === 'object' &&
        emIDArray &&
        Array.isArray(emIDArray)
    ) {
        // Object version: use emIDArray for order
        for (let i = 0; i < emIDArray.length; i++) {
            let em = emIDArray[i];
            let lastRow = lastDataRowPerEM[em] || lastDatarowNo;
            let colLetter = worksheet.getCell(lastRow, colIndex - shiftleftcol).address.replace(/\d+/g, '');
            if (formulatype === 'sum') {
                sumformularow.getCell(colIndex).value = { formula: `SUM(${colLetter}${firstdatarow}:${colLetter}${lastRow})` };
            } else if (formulatype === 'upper') {
                sumformularow.getCell(colIndex).value = { formula: `${colLetter}${lastRow}` };
            }
            colIndex += dataColumnsLength;
        }
    } else if (lastDataRowPerEM && Array.isArray(lastDataRowPerEM)) {
        // Array version (old logic)
        for (let i = 0; i < lastDataRowPerEM.length; i++) {
            let lastRow = lastDataRowPerEM[i] || lastDatarowNo;
            let colLetter = worksheet.getCell(lastRow, colIndex - shiftleftcol).address.replace(/\d+/g, '');
            if (formulatype === 'sum') {
                sumformularow.getCell(colIndex).value = { formula: `SUM(${colLetter}${firstdatarow}:${colLetter}${lastRow})` };
            } else if (formulatype === 'upper') {
                sumformularow.getCell(colIndex).value = { formula: `${colLetter}${lastRow}` };
            }
            colIndex += dataColumnsLength;
        }
    } else {
        // Fallback logic
        while (worksheet.getCell(lastDatarowNo-shiftup, colIndex).value) {
            let colLetter = worksheet.getCell(lastDatarowNo, colIndex - shiftleftcol).address.replace(/\d+/g, '');
              console.log(colLetter);
            if (formulatype === 'sum') {
                sumformularow.getCell(colIndex).value = { formula: `SUM(${colLetter}${firstdatarow}:${colLetter}${lastDatarowNo})` };
            } else if (formulatype === 'upper') {
                sumformularow.getCell(colIndex).value = { formula: `${colLetter}${lastDatarowNo}` };
            }
            colIndex += dataColumnsLength;
        }
    }
    sumformularow.commit();
}

function writeEMNames(worksheet, emNameArray, dataColumnsLength = 8, EMnameRowNo = 5) {
    let colIndex = 2; // Start at column 2 (B)

    emNameArray.forEach((name) => {
        worksheet.getCell(EMnameRowNo, colIndex).value = name; // Write EM name in Row 5
        colIndex += dataColumnsLength; // Move to the next EM position (every 8th column)
    });
}

function expandMergedCells(worksheet, emIDArray, dataColumnsLength = 8, endColumnLetter = 'I', startDescColumn = 'D', extracolumns = 0) {
    let additionalColumns = ((emIDArray.length - 1) * dataColumnsLength) + extracolumns; // Extra width needed
    /// console.log('additionalColumns:', additionalColumns);
    if (additionalColumns > 0) {
        let newCol = getExcelColumn(dataColumnsLength + 1 + additionalColumns); // New ending column0
        //console.log(startDescColumn);
        // Unmerge existing cells before merging again
        safeUnmerge(worksheet, `B1:${endColumnLetter}1`);
        safeUnmerge(worksheet, `${startDescColumn}2:${endColumnLetter}2`);
        safeUnmerge(worksheet, `${startDescColumn}3:${endColumnLetter}3`);
        safeUnmerge(worksheet, `${startDescColumn}4:${endColumnLetter}4`);

        // console.log( `B1:${endColumnLetter}1`);
        // console.log( `${startDescColumn}2:${endColumnLetter}2`);
        // console.log( `${startDescColumn}2:${endColumnLetter}3`);
        //  console.log( `${startDescColumn}2:${endColumnLetter}4`);


        // Re-merge with the new expanded range
        worksheet.mergeCells(`B1:${newCol}1`);
        worksheet.mergeCells(`${startDescColumn}2:${newCol}2`);
        worksheet.mergeCells(`${startDescColumn}3:${newCol}3`);
        worksheet.mergeCells(`${startDescColumn}4:${newCol}4`);

    }
}

// Function to safely unmerge a cell range (avoiding errors)
function safeUnmerge(worksheet, range) {
    if (worksheet.getCell(range.split(':')[0]).isMerged) {
        worksheet.unMergeCells(range);
    }
}

function applyThickBorderToSheet(worksheet, firstdatarow = 8, shituprows = 0, lastRow = null) {
    let firstRow = 1; // Typically the starting row
    let firstCol = 1; // Typically the starting column
    if (lastRow === null)
        lastRow = worksheet.rowCount - shituprows; // Get last row with data

    // Find the last column by checking row 8
    let lastCol = 1;
    let checkrow = worksheet.getRow(firstdatarow);
    checkrow.eachCell((cell, colNumber) => {
        if (cell.value)
            lastCol = colNumber; // Update lastCol if cell has a value
    });
    //    console.log(lastRow, lastCol);



    // Apply thick border only to the outermost edges
    for (let row = firstRow; row <= lastRow; row++) {
        for (let col = firstCol; col <= lastCol; col++) {
            let cell = worksheet.getCell(row, col);

            // Preserve existing styles
            let existingBorder = cell.border || {};
            if (col === 2) {
                //   console.log(row, col, existingBorder);
            }
            cell.border = {
                top: row === firstRow ? { style: 'thick' } : existingBorder.top,
                bottom: row === lastRow ? { style: 'thick' } : existingBorder.bottom,
                left: col === firstCol ? { style: 'thick' } : existingBorder.left,
                right: col === lastCol ? { style: 'thick' } : existingBorder.right
            };
            if (col === 2) {
                //   console.log(row, col, cell.border);
            }
        }
    }
}

function applyThickToLeft(worksheet, startRow, endRow) {
    const firstCol = 1; // Typically the first column
    for (let row = startRow; row <= endRow; row++) {
        let cell = worksheet.getCell(row, firstCol);
        let existingBorder = cell.border || {};
        cell.border = {
            ...existingBorder,
            left: { style: 'thick' } // Apply thick border to the left
        };
    }
}


// Helper function to get Excel column letter from index
function getExcelColumn(colIndex) {
    let colLetter = '';
    while (colIndex > 0) {
        colIndex--;
        colLetter = String.fromCharCode(65 + (colIndex % 26)) + colLetter;
        colIndex = Math.floor(colIndex / 26);
    }
    return colLetter;
}

function columnToNumber(column) {
    let result = 0;
    for (let i = 0; i < column.length; i++) {
        result = result * 26 + (column.charCodeAt(i) - 64);
    }
    return result;
}


function copyColumnLength(worksheet, startColumn, endColumn, repeatCount) {
    for (let i = 0; i < repeatCount; i++) {
        for (let col = startColumn; col <= endColumn; col++) {
            const currentColumn = worksheet.getColumn(col);
            const newColumn = worksheet.getColumn(endColumn + 1 + i * (endColumn - startColumn + 1) + (col - startColumn));
            newColumn.width = currentColumn.width;
        }
    }
}

function moveproductioncells(worksheet, sourceStartCol, sourceEndCol, sourceStartRow, sourceEndRow, emIDArray) {
    // Copy production cells from source to target    
    // Number of columns to leave blank
    const emLength = emIDArray.length; // Assuming Emarray is defined
    const blankCols = (emLength - 1) * 3;

    // Determine the target start column
    const targetStartCol = sourceEndCol + blankCols + 1; // Start after blank columns

    // Copy values & formatting to the target location
    for (let srcCol = sourceStartCol; srcCol <= sourceEndCol; srcCol++) {
        let targetCol = targetStartCol + (srcCol - sourceStartCol); // Maintain relative position

        for (let row = sourceStartRow; row <= sourceEndRow; row++) {
            let sourceCell = worksheet.getCell(row, srcCol);
            let targetCell = worksheet.getCell(row, targetCol);

            // Copy value
            targetCell.value = sourceCell.value;

            // Copy formatting (font, alignment, fill, border, etc.)
            targetCell.style = { ...sourceCell.style };
        }


    }
    for (let srcCol = sourceStartCol; srcCol <= sourceEndCol; srcCol++) {
        for (let row = sourceStartRow; row <= sourceEndRow; row++) {
            let sourceCell = worksheet.getCell(row, srcCol);

            //console.log(sourceCell.value);
            sourceCell.value = null;  // Remove the value
            sourceCell.style = {};     // Reset the style
        }
    }

}

function applyProductionFormula(worksheet, targetColumn, targetRow, row1, row2) {
    const targetCell = worksheet.getCell(targetRow, targetColumn);
    const col1Letter = String.fromCharCode(64 + targetColumn); // Convert column number to letter

    targetCell.value = { formula: `IF(AND(ISNUMBER(${col1Letter}${row1}),ISNUMBER(${col1Letter}${row2})),${col1Letter}${row1}/${col1Letter}${row2},"")` };

}

function applyMergedCellFormula(worksheet, startColumn, startRow, endRow, repeatCount, dataColumnsLength = 3, formulatype = 'equal', endColumn = null) {
    const merges = worksheet.model.merges; // Access merged cells

    // console.log(merges);
    if (endColumn === null) {
        endColumn = startColumn + (dataColumnsLength) * repeatCount; // Calculate the end column based on the start column and dataColumnsLength
    }

    // Process merged cells within the given criteria
    merges.forEach((mergeRange) => {
        // console.log(mergeRange);
        const [start, end] = mergeRange.split(':');

        const [currstartCol, currstartRow] = [start.replace(/\d+/g, ''), parseInt(start.replace(/\D+/g, ''))];
        const [currendCol, currendRowMerge] = [end.replace(/\d+/g, ''), parseInt(end.replace(/\D+/g, ''))];

        // Check if the merged cell lies within the given column and row criteria

        if (currstartRow >= startRow && currendRowMerge <= endRow && columnToNumber(currstartCol) >= startColumn && columnToNumber(currendCol) <= endColumn) {
            if (formulatype === 'equal') {
                const formulaCell = worksheet.getCell(currendRowMerge, columnToNumber(currendCol));
                const previousColumnLetter = getExcelColumn(columnToNumber(currendCol) - 1); // Get the previous column letter
                if (currendRowMerge - startRow <= 8) {
                    formulaCell.value = { formula: `IF(ISNUMBER(${previousColumnLetter}${currendRowMerge}),${previousColumnLetter}${currendRowMerge},"")` }; // Set formula to reference the previous column's value
                } else {
                    formulaCell.value = { formula: `IF(AND(ISNUMBER(${previousColumnLetter}${currendRowMerge}), ISNUMBER(${previousColumnLetter}${currstartRow - 1})),${previousColumnLetter}${currendRowMerge} - ${previousColumnLetter}${currstartRow - 1},"")` }; // Set formula to reference the previous column's value
                }

            } else if (formulatype === 'sum') {
                const formulaCell = worksheet.getCell(currstartRow, columnToNumber(currstartCol));
                let sumColumns = [];
                for (let i = 0; i <= repeatCount; i++) {
                    const columnLetter = getExcelColumn(columnToNumber(currendCol) - (i * dataColumnsLength) - 2); // Calculate column letter
                    sumColumns.push(`${columnLetter}${currstartRow}`);
                }
                formulaCell.value = { formula: `IF(LEN(${sumColumns.join('&')})>0,SUM(${sumColumns.join(',')}),"")` }; // Set formula to sum the calculated columns
            }
        }
    });

}

function finalTotalFormula(worksheet, lastcolumnId, totalrowId,datastartRow) {
    const columnLetter = getExcelColumn(lastcolumnId); // Get the letter of the last column
    const formulaCell = worksheet.getCell(totalrowId, lastcolumnId - 1);
    let sumColumns = [];
    for (let i = datastartRow; i <= totalrowId-1; i++) {
        sumColumns.push(`${columnLetter}${i}`);
    }
    formulaCell.value = { formula: `IF(LEN(${sumColumns.join('&')})>0,SUM(${sumColumns.join(',')}),"")` }; // Set formula to sum the calculated columns
}

async function protectsheetfn(worksheet, arrayunlockedcells, password = 'randomprotecPassword') {
    await worksheet.protect(password, { selectLockedCells: true, selectUnlockedCells: true });

    arrayunlockedcells.forEach(unlockcell => {
        worksheet.getCell(unlockcell).protection = {
            locked: false,
            hidden: false,
        };
    });
}

function totalLastColumns(worksheet, emIDArray, startRow, endRow, lastColumnId) {
    const totalColumns = emIDArray.length; // Number of columns to sum
    const lastColumnLetter = getExcelColumn(lastColumnId); // Get the letter of the last column

    for (let row = startRow; row <= endRow; row++) {
        let sumColumns = [];
        for (let i = 0; i < totalColumns; i++) {
            const columnLetter = getExcelColumn(lastColumnId - 1 - i); // Get the column letter for each of the last columns
            sumColumns.push(`${columnLetter}${row}`);
        }
        const targetCell = worksheet.getCell(row, lastColumnId); // Place the total in the next column
        targetCell.value = { formula: `IF(LEN(${sumColumns.join('&')})>0,SUM(${sumColumns.join(',')}),"")` }; // Set the sum formula
      //  console.log(targetCell.value);
    }
}

function applyThinBorderToRange(worksheet, startCol, startRow, endCol, endRow) {
    // Convert cell references to row & column numbers

    // console.log(startCol, startRow, endCol, endRow);
    // Apply thin border to each cell in the range
    for (let row = startRow; row <= endRow; row++) {
        for (let col = startCol; col <= endCol; col++) {

            let cell = worksheet.getCell(row, col);
            //  console.log(row, col);

            cell.border = {
                top: { style: 'thin', color: { indexed: 64 } },
                bottom: { style: 'thin', color: { indexed: 64 } },
                left: (col === 1) ?
                    { style: 'thick', color: { indexed: 64 } } : { style: 'thin', color: { indexed: 64 } },
                right: { style: 'thin', color: { indexed: 64 } }
            };


            /*cell.border = {
                top: { style: 'thin', color: { indexed: 64 } },
                bottom: { style: 'thin', color: { indexed: 64 } },
                left: { style: 'thin', color: { indexed: 64 } },
                right: { style: 'thin', color: { indexed: 64 } }*/


        }
    }
}

function applyThickToBottom(worksheet, cellArray = []) {

    cellArray.forEach(cellAdress => {
        worksheet.getCell(cellAdress).style = {
            ...worksheet.getCell(cellAdress).style,
            border: {
                ...worksheet.getCell(cellAdress).style.border,
                bottom: { style: 'thick' }
            }
        };

    })

}

function applyThickToRight(worksheet, cellArray = []) {

    cellArray.forEach(cellAdress => {
        worksheet.getCell(cellAdress).style = {
            ...worksheet.getCell(cellAdress).style,
            border: {
                ...worksheet.getCell(cellAdress).style.border,
                right: { style: 'thick' }
            }
        };

    })

}

module.exports = {
    copyFormatAndText,
    copyMergedCells,
    addFormulaForRow,
    writeEMNames,
    expandMergedCells,
    applyThickBorderToSheet,
    safeUnmerge,
    copyColumnLength,
    moveproductioncells,
    applyProductionFormula,
    applyMergedCellFormula,
    protectsheetfn,
    finalTotalFormula,
    totalLastColumns,
    applyThinBorderToRange,
    applyThickToLeft,
    applyThickToBottom, applyThickToRight
};