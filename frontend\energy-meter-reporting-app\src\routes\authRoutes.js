const express = require('express');
const { login, register, session, getUserProfile } = require('../controllers/authController');
const { authenticate } = require('../utils/middleware');

const router = express.Router();

// User registration route
router.post('/register', register);

// User login route
router.post('/login', login);

// Example of a protected route
/*router.get('/profile', authenticate, (req, res) => {
    res.json({ message: 'This is your profile', user: req.user });
});

router.get('/session', session);*/
router.get('/profile', authenticate, getUserProfile);
router.get('/session', authenticate, session);


module.exports = router;