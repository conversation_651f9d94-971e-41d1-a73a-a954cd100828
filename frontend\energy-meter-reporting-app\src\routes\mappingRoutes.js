const express = require('express');
const router = express.Router();
const mappingController = require('../controllers/mappingController');
const { authenticate, authorizeRole } = require('../utils/middleware');


// Route to map an energy meter to a location
router.post('/map', authenticate, authorize<PERSON><PERSON>('SuperAdmin'),mappingController.mapEnergyMeter);

// Route to get all mappings
router.get('/', mappingController.getAllMappings);

// Route to get a specific mapping by ID
router.get('/:id', mappingController.getMappingById);

// Route to update a mapping
router.put('/:id',authenticate, authorizeRole('SuperAdmin'), mappingController.updateMapping);

// Route to delete a mapping
router.delete('/:id',authenticate, authorize<PERSON>ole('SuperAdmin'), mappingController.deleteMapping);

module.exports = router;