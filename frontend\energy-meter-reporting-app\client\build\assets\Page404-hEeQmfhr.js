import{j as s}from"./index-BDJ8oeCE.js";import{b as r,c as o,a as e}from"./index.esm-DSzlmaRN.js";import{C as a,a as t}from"./CRow-C1o2zw34.js";import{C as i,a as n}from"./CInputGroupText-BGHrT9V9.js";import{c as l}from"./cil-magnifying-glass-COgjSVTM.js";import{C as c}from"./CFormInput-LKfVdWds.js";import"./CFormControlWrapper-CyzWU6Dg.js";import"./CFormControlValidation-_wBnnnml.js";import"./CFormLabel-CzXD3nfE.js";const y=()=>s.jsx("div",{className:"bg-body-tertiary min-vh-100 d-flex flex-row align-items-center",children:s.jsx(r,{children:s.jsx(a,{className:"justify-content-center",children:s.jsxs(t,{md:6,children:[s.jsxs("div",{className:"clearfix",children:[s.jsx("h1",{className:"float-start display-3 me-4",children:"404"}),s.jsxs("h4",{className:"pt-3",children:["Oops! You","'","re lost."]}),s.jsx("p",{className:"text-body-secondary float-start",children:"The page you are looking for was not found."})]}),s.jsxs(i,{className:"input-prepend",children:[s.jsx(n,{children:s.jsx(o,{icon:l})}),s.jsx(c,{type:"text",placeholder:"What are you looking for?"}),s.jsx(e,{color:"info",children:"Search"})]})]})})})});export{y as default};
